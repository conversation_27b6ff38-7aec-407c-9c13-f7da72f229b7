#!/bin/bash

# =============================================================================
# Yogurt AI QC - macOS direnv修复脚本
# =============================================================================
# 专门为macOS系统修复direnv配置问题
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "=================================="
    echo "    macOS Direnv 修复脚本"
    echo "=================================="
    echo -e "${NC}"
    echo "🍎 专门为macOS系统优化direnv配置"
    echo ""
}

# 检测当前Shell
detect_shell() {
    if [ -n "$ZSH_VERSION" ]; then
        echo "zsh"
    elif [ -n "$BASH_VERSION" ]; then
        echo "bash"
    else
        # 从SHELL环境变量检测
        case "$SHELL" in
            */zsh) echo "zsh" ;;
            */bash) echo "bash" ;;
            *) echo "zsh" ;;  # macOS默认使用zsh
        esac
    fi
}

# 主修复函数
main() {
    show_banner
    
    local shell=$(detect_shell)
    log_step "检测到Shell: $shell"
    
    # 设置配置文件路径
    local config_file=""
    local hook_command=""
    
    if [ "$shell" = "zsh" ]; then
        config_file="$HOME/.zshrc"
        hook_command='eval "$(direnv hook zsh)"'
    else
        config_file="$HOME/.bashrc"
        hook_command='eval "$(direnv hook bash)"'
    fi
    
    log_info "配置文件: $config_file"
    
    # 检查direnv是否安装
    log_step "检查direnv安装状态..."
    
    if [ ! -f "/opt/homebrew/bin/direnv" ]; then
        log_error "direnv未安装，正在安装..."
        brew install direnv
    else
        log_success "direnv已安装: $(/opt/homebrew/bin/direnv version)"
    fi
    
    # 确保Homebrew路径在PATH中
    log_step "检查PATH配置..."
    
    if ! echo "$PATH" | grep -q "/opt/homebrew/bin"; then
        log_warning "PATH中缺少Homebrew路径"
        
        # 添加到配置文件
        if ! grep -q 'export PATH="/opt/homebrew/bin:$PATH"' "$config_file" 2>/dev/null; then
            echo "" >> "$config_file"
            echo "# Homebrew PATH" >> "$config_file"
            echo 'export PATH="/opt/homebrew/bin:$PATH"' >> "$config_file"
            log_success "已添加Homebrew PATH到 $config_file"
        fi
    else
        log_success "PATH配置正确"
    fi
    
    # 配置direnv hook
    log_step "配置direnv hook..."
    
    if ! grep -q "direnv hook" "$config_file" 2>/dev/null; then
        echo "" >> "$config_file"
        echo "# Direnv hook for automatic environment activation" >> "$config_file"
        echo "$hook_command" >> "$config_file"
        log_success "已添加direnv hook到 $config_file"
    else
        log_info "direnv hook已存在"
    fi
    
    # 立即应用配置
    log_step "应用配置..."
    
    # 确保PATH包含Homebrew
    export PATH="/opt/homebrew/bin:$PATH"
    
    # 初始化direnv
    eval "$hook_command"
    
    # 验证direnv是否工作
    if command -v direnv >/dev/null 2>&1; then
        log_success "direnv现在可以使用: $(direnv version)"
    else
        log_error "direnv仍然无法使用"
        exit 1
    fi
    
    # 配置项目环境
    log_step "配置项目环境..."
    
    if [ -f ".envrc" ]; then
        log_info "允许.envrc文件..."
        direnv allow
        log_success "项目环境已配置"
    else
        log_warning ".envrc文件不存在"
    fi
    
    # 显示使用指南
    show_usage_guide "$shell" "$config_file"
    
    log_success "macOS direnv配置完成！"
}

# 显示使用指南
show_usage_guide() {
    local shell="$1"
    local config_file="$2"
    
    echo ""
    echo -e "${CYAN}🎯 使用指南:${NC}"
    echo ""
    echo "1. 重新启动终端，或运行以下命令:"
    echo "   source $config_file"
    echo ""
    echo "2. 验证direnv是否工作:"
    echo "   direnv version"
    echo ""
    echo "3. 测试自动环境激活:"
    echo "   cd $(pwd)"
    echo "   # 应该看到环境自动激活的消息"
    echo ""
    echo "4. 如果仍有问题，请:"
    echo "   - 完全重启终端应用"
    echo "   - 或运行: exec $shell"
    echo ""
    echo -e "${GREEN}🚀 现在每次进入项目目录都会自动激活conda环境！${NC}"
}

# 运行主函数
main "$@"
