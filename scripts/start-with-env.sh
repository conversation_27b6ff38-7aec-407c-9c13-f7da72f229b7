#!/bin/bash

# =============================================================================
# 带环境变量的启动脚本
# =============================================================================
# 此脚本加载 .env 和 .env.local 文件，然后启动 Docker Compose
# =============================================================================

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查 .env.local 文件
check_env_local() {
    if [ ! -f .env.local ]; then
        log_warning ".env.local 文件不存在"
        echo "请运行以下命令创建安全环境配置："
        echo "  ./scripts/setup-secure-env.sh"
        exit 1
    fi
    log_success ".env.local 文件存在"
}

# 加载环境变量
load_env_vars() {
    log_info "加载环境变量..."
    
    # 加载 .env 文件
    if [ -f .env ]; then
        set -a
        source .env
        set +a
        log_info "已加载 .env"
    fi
    
    # 加载 .env.local 文件（覆盖 .env 中的值）
    if [ -f .env.local ]; then
        set -a
        source .env.local
        set +a
        log_info "已加载 .env.local"
    fi
    
    log_success "环境变量加载完成"
}

# 启动服务
start_services() {
    local service="$1"
    
    log_info "启动服务: ${service:-all}"
    
    if [ -n "$service" ]; then
        docker-compose up -d "$service"
    else
        docker-compose up -d
    fi
    
    log_success "服务启动完成"
}

# 显示帮助信息
show_help() {
    echo "使用方法: $0 [选项] [服务名]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -d, --down     停止所有服务"
    echo "  -r, --restart  重启服务"
    echo ""
    echo "服务名:"
    echo "  postgres       只启动 PostgreSQL"
    echo "  redis          只启动 Redis"
    echo "  (空)           启动所有服务"
    echo ""
    echo "示例:"
    echo "  $0                    # 启动所有服务"
    echo "  $0 postgres           # 只启动 PostgreSQL"
    echo "  $0 --down             # 停止所有服务"
    echo "  $0 --restart postgres # 重启 PostgreSQL"
}

# 主函数
main() {
    case "$1" in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--down)
            log_info "停止所有服务..."
            docker-compose down
            log_success "服务已停止"
            exit 0
            ;;
        -r|--restart)
            log_info "重启服务: ${2:-all}"
            check_env_local
            load_env_vars
            docker-compose down "${2:-}"
            start_services "$2"
            exit 0
            ;;
        *)
            check_env_local
            load_env_vars
            start_services "$1"
            ;;
    esac
}

# 运行主函数
main "$@"
