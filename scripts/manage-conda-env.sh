#!/bin/bash

# =============================================================================
# Yogurt AI QC - Conda环境管理脚本
# =============================================================================
# 此脚本用于管理项目的conda环境，包括创建、更新、删除等操作
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 环境配置
ENV_NAME="yoghurt-ai-qc"
ENV_FILE="environment-latest.yml"
REQUIREMENTS_FILE="ai-service/requirements-slim.txt"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "=================================="
    echo "   Conda环境管理 - Yogurt AI QC"
    echo "=================================="
    echo -e "${NC}"
}

# 检查conda是否安装
check_conda() {
    if ! command -v conda &> /dev/null; then
        log_error "Conda 未安装！"
        echo ""
        echo "请安装 Miniconda 或 Anaconda："
        echo "  Miniconda: https://docs.conda.io/en/latest/miniconda.html"
        echo "  Anaconda: https://www.anaconda.com/products/distribution"
        exit 1
    fi
    
    log_success "Conda 已安装: $(conda --version)"
}

# 检查环境文件是否存在
check_env_files() {
    if [ ! -f "$ENV_FILE" ]; then
        log_error "环境文件不存在: $ENV_FILE"
        exit 1
    fi
    
    if [ ! -f "$REQUIREMENTS_FILE" ]; then
        log_warning "Requirements文件不存在: $REQUIREMENTS_FILE"
    fi
    
    log_success "环境配置文件检查完成"
}

# 检查环境是否存在
env_exists() {
    conda env list | grep -q "^$ENV_NAME\s"
}

# 创建conda环境
create_env() {
    log_step "创建Conda环境: $ENV_NAME"
    
    if env_exists; then
        log_warning "环境 '$ENV_NAME' 已存在"
        echo "是否要删除现有环境并重新创建? (y/N)"
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            remove_env
        else
            log_info "跳过环境创建"
            return 0
        fi
    fi
    
    log_info "从 $ENV_FILE 创建环境..."
    conda env create -f "$ENV_FILE"
    
    if env_exists; then
        log_success "环境创建成功"
        
        # 激活环境并安装额外依赖
        if [ -f "$REQUIREMENTS_FILE" ]; then
            log_info "安装额外的pip依赖..."
            conda run -n "$ENV_NAME" pip install -r "$REQUIREMENTS_FILE"
            log_success "pip依赖安装完成"
        fi
    else
        log_error "环境创建失败"
        exit 1
    fi
}

# 更新conda环境
update_env() {
    log_step "更新Conda环境: $ENV_NAME"
    
    if ! env_exists; then
        log_error "环境 '$ENV_NAME' 不存在，请先创建环境"
        exit 1
    fi
    
    log_info "更新环境依赖..."
    conda env update -f "$ENV_FILE" --prune
    
    # 更新pip依赖
    if [ -f "$REQUIREMENTS_FILE" ]; then
        log_info "更新pip依赖..."
        conda run -n "$ENV_NAME" pip install -r "$REQUIREMENTS_FILE" --upgrade
        log_success "pip依赖更新完成"
    fi
    
    log_success "环境更新完成"
}

# 删除conda环境
remove_env() {
    log_step "删除Conda环境: $ENV_NAME"
    
    if ! env_exists; then
        log_warning "环境 '$ENV_NAME' 不存在"
        return 0
    fi
    
    echo "确定要删除环境 '$ENV_NAME' 吗? (y/N)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        conda env remove -n "$ENV_NAME"
        log_success "环境删除完成"
    else
        log_info "取消删除操作"
    fi
}

# 显示环境信息
show_env_info() {
    log_step "环境信息: $ENV_NAME"
    
    if ! env_exists; then
        log_warning "环境 '$ENV_NAME' 不存在"
        return 0
    fi
    
    echo ""
    echo "📋 环境详情:"
    conda info --envs | grep "$ENV_NAME"
    
    echo ""
    echo "📦 主要包信息:"
    conda run -n "$ENV_NAME" conda list | grep -E "(python|fastapi|tensorflow|torch|opencv|numpy|pandas)" | head -10
    
    echo ""
    echo "💾 环境大小:"
    local env_path=$(conda info --envs | grep "$ENV_NAME" | awk '{print $2}')
    if [ -d "$env_path" ]; then
        du -sh "$env_path" 2>/dev/null || echo "无法计算大小"
    fi
}

# 导出环境
export_env() {
    log_step "导出环境配置"
    
    if ! env_exists; then
        log_error "环境 '$ENV_NAME' 不存在"
        exit 1
    fi
    
    local export_file="environment-export-$(date +%Y%m%d).yml"
    
    log_info "导出环境到: $export_file"
    conda env export -n "$ENV_NAME" > "$export_file"
    
    # 也导出pip requirements
    local pip_export="requirements-export-$(date +%Y%m%d).txt"
    log_info "导出pip依赖到: $pip_export"
    conda run -n "$ENV_NAME" pip freeze > "$pip_export"
    
    log_success "环境导出完成"
    echo "  - Conda环境: $export_file"
    echo "  - Pip依赖: $pip_export"
}

# 激活环境指南
show_activation_guide() {
    echo ""
    echo -e "${CYAN}🚀 环境激活指南:${NC}"
    echo ""
    echo "1. 激活环境:"
    echo "   conda activate $ENV_NAME"
    echo ""
    echo "2. 启动AI服务:"
    echo "   cd ai-service"
    echo "   python -m uvicorn src.main:app --reload --port 6800"
    echo ""
    echo "3. 或使用项目脚本:"
    echo "   npm run dev:ai"
    echo ""
    echo "4. 停用环境:"
    echo "   conda deactivate"
    echo ""
    echo "5. Jupyter开发:"
    echo "   jupyter lab --port 8888"
}

# 显示帮助信息
show_help() {
    echo "使用方法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  create    创建conda环境"
    echo "  update    更新conda环境"
    echo "  remove    删除conda环境"
    echo "  info      显示环境信息"
    echo "  export    导出环境配置"
    echo "  activate  显示激活指南"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 create     # 创建环境"
    echo "  $0 update     # 更新环境"
    echo "  $0 info       # 查看环境信息"
}

# 主函数
main() {
    show_banner
    
    local command=${1:-help}
    
    case $command in
        create)
            check_conda
            check_env_files
            create_env
            show_activation_guide
            ;;
        update)
            check_conda
            check_env_files
            update_env
            ;;
        remove)
            check_conda
            remove_env
            ;;
        info)
            check_conda
            show_env_info
            ;;
        export)
            check_conda
            export_env
            ;;
        activate)
            show_activation_guide
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
