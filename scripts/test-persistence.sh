#!/bin/bash

# =============================================================================
# 数据持久化测试脚本
# =============================================================================
# 
# 此脚本用于测试数据库的持久化存储功能
# 验证数据在容器重启后是否保持
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🧪 数据持久化测试"
echo "=================="

# 检查容器是否运行
log_info "检查容器状态..."
if ! docker ps | grep -q "yoghurt-postgres"; then
    log_error "PostgreSQL容器未运行，请先启动: docker-compose up -d postgres redis"
    exit 1
fi

if ! docker ps | grep -q "yoghurt-redis"; then
    log_error "Redis容器未运行，请先启动: docker-compose up -d postgres redis"
    exit 1
fi

# 测试数据
TEST_TABLE="persistence_test_$(date +%s)"
TEST_DATA="持久化测试数据_$(date +%Y%m%d_%H%M%S)"
REDIS_KEY="test:persistence:$(date +%s)"
REDIS_VALUE="Redis持久化测试_$(date +%Y%m%d_%H%M%S)"

echo ""
log_info "步骤1: 创建测试数据..."

# 在PostgreSQL中创建测试数据
log_info "在PostgreSQL中创建测试表和数据..."
docker exec yoghurt-postgres psql -U postgres -d postgres -c "
CREATE TABLE $TEST_TABLE (
    id SERIAL PRIMARY KEY,
    data TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);"

docker exec yoghurt-postgres psql -U postgres -d postgres -c "
INSERT INTO $TEST_TABLE (data) VALUES ('$TEST_DATA');"

# 验证数据插入
PG_COUNT=$(docker exec yoghurt-postgres psql -U postgres -d postgres -t -c "SELECT COUNT(*) FROM $TEST_TABLE;")
if [ "$PG_COUNT" -eq 1 ]; then
    log_success "PostgreSQL测试数据创建成功"
else
    log_error "PostgreSQL测试数据创建失败"
    exit 1
fi

# 在Redis中创建测试数据
log_info "在Redis中创建测试数据..."
docker exec yoghurt-redis redis-cli SET "$REDIS_KEY" "$REDIS_VALUE"

# 验证Redis数据
REDIS_RESULT=$(docker exec yoghurt-redis redis-cli GET "$REDIS_KEY")
if [ "$REDIS_RESULT" = "$REDIS_VALUE" ]; then
    log_success "Redis测试数据创建成功"
else
    log_error "Redis测试数据创建失败"
    exit 1
fi

echo ""
log_info "步骤2: 停止容器..."
docker-compose down

echo ""
log_info "步骤3: 重新启动容器..."
docker-compose up -d postgres redis

# 等待容器完全启动
log_info "等待容器启动..."
sleep 15

# 等待健康检查通过
log_info "等待健康检查..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if docker ps | grep yoghurt-postgres | grep -q "healthy\|Up" && \
       docker ps | grep yoghurt-redis | grep -q "healthy\|Up"; then
        log_success "容器启动完成"
        break
    fi
    
    sleep 2
    attempt=$((attempt + 1))
done

if [ $attempt -gt $max_attempts ]; then
    log_error "容器启动超时"
    exit 1
fi

echo ""
log_info "步骤4: 验证数据持久化..."

# 验证PostgreSQL数据
log_info "验证PostgreSQL数据..."
PG_DATA=$(docker exec yoghurt-postgres psql -U postgres -d postgres -t -c "SELECT data FROM $TEST_TABLE WHERE id = 1;")
PG_DATA=$(echo "$PG_DATA" | xargs)  # 去除空格

if [ "$PG_DATA" = "$TEST_DATA" ]; then
    log_success "✅ PostgreSQL数据持久化测试通过"
else
    log_error "❌ PostgreSQL数据持久化测试失败"
    echo "期望: $TEST_DATA"
    echo "实际: $PG_DATA"
fi

# 验证Redis数据
log_info "验证Redis数据..."
REDIS_RESULT=$(docker exec yoghurt-redis redis-cli GET "$REDIS_KEY")

if [ "$REDIS_RESULT" = "$REDIS_VALUE" ]; then
    log_success "✅ Redis数据持久化测试通过"
else
    log_error "❌ Redis数据持久化测试失败"
    echo "期望: $REDIS_VALUE"
    echo "实际: $REDIS_RESULT"
fi

echo ""
log_info "步骤5: 清理测试数据..."

# 清理PostgreSQL测试数据
docker exec yoghurt-postgres psql -U postgres -d postgres -c "DROP TABLE $TEST_TABLE;"
log_success "PostgreSQL测试数据已清理"

# 清理Redis测试数据
docker exec yoghurt-redis redis-cli DEL "$REDIS_KEY"
log_success "Redis测试数据已清理"

echo ""
log_success "🎉 数据持久化测试完成！"

echo ""
log_info "📊 数据卷信息:"
docker volume ls | grep yoghurt

echo ""
log_info "💾 存储使用情况:"
docker system df -v | grep yoghurt
