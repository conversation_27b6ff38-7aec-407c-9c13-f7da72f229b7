#!/bin/bash

# =============================================================================
# 环境变量加载脚本
# =============================================================================
# 此脚本用于加载 .env 和 .env.local 文件中的环境变量
# =============================================================================

# 加载 .env 文件
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
fi

# 加载 .env.local 文件（覆盖 .env 中的值）
if [ -f .env.local ]; then
    export $(grep -v '^#' .env.local | xargs)
fi

echo "环境变量已加载"
