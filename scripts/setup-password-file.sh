#!/bin/bash

# =============================================================================
# 密码文件设置脚本
# =============================================================================
# 此脚本创建一个外部密码文件，完全避免在代码中存储密码
# =============================================================================

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建密码目录
create_password_dir() {
    local password_dir=".secrets"
    
    if [ ! -d "$password_dir" ]; then
        mkdir -p "$password_dir"
        chmod 700 "$password_dir"
        log_success "创建密码目录: $password_dir"
    fi
    
    echo "$password_dir"
}

# 生成密码文件
create_password_file() {
    local password_dir="$1"
    local password_file="$password_dir/db_password"
    local password=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-32)
    
    echo "$password" > "$password_file"
    chmod 600 "$password_file"
    
    log_success "密码文件已创建: $password_file"
    echo "$password"
}

# 更新 .env 文件使用密码文件
update_env_file() {
    local password_file="$1"
    
    log_info "更新 .env 文件使用密码文件..."
    
    # 备份原文件
    cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
    
    # 创建新的 .env 内容
    cat > .env << EOF
# =============================================================================
# Yogurt AI QC - 环境变量配置
# =============================================================================
# 
# 安全说明：
# - 数据库密码存储在外部文件中，不在此配置文件中
# - 密码文件路径: .secrets/db_password
# - 应用程序会自动读取密码文件
# =============================================================================

# =============================================================================
# 应用基础配置
# =============================================================================
NODE_ENV=development
APP_NAME="Yogurt AI QC"
APP_VERSION=1.0.0
APP_PORT=6000

# =============================================================================
# 数据库配置
# =============================================================================
# PostgreSQL 主数据库
# 注意：密码从外部文件读取，不在此处硬编码
DATABASE_URL_TEMPLATE=postgresql://postgres:PASSWORD_PLACEHOLDER@localhost:6432/postgres
DB_HOST=localhost
DB_PORT=6432
DB_NAME=postgres
DB_USER=postgres
DB_PASSWORD_FILE=$password_file
DB_SSL=false

# 数据库连接池配置
DB_POOL_MIN=2
DB_POOL_MAX=20
DB_POOL_IDLE_TIMEOUT=30000

# =============================================================================
# Redis 缓存配置
# =============================================================================
REDIS_URL=redis://localhost:6479
REDIS_HOST=localhost
REDIS_PORT=6479
REDIS_PASSWORD=
REDIS_DB=0

# Redis 连接池配置
REDIS_POOL_MIN=2
REDIS_POOL_MAX=10
REDIS_POOL_IDLE_TIMEOUT=30000

# =============================================================================
# 服务器配置
# =============================================================================
PORT=3010
HOST=localhost

# 跨域配置
CORS_ORIGIN=http://localhost:6173,http://localhost:6174
CORS_CREDENTIALS=true

# 会话配置
SESSION_SECRET_FILE=.secrets/session_secret
SESSION_MAX_AGE=86400000

# =============================================================================
# JWT 认证配置
# =============================================================================
JWT_SECRET_FILE=.secrets/jwt_secret
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET_FILE=.secrets/jwt_refresh_secret
JWT_REFRESH_EXPIRES_IN=7d

# =============================================================================
# 文件上传配置
# =============================================================================
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,image/webp
UPLOAD_DEST=uploads

# =============================================================================
# AI 服务配置
# =============================================================================
AI_SERVICE_URL=http://localhost:6800
AI_SERVICE_TIMEOUT=30000

# =============================================================================
# 邮件服务配置
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_FROM=<EMAIL>

# =============================================================================
# 日志配置
# =============================================================================
LOG_LEVEL=info
LOG_FILE=logs/combined-%DATE%.log
LOG_ERROR_FILE=logs/error-%DATE%.log
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# =============================================================================
# 监控和性能配置
# =============================================================================
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_TIMEOUT=5000

# =============================================================================
# 安全配置
# =============================================================================
# 限流配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 慢查询配置
SLOW_DOWN_DELAY_AFTER=50
SLOW_DOWN_DELAY_MS=500

# Helmet 安全头配置
HELMET_ENABLED=true
HELMET_CSP_ENABLED=false

# =============================================================================
# 开发环境配置
# =============================================================================
# 调试配置
DEBUG_ENABLED=false
DEBUG_NAMESPACE=yoghurt:*

# 热重载配置
NODEMON_ENABLED=true
NODEMON_WATCH_EXTENSIONS=ts,json

# =============================================================================
# 生产环境配置
# =============================================================================
# 集群配置
CLUSTER_ENABLED=false
CLUSTER_WORKERS=auto

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_KEYS=1000

# 压缩配置
COMPRESSION_ENABLED=true
COMPRESSION_LEVEL=6

# =============================================================================
# 特性开关
# =============================================================================
FEATURE_AI_ANALYSIS=true
FEATURE_BATCH_PROCESSING=true
FEATURE_REAL_TIME_MONITORING=true
FEATURE_ADVANCED_REPORTS=true
FEATURE_EMAIL_NOTIFICATIONS=true
EOF

    log_success ".env 文件已更新"
}

# 更新 .gitignore
update_gitignore() {
    log_info "更新 .gitignore..."
    
    if ! grep -q ".secrets/" .gitignore 2>/dev/null; then
        echo "" >> .gitignore
        echo "# 密码和密钥文件" >> .gitignore
        echo ".secrets/" >> .gitignore
        echo "*.key" >> .gitignore
        echo "*.pem" >> .gitignore
        log_success ".gitignore 已更新"
    else
        log_info ".gitignore 已包含密码目录"
    fi
}

# 创建其他密钥文件
create_other_secrets() {
    local password_dir="$1"
    
    log_info "创建其他密钥文件..."
    
    # JWT 密钥
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-32 > "$password_dir/jwt_secret"
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-32 > "$password_dir/jwt_refresh_secret"
    
    # 会话密钥
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-32 > "$password_dir/session_secret"
    
    # 设置权限
    chmod 600 "$password_dir"/*
    
    log_success "所有密钥文件已创建"
}

# 主函数
main() {
    log_info "开始设置密码文件系统..."
    
    # 创建密码目录
    password_dir=$(create_password_dir)
    
    # 生成密码文件
    password=$(create_password_file "$password_dir")
    
    # 更新 .env 文件
    update_env_file ".secrets/db_password"
    
    # 更新 .gitignore
    update_gitignore
    
    # 创建其他密钥文件
    create_other_secrets "$password_dir"
    
    log_success "密码文件系统设置完成！"
    
    echo ""
    log_info "生成的密码: $password"
    log_info "密码文件位置: $password_dir/db_password"
    echo ""
    log_warning "重要提醒："
    echo "- 密码文件存储在 .secrets/ 目录中"
    echo "- 此目录不会被提交到 Git"
    echo "- 请妥善保管密码文件"
    echo "- 在生产环境中使用类似的方法"
}

# 运行主函数
main "$@"
