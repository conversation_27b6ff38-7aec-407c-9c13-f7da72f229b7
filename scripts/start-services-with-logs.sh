#!/bin/bash

# =============================================================================
# 带日志显示的服务启动脚本
# =============================================================================

set -e

# 获取脚本所在目录并切换到项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_ROOT"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "🚀 启动Yogurt AI QC服务"
    echo "======================="
    echo -e "${NC}"
}

# 清理现有服务
cleanup_services() {
    log_info "停止现有服务..."
    
    # 停止已有的进程
    pkill -f "node.*backend" 2>/dev/null || true
    pkill -f "node.*vite.*yoghurt" 2>/dev/null || true
    pkill -f "npm.*dev" 2>/dev/null || true
    
    sleep 2
    log_success "服务清理完成"
}

# 启动Docker服务（如果需要）
start_docker_if_needed() {
    log_info "检查Docker服务..."
    
    # 检查PostgreSQL是否运行
    if ! docker ps | grep -q yoghurt-postgres; then
        log_info "启动PostgreSQL..."
        docker-compose up -d postgres
        sleep 5
    else
        log_success "PostgreSQL已运行"
    fi
    
    # 检查Redis是否运行
    if ! docker ps | grep -q yoghurt-redis; then
        log_info "启动Redis..."
        docker-compose up -d redis
        sleep 3
    else
        log_success "Redis已运行"
    fi
}

# 创建日志目录
setup_logs() {
    mkdir -p logs
    log_info "日志目录已准备"
}

# 启动后端服务（前台运行）
start_backend() {
    log_info "启动后端服务..."
    
    cd backend
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        log_info "安装后端依赖..."
        npm install
    fi
    
    echo -e "${GREEN}================================${NC}"
    echo -e "${GREEN}      后端服务日志              ${NC}"
    echo -e "${GREEN}================================${NC}"
    echo ""
    
    # 启动后端（前台运行以显示日志）
    exec npm run dev
}

# 启动前端服务（前台运行）
start_frontend() {
    log_info "启动前端服务..."
    
    cd frontend
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        log_info "安装前端依赖..."
        npm install
    fi
    
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}      前端服务日志              ${NC}"
    echo -e "${CYAN}================================${NC}"
    echo ""
    
    # 启动前端（前台运行以显示日志）
    exec npm run dev
}

# 启动所有服务（并行方式）
start_services_parallel() {
    log_info "并行启动所有服务..."
    
    # 创建命名管道用于日志
    mkfifo backend_pipe frontend_pipe 2>/dev/null || true
    
    # 启动后端
    (
        cd backend
        if [ ! -d "node_modules" ]; then
            npm install
        fi
        npm run dev > ../logs/backend.log 2>&1
    ) &
    BACKEND_PID=$!
    
    # 启动前端  
    (
        cd frontend
        if [ ! -d "node_modules" ]; then
            npm install
        fi
        npm run dev > ../logs/frontend.log 2>&1
    ) &
    FRONTEND_PID=$!
    
    log_success "后端服务已启动 (PID: $BACKEND_PID)"
    log_success "前端服务已启动 (PID: $FRONTEND_PID)"
    
    # 显示日志
    echo ""
    log_info "实时日志显示 (Ctrl+C 停止)："
    echo -e "${YELLOW}后端日志:${NC}"
    tail -f logs/backend.log &
    BACKEND_TAIL_PID=$!
    
    echo -e "${CYAN}前端日志:${NC}" 
    tail -f logs/frontend.log &
    FRONTEND_TAIL_PID=$!
    
    # 等待
    wait $BACKEND_PID $FRONTEND_PID
}

# 清理函数
cleanup() {
    log_info "正在停止服务..."
    
    if [ -n "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    if [ -n "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    if [ -n "$BACKEND_TAIL_PID" ]; then
        kill $BACKEND_TAIL_PID 2>/dev/null || true
    fi
    if [ -n "$FRONTEND_TAIL_PID" ]; then
        kill $FRONTEND_TAIL_PID 2>/dev/null || true
    fi
    
    # 清理命名管道
    rm -f backend_pipe frontend_pipe 2>/dev/null || true
    
    log_success "服务已停止"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主函数
main() {
    show_banner
    cleanup_services
    start_docker_if_needed
    setup_logs
    
    echo ""
    echo "选择启动模式："
    echo "1) 后端服务 (端口 3010)"
    echo "2) 前端服务 (端口 6174)" 
    echo "3) 所有服务 (并行启动并显示日志)"
    echo ""
    read -p "请选择 (1-3): " choice
    
    case $choice in
        1)
            start_backend
            ;;
        2)
            start_frontend
            ;;
        3)
            start_services_parallel
            ;;
        *)
            log_error "无效选择"
            exit 1
            ;;
    esac
}

# 检查参数
if [ "$1" = "backend" ]; then
    show_banner
    cleanup_services
    start_docker_if_needed
    setup_logs
    start_backend
elif [ "$1" = "frontend" ]; then
    show_banner
    cleanup_services
    setup_logs
    start_frontend
elif [ "$1" = "all" ]; then
    show_banner
    cleanup_services
    start_docker_if_needed
    setup_logs
    start_services_parallel
else
    main
fi