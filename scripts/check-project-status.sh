#!/bin/bash

# =============================================================================
# Yogurt AI QC - 项目状态检查脚本
# =============================================================================
# 检查项目环境、服务状态和配置
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "=========================================="
    echo "    Yogurt AI QC 项目状态检查"
    echo "=========================================="
    echo -e "${NC}"
}

# 检查环境状态
check_environment() {
    echo -e "${BLUE}🔍 环境状态检查${NC}"
    echo ""
    
    # 检查conda环境
    echo -e "${PURPLE}📦 Conda环境:${NC}"
    if [ "$CONDA_DEFAULT_ENV" = "yoghurt-ai-qc" ]; then
        echo -e "  ✅ 当前环境: ${GREEN}$CONDA_DEFAULT_ENV${NC}"
        echo -e "  ✅ Python版本: ${GREEN}$(python --version)${NC}"
        echo -e "  ✅ 环境路径: ${GREEN}$CONDA_PREFIX${NC}"
    else
        echo -e "  ❌ 当前环境: ${RED}${CONDA_DEFAULT_ENV:-未激活}${NC}"
        echo -e "  💡 请运行: ${YELLOW}conda activate yoghurt-ai-qc${NC}"
    fi
    
    # 检查Node.js环境
    echo ""
    echo -e "${PURPLE}🟢 Node.js环境:${NC}"
    if command -v node >/dev/null 2>&1; then
        echo -e "  ✅ Node.js版本: ${GREEN}$(node --version)${NC}"
        echo -e "  ✅ npm版本: ${GREEN}$(npm --version)${NC}"
    else
        echo -e "  ❌ Node.js未安装"
    fi
    
    # 检查环境变量
    echo ""
    echo -e "${PURPLE}🔧 环境变量:${NC}"
    echo -e "  PROJECT_ROOT: ${PROJECT_ROOT:-❌ 未设置}"
    echo -e "  AI_SERVICE_PORT: ${AI_SERVICE_PORT:-❌ 未设置}"
    echo -e "  FRONTEND_PORT: ${FRONTEND_PORT:-❌ 未设置}"
    echo -e "  BACKEND_PORT: ${BACKEND_PORT:-❌ 未设置}"
}

# 检查服务状态
check_services() {
    echo ""
    echo -e "${BLUE}🌐 服务状态检查${NC}"
    echo ""
    
    # 检查各个服务端口
    local services=(
        "前端:6174:http://localhost:6174"
        "后端:3010:http://localhost:3010/health"
        "AI服务:6800:http://localhost:6800/health"
        "Prisma Studio:5555:http://localhost:5555"
    )
    
    for service_info in "${services[@]}"; do
        IFS=':' read -r name port url <<< "$service_info"
        
        if lsof -i :$port >/dev/null 2>&1; then
            echo -e "  ✅ ${name}: ${GREEN}运行中${NC} (端口 $port)"
            
            # 尝试HTTP检查
            if curl -s "$url" >/dev/null 2>&1; then
                echo -e "     🌐 HTTP响应: ${GREEN}正常${NC}"
            else
                echo -e "     ⚠️  HTTP响应: ${YELLOW}无响应${NC}"
            fi
        else
            echo -e "  ❌ ${name}: ${RED}未运行${NC} (端口 $port)"
        fi
    done
    
    # 检查Docker服务
    echo ""
    echo -e "${PURPLE}🐳 Docker服务:${NC}"
    if command -v docker >/dev/null 2>&1; then
        if docker ps | grep -q "yoghurt-postgres"; then
            echo -e "  ✅ PostgreSQL: ${GREEN}运行中${NC}"
        else
            echo -e "  ❌ PostgreSQL: ${RED}未运行${NC}"
        fi
        
        if docker ps | grep -q "yoghurt-redis"; then
            echo -e "  ✅ Redis: ${GREEN}运行中${NC}"
        else
            echo -e "  ❌ Redis: ${RED}未运行${NC}"
        fi
    else
        echo -e "  ❌ Docker未安装或未运行"
    fi
}

# 检查Python组件
check_python_components() {
    echo ""
    echo -e "${BLUE}🐍 Python组件检查${NC}"
    echo ""
    
    if [ "$CONDA_DEFAULT_ENV" = "yoghurt-ai-qc" ]; then
        local components=("fastapi" "openai" "cv2" "numpy" "pandas")
        
        for component in "${components[@]}"; do
            if python -c "import $component" 2>/dev/null; then
                local version=$(python -c "import $component; print(getattr($component, '__version__', 'unknown'))" 2>/dev/null)
                echo -e "  ✅ ${component}: ${GREEN}${version}${NC}"
            else
                echo -e "  ❌ ${component}: ${RED}未安装${NC}"
            fi
        done
    else
        echo -e "  ⚠️  请先激活conda环境"
    fi
}

# 检查配置文件
check_configuration() {
    echo ""
    echo -e "${BLUE}📄 配置文件检查${NC}"
    echo ""
    
    local config_files=(
        ".envrc:direnv配置"
        "environment-latest.yml:conda环境"
        "ai-service/requirements-slim.txt:Python依赖"
        "docker-compose.yml:Docker配置"
        ".env:环境变量"
        "package.json:Node.js配置"
    )
    
    for file_info in "${config_files[@]}"; do
        IFS=':' read -r file desc <<< "$file_info"
        
        if [ -f "$file" ]; then
            echo -e "  ✅ ${desc}: ${GREEN}存在${NC} ($file)"
        else
            echo -e "  ❌ ${desc}: ${RED}缺失${NC} ($file)"
        fi
    done
}

# 显示快速修复建议
show_quick_fixes() {
    echo ""
    echo -e "${BLUE}🛠️  快速修复建议${NC}"
    echo ""
    
    # 检查是否需要激活环境
    if [ "$CONDA_DEFAULT_ENV" != "yoghurt-ai-qc" ]; then
        echo -e "${YELLOW}🔧 激活conda环境:${NC}"
        echo "   conda activate yoghurt-ai-qc"
        echo "   # 或使用: source activate-env.sh"
        echo ""
    fi
    
    # 检查是否需要启动服务
    if ! lsof -i :3010 >/dev/null 2>&1; then
        echo -e "${YELLOW}🚀 启动服务:${NC}"
        echo "   ./scripts/start-all-services.sh  # 一键启动"
        echo "   # 或分别启动:"
        echo "   docker-compose up -d postgres redis"
        echo "   npm run dev:backend"
        echo "   npm run dev:frontend"
        echo "   npm run dev:ai"
        echo ""
    fi
    
    # 检查是否需要安装依赖
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}📦 安装依赖:${NC}"
        echo "   npm install"
        echo ""
    fi
}

# 显示访问地址
show_access_urls() {
    echo ""
    echo -e "${BLUE}🌐 访问地址${NC}"
    echo ""
    
    echo -e "${GREEN}主要服务:${NC}"
    echo "  🎨 前端应用: http://localhost:6174"
    echo "  🔧 后端API: http://localhost:3010"
    echo "  🤖 AI服务: http://localhost:6800"
    echo ""
    
    echo -e "${GREEN}开发工具:${NC}"
    echo "  📚 API文档: http://localhost:3010/api/docs"
    echo "  🐍 AI文档: http://localhost:6800/docs"
    echo "  🗄️  数据库管理: http://localhost:5555"
    echo ""
    
    echo -e "${GREEN}测试账户:${NC}"
    echo "  👤 管理员: <EMAIL> / password123"
    echo "  👤 用户: <EMAIL> / password123"
}

# 主函数
main() {
    show_banner
    check_environment
    check_services
    check_python_components
    check_configuration
    show_quick_fixes
    show_access_urls
    
    echo ""
    echo -e "${GREEN}✅ 状态检查完成！${NC}"
}

# 运行主函数
main "$@"
