#!/bin/bash

# =============================================================================
# Yoghurt AI QC - 快速启动脚本
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要工具
check_requirements() {
    log_info "检查系统要求..."
    
    local missing_tools=()
    
    if ! command -v node &> /dev/null; then
        missing_tools+=("Node.js")
    fi
    
    if ! command -v npm &> /dev/null; then
        missing_tools+=("npm")
    fi
    
    if ! command -v docker &> /dev/null; then
        missing_tools+=("Docker")
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        missing_tools+=("Docker Compose")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "缺少必要工具: ${missing_tools[*]}"
        log_error "请安装缺少的工具后重试"
        exit 1
    fi
    
    log_success "所有必要工具已安装"
}

# 检查端口占用
check_ports() {
    log_info "检查端口占用情况..."

    local ports=(6000 6173 6432 6479 6800)
    local occupied_ports=()
    local docker_ports=()

    for port in "${ports[@]}"; do
        if lsof -i :$port &> /dev/null; then
            occupied_ports+=($port)

            # 检查是否是我们项目的Docker容器占用
            if docker ps | grep -E "(yoghurt-|yoghurt_)" | grep -q ":$port->"; then
                docker_ports+=($port)
            fi
        fi
    done

    if [ ${#occupied_ports[@]} -ne 0 ]; then
        if [ ${#docker_ports[@]} -eq ${#occupied_ports[@]} ]; then
            log_warning "检测到项目的Docker容器正在运行"
            log_info "将自动停止现有容器并重新启动..."
            docker-compose down
            sleep 2
            log_success "现有容器已停止"
        else
            log_warning "以下端口被占用: ${occupied_ports[*]}"

            if [ ${#docker_ports[@]} -gt 0 ]; then
                log_info "其中端口 ${docker_ports[*]} 被项目Docker容器占用"
                read -p "是否停止项目容器并继续？(Y/n): " -n 1 -r
                echo
                if [[ ! $REPLY =~ ^[Nn]$ ]]; then
                    docker-compose down
                    sleep 2
                    log_success "项目容器已停止"
                else
                    log_info "启动已取消"
                    exit 0
                fi
            else
                log_warning "端口被其他服务占用，请手动处理"
                log_info "您可以运行: bash scripts/cleanup-ports.sh"

                read -p "是否继续启动？(y/N): " -n 1 -r
                echo
                if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                    log_info "启动已取消"
                    exit 0
                fi
            fi
        fi
    else
        log_success "所有端口都可用"
    fi
}

# 检查环境变量
check_env() {
    log_info "检查环境变量配置..."

    if [ ! -f .env ]; then
        log_warning ".env 文件不存在，从模板创建..."
        cp .env.example .env
        log_success ".env 文件已创建"
    fi

    # 安全地检查关键配置，避免执行.env文件中的命令
    OPENAI_API_KEY=$(grep -E "^OPENAI_API_KEY=" .env | cut -d= -f2 | tr -d '"' || echo "")

    if [ -z "$OPENAI_API_KEY" ] || [ "$OPENAI_API_KEY" = "sk-your-openai-api-key-here" ]; then
        log_warning "OpenAI API 密钥未配置，AI功能将不可用"
        log_info "请在 .env 文件中设置 OPENAI_API_KEY"
    else
        log_success "OpenAI API 密钥已配置"
    fi

    log_success "环境变量检查完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    # 根目录依赖
    if [ ! -d node_modules ]; then
        log_info "安装根目录依赖..."
        npm install
    fi
    
    # 前端依赖
    if [ ! -d frontend/node_modules ]; then
        log_info "安装前端依赖..."
        cd frontend && npm install && cd ..
    fi
    
    # 后端依赖
    if [ ! -d backend/node_modules ]; then
        log_info "安装后端依赖..."
        cd backend && npm install && cd ..
    fi
    
    log_success "依赖安装完成"
}

# 启动数据库服务
start_database() {
    log_info "启动数据库服务..."
    
    # 检查Docker是否运行
    if ! docker info &> /dev/null; then
        log_error "Docker 未运行，请启动 Docker 后重试"
        exit 1
    fi
    
    # 启动数据库服务
    docker-compose up -d postgres redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 10
    
    # 检查数据库连接
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose exec -T postgres pg_isready -U yoghurt_user -d yoghurt_qc &> /dev/null; then
            log_success "数据库连接成功"
            break
        fi
        
        log_info "等待数据库启动... (尝试 $attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "数据库连接超时"
        exit 1
    fi
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 运行数据库设置脚本
    if [ -f scripts/setup-database.sh ]; then
        bash scripts/setup-database.sh
    else
        log_warning "数据库设置脚本不存在，跳过数据库初始化"
    fi
}

# 启动开发服务
start_services() {
    log_info "启动开发服务..."
    
    # 创建日志目录
    mkdir -p logs
    
    log_success "所有服务启动完成！"
    echo ""
    log_info "访问地址："
    echo "  - 前端应用: http://localhost:6173"
    echo "  - 后端API: http://localhost:6000"
    echo "  - API文档: http://localhost:6000/api/docs"
    echo "  - AI服务: http://localhost:6800"
    echo "  - AI文档: http://localhost:6800/docs"
    echo ""
    log_info "使用 Ctrl+C 停止服务"
    echo ""
    
    # 启动所有服务
    npm run dev
}

# 主函数
main() {
    echo "🚀 Yogurt AI QC - 快速启动"
    echo "================================"
    
    check_requirements
    check_ports
    check_env
    install_dependencies
    start_database
    init_database
    start_services
}

# 错误处理
trap 'log_error "启动过程中发生错误，正在清理..."; docker-compose down; exit 1' ERR

# 运行主函数
main "$@"
