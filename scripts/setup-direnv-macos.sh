#!/bin/zsh

# =============================================================================
# Yogurt AI QC - macOS direnv完整配置脚本
# =============================================================================
# 专门为macOS zsh配置direnv自动环境激活
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "=========================================="
    echo "    macOS Direnv 完整配置脚本"
    echo "=========================================="
    echo -e "${NC}"
    echo "🍎 为macOS zsh配置direnv自动环境激活"
    echo ""
}

# 主配置函数
main() {
    show_banner
    
    # 检查系统
    log_step "检查系统环境..."
    
    # 检查是否在项目根目录
    if [[ ! -f "package.json" || ! -d "ai-service" ]]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 检查direnv是否安装
    if [[ ! -f "/opt/homebrew/bin/direnv" ]]; then
        log_error "direnv未安装，正在安装..."
        brew install direnv
    else
        log_success "direnv已安装: $(/opt/homebrew/bin/direnv version)"
    fi
    
    # 检查conda环境
    if ! conda env list | grep -q "yoghurt-ai-qc"; then
        log_error "yoghurt-ai-qc conda环境不存在"
        echo "请先运行: ./scripts/create-latest-env.sh"
        exit 1
    fi
    
    log_success "yoghurt-ai-qc conda环境存在"
    
    # 配置zsh
    log_step "配置zsh集成..."
    
    local zshrc="$HOME/.zshrc"
    
    # 确保Homebrew路径在PATH中
    if ! grep -q 'export PATH="/opt/homebrew/bin:$PATH"' "$zshrc" 2>/dev/null; then
        log_info "添加Homebrew PATH到 $zshrc"
        echo '' >> "$zshrc"
        echo '# Homebrew PATH' >> "$zshrc"
        echo 'export PATH="/opt/homebrew/bin:$PATH"' >> "$zshrc"
    fi
    
    # 添加direnv hook
    if ! grep -q 'eval "$(direnv hook zsh)"' "$zshrc" 2>/dev/null; then
        log_info "添加direnv hook到 $zshrc"
        echo '' >> "$zshrc"
        echo '# Direnv hook for automatic environment activation' >> "$zshrc"
        echo 'eval "$(direnv hook zsh)"' >> "$zshrc"
    fi
    
    log_success "zsh配置完成"
    
    # 立即应用配置
    log_step "应用配置..."
    
    # 设置PATH
    export PATH="/opt/homebrew/bin:$PATH"
    
    # 初始化direnv
    eval "$(/opt/homebrew/bin/direnv hook zsh)"
    
    # 验证direnv
    if command -v direnv >/dev/null 2>&1; then
        log_success "direnv现在可用: $(direnv version)"
    else
        log_error "direnv配置失败"
        exit 1
    fi
    
    # 配置项目环境
    log_step "配置项目环境..."
    
    # 检查.envrc文件
    if [[ -f ".envrc" ]]; then
        log_info ".envrc文件存在，允许执行..."
        direnv allow
        log_success "项目环境已配置"
    else
        log_error ".envrc文件不存在"
        exit 1
    fi
    
    # 测试环境激活
    log_step "测试环境激活..."
    
    # 模拟离开和重新进入目录
    local current_dir=$(pwd)
    cd ..
    cd "$current_dir"
    
    # 检查环境变量
    if [[ -n "$CONDA_DEFAULT_ENV" && "$CONDA_DEFAULT_ENV" = "yoghurt-ai-qc" ]]; then
        log_success "环境自动激活成功！"
        log_success "当前conda环境: $CONDA_DEFAULT_ENV"
    else
        log_warning "环境可能未完全激活"
        log_info "当前conda环境: ${CONDA_DEFAULT_ENV:-未设置}"
    fi
    
    # 显示使用指南
    show_usage_guide
    
    log_success "direnv配置完成！"
}

# 显示使用指南
show_usage_guide() {
    echo ""
    echo -e "${CYAN}🎯 使用指南:${NC}"
    echo ""
    echo "1. 重新启动终端应用，或在新终端中运行:"
    echo "   source ~/.zshrc"
    echo ""
    echo "2. 测试自动激活:"
    echo "   cd .."
    echo "   cd $(basename $(pwd))"
    echo "   # 应该看到环境自动激活的消息"
    echo ""
    echo "3. 验证环境:"
    echo "   echo \$CONDA_DEFAULT_ENV  # 应该显示: yoghurt-ai-qc"
    echo "   python --version         # 应该显示: Python 3.12.11"
    echo ""
    echo "4. 常用direnv命令:"
    echo "   direnv status    # 查看状态"
    echo "   direnv reload    # 重新加载"
    echo "   direnv allow     # 允许.envrc"
    echo "   direnv deny      # 禁用.envrc"
    echo ""
    echo -e "${GREEN}🚀 现在每次进入项目目录都会自动激活conda环境！${NC}"
    echo ""
    echo -e "${BLUE}📝 如果仍有问题:${NC}"
    echo "1. 完全重启终端应用"
    echo "2. 或运行: exec zsh"
    echo "3. 或使用备用方案: source activate-env.sh"
}

# 运行主函数
main "$@"
