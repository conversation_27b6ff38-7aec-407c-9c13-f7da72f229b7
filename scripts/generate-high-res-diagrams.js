#!/usr/bin/env node

// =============================================================================
// 高分辨率图表生成脚本
// =============================================================================

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 配置
const config = {
  outputDir: 'docs/images',
  diagramsDir: 'docs/diagrams',
  configFile: 'docs/diagrams/mermaid-config.json',
  formats: ['png', 'svg'],
  highRes: {
    width: 3200,
    height: 2400,
    scale: 3,
    quality: 100
  },
  mediumRes: {
    width: 1600,
    height: 1200,
    scale: 2,
    quality: 90
  }
};

// 图表定义
const diagrams = [
  {
    name: 'database-er',
    title: '数据库实体关系图',
    input: 'database-er.mmd',
    size: 'highRes'
  },
  {
    name: 'data-flow',
    title: '数据流程图',
    input: 'data-flow.mmd',
    size: 'mediumRes'
  }
];

// 确保目录存在
function ensureDir(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`📁 创建目录: ${dir}`);
  }
}

// 检查依赖
function checkDependencies() {
  try {
    execSync('mmdc --version', { stdio: 'ignore' });
    console.log('✅ Mermaid CLI 已安装');
  } catch (error) {
    console.log('📦 安装 Mermaid CLI...');
    try {
      execSync('npm install -g @mermaid-js/mermaid-cli', { stdio: 'inherit' });
      console.log('✅ Mermaid CLI 安装完成');
    } catch (installError) {
      console.error('❌ 无法安装 Mermaid CLI:', installError.message);
      process.exit(1);
    }
  }
}

// 生成单个图表
function generateDiagram(diagram, format) {
  const inputFile = path.join(config.diagramsDir, diagram.input);
  const sizeConfig = config[diagram.size];
  
  // 检查输入文件是否存在
  if (!fs.existsSync(inputFile)) {
    console.error(`❌ 输入文件不存在: ${inputFile}`);
    return false;
  }

  const outputFile = path.join(
    config.outputDir, 
    `${diagram.name}-diagram.${format}`
  );

  console.log(`🎨 生成 ${diagram.title} (${format.toUpperCase()})...`);

  try {
    let command = `mmdc -i "${inputFile}" -o "${outputFile}"`;
    
    // 添加配置参数
    if (fs.existsSync(config.configFile)) {
      command += ` -c "${config.configFile}"`;
    }
    
    // 添加尺寸参数
    command += ` --width ${sizeConfig.width}`;
    command += ` --height ${sizeConfig.height}`;
    command += ` --scale ${sizeConfig.scale}`;
    command += ` --backgroundColor white`;
    
    // PNG特定参数
    if (format === 'png') {
      command += ` --quality ${sizeConfig.quality}`;
    }

    execSync(command, { stdio: 'pipe' });
    
    // 检查文件大小
    const stats = fs.statSync(outputFile);
    const fileSizeMB = (stats.size / 1024 / 1024).toFixed(2);
    
    console.log(`✅ ${diagram.title} 生成成功`);
    console.log(`   文件: ${outputFile}`);
    console.log(`   大小: ${fileSizeMB} MB`);
    
    return true;
  } catch (error) {
    console.error(`❌ 生成 ${diagram.title} 失败:`, error.message);
    return false;
  }
}

// 生成所有图表
function generateAllDiagrams() {
  console.log('🚀 开始生成高分辨率图表...\n');
  
  // 检查依赖
  checkDependencies();
  
  // 确保输出目录存在
  ensureDir(config.outputDir);
  
  let successCount = 0;
  let totalCount = 0;
  
  // 生成每个图表的每种格式
  for (const diagram of diagrams) {
    console.log(`\n📊 处理 ${diagram.title}:`);
    
    for (const format of config.formats) {
      totalCount++;
      if (generateDiagram(diagram, format)) {
        successCount++;
      }
    }
  }
  
  // 生成额外的组合图表
  generateCombinedDiagram();
  
  console.log(`\n🎉 图表生成完成!`);
  console.log(`✅ 成功: ${successCount}/${totalCount}`);
  console.log(`📁 输出目录: ${config.outputDir}`);
  
  // 显示生成的文件
  console.log('\n📋 生成的文件:');
  const files = fs.readdirSync(config.outputDir);
  files.forEach(file => {
    const filePath = path.join(config.outputDir, file);
    const stats = fs.statSync(filePath);
    const fileSizeMB = (stats.size / 1024 / 1024).toFixed(2);
    console.log(`   ${file} (${fileSizeMB} MB)`);
  });
}

// 生成组合图表（包含多个子图）
function generateCombinedDiagram() {
  console.log('\n🔄 生成组合架构图...');
  
  const combinedMermaid = `
graph TB
    subgraph "数据库实体关系"
        direction TB
        User[用户表<br/>Users]
        Recipe[配方表<br/>Recipes]
        Batch[批次表<br/>Batches]
        Assessment[感官评估表<br/>Sensory Assessments]
        Image[显微镜图像表<br/>Microscope Images]
        Analysis[AI分析表<br/>AI Analyses]
        Report[质量报告表<br/>Quality Reports]
        Log[系统日志表<br/>System Logs]
        
        User --> Recipe
        User --> Batch
        User --> Assessment
        User --> Image
        User --> Report
        User --> Log
        Recipe --> Batch
        Batch --> Assessment
        Batch --> Image
        Batch --> Analysis
        Batch --> Report
        Image --> Analysis
    end
    
    subgraph "数据流程"
        direction LR
        A[用户登录] --> B[创建配方]
        B --> C[开始批次]
        C --> D[生产酸奶]
        D --> E[感官评估]
        D --> F[显微镜拍照]
        F --> G[AI分析]
        G --> H[质量报告]
    end
    
    style User fill:#e1f5fe
    style Analysis fill:#fff3e0
    style Report fill:#f3e5f5
    style G fill:#fff3e0
    style H fill:#f3e5f5
`;

  const tempFile = path.join(config.diagramsDir, 'temp-combined.mmd');
  fs.writeFileSync(tempFile, combinedMermaid);
  
  try {
    const outputFile = path.join(config.outputDir, 'architecture-overview.png');
    let command = `mmdc -i "${tempFile}" -o "${outputFile}"`;
    command += ` --width 2400 --height 1600 --scale 2`;
    command += ` --backgroundColor white`;
    
    execSync(command, { stdio: 'pipe' });
    
    const stats = fs.statSync(outputFile);
    const fileSizeMB = (stats.size / 1024 / 1024).toFixed(2);
    
    console.log(`✅ 架构概览图生成成功 (${fileSizeMB} MB)`);
    
    // 清理临时文件
    fs.unlinkSync(tempFile);
  } catch (error) {
    console.error('❌ 生成架构概览图失败:', error.message);
  }
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
使用方法: node generate-high-res-diagrams.js [选项]

选项:
  --help, -h     显示帮助信息
  --list, -l     列出可用的图表
  --clean, -c    清理输出目录

示例:
  node generate-high-res-diagrams.js
  npm run docs:diagrams:hd
`);
    return;
  }
  
  if (args.includes('--list') || args.includes('-l')) {
    console.log('📋 可用的图表:');
    diagrams.forEach((diagram, index) => {
      console.log(`  ${index + 1}. ${diagram.title} (${diagram.input})`);
    });
    return;
  }
  
  if (args.includes('--clean') || args.includes('-c')) {
    if (fs.existsSync(config.outputDir)) {
      const files = fs.readdirSync(config.outputDir);
      files.forEach(file => {
        fs.unlinkSync(path.join(config.outputDir, file));
      });
      console.log(`🧹 清理完成: ${files.length} 个文件已删除`);
    }
    return;
  }
  
  generateAllDiagrams();
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { generateAllDiagrams, generateDiagram };
