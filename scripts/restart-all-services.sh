#!/bin/bash

# =============================================================================
# Yogurt AI QC - 重启所有服务脚本
# =============================================================================
# 此脚本会先停止所有服务，然后重新启动
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "=================================="
    echo "    Yogurt AI QC 重启脚本"
    echo "=================================="
    echo -e "${NC}"
    echo "🔄 正在重启所有服务..."
    echo ""
}

# 检查脚本是否存在
check_scripts() {
    local scripts_dir="$(dirname "$0")"
    
    if [ ! -f "$scripts_dir/stop-all-services.sh" ]; then
        log_error "停止脚本不存在: $scripts_dir/stop-all-services.sh"
        exit 1
    fi
    
    if [ ! -f "$scripts_dir/start-all-services.sh" ]; then
        log_error "启动脚本不存在: $scripts_dir/start-all-services.sh"
        exit 1
    fi
    
    # 确保脚本可执行
    chmod +x "$scripts_dir/stop-all-services.sh"
    chmod +x "$scripts_dir/start-all-services.sh"
}

# 主函数
main() {
    show_banner
    
    local scripts_dir="$(dirname "$0")"
    
    # 检查脚本
    check_scripts
    
    # 停止所有服务
    log_info "第1步: 停止所有服务..."
    "$scripts_dir/stop-all-services.sh"
    
    echo ""
    log_info "等待服务完全停止..."
    sleep 3
    
    # 启动所有服务
    log_info "第2步: 启动所有服务..."
    "$scripts_dir/start-all-services.sh"
}

# 运行主函数
main "$@"
