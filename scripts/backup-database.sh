#!/bin/bash

# =============================================================================
# 数据库备份脚本
# =============================================================================
# 
# 此脚本用于备份PostgreSQL和Redis数据
# 
# 使用方法:
# ./scripts/backup-database.sh [backup_dir]
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 设置备份目录
BACKUP_DIR=${1:-"./backups"}
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

echo "🗄️ 数据库备份工具"
echo "=================="

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 检查容器是否运行
log_info "检查容器状态..."
if ! docker ps | grep -q "yoghurt-postgres"; then
    log_error "PostgreSQL容器未运行"
    exit 1
fi

if ! docker ps | grep -q "yoghurt-redis"; then
    log_error "Redis容器未运行"
    exit 1
fi

# 备份PostgreSQL
log_info "备份PostgreSQL数据库..."
PG_BACKUP_FILE="$BACKUP_DIR/postgres_backup_$TIMESTAMP.sql"

if docker exec yoghurt-postgres pg_dump -U postgres postgres > "$PG_BACKUP_FILE"; then
    log_success "PostgreSQL备份完成: $PG_BACKUP_FILE"
    
    # 压缩备份文件
    gzip "$PG_BACKUP_FILE"
    log_success "备份文件已压缩: ${PG_BACKUP_FILE}.gz"
else
    log_error "PostgreSQL备份失败"
    exit 1
fi

# 备份Redis
log_info "备份Redis数据..."
REDIS_BACKUP_FILE="$BACKUP_DIR/redis_backup_$TIMESTAMP.rdb"

# 触发Redis保存
docker exec yoghurt-redis redis-cli BGSAVE

# 等待保存完成
sleep 3

# 复制RDB文件
if docker cp yoghurt-redis:/data/dump.rdb "$REDIS_BACKUP_FILE"; then
    log_success "Redis备份完成: $REDIS_BACKUP_FILE"
    
    # 压缩备份文件
    gzip "$REDIS_BACKUP_FILE"
    log_success "备份文件已压缩: ${REDIS_BACKUP_FILE}.gz"
else
    log_error "Redis备份失败"
    exit 1
fi

# 创建备份信息文件
BACKUP_INFO="$BACKUP_DIR/backup_info_$TIMESTAMP.txt"
cat > "$BACKUP_INFO" << EOF
备份时间: $(date)
PostgreSQL备份: postgres_backup_$TIMESTAMP.sql.gz
Redis备份: redis_backup_$TIMESTAMP.rdb.gz
数据库版本: $(docker exec yoghurt-postgres psql -U postgres -t -c "SELECT version();")
Redis版本: $(docker exec yoghurt-redis redis-cli INFO server | grep redis_version)
EOF

log_success "备份信息文件: $BACKUP_INFO"

# 显示备份文件大小
echo ""
log_info "备份文件详情:"
ls -lh "$BACKUP_DIR"/*_$TIMESTAMP.*

echo ""
log_success "数据库备份完成！"
echo ""
echo "📋 备份文件位置:"
echo "  - PostgreSQL: ${PG_BACKUP_FILE}.gz"
echo "  - Redis: ${REDIS_BACKUP_FILE}.gz"
echo "  - 信息文件: $BACKUP_INFO"
echo ""
echo "💡 恢复命令:"
echo "  ./scripts/restore-database.sh ${PG_BACKUP_FILE}.gz"
