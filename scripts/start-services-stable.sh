#!/bin/bash

# =============================================================================
# 稳定的服务启动脚本
# =============================================================================
# 
# 此脚本使用更稳定的方式启动前端和后端服务
# 使用nohup确保服务在后台持续运行
# =============================================================================

# 获取脚本所在目录并切换到项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_ROOT"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🚀 启动Yogurt AI QC服务"
echo "======================="

# 检查并创建logs目录
mkdir -p logs

# 停止现有服务
log_info "停止现有服务..."
if [ -f .backend.pid ]; then
    BACKEND_PID=$(cat .backend.pid)
    kill $BACKEND_PID 2>/dev/null || true
    rm -f .backend.pid
fi

if [ -f .frontend.pid ]; then
    FRONTEND_PID=$(cat .frontend.pid)
    kill $FRONTEND_PID 2>/dev/null || true
    rm -f .frontend.pid
fi

# 清理端口
pkill -f "node.*backend" 2>/dev/null || true
pkill -f "node.*vite.*yoghurt" 2>/dev/null || true

sleep 2

# 启动后端服务
log_info "启动后端服务 (端口 3010)..."
cd backend
APP_PORT=3010 nohup npm run dev > ../logs/backend.log 2>&1 &
BACKEND_PID=$!
echo $BACKEND_PID > ../.backend.pid
cd ..

log_success "后端服务已启动 (PID: $BACKEND_PID)"

# 等待后端启动
log_info "等待后端服务启动..."
for i in {1..30}; do
    if curl -s http://localhost:3010/health &> /dev/null; then
        log_success "后端服务启动成功"
        break
    fi
    sleep 1
done

# 启动前端服务
log_info "启动前端服务 (端口 6174)..."
cd frontend
nohup npm run dev > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!
echo $FRONTEND_PID > ../.frontend.pid
cd ..

log_success "前端服务已启动 (PID: $FRONTEND_PID)"

# 等待前端启动
log_info "等待前端服务启动..."
for i in {1..30}; do
    if curl -s http://localhost:6174 &> /dev/null; then
        log_success "前端服务启动成功"
        break
    fi
    sleep 1
done

echo ""
log_success "🎉 所有服务启动完成！"
echo ""
echo "📊 服务状态:"
echo "┌─────────────────┬──────┬─────────────────────────────┐"
echo "│ 服务            │ 端口 │ 访问地址                    │"
echo "├─────────────────┼──────┼─────────────────────────────┤"
echo "│ 前端应用        │ 6174 │ http://localhost:6174       │"
echo "│ 后端API         │ 3010 │ http://localhost:3010       │"
echo "│ API文档         │ 3010 │ http://localhost:3010/api/docs │"
echo "└─────────────────┴──────┴─────────────────────────────┘"
echo ""
echo "📝 管理命令:"
echo "  查看日志: tail -f logs/backend.log 或 tail -f logs/frontend.log"
echo "  停止服务: ./scripts/stop-services.sh"
echo "  检查状态: ./scripts/check-services.sh"
echo ""
echo "🌐 现在可以访问应用了："
echo "  👉 http://localhost:6174"
echo ""

# 验证服务状态
log_info "验证服务状态..."
if curl -s http://localhost:3010/health &> /dev/null; then
    log_success "✅ 后端服务运行正常"
else
    log_warning "⚠️  后端服务可能未完全启动"
fi

if curl -s http://localhost:6174 &> /dev/null; then
    log_success "✅ 前端服务运行正常"
else
    log_warning "⚠️  前端服务可能未完全启动"
fi

echo ""
log_info "服务已在后台运行，可以安全关闭此终端"
