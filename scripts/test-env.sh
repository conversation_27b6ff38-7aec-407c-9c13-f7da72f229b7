#!/bin/bash

echo "测试环境变量读取..."

# 方法1: 使用grep读取
echo "方法1 - 使用grep:"
OPENAI_API_KEY=$(grep -E "^OPENAI_API_KEY=" .env | cut -d= -f2 | tr -d '"' || echo "")
echo "OPENAI_API_KEY长度: ${#OPENAI_API_KEY}"

# 方法2: 检查APP_NAME
echo "方法2 - 检查APP_NAME:"
APP_NAME=$(grep -E "^APP_NAME=" .env | cut -d= -f2 | tr -d '"' || echo "")
echo "APP_NAME: $APP_NAME"

# 方法3: 检查第10行
echo "方法3 - 检查第10行:"
sed -n '10p' .env

echo "测试完成"
