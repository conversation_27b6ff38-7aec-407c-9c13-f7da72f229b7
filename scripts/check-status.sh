#!/bin/bash

# =============================================================================
# Yoghurt AI QC - 项目状态检查脚本
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

echo "🔍 Yogurt AI QC - 项目状态检查"
echo "================================"

# 检查项目结构
log_info "检查项目结构..."
required_dirs=("frontend" "backend" "ai-service" "docs" "scripts" "infrastructure")
for dir in "${required_dirs[@]}"; do
    if [ -d "$dir" ]; then
        log_success "目录存在: $dir"
    else
        log_error "目录缺失: $dir"
    fi
done

# 检查配置文件
log_info "检查配置文件..."
config_files=(
    ".env"
    ".env.example"
    "package.json"
    "docker-compose.yml"
    "environment.yml"
    "frontend/package.json"
    "frontend/tsconfig.json"
    "frontend/tsconfig.node.json"
    "frontend/vite.config.ts"
    "backend/package.json"
    "backend/tsconfig.json"
    "ai-service/requirements.txt"
)

for file in "${config_files[@]}"; do
    if [ -f "$file" ]; then
        log_success "配置文件存在: $file"
    else
        log_error "配置文件缺失: $file"
    fi
done

# 检查端口配置
log_info "检查端口配置..."
if [ -f ".env" ]; then
    # 从.env文件中提取端口信息，避免直接source可能导致的问题
    VITE_PORT=$(grep -E "^VITE_PORT=" .env | cut -d= -f2 || echo "6173")
    APP_PORT=$(grep -E "^APP_PORT=" .env | cut -d= -f2 || echo "6000")
    AI_SERVICE_PORT=$(grep -E "^AI_SERVICE_PORT=" .env | cut -d= -f2 || echo "6800")
    DB_PORT=$(grep -E "^DB_PORT=" .env | cut -d= -f2 || echo "6432")
    REDIS_PORT=$(grep -E "^REDIS_PORT=" .env | cut -d= -f2 || echo "6479")

    echo "  - 前端端口: ${VITE_PORT}"
    echo "  - 后端端口: ${APP_PORT}"
    echo "  - AI服务端口: ${AI_SERVICE_PORT}"
    echo "  - 数据库端口: ${DB_PORT}"
    echo "  - Redis端口: ${REDIS_PORT}"
    
    # 检查端口占用
    ports=(6000 6173 6432 6479 6800)
    for port in "${ports[@]}"; do
        if lsof -i :$port &> /dev/null; then
            log_warning "端口 $port 被占用"
        else
            log_success "端口 $port 可用"
        fi
    done
else
    log_error ".env 文件不存在"
fi

# 检查依赖安装
log_info "检查依赖安装..."

# 根目录依赖
if [ -d "node_modules" ]; then
    log_success "根目录依赖已安装"
else
    log_warning "根目录依赖未安装"
fi

# 前端依赖
if [ -d "frontend/node_modules" ]; then
    log_success "前端依赖已安装"
else
    log_warning "前端依赖未安装"
fi

# 后端依赖
if [ -d "backend/node_modules" ]; then
    log_success "后端依赖已安装"
else
    log_warning "后端依赖未安装"
fi

# 检查Docker服务
log_info "检查Docker服务..."
if command -v docker &> /dev/null; then
    if docker info &> /dev/null; then
        log_success "Docker 运行正常"
        
        # 检查容器状态
        if docker-compose ps | grep -q "Up"; then
            log_success "Docker 容器正在运行"
            docker-compose ps
        else
            log_warning "Docker 容器未运行"
        fi
    else
        log_error "Docker 未运行"
    fi
else
    log_error "Docker 未安装"
fi

# 检查环境变量
log_info "检查关键环境变量..."
if [ -f ".env" ]; then
    # 安全地读取环境变量，去除引号
    OPENAI_API_KEY=$(grep -E "^OPENAI_API_KEY=" .env | cut -d= -f2 | tr -d '"' || echo "")
    JWT_SECRET=$(grep -E "^JWT_SECRET=" .env | cut -d= -f2 | tr -d '"' || echo "")
    DATABASE_URL=$(grep -E "^DATABASE_URL=" .env | cut -d= -f2 | tr -d '"' || echo "")

    if [ -n "$OPENAI_API_KEY" ] && [ "$OPENAI_API_KEY" != "sk-your-openai-api-key-here" ]; then
        log_success "OpenAI API 密钥已配置"
    else
        log_warning "OpenAI API 密钥未配置"
    fi

    if [ -n "$JWT_SECRET" ] && [ "$JWT_SECRET" != "your-super-secret-jwt-key" ]; then
        log_success "JWT 密钥已配置"
    else
        log_warning "JWT 密钥使用默认值"
    fi

    if [ -n "$DATABASE_URL" ]; then
        log_success "数据库URL已配置"
    else
        log_error "数据库URL未配置"
    fi
fi

# 检查网络连接
log_info "检查服务连接..."

# 检查后端API
if curl -s http://localhost:6000/health &> /dev/null; then
    log_success "后端API (6000) 可访问"
else
    log_warning "后端API (6000) 不可访问"
fi

# 检查前端应用
if curl -s http://localhost:6173 &> /dev/null; then
    log_success "前端应用 (6173) 可访问"
else
    log_warning "前端应用 (6173) 不可访问"
fi

# 检查AI服务
if curl -s http://localhost:6800/health &> /dev/null; then
    log_success "AI服务 (6800) 可访问"
else
    log_warning "AI服务 (6800) 不可访问"
fi

# 检查数据库连接
if [ -f ".env" ]; then
    DB_PORT=$(grep -E "^DB_PORT=" .env | cut -d= -f2 || echo "6432")
    DB_USER=$(grep -E "^DB_USER=" .env | cut -d= -f2 || echo "yoghurt_user")

    if command -v pg_isready &> /dev/null; then
        if pg_isready -h localhost -p ${DB_PORT} -U ${DB_USER} &> /dev/null; then
            log_success "数据库连接正常"
        else
            log_warning "数据库连接失败"
        fi
    else
        log_warning "pg_isready 命令不可用，跳过数据库连接检查"
    fi
fi

echo ""
log_info "状态检查完成！"
echo ""
log_info "如果发现问题，请运行以下命令："
echo "  - 安装依赖: npm install"
echo "  - 启动数据库: docker-compose up -d postgres redis"
echo "  - 完整启动: bash scripts/quick-start.sh"
echo "  - 查看日志: docker-compose logs -f"
