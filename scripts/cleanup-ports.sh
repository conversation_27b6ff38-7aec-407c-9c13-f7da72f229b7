#!/bin/bash

# =============================================================================
# Yoghurt AI QC - 端口清理脚本
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🧹 Yogurt AI QC - 端口清理工具"
echo "================================"

# 项目使用的端口
ports=(6000 6173 6432 6479 6800)

log_info "检查项目端口占用情况..."

# 检查每个端口的占用情况
for port in "${ports[@]}"; do
    echo ""
    log_info "检查端口 $port..."
    
    # 获取占用端口的进程信息
    if lsof -i :$port &> /dev/null; then
        log_warning "端口 $port 被占用"
        
        # 显示占用进程的详细信息
        echo "占用进程信息:"
        lsof -i :$port | head -10
        
        # 检查是否是Docker进程
        if lsof -i :$port | grep -q "docker"; then
            log_info "这是Docker容器占用的端口"
            
            # 检查是否是我们项目的容器
            if docker ps | grep -E "(yoghurt-|yoghurt_)" | grep -q ":$port->"; then
                log_info "这是我们项目的Docker容器"
                echo "建议使用: docker-compose down 来停止"
            else
                log_warning "这是其他Docker容器占用的端口"
            fi
        else
            log_warning "这是非Docker进程占用的端口"
            
            # 获取进程PID
            PID=$(lsof -ti :$port | head -1)
            if [ -n "$PID" ]; then
                echo "进程PID: $PID"
                echo "进程信息: $(ps -p $PID -o comm= 2>/dev/null || echo '未知进程')"
                
                read -p "是否要终止这个进程？(y/N): " -n 1 -r
                echo
                if [[ $REPLY =~ ^[Yy]$ ]]; then
                    if kill -9 $PID 2>/dev/null; then
                        log_success "进程 $PID 已终止"
                    else
                        log_error "无法终止进程 $PID，可能需要管理员权限"
                    fi
                else
                    log_info "跳过终止进程"
                fi
            fi
        fi
    else
        log_success "端口 $port 可用"
    fi
done

echo ""
log_info "检查Docker容器状态..."

# 检查我们项目的Docker容器
if docker-compose ps | grep -q "Up"; then
    log_warning "发现运行中的项目容器:"
    docker-compose ps
    echo ""
    
    read -p "是否要停止所有项目容器？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "停止Docker容器..."
        docker-compose down
        log_success "Docker容器已停止"
    else
        log_info "保持容器运行状态"
    fi
else
    log_info "没有运行中的项目容器"
fi

echo ""
log_info "最终端口状态检查..."

all_clear=true
for port in "${ports[@]}"; do
    if lsof -i :$port &> /dev/null; then
        log_warning "端口 $port 仍被占用"
        all_clear=false
    else
        log_success "端口 $port 可用"
    fi
done

echo ""
if [ "$all_clear" = true ]; then
    log_success "🎉 所有端口都已清理完成！"
    echo ""
    log_info "现在可以运行以下命令启动项目:"
    echo "  bash scripts/quick-start.sh"
else
    log_warning "⚠️  仍有端口被占用"
    echo ""
    log_info "建议操作:"
    echo "  1. 手动检查并停止占用端口的服务"
    echo "  2. 重启计算机以清理所有进程"
    echo "  3. 修改项目配置使用其他端口"
fi

echo ""
log_info "清理完成！"
