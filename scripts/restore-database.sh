#!/bin/bash

# =============================================================================
# 数据库恢复脚本
# =============================================================================
# 
# 此脚本用于恢复PostgreSQL和Redis数据
# 
# 使用方法:
# ./scripts/restore-database.sh <postgres_backup_file> [redis_backup_file]
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -lt 1 ]; then
    echo "使用方法: $0 <postgres_backup_file> [redis_backup_file]"
    echo ""
    echo "示例:"
    echo "  $0 ./backups/postgres_backup_20250719_120000.sql.gz"
    echo "  $0 ./backups/postgres_backup_20250719_120000.sql ./backups/redis_backup_20250719_120000.rdb"
    exit 1
fi

PG_BACKUP_FILE="$1"
REDIS_BACKUP_FILE="$2"

echo "🔄 数据库恢复工具"
echo "=================="

# 检查备份文件是否存在
if [ ! -f "$PG_BACKUP_FILE" ]; then
    log_error "PostgreSQL备份文件不存在: $PG_BACKUP_FILE"
    exit 1
fi

# 检查容器是否运行
log_info "检查容器状态..."
if ! docker ps | grep -q "yoghurt-postgres"; then
    log_error "PostgreSQL容器未运行，请先启动: docker-compose up -d postgres"
    exit 1
fi

# 确认恢复操作
echo ""
log_warning "⚠️  警告: 此操作将覆盖现有数据库数据！"
echo "PostgreSQL备份文件: $PG_BACKUP_FILE"
if [ -n "$REDIS_BACKUP_FILE" ]; then
    echo "Redis备份文件: $REDIS_BACKUP_FILE"
fi
echo ""
read -p "确认继续恢复操作？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_info "恢复操作已取消"
    exit 0
fi

# 恢复PostgreSQL
log_info "恢复PostgreSQL数据库..."

# 处理压缩文件
TEMP_SQL_FILE=""
if [[ "$PG_BACKUP_FILE" == *.gz ]]; then
    log_info "解压备份文件..."
    TEMP_SQL_FILE="/tmp/postgres_restore_$(date +%s).sql"
    gunzip -c "$PG_BACKUP_FILE" > "$TEMP_SQL_FILE"
    SQL_FILE="$TEMP_SQL_FILE"
else
    SQL_FILE="$PG_BACKUP_FILE"
fi

# 创建新的数据库（如果需要）
log_info "准备数据库..."
docker exec yoghurt-postgres psql -U postgres -c "DROP DATABASE IF EXISTS postgres_backup;"
docker exec yoghurt-postgres psql -U postgres -c "CREATE DATABASE postgres_backup;"

# 恢复数据
if docker exec -i yoghurt-postgres psql -U postgres postgres < "$SQL_FILE"; then
    log_success "PostgreSQL数据恢复完成"
else
    log_error "PostgreSQL数据恢复失败"
    # 清理临时文件
    [ -n "$TEMP_SQL_FILE" ] && rm -f "$TEMP_SQL_FILE"
    exit 1
fi

# 清理临时文件
[ -n "$TEMP_SQL_FILE" ] && rm -f "$TEMP_SQL_FILE"

# 恢复Redis（如果提供了备份文件）
if [ -n "$REDIS_BACKUP_FILE" ] && [ -f "$REDIS_BACKUP_FILE" ]; then
    log_info "恢复Redis数据..."
    
    if ! docker ps | grep -q "yoghurt-redis"; then
        log_error "Redis容器未运行，请先启动: docker-compose up -d redis"
        exit 1
    fi
    
    # 处理压缩文件
    TEMP_RDB_FILE=""
    if [[ "$REDIS_BACKUP_FILE" == *.gz ]]; then
        log_info "解压Redis备份文件..."
        TEMP_RDB_FILE="/tmp/redis_restore_$(date +%s).rdb"
        gunzip -c "$REDIS_BACKUP_FILE" > "$TEMP_RDB_FILE"
        RDB_FILE="$TEMP_RDB_FILE"
    else
        RDB_FILE="$REDIS_BACKUP_FILE"
    fi
    
    # 停止Redis，复制文件，重启Redis
    docker exec yoghurt-redis redis-cli SHUTDOWN NOSAVE || true
    sleep 2
    
    if docker cp "$RDB_FILE" yoghurt-redis:/data/dump.rdb; then
        docker restart yoghurt-redis
        sleep 5
        log_success "Redis数据恢复完成"
    else
        log_error "Redis数据恢复失败"
        docker restart yoghurt-redis
    fi
    
    # 清理临时文件
    [ -n "$TEMP_RDB_FILE" ] && rm -f "$TEMP_RDB_FILE"
fi

echo ""
log_success "数据库恢复完成！"
echo ""
log_info "验证恢复结果:"
echo "  - PostgreSQL: docker exec yoghurt-postgres psql -U postgres -c '\\l'"
echo "  - Redis: docker exec yoghurt-redis redis-cli INFO keyspace"
