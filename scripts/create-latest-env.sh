#!/bin/bash

# =============================================================================
# Yogurt AI QC - 创建最新版本环境脚本
# =============================================================================
# 此脚本专门用于创建使用最新稳定版本的精简conda环境
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 环境配置
ENV_NAME="yoghurt-ai-qc"
ENV_FILE="environment-latest.yml"
REQUIREMENTS_FILE="ai-service/requirements-slim.txt"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "=========================================="
    echo "   创建最新版本Conda环境 - Yogurt AI QC"
    echo "=========================================="
    echo -e "${NC}"
    echo "🚀 使用2025年最新稳定版本创建精简环境"
    echo "📦 Python 3.12 + 最新依赖包"
    echo "⚡ 快速安装，优化性能"
    echo ""
}

# 检查conda是否安装
check_conda() {
    log_step "检查Conda安装状态..."
    
    if ! command -v conda &> /dev/null; then
        log_error "Conda 未安装！"
        echo ""
        echo "请安装 Miniconda 或 Anaconda："
        echo "  Miniconda: https://docs.conda.io/en/latest/miniconda.html"
        echo "  Anaconda: https://www.anaconda.com/products/distribution"
        exit 1
    fi
    
    log_success "Conda 已安装: $(conda --version)"
}

# 检查环境文件
check_env_files() {
    log_step "检查环境配置文件..."
    
    if [ ! -f "$ENV_FILE" ]; then
        log_error "环境文件不存在: $ENV_FILE"
        echo ""
        echo "请确保以下文件存在："
        echo "  - $ENV_FILE"
        echo "  - $REQUIREMENTS_FILE"
        exit 1
    fi
    
    log_success "环境配置文件检查完成"
}

# 检查现有环境
check_existing_env() {
    log_step "检查现有环境..."
    
    if conda env list | grep -q "^$ENV_NAME\s"; then
        log_warning "环境 '$ENV_NAME' 已存在"
        echo ""
        echo "选择操作："
        echo "  1) 删除现有环境并重新创建"
        echo "  2) 更新现有环境"
        echo "  3) 取消操作"
        echo ""
        read -p "请选择 (1/2/3): " choice
        
        case $choice in
            1)
                log_info "删除现有环境..."
                conda env remove -n "$ENV_NAME" -y
                log_success "现有环境已删除"
                ;;
            2)
                log_info "将更新现有环境"
                return 1  # 返回1表示更新模式
                ;;
            3)
                log_info "操作已取消"
                exit 0
                ;;
            *)
                log_error "无效选择，操作已取消"
                exit 1
                ;;
        esac
    fi
    
    return 0  # 返回0表示创建模式
}

# 创建环境
create_env() {
    log_step "创建最新版本Conda环境..."
    
    log_info "从 $ENV_FILE 创建环境..."
    log_info "这可能需要3-5分钟，请耐心等待..."
    
    # 显示进度
    conda env create -f "$ENV_FILE" --verbose
    
    if conda env list | grep -q "^$ENV_NAME\s"; then
        log_success "环境创建成功！"
    else
        log_error "环境创建失败"
        exit 1
    fi
}

# 更新环境
update_env() {
    log_step "更新现有Conda环境..."
    
    log_info "更新环境依赖..."
    conda env update -f "$ENV_FILE" --prune
    
    log_success "环境更新完成！"
}

# 验证环境
verify_env() {
    log_step "验证环境安装..."
    
    log_info "检查关键组件..."
    
    # 激活环境并测试关键组件
    if conda run -n "$ENV_NAME" python -c "
import sys
print(f'Python版本: {sys.version}')

# 测试关键组件
try:
    import fastapi
    print(f'✅ FastAPI: {fastapi.__version__}')
except ImportError as e:
    print(f'❌ FastAPI导入失败: {e}')

try:
    import openai
    print(f'✅ OpenAI: {openai.__version__}')
except ImportError as e:
    print(f'❌ OpenAI导入失败: {e}')

try:
    import cv2
    print(f'✅ OpenCV: {cv2.__version__}')
except ImportError as e:
    print(f'❌ OpenCV导入失败: {e}')

try:
    import numpy as np
    print(f'✅ NumPy: {np.__version__}')
except ImportError as e:
    print(f'❌ NumPy导入失败: {e}')

print('🎉 环境验证完成！')
"; then
        log_success "环境验证通过！"
    else
        log_error "环境验证失败"
        exit 1
    fi
}

# 显示使用指南
show_usage_guide() {
    echo ""
    echo -e "${CYAN}🎯 环境使用指南:${NC}"
    echo ""
    echo "1. 激活环境:"
    echo "   conda activate $ENV_NAME"
    echo ""
    echo "2. 启动AI服务:"
    echo "   cd ai-service"
    echo "   python -m uvicorn src.main:app --reload --port 6800"
    echo ""
    echo "3. 或使用项目脚本:"
    echo "   npm run dev:ai"
    echo ""
    echo "4. 验证安装:"
    echo "   curl http://localhost:6800/health"
    echo ""
    echo "5. 查看API文档:"
    echo "   http://localhost:6800/docs"
    echo ""
    echo "6. 停用环境:"
    echo "   conda deactivate"
    echo ""
    echo -e "${GREEN}🚀 环境已准备就绪，开始开发吧！${NC}"
}

# 显示环境信息
show_env_info() {
    echo ""
    echo -e "${CYAN}📊 环境信息:${NC}"
    echo ""
    
    # 环境路径
    local env_path=$(conda info --envs | grep "$ENV_NAME" | awk '{print $2}')
    echo "📁 环境路径: $env_path"
    
    # 环境大小
    if [ -d "$env_path" ]; then
        local env_size=$(du -sh "$env_path" 2>/dev/null | cut -f1)
        echo "💾 环境大小: $env_size"
    fi
    
    # Python版本
    local python_version=$(conda run -n "$ENV_NAME" python --version)
    echo "🐍 $python_version"
    
    # 包数量
    local package_count=$(conda list -n "$ENV_NAME" | wc -l)
    echo "📦 安装包数量: $package_count"
}

# 主函数
main() {
    show_banner
    check_conda
    check_env_files
    
    # 检查现有环境
    if check_existing_env; then
        # 创建模式
        create_env
    else
        # 更新模式
        update_env
    fi
    
    verify_env
    show_env_info
    show_usage_guide
    
    echo ""
    log_success "最新版本环境创建完成！"
}

# 运行主函数
main "$@"
