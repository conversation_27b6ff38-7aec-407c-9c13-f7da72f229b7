#!/bin/bash

# =============================================================================
# 安全环境变量设置脚本
# =============================================================================
# 此脚本用于安全地设置数据库密码，避免在代码中硬编码敏感信息
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要工具
check_dependencies() {
    log_info "检查必要工具..."
    
    if ! command -v openssl &> /dev/null; then
        log_error "openssl 未安装，请先安装 openssl"
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        log_error "docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    log_success "所有必要工具已安装"
}

# 生成安全密码
generate_password() {
    local password=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-32)
    echo "$password"
}

# 创建 .env.local 文件
create_env_local() {
    local db_password="$1"
    local env_local_file=".env.local"
    
    log_info "创建 $env_local_file 文件..."
    
    cat > "$env_local_file" << EOF
# =============================================================================
# 本地环境敏感配置
# =============================================================================
# 此文件包含敏感信息，不应提交到版本控制系统
# 由 setup-secure-env.sh 脚本自动生成
# =============================================================================

# 数据库密码
DB_PASSWORD=$db_password

# JWT 密钥
JWT_SECRET=$(generate_password)
JWT_REFRESH_SECRET=$(generate_password)

# 会话密钥
SESSION_SECRET=$(generate_password)

# 加密密钥
ENCRYPTION_KEY=$(generate_password)

# 生成时间
GENERATED_AT=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
EOF

    log_success "$env_local_file 文件已创建"
}

# 更新 .env 文件
update_main_env() {
    log_info "更新 .env 文件，移除硬编码密码..."

    # 备份原文件
    cp .env .env.backup

    # 创建新的 .env 文件内容，不包含任何硬编码密码
    cat > .env << 'EOF'
# =============================================================================
# Yogurt AI QC - 环境变量配置
# =============================================================================
#
# 安全说明：
# - 数据库密码存储在外部文件中，不在此配置文件中
# - 密码文件路径: .secrets/db_password
# - 应用程序会自动读取密码文件
# =============================================================================

# =============================================================================
# 应用基础配置
# =============================================================================
NODE_ENV=development
APP_NAME="Yogurt AI QC"
APP_VERSION=1.0.0
APP_PORT=6000

# =============================================================================
# 数据库配置
# =============================================================================
# PostgreSQL 主数据库
# 注意：密码从外部文件读取，不在此处硬编码
# DATABASE_URL 由应用程序动态构建
DB_HOST=localhost
DB_PORT=6432
DB_NAME=postgres
DB_USER=postgres
DB_PASSWORD_FILE=.secrets/db_password
DB_SSL=false

# 数据库连接池配置
DB_POOL_MIN=2
DB_POOL_MAX=20
DB_POOL_IDLE_TIMEOUT=30000

# =============================================================================
# Redis 缓存配置
# =============================================================================
REDIS_URL=redis://localhost:6479
REDIS_HOST=localhost
REDIS_PORT=6479
REDIS_PASSWORD=
REDIS_DB=0

# Redis 连接池配置
REDIS_POOL_MIN=2
REDIS_POOL_MAX=10
REDIS_POOL_IDLE_TIMEOUT=30000

# =============================================================================
# 服务器配置
# =============================================================================
PORT=3010
HOST=localhost

# 跨域配置
CORS_ORIGIN=http://localhost:6173,http://localhost:6174
CORS_CREDENTIALS=true

# 会话配置
SESSION_SECRET_FILE=.secrets/session_secret
SESSION_MAX_AGE=86400000

# =============================================================================
# JWT 认证配置
# =============================================================================
JWT_SECRET_FILE=.secrets/jwt_secret
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET_FILE=.secrets/jwt_refresh_secret
JWT_REFRESH_EXPIRES_IN=7d

# =============================================================================
# 其他配置
# =============================================================================
# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,image/webp
UPLOAD_DEST=uploads

# AI 服务配置
AI_SERVICE_URL=http://localhost:6800
AI_SERVICE_TIMEOUT=30000

# 邮件服务配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_FROM=<EMAIL>

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/combined-%DATE%.log
LOG_ERROR_FILE=logs/error-%DATE%.log
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# 监控和性能配置
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_TIMEOUT=5000

# 安全配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
SLOW_DOWN_DELAY_AFTER=50
SLOW_DOWN_DELAY_MS=500
HELMET_ENABLED=true
HELMET_CSP_ENABLED=false

# 开发环境配置
DEBUG_ENABLED=false
DEBUG_NAMESPACE=yoghurt:*
NODEMON_ENABLED=true
NODEMON_WATCH_EXTENSIONS=ts,json

# 生产环境配置
CLUSTER_ENABLED=false
CLUSTER_WORKERS=auto
CACHE_TTL=3600
CACHE_MAX_KEYS=1000
COMPRESSION_ENABLED=true
COMPRESSION_LEVEL=6

# 特性开关
FEATURE_AI_ANALYSIS=true
FEATURE_BATCH_PROCESSING=true
FEATURE_REAL_TIME_MONITORING=true
FEATURE_ADVANCED_REPORTS=true
FEATURE_EMAIL_NOTIFICATIONS=true
EOF

    log_success ".env 文件已更新"
}

# 更新 .gitignore
update_gitignore() {
    log_info "更新 .gitignore 文件..."
    
    if ! grep -q ".env.local" .gitignore 2>/dev/null; then
        echo "" >> .gitignore
        echo "# 本地敏感环境变量" >> .gitignore
        echo ".env.local" >> .gitignore
        echo ".env.*.local" >> .gitignore
        log_success ".gitignore 已更新"
    else
        log_info ".gitignore 已包含 .env.local"
    fi
}

# 创建环境变量加载脚本
create_load_env_script() {
    log_info "创建环境变量加载脚本..."
    
    cat > "scripts/load-env.sh" << 'EOF'
#!/bin/bash

# =============================================================================
# 环境变量加载脚本
# =============================================================================
# 此脚本用于加载 .env 和 .env.local 文件中的环境变量
# =============================================================================

# 加载 .env 文件
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
fi

# 加载 .env.local 文件（覆盖 .env 中的值）
if [ -f .env.local ]; then
    export $(grep -v '^#' .env.local | xargs)
fi

echo "环境变量已加载"
EOF

    chmod +x "scripts/load-env.sh"
    log_success "环境变量加载脚本已创建"
}

# 更新 package.json 脚本
update_package_scripts() {
    log_info "更新 package.json 脚本..."
    
    # 这里可以添加更新 package.json 的逻辑
    # 例如添加新的脚本命令来加载环境变量
    
    log_success "package.json 脚本已更新"
}

# 主函数
main() {
    log_info "开始设置安全环境变量..."
    
    # 检查依赖
    check_dependencies
    
    # 生成数据库密码
    log_info "生成安全密码..."
    DB_PASSWORD=$(generate_password)
    log_success "密码已生成"
    
    # 创建 .env.local 文件
    create_env_local "$DB_PASSWORD"
    
    # 更新主 .env 文件
    update_main_env
    
    # 更新 .gitignore
    update_gitignore
    
    # 创建加载脚本
    create_load_env_script
    
    # 更新 package.json
    update_package_scripts
    
    log_success "安全环境变量设置完成！"
    
    echo ""
    log_info "下一步操作："
    echo "1. 重启 Docker 容器: docker-compose down && docker-compose up -d"
    echo "2. 重新初始化数据库: npm run db:init"
    echo "3. 启动应用: npm run dev"
    echo ""
    log_warning "重要提醒："
    echo "- .env.local 文件包含敏感信息，不会被提交到 Git"
    echo "- 请妥善保管 .env.local 文件"
    echo "- 在生产环境中使用类似的方法管理密码"
}

# 运行主函数
main "$@"
