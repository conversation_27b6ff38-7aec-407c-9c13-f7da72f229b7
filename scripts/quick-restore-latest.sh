#!/bin/bash

# =============================================================================
# 快速恢复最新备份脚本
# =============================================================================
# 
# 此脚本自动恢复最新的数据库备份
# 
# 使用方法:
# ./scripts/quick-restore-latest.sh
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

BACKUP_DIR="/Volumes/acasis/yoghurt/backups"

echo "⚡ 快速恢复最新备份"
echo "===================="

# 查找最新的备份文件
log_info "查找最新备份文件..."

LATEST_PG_BACKUP=$(ls -t "$BACKUP_DIR"/postgres_backup_*.sql.gz 2>/dev/null | head -1)
LATEST_REDIS_BACKUP=$(ls -t "$BACKUP_DIR"/redis_backup_*.rdb.gz 2>/dev/null | head -1)

if [ -z "$LATEST_PG_BACKUP" ]; then
    log_error "未找到PostgreSQL备份文件"
    exit 1
fi

echo ""
log_info "找到最新备份:"
echo "  - PostgreSQL: $(basename "$LATEST_PG_BACKUP")"
if [ -n "$LATEST_REDIS_BACKUP" ]; then
    echo "  - Redis: $(basename "$LATEST_REDIS_BACKUP")"
fi

# 显示备份信息
BACKUP_TIMESTAMP=$(basename "$LATEST_PG_BACKUP" .sql.gz | sed 's/postgres_backup_//')
BACKUP_INFO_FILE="$BACKUP_DIR/backup_info_${BACKUP_TIMESTAMP}.txt"

if [ -f "$BACKUP_INFO_FILE" ]; then
    echo ""
    log_info "备份信息:"
    cat "$BACKUP_INFO_FILE"
fi

echo ""
log_warning "⚠️  警告: 此操作将覆盖当前数据库数据！"
read -p "确认恢复到最新备份？(y/N): " -n 1 -r
echo

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_info "恢复操作已取消"
    exit 0
fi

# 执行恢复
log_info "开始恢复数据库..."

if [ -n "$LATEST_REDIS_BACKUP" ]; then
    /Volumes/acasis/yoghurt/scripts/restore-database.sh "$LATEST_PG_BACKUP" "$LATEST_REDIS_BACKUP"
else
    /Volumes/acasis/yoghurt/scripts/restore-database.sh "$LATEST_PG_BACKUP"
fi

if [ $? -eq 0 ]; then
    log_success "快速恢复完成！"
else
    log_error "恢复过程中出现错误"
    exit 1
fi