#!/bin/bash

# =============================================================================
# Yogurt AI QC - 测试自动环境激活
# =============================================================================
# 此脚本测试direnv自动环境激活是否正常工作
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}"
echo "=================================="
echo "    测试自动环境激活"
echo "=================================="
echo -e "${NC}"

# 检查direnv是否可用
echo -e "${BLUE}🔍 检查direnv状态...${NC}"

if ! command -v direnv >/dev/null 2>&1; then
    echo -e "${RED}❌ direnv未找到${NC}"
    echo "请运行: ./scripts/fix-direnv-path.sh"
    exit 1
fi

echo -e "${GREEN}✅ direnv可用: $(direnv version)${NC}"

# 检查.envrc文件
if [ ! -f ".envrc" ]; then
    echo -e "${RED}❌ .envrc文件不存在${NC}"
    exit 1
fi

echo -e "${GREEN}✅ .envrc文件存在${NC}"

# 检查direnv状态
echo -e "${BLUE}📊 Direnv状态:${NC}"
direnv status

echo ""
echo -e "${BLUE}🧪 测试环境激活...${NC}"

# 模拟离开和重新进入目录
echo -e "${YELLOW}📁 模拟离开项目目录...${NC}"
cd ..

echo -e "${YELLOW}📁 重新进入项目目录...${NC}"
cd "$(basename "$OLDPWD")"

echo ""
echo -e "${BLUE}🔍 检查环境变量...${NC}"

# 检查关键环境变量
check_var() {
    local var_name="$1"
    local var_value="${!var_name}"
    
    if [ -n "$var_value" ]; then
        echo -e "${GREEN}✅ $var_name=${var_value}${NC}"
    else
        echo -e "${RED}❌ $var_name 未设置${NC}"
    fi
}

check_var "CONDA_DEFAULT_ENV"
check_var "CONDA_PREFIX"
check_var "PROJECT_ROOT"
check_var "AI_SERVICE_PORT"
check_var "FRONTEND_PORT"
check_var "BACKEND_PORT"

echo ""
echo -e "${BLUE}🐍 检查Python环境...${NC}"

if command -v python >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Python版本: $(python --version)${NC}"
    echo -e "${GREEN}✅ Python路径: $(which python)${NC}"
    
    # 检查是否在正确的conda环境中
    if [ "$CONDA_DEFAULT_ENV" = "yoghurt-ai-qc" ]; then
        echo -e "${GREEN}✅ 正确的conda环境: $CONDA_DEFAULT_ENV${NC}"
    else
        echo -e "${RED}❌ 错误的conda环境: $CONDA_DEFAULT_ENV (应该是 yoghurt-ai-qc)${NC}"
    fi
else
    echo -e "${RED}❌ Python未找到${NC}"
fi

echo ""
echo -e "${BLUE}📦 检查关键组件...${NC}"

# 检查Python包
check_package() {
    local package="$1"
    if python -c "import $package" 2>/dev/null; then
        local version=$(python -c "import $package; print(getattr($package, '__version__', 'unknown'))" 2>/dev/null)
        echo -e "${GREEN}✅ $package: $version${NC}"
    else
        echo -e "${RED}❌ $package 未安装${NC}"
    fi
}

check_package "fastapi"
check_package "openai"
check_package "cv2"
check_package "numpy"
check_package "pandas"

echo ""
echo -e "${BLUE}🌐 检查服务端口...${NC}"

# 检查端口是否被占用
check_port() {
    local port="$1"
    local service="$2"
    
    if lsof -i :$port >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  端口 $port ($service) 被占用${NC}"
    else
        echo -e "${GREEN}✅ 端口 $port ($service) 可用${NC}"
    fi
}

check_port "$FRONTEND_PORT" "前端"
check_port "$BACKEND_PORT" "后端"
check_port "$AI_SERVICE_PORT" "AI服务"

echo ""
echo -e "${BLUE}📋 测试总结:${NC}"

# 计算成功率
success_count=0
total_count=6

[ -n "$CONDA_DEFAULT_ENV" ] && ((success_count++))
[ "$CONDA_DEFAULT_ENV" = "yoghurt-ai-qc" ] && ((success_count++))
command -v python >/dev/null 2>&1 && ((success_count++))
python -c "import fastapi" 2>/dev/null && ((success_count++))
python -c "import openai" 2>/dev/null && ((success_count++))
python -c "import cv2" 2>/dev/null && ((success_count++))

success_rate=$((success_count * 100 / total_count))

if [ $success_rate -ge 80 ]; then
    echo -e "${GREEN}🎉 测试通过! 成功率: $success_rate% ($success_count/$total_count)${NC}"
    echo -e "${GREEN}✅ 自动环境激活工作正常${NC}"
elif [ $success_rate -ge 50 ]; then
    echo -e "${YELLOW}⚠️  部分通过! 成功率: $success_rate% ($success_count/$total_count)${NC}"
    echo -e "${YELLOW}💡 可能需要安装一些依赖包${NC}"
else
    echo -e "${RED}❌ 测试失败! 成功率: $success_rate% ($success_count/$total_count)${NC}"
    echo -e "${RED}🔧 需要修复环境配置${NC}"
fi

echo ""
echo -e "${BLUE}💡 建议操作:${NC}"

if [ $success_rate -lt 100 ]; then
    echo "1. 如果conda环境有问题:"
    echo "   ./scripts/create-latest-env.sh"
    echo ""
    echo "2. 如果Python包缺失:"
    echo "   conda activate yoghurt-ai-qc"
    echo "   pip install -r ai-service/requirements-slim.txt"
    echo ""
    echo "3. 如果direnv有问题:"
    echo "   ./scripts/fix-direnv-path.sh"
fi

echo ""
echo "4. 启动开发服务:"
echo "   npm run dev"
echo ""
echo "5. 查看详细文档:"
echo "   cat QUICK_SETUP.md"
