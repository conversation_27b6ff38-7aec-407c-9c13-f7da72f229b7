#!/bin/bash

# =============================================================================
# Yoghurt AI QC - 开发环境设置脚本
# =============================================================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未安装，请先安装 $1"
        return 1
    fi
    return 0
}

# 主函数
main() {
    log_info "开始设置 Yoghurt AI QC 开发环境..."
    
    # 检查必要的工具
    log_info "检查必要工具..."
    
    if ! check_command "conda"; then
        log_error "Conda 未安装，请先安装 Miniconda 或 Anaconda"
        exit 1
    fi
    
    if ! check_command "node"; then
        log_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    if ! check_command "docker"; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! check_command "docker-compose"; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "所有必要工具已安装"
    
    # 创建 Conda 环境
    log_info "创建 Conda 环境..."
    if conda env list | grep -q "yoghurt-ai-qc"; then
        log_warning "Conda 环境 'yoghurt-ai-qc' 已存在，跳过创建"
    else
        conda env create -f environment.yml
        log_success "Conda 环境创建完成"
    fi
    
    # 激活 Conda 环境
    log_info "激活 Conda 环境..."
    source $(conda info --base)/etc/profile.d/conda.sh
    conda activate yoghurt-ai-qc
    log_success "Conda 环境已激活"
    
    # 安装根目录依赖
    log_info "安装根目录 Node.js 依赖..."
    npm install
    log_success "根目录依赖安装完成"
    
    # 创建环境变量文件
    if [ ! -f .env ]; then
        log_info "创建环境变量文件..."
        cp .env.example .env
        log_success "环境变量文件已创建，请编辑 .env 文件填入实际配置"
    else
        log_warning ".env 文件已存在，跳过创建"
    fi
    
    # 创建必要的目录
    log_info "创建必要的目录..."
    mkdir -p logs uploads temp
    log_success "目录创建完成"
    
    # 设置前端环境
    if [ -d "frontend" ]; then
        log_info "设置前端开发环境..."
        cd frontend
        if [ -f "package.json" ]; then
            npm install
            log_success "前端依赖安装完成"
        else
            log_warning "前端 package.json 不存在，跳过依赖安装"
        fi
        cd ..
    fi
    
    # 设置后端环境
    if [ -d "backend" ]; then
        log_info "设置后端开发环境..."
        cd backend
        if [ -f "package.json" ]; then
            npm install
            log_success "后端依赖安装完成"
        else
            log_warning "后端 package.json 不存在，跳过依赖安装"
        fi
        cd ..
    fi
    
    # 设置 AI 服务环境
    if [ -d "ai-service" ]; then
        log_info "设置 AI 服务环境..."
        cd ai-service
        if [ -f "requirements.txt" ]; then
            pip install -r requirements.txt
            log_success "AI 服务依赖安装完成"
        else
            log_warning "AI 服务 requirements.txt 不存在，跳过依赖安装"
        fi
        cd ..
    fi
    
    # 启动 Docker 服务
    log_info "启动 Docker 开发服务..."
    if docker-compose ps | grep -q "Up"; then
        log_warning "Docker 服务已在运行"
    else
        docker-compose up -d postgres redis
        log_success "Docker 基础服务已启动"
    fi
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 10
    
    # 运行数据库迁移
    if [ -f "backend/package.json" ] && grep -q "migrate" backend/package.json; then
        log_info "运行数据库迁移..."
        cd backend
        npm run migrate
        log_success "数据库迁移完成"
        cd ..
    fi
    
    log_success "开发环境设置完成！"
    echo ""
    log_info "下一步操作："
    echo "  1. 编辑 .env 文件，填入 API 密钥等配置"
    echo "  2. 运行 'npm run dev' 启动开发服务器"
    echo "  3. 访问 http://localhost:5173 查看前端应用"
    echo "  4. 访问 http://localhost:3000/api/docs 查看 API 文档"
    echo ""
    log_info "有用的命令："
    echo "  - npm run dev          # 启动所有开发服务"
    echo "  - npm run docker:up    # 启动 Docker 服务"
    echo "  - npm run docker:logs  # 查看 Docker 日志"
    echo "  - conda activate yoghurt-ai-qc  # 激活 Python 环境"
}

# 运行主函数
main "$@"
