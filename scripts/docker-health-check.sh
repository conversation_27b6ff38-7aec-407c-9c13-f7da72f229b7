#!/bin/bash

# =============================================================================
# Docker健康检查脚本
# =============================================================================
# 
# 此脚本用于检查Docker和项目容器的健康状态
# 可以在启动服务前运行，确保Docker环境正常
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🏥 Docker健康检查"
echo "=================="

# 检查Docker是否安装
log_info "检查Docker安装..."
if command -v docker &> /dev/null; then
    log_success "Docker已安装: $(docker --version)"
else
    log_error "Docker未安装"
    exit 1
fi

# 检查Docker是否运行
log_info "检查Docker运行状态..."
if docker info &> /dev/null; then
    log_success "Docker运行正常"
else
    log_error "Docker未运行，请启动Docker Desktop"
    exit 1
fi

# 检查Docker资源使用
log_info "检查Docker资源使用..."
docker system df

# 检查项目容器状态
log_info "检查项目容器状态..."
if docker ps -a | grep -q yoghurt; then
    echo ""
    echo "项目容器状态："
    docker ps -a | grep yoghurt
    echo ""
    
    # 检查是否有异常退出的容器
    if docker ps -a | grep yoghurt | grep -q "Exited"; then
        log_warning "发现异常退出的容器"
        log_info "建议运行: docker-compose down && docker-compose up -d postgres redis"
    fi
else
    log_info "没有发现项目容器"
fi

# 检查端口占用
log_info "检查端口占用情况..."
ports=(6432 6479 6000 6173 6800)
for port in "${ports[@]}"; do
    if lsof -i :$port &> /dev/null; then
        log_warning "端口 $port 被占用"
        lsof -i :$port
    else
        log_success "端口 $port 可用"
    fi
done

# 检查网络
log_info "检查Docker网络..."
if docker network ls | grep -q yoghurt; then
    log_success "项目网络存在"
else
    log_info "项目网络不存在（正常，启动时会创建）"
fi

echo ""
log_success "Docker健康检查完成！"

# 提供建议
echo ""
echo "💡 建议："
echo "- 如果发现异常容器，运行: docker-compose down"
echo "- 如果端口被占用，运行: ./scripts/cleanup-ports.sh"
echo "- 如果Docker响应慢，考虑重启Docker Desktop"
