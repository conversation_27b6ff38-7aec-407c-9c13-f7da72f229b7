#!/bin/bash

# =============================================================================
# Yogurt AI QC - 修复direnv PATH问题
# =============================================================================
# 此脚本修复macOS上direnv PATH问题，确保direnv可以正常工作
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo -e "${BLUE}🔧 修复direnv PATH问题...${NC}"

# 检测Shell类型
if [ -n "$ZSH_VERSION" ]; then
    SHELL_CONFIG="$HOME/.zshrc"
    SHELL_NAME="zsh"
elif [ -n "$BASH_VERSION" ]; then
    SHELL_CONFIG="$HOME/.bashrc"
    SHELL_NAME="bash"
else
    SHELL_CONFIG="$HOME/.bashrc"
    SHELL_NAME="bash"
fi

log_info "检测到Shell: $SHELL_NAME"
log_info "配置文件: $SHELL_CONFIG"

# 检查Homebrew路径
HOMEBREW_PATHS=(
    "/opt/homebrew/bin"
    "/usr/local/bin"
    "/opt/local/bin"
)

DIRENV_PATH=""
for path in "${HOMEBREW_PATHS[@]}"; do
    if [ -f "$path/direnv" ]; then
        DIRENV_PATH="$path"
        break
    fi
done

if [ -z "$DIRENV_PATH" ]; then
    log_error "未找到direnv，请重新安装"
    echo "运行: brew install direnv"
    exit 1
fi

log_success "找到direnv: $DIRENV_PATH/direnv"

# 检查PATH是否包含Homebrew路径
if ! echo "$PATH" | grep -q "$DIRENV_PATH"; then
    log_warning "PATH中缺少Homebrew路径"
    
    # 添加PATH到shell配置
    if ! grep -q "export PATH=\"$DIRENV_PATH:\$PATH\"" "$SHELL_CONFIG" 2>/dev/null; then
        log_info "添加Homebrew路径到 $SHELL_CONFIG"
        echo "" >> "$SHELL_CONFIG"
        echo "# Homebrew PATH" >> "$SHELL_CONFIG"
        echo "export PATH=\"$DIRENV_PATH:\$PATH\"" >> "$SHELL_CONFIG"
        log_success "PATH配置已添加"
    else
        log_info "PATH配置已存在"
    fi
fi

# 检查direnv hook是否配置
HOOK_LINE=""
if [ "$SHELL_NAME" = "zsh" ]; then
    HOOK_LINE='eval "$(direnv hook zsh)"'
else
    HOOK_LINE='eval "$(direnv hook bash)"'
fi

if ! grep -q "direnv hook" "$SHELL_CONFIG" 2>/dev/null; then
    log_info "添加direnv hook到 $SHELL_CONFIG"
    echo "" >> "$SHELL_CONFIG"
    echo "# Direnv hook" >> "$SHELL_CONFIG"
    echo "$HOOK_LINE" >> "$SHELL_CONFIG"
    log_success "Direnv hook已添加"
else
    log_info "Direnv hook已存在"
fi

# 立即应用配置
log_info "应用配置..."
export PATH="$DIRENV_PATH:$PATH"
eval "$(direnv hook $SHELL_NAME)"

# 验证direnv是否工作
if command -v direnv >/dev/null 2>&1; then
    log_success "Direnv现在可以使用: $(direnv version)"
    
    # 重新允许.envrc
    if [ -f ".envrc" ]; then
        log_info "重新加载项目环境..."
        direnv allow
        log_success "项目环境已重新加载"
    fi
else
    log_error "Direnv仍然无法使用"
    exit 1
fi

echo ""
echo -e "${GREEN}✅ 修复完成！${NC}"
echo ""
echo -e "${BLUE}📝 下一步:${NC}"
echo "1. 重新启动终端或运行:"
echo "   source $SHELL_CONFIG"
echo ""
echo "2. 进入项目目录测试:"
echo "   cd $(pwd)"
echo "   # 应该看到环境自动激活"
echo ""
echo "3. 如果仍有问题，请重新启动终端"
