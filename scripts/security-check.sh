#!/bin/bash

# =============================================================================
# 安全检查脚本
# =============================================================================
# 此脚本检查项目中是否存在硬编码的密码或敏感信息
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查硬编码密码
check_hardcoded_passwords() {
    log_info "检查硬编码密码..."
    
    local found_issues=0
    
    # 检查常见的密码模式
    local password_patterns=(
        "password.*=.*['\"][^'\"]{8,}['\"]"
        "PASSWORD.*=.*['\"][^'\"]{8,}['\"]"
        "secret.*=.*['\"][^'\"]{8,}['\"]"
        "SECRET.*=.*['\"][^'\"]{8,}['\"]"
        "key.*=.*['\"][^'\"]{8,}['\"]"
        "KEY.*=.*['\"][^'\"]{8,}['\"]"
    )
    
    for pattern in "${password_patterns[@]}"; do
        local matches=$(grep -r -E "$pattern" . \
            --exclude-dir=node_modules \
            --exclude-dir=.git \
            --exclude-dir=dist \
            --exclude-dir=build \
            --exclude="*.log" \
            --exclude="security-check.sh" \
            2>/dev/null || true)
        
        if [ -n "$matches" ]; then
            log_warning "发现可能的硬编码密码:"
            echo "$matches"
            found_issues=$((found_issues + 1))
        fi
    done
    
    if [ $found_issues -eq 0 ]; then
        log_success "未发现硬编码密码"
    fi
    
    return $found_issues
}

# 检查敏感文件权限
check_file_permissions() {
    log_info "检查敏感文件权限..."
    
    local found_issues=0
    
    # 检查 .secrets 目录
    if [ -d ".secrets" ]; then
        local secrets_perm=$(stat -f "%A" .secrets 2>/dev/null || stat -c "%a" .secrets 2>/dev/null)
        if [ "$secrets_perm" != "700" ]; then
            log_warning ".secrets 目录权限不安全: $secrets_perm (应该是 700)"
            found_issues=$((found_issues + 1))
        else
            log_success ".secrets 目录权限正确: $secrets_perm"
        fi
        
        # 检查密码文件权限
        for file in .secrets/*; do
            if [ -f "$file" ]; then
                local file_perm=$(stat -f "%A" "$file" 2>/dev/null || stat -c "%a" "$file" 2>/dev/null)
                if [ "$file_perm" != "600" ]; then
                    log_warning "$file 权限不安全: $file_perm (应该是 600)"
                    found_issues=$((found_issues + 1))
                else
                    log_success "$file 权限正确: $file_perm"
                fi
            fi
        done
    else
        log_warning ".secrets 目录不存在"
        found_issues=$((found_issues + 1))
    fi
    
    return $found_issues
}

# 检查 .gitignore 配置
check_gitignore() {
    log_info "检查 .gitignore 配置..."
    
    local found_issues=0
    local required_patterns=(
        ".secrets/"
        ".env.local"
        "*.key"
        "*.pem"
    )
    
    for pattern in "${required_patterns[@]}"; do
        if ! grep -q "$pattern" .gitignore 2>/dev/null; then
            log_warning ".gitignore 缺少模式: $pattern"
            found_issues=$((found_issues + 1))
        else
            log_success ".gitignore 包含模式: $pattern"
        fi
    done
    
    return $found_issues
}

# 检查环境变量配置
check_env_config() {
    log_info "检查环境变量配置..."
    
    local found_issues=0
    
    # 检查 .env 文件是否包含硬编码密码
    if [ -f ".env" ]; then
        local suspicious_lines=$(grep -E "(password|secret|key).*=.*[a-zA-Z0-9]{8,}" .env | grep -v "FILE=" | grep -v "PLACEHOLDER" || true)
        if [ -n "$suspicious_lines" ]; then
            log_warning ".env 文件可能包含硬编码密码:"
            echo "$suspicious_lines"
            found_issues=$((found_issues + 1))
        else
            log_success ".env 文件未发现硬编码密码"
        fi
    else
        log_warning ".env 文件不存在"
        found_issues=$((found_issues + 1))
    fi
    
    return $found_issues
}

# 检查密码文件是否存在
check_password_files() {
    log_info "检查密码文件..."
    
    local found_issues=0
    local required_files=(
        ".secrets/db_password"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_warning "密码文件不存在: $file"
            found_issues=$((found_issues + 1))
        else
            log_success "密码文件存在: $file"
            
            # 检查文件是否为空
            if [ ! -s "$file" ]; then
                log_warning "密码文件为空: $file"
                found_issues=$((found_issues + 1))
            fi
        fi
    done
    
    return $found_issues
}

# 生成安全报告
generate_report() {
    local total_issues=$1
    
    echo ""
    echo "=================================="
    echo "         安全检查报告"
    echo "=================================="
    echo "检查时间: $(date)"
    echo "项目路径: $(pwd)"
    echo ""
    
    if [ $total_issues -eq 0 ]; then
        log_success "🎉 所有安全检查通过！"
        echo ""
        echo "✅ 无硬编码密码"
        echo "✅ 文件权限正确"
        echo "✅ .gitignore 配置完整"
        echo "✅ 环境变量配置安全"
        echo "✅ 密码文件存在且有效"
    else
        log_error "❌ 发现 $total_issues 个安全问题"
        echo ""
        echo "请根据上述警告修复安全问题"
    fi
    
    echo "=================================="
}

# 主函数
main() {
    log_info "开始安全检查..."
    echo ""
    
    local total_issues=0
    
    # 执行各项检查
    check_hardcoded_passwords
    total_issues=$((total_issues + $?))
    echo ""
    
    check_file_permissions
    total_issues=$((total_issues + $?))
    echo ""
    
    check_gitignore
    total_issues=$((total_issues + $?))
    echo ""
    
    check_env_config
    total_issues=$((total_issues + $?))
    echo ""
    
    check_password_files
    total_issues=$((total_issues + $?))
    echo ""
    
    # 生成报告
    generate_report $total_issues
    
    # 返回适当的退出码
    if [ $total_issues -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# 运行主函数
main "$@"
