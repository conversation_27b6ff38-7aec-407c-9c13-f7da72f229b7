#!/bin/bash

# =============================================================================
# 检查服务状态脚本
# =============================================================================
# 
# 此脚本用于检查前端和后端服务的运行状态
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "📊 Yogurt AI QC服务状态检查"
echo "=========================="

# 检查PID文件
echo ""
log_info "检查PID文件..."
if [ -f .backend.pid ]; then
    BACKEND_PID=$(cat .backend.pid)
    if ps -p $BACKEND_PID > /dev/null 2>&1; then
        log_success "后端服务运行中 (PID: $BACKEND_PID)"
    else
        log_warning "后端PID文件存在但进程不存在"
        rm -f .backend.pid
    fi
else
    log_info "没有后端PID文件"
fi

if [ -f .frontend.pid ]; then
    FRONTEND_PID=$(cat .frontend.pid)
    if ps -p $FRONTEND_PID > /dev/null 2>&1; then
        log_success "前端服务运行中 (PID: $FRONTEND_PID)"
    else
        log_warning "前端PID文件存在但进程不存在"
        rm -f .frontend.pid
    fi
else
    log_info "没有前端PID文件"
fi

# 检查端口占用
echo ""
log_info "检查端口占用..."
if lsof -i :3010 &> /dev/null; then
    log_success "端口3010被占用 (后端服务)"
    lsof -i :3010 | head -2
else
    log_warning "端口3010未被占用"
fi

if lsof -i :6174 &> /dev/null; then
    log_success "端口6174被占用 (前端服务)"
    lsof -i :6174 | head -2
else
    log_warning "端口6174未被占用"
fi

# 检查服务响应
echo ""
log_info "检查服务响应..."

# 检查后端健康状态
if curl -s http://localhost:3010/health &> /dev/null; then
    log_success "✅ 后端服务响应正常"
    BACKEND_STATUS=$(curl -s http://localhost:3010/health | head -1)
    echo "   响应: $BACKEND_STATUS"
else
    log_error "❌ 后端服务无响应"
fi

# 检查前端服务
if curl -s http://localhost:6174 &> /dev/null; then
    log_success "✅ 前端服务响应正常"
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:6174)
    echo "   HTTP状态码: $HTTP_CODE"
else
    log_error "❌ 前端服务无响应"
fi

# 检查Docker服务
echo ""
log_info "检查Docker服务..."
if docker ps | grep -q "yoghurt-postgres.*healthy\|yoghurt-postgres.*Up"; then
    log_success "✅ PostgreSQL运行正常"
else
    log_warning "⚠️  PostgreSQL状态异常"
fi

if docker ps | grep -q "yoghurt-redis.*healthy\|yoghurt-redis.*Up"; then
    log_success "✅ Redis运行正常"
else
    log_warning "⚠️  Redis状态异常"
fi

# 显示日志文件信息
echo ""
log_info "日志文件信息..."
if [ -f logs/backend.log ]; then
    BACKEND_LOG_SIZE=$(wc -l < logs/backend.log)
    log_info "后端日志: logs/backend.log ($BACKEND_LOG_SIZE 行)"
else
    log_warning "没有后端日志文件"
fi

if [ -f logs/frontend.log ]; then
    FRONTEND_LOG_SIZE=$(wc -l < logs/frontend.log)
    log_info "前端日志: logs/frontend.log ($FRONTEND_LOG_SIZE 行)"
else
    log_warning "没有前端日志文件"
fi

echo ""
log_info "📋 快速命令:"
echo "  查看后端日志: tail -f logs/backend.log"
echo "  查看前端日志: tail -f logs/frontend.log"
echo "  启动服务: ./scripts/start-services-stable.sh"
echo "  停止服务: ./scripts/stop-services.sh"
