#!/bin/bash

# =============================================================================
# Yogurt AI QC - 停止所有服务脚本
# =============================================================================
# 此脚本会优雅地停止所有运行中的服务
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "=================================="
    echo "    Yogurt AI QC 停止脚本"
    echo "=================================="
    echo -e "${NC}"
    echo "🛑 正在停止所有服务..."
    echo ""
}

# 停止Node.js应用
stop_node_services() {
    log_info "停止Node.js应用服务..."
    
    # 停止后端服务
    if [ -f ".backend.pid" ]; then
        local backend_pid=$(cat .backend.pid)
        if kill -0 $backend_pid 2>/dev/null; then
            log_info "停止后端服务 (PID: $backend_pid)..."
            kill $backend_pid
            log_success "后端服务已停止"
        else
            log_warning "后端服务进程不存在"
        fi
        rm -f .backend.pid
    fi
    
    # 停止前端服务
    if [ -f ".frontend.pid" ]; then
        local frontend_pid=$(cat .frontend.pid)
        if kill -0 $frontend_pid 2>/dev/null; then
            log_info "停止前端服务 (PID: $frontend_pid)..."
            kill $frontend_pid
            log_success "前端服务已停止"
        else
            log_warning "前端服务进程不存在"
        fi
        rm -f .frontend.pid
    fi
    
    # 强制停止所有相关进程
    log_info "清理残留进程..."
    
    # 停止可能的nodemon进程
    pkill -f "nodemon" 2>/dev/null || true
    
    # 停止可能的vite进程
    pkill -f "vite" 2>/dev/null || true
    
    # 停止占用端口的进程
    local ports=(3010 6174)
    for port in "${ports[@]}"; do
        local pid=$(lsof -ti :$port 2>/dev/null || true)
        if [ -n "$pid" ]; then
            log_info "停止占用端口 $port 的进程 (PID: $pid)..."
            kill $pid 2>/dev/null || true
        fi
    done
    
    log_success "Node.js服务已停止"
    echo ""
}

# 停止Docker服务
stop_docker_services() {
    log_info "停止Docker服务..."
    
    # 检查Docker是否运行
    if ! docker info &> /dev/null; then
        log_warning "Docker 未运行"
        return
    fi
    
    # 停止项目相关的Docker容器
    if docker-compose ps -q | grep -q .; then
        log_info "停止Docker Compose服务..."
        docker-compose down
        log_success "Docker服务已停止"
    else
        log_info "没有运行中的Docker服务"
    fi
    
    echo ""
}

# 清理临时文件
cleanup_temp_files() {
    log_info "清理临时文件..."
    
    # 清理日志文件
    if [ -d "logs" ]; then
        log_info "清理日志文件..."
        rm -f logs/backend.log logs/frontend.log
    fi
    
    # 清理PID文件
    rm -f .backend.pid .frontend.pid
    
    log_success "临时文件清理完成"
    echo ""
}

# 显示停止结果
show_results() {
    echo -e "${GREEN}"
    echo "✅ 所有服务已停止！"
    echo -e "${NC}"
    
    echo "📊 服务状态:"
    echo "┌─────────────────┬──────┬─────────────┐"
    echo "│ 服务            │ 端口 │ 状态        │"
    echo "├─────────────────┼──────┼─────────────┤"
    
    # 检查端口状态
    local ports=(6174 3010 6432 6479)
    local services=("前端应用" "后端API" "PostgreSQL" "Redis")
    
    for i in "${!ports[@]}"; do
        local port=${ports[$i]}
        local service=${services[$i]}
        local status="已停止"
        local color="${GREEN}"
        
        if lsof -i :$port &> /dev/null; then
            status="仍在运行"
            color="${YELLOW}"
        fi
        
        printf "│ %-15s │ %-4s │ ${color}%-11s${NC} │\n" "$service" "$port" "$status"
    done
    
    echo "└─────────────────┴──────┴─────────────┘"
    
    echo ""
    echo "🔄 重新启动服务:"
    echo "  ./scripts/start-all-services.sh"
    
    echo ""
    echo "🔍 如果有服务仍在运行，可以使用以下命令强制停止:"
    echo "  ./scripts/cleanup-ports.sh"
}

# 主函数
main() {
    show_banner
    stop_node_services
    stop_docker_services
    cleanup_temp_files
    show_results
}

# 运行主函数
main "$@"
