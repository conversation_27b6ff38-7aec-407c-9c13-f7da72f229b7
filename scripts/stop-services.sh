#!/bin/bash

# =============================================================================
# 停止服务脚本
# =============================================================================
# 
# 此脚本用于停止前端和后端服务
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🛑 停止Yogurt AI QC服务"
echo "======================"

# 停止后端服务
if [ -f .backend.pid ]; then
    BACKEND_PID=$(cat .backend.pid)
    log_info "停止后端服务 (PID: $BACKEND_PID)..."
    if kill $BACKEND_PID 2>/dev/null; then
        log_success "后端服务已停止"
    else
        log_warning "后端服务可能已经停止"
    fi
    rm -f .backend.pid
else
    log_info "没有找到后端服务PID文件"
fi

# 停止前端服务
if [ -f .frontend.pid ]; then
    FRONTEND_PID=$(cat .frontend.pid)
    log_info "停止前端服务 (PID: $FRONTEND_PID)..."
    if kill $FRONTEND_PID 2>/dev/null; then
        log_success "前端服务已停止"
    else
        log_warning "前端服务可能已经停止"
    fi
    rm -f .frontend.pid
else
    log_info "没有找到前端服务PID文件"
fi

# 强制清理相关进程
log_info "清理相关进程..."
pkill -f "node.*backend" 2>/dev/null && log_info "清理了后端进程" || true
pkill -f "node.*vite.*yoghurt" 2>/dev/null && log_info "清理了前端进程" || true

# 验证端口是否释放
sleep 2

if lsof -i :3010 &> /dev/null; then
    log_warning "端口3010仍被占用"
    lsof -i :3010
else
    log_success "端口3010已释放"
fi

if lsof -i :6174 &> /dev/null; then
    log_warning "端口6174仍被占用"
    lsof -i :6174
else
    log_success "端口6174已释放"
fi

echo ""
log_success "🎉 所有服务已停止！"
