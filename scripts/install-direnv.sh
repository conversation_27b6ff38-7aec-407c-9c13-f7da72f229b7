#!/bin/bash

# =============================================================================
# Yogurt AI QC - Direnv安装脚本
# =============================================================================
# 此脚本会自动安装和配置direnv，实现项目环境自动激活
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "=================================="
    echo "    Direnv 安装配置脚本"
    echo "=================================="
    echo -e "${NC}"
    echo "🔄 自动安装direnv并配置环境激活"
    echo ""
}

# 检测操作系统
detect_os() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if [ -f /etc/debian_version ]; then
            echo "debian"
        elif [ -f /etc/redhat-release ]; then
            echo "redhat"
        elif [ -f /etc/arch-release ]; then
            echo "arch"
        else
            echo "linux"
        fi
    else
        echo "unknown"
    fi
}

# 检测Shell类型
detect_shell() {
    if [ -n "$ZSH_VERSION" ]; then
        echo "zsh"
    elif [ -n "$BASH_VERSION" ]; then
        echo "bash"
    elif [ -n "$FISH_VERSION" ]; then
        echo "fish"
    else
        echo "unknown"
    fi
}

# 安装direnv
install_direnv() {
    local os=$(detect_os)
    
    log_step "检测到操作系统: $os"
    
    case $os in
        macos)
            if command -v brew >/dev/null 2>&1; then
                log_info "使用Homebrew安装direnv..."
                brew install direnv
            else
                log_error "未找到Homebrew，请先安装Homebrew"
                echo "安装Homebrew: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
                exit 1
            fi
            ;;
        debian)
            log_info "使用apt安装direnv..."
            sudo apt update
            sudo apt install -y direnv
            ;;
        redhat)
            log_info "使用yum/dnf安装direnv..."
            if command -v dnf >/dev/null 2>&1; then
                sudo dnf install -y direnv
            else
                sudo yum install -y direnv
            fi
            ;;
        arch)
            log_info "使用pacman安装direnv..."
            sudo pacman -S --noconfirm direnv
            ;;
        *)
            log_error "不支持的操作系统，请手动安装direnv"
            echo "参考: https://direnv.net/docs/installation.html"
            exit 1
            ;;
    esac
}

# 配置Shell集成
configure_shell() {
    local shell=$(detect_shell)
    local shell_config=""
    local hook_command=""
    
    log_step "配置Shell集成: $shell"
    
    case $shell in
        bash)
            shell_config="$HOME/.bashrc"
            hook_command='eval "$(direnv hook bash)"'
            ;;
        zsh)
            shell_config="$HOME/.zshrc"
            hook_command='eval "$(direnv hook zsh)"'
            ;;
        fish)
            shell_config="$HOME/.config/fish/config.fish"
            hook_command='direnv hook fish | source'
            # 确保fish配置目录存在
            mkdir -p "$(dirname "$shell_config")"
            ;;
        *)
            log_error "不支持的Shell: $shell"
            echo "请手动配置direnv hook"
            return 1
            ;;
    esac
    
    # 检查是否已经配置
    if [ -f "$shell_config" ] && grep -q "direnv hook" "$shell_config"; then
        log_info "Shell集成已配置"
    else
        log_info "添加direnv hook到 $shell_config"
        echo "" >> "$shell_config"
        echo "# Direnv hook for automatic environment activation" >> "$shell_config"
        echo "$hook_command" >> "$shell_config"
        log_success "Shell集成配置完成"
    fi
}

# 验证安装
verify_installation() {
    log_step "验证direnv安装..."
    
    # 重新加载shell配置
    if [ -n "$BASH_VERSION" ]; then
        source ~/.bashrc 2>/dev/null || true
    elif [ -n "$ZSH_VERSION" ]; then
        source ~/.zshrc 2>/dev/null || true
    fi
    
    # 检查direnv命令
    if command -v direnv >/dev/null 2>&1; then
        log_success "Direnv安装成功: $(direnv version)"
    else
        log_error "Direnv安装失败"
        exit 1
    fi
}

# 配置项目
setup_project() {
    log_step "配置项目环境..."
    
    # 检查是否在项目根目录
    if [ ! -f "package.json" ] || [ ! -f ".envrc" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 允许direnv在此项目中工作
    log_info "允许direnv在此项目中工作..."
    direnv allow
    
    log_success "项目配置完成"
}

# 显示使用指南
show_usage_guide() {
    echo ""
    echo -e "${CYAN}🎯 使用指南:${NC}"
    echo ""
    echo "1. 重新启动终端或运行以下命令激活direnv:"
    if [ -n "$BASH_VERSION" ]; then
        echo "   source ~/.bashrc"
    elif [ -n "$ZSH_VERSION" ]; then
        echo "   source ~/.zshrc"
    fi
    echo ""
    echo "2. 进入项目目录测试自动激活:"
    echo "   cd $(pwd)"
    echo "   # 应该看到环境自动激活的消息"
    echo ""
    echo "3. 离开并重新进入目录验证:"
    echo "   cd .."
    echo "   cd $(basename $(pwd))"
    echo ""
    echo "4. 常用direnv命令:"
    echo "   direnv status    # 查看状态"
    echo "   direnv reload    # 重新加载.envrc"
    echo "   direnv allow     # 允许.envrc"
    echo "   direnv deny      # 禁用.envrc"
    echo ""
    echo -e "${GREEN}🚀 现在每次进入项目目录都会自动激活conda环境！${NC}"
}

# 主函数
main() {
    show_banner
    
    # 检查是否已安装
    if command -v direnv >/dev/null 2>&1; then
        log_info "Direnv已安装: $(direnv version)"
    else
        install_direnv
    fi
    
    configure_shell
    verify_installation
    setup_project
    show_usage_guide
    
    echo ""
    log_success "Direnv安装和配置完成！"
}

# 运行主函数
main "$@"
