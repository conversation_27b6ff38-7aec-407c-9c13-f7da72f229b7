#!/bin/bash

# =============================================================================
# Docker数据卷管理脚本
# =============================================================================
# 
# 此脚本用于管理项目的Docker数据卷
# 
# 使用方法:
# ./scripts/manage-volumes.sh [command]
# 
# 命令:
#   list     - 列出所有项目数据卷
#   info     - 显示数据卷详细信息
#   usage    - 显示存储使用情况
#   clean    - 清理未使用的数据卷（谨慎使用）
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "🗄️ Docker数据卷管理工具"
    echo "========================"
    echo ""
    echo "使用方法: $0 [command]"
    echo ""
    echo "命令:"
    echo "  list     - 列出所有项目数据卷"
    echo "  info     - 显示数据卷详细信息"
    echo "  usage    - 显示存储使用情况"
    echo "  backup   - 创建数据备份"
    echo "  clean    - 清理未使用的数据卷（谨慎使用）"
    echo ""
    echo "示例:"
    echo "  $0 list"
    echo "  $0 info"
    echo "  $0 usage"
}

# 列出项目数据卷
list_volumes() {
    echo "📋 项目数据卷列表"
    echo "=================="
    
    if docker volume ls | grep -q yoghurt; then
        echo ""
        docker volume ls | head -1
        docker volume ls | grep yoghurt
        echo ""
        
        # 统计信息
        local count=$(docker volume ls | grep yoghurt | wc -l)
        log_success "找到 $count 个项目数据卷"
    else
        log_warning "没有找到项目数据卷"
    fi
}

# 显示数据卷详细信息
show_info() {
    echo "📊 数据卷详细信息"
    echo "=================="
    
    local volumes=(yoghurt_postgres_data yoghurt_redis_data yoghurt_ai_models)
    
    for volume in "${volumes[@]}"; do
        echo ""
        echo "🔍 $volume:"
        if docker volume inspect "$volume" &> /dev/null; then
            docker volume inspect "$volume" | jq -r '.[0] | "  创建时间: \(.CreatedAt)\n  挂载点: \(.Mountpoint)\n  驱动: \(.Driver)"'
        else
            log_warning "  数据卷不存在"
        fi
    done
}

# 显示存储使用情况
show_usage() {
    echo "💾 存储使用情况"
    echo "================"
    echo ""
    
    log_info "整体存储使用:"
    docker system df
    
    echo ""
    log_info "数据卷详细使用:"
    docker system df -v | grep -E "(VOLUME NAME|yoghurt)"
    
    echo ""
    log_info "容器存储使用:"
    docker ps --format "table {{.Names}}\t{{.Size}}" | grep -E "(NAMES|yoghurt)"
}

# 创建备份
create_backup() {
    echo "💾 创建数据备份"
    echo "================"
    
    if [ -f "./scripts/backup-database.sh" ]; then
        ./scripts/backup-database.sh
    else
        log_error "备份脚本不存在: ./scripts/backup-database.sh"
        exit 1
    fi
}

# 清理未使用的数据卷
clean_volumes() {
    echo "🧹 清理未使用的数据卷"
    echo "===================="
    
    log_warning "⚠️  警告: 此操作将删除所有未使用的Docker数据卷！"
    log_warning "⚠️  这可能包括其他项目的数据卷！"
    echo ""
    
    # 显示将要删除的卷
    log_info "未使用的数据卷:"
    docker volume ls -qf dangling=true
    
    echo ""
    read -p "确认删除所有未使用的数据卷？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker volume prune -f
        log_success "清理完成"
    else
        log_info "清理操作已取消"
    fi
}

# 主函数
main() {
    local command=${1:-"help"}
    
    case $command in
        "list"|"ls")
            list_volumes
            ;;
        "info"|"inspect")
            show_info
            ;;
        "usage"|"df")
            show_usage
            ;;
        "backup")
            create_backup
            ;;
        "clean"|"prune")
            clean_volumes
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 检查jq是否安装（用于JSON解析）
if ! command -v jq &> /dev/null; then
    log_warning "jq未安装，某些功能可能受限"
    log_info "安装jq: brew install jq (macOS) 或 apt-get install jq (Ubuntu)"
fi

# 运行主函数
main "$@"
