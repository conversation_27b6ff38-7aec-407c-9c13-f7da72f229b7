#!/bin/bash

# =============================================================================
# Yoghurt AI QC - 数据库设置脚本
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 安全地加载环境变量
if [ -f .env ]; then
    # 使用grep安全地读取环境变量，避免执行.env文件中的命令
    DB_HOST=$(grep -E "^DB_HOST=" .env | cut -d= -f2 | tr -d '"' || echo "localhost")
    DB_PORT=$(grep -E "^DB_PORT=" .env | cut -d= -f2 | tr -d '"' || echo "6432")
    DB_NAME=$(grep -E "^DB_NAME=" .env | cut -d= -f2 | tr -d '"' || echo "yoghurt_qc")
    DB_USER=$(grep -E "^DB_USER=" .env | cut -d= -f2 | tr -d '"' || echo "yoghurt_user")
    DB_PASSWORD=$(grep -E "^DB_PASSWORD=" .env | cut -d= -f2 | tr -d '"' || echo "R4t7cjrp")
else
    log_error ".env 文件不存在，请先运行 setup-environment.sh"
    exit 1
fi

# 设置默认值（如果环境变量为空）
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-6432}
DB_NAME=${DB_NAME:-yoghurt_qc}
DB_USER=${DB_USER:-yoghurt_user}
DB_PASSWORD=${DB_PASSWORD:-R4t7cjrp}

main() {
    log_info "开始设置数据库..."
    
    # 检查 PostgreSQL 是否运行
    log_info "检查 PostgreSQL 连接..."
    if ! docker-compose ps postgres | grep -q "Up"; then
        log_info "启动 PostgreSQL 容器..."
        docker-compose up -d postgres
        sleep 10
    fi
    
    # 等待数据库就绪
    log_info "等待数据库就绪..."
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose exec -T postgres pg_isready -U $DB_USER -d $DB_NAME > /dev/null 2>&1; then
            log_success "数据库连接成功"
            break
        fi
        
        log_info "等待数据库启动... (尝试 $attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "数据库连接超时"
        exit 1
    fi
    
    # 创建数据库表结构
    log_info "创建数据库表结构..."
    
    # 用户表
    docker-compose exec -T postgres psql -U $DB_USER -d $DB_NAME << 'EOF'
-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    role VARCHAR(50) DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建配方表
CREATE TABLE IF NOT EXISTS recipes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    ingredients JSONB NOT NULL,
    process JSONB NOT NULL,
    fermentation_temperature DECIMAL(4,2),
    fermentation_duration INTEGER, -- 小时
    filtration_duration INTEGER, -- 小时
    version INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建生产批次表
CREATE TABLE IF NOT EXISTS batches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    batch_number VARCHAR(50) UNIQUE NOT NULL,
    recipe_id UUID REFERENCES recipes(id),
    user_id UUID REFERENCES users(id),
    status VARCHAR(50) DEFAULT 'in_progress',
    production_date DATE DEFAULT CURRENT_DATE,
    quantity DECIMAL(8,2), -- 数量 (L 或 kg)
    actual_fermentation_duration INTEGER,
    actual_temperature DECIMAL(4,2),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建感官评估表
CREATE TABLE IF NOT EXISTS sensory_assessments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    batch_id UUID REFERENCES batches(id) ON DELETE CASCADE,
    assessor_id UUID REFERENCES users(id),
    texture_score INTEGER CHECK (texture_score >= 1 AND texture_score <= 5),
    acidity_score INTEGER CHECK (acidity_score >= 1 AND acidity_score <= 5),
    flavor_tags TEXT[],
    flavor_notes TEXT,
    overall_score INTEGER CHECK (overall_score >= 1 AND overall_score <= 5),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建 AI 分析记录表
CREATE TABLE IF NOT EXISTS ai_analyses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    batch_id UUID REFERENCES batches(id) ON DELETE CASCADE,
    image_urls TEXT[] NOT NULL,
    magnification INTEGER NOT NULL,
    staining_method VARCHAR(50),
    analysis_result JSONB NOT NULL,
    quality_score DECIMAL(5,2),
    confidence_score DECIMAL(3,2),
    contamination_detected BOOLEAN DEFAULT false,
    contamination_type VARCHAR(100),
    bacterial_count INTEGER,
    processing_time_ms INTEGER,
    model_version VARCHAR(50),
    api_provider VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建质量报告表
CREATE TABLE IF NOT EXISTS quality_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    batch_id UUID REFERENCES batches(id) ON DELETE CASCADE,
    report_type VARCHAR(50) NOT NULL,
    report_data JSONB NOT NULL,
    generated_by UUID REFERENCES users(id),
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建系统日志表
CREATE TABLE IF NOT EXISTS system_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id UUID,
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_recipes_user_id ON recipes(user_id);
CREATE INDEX IF NOT EXISTS idx_recipes_created_at ON recipes(created_at);
CREATE INDEX IF NOT EXISTS idx_batches_recipe_id ON batches(recipe_id);
CREATE INDEX IF NOT EXISTS idx_batches_user_id ON batches(user_id);
CREATE INDEX IF NOT EXISTS idx_batches_status ON batches(status);
CREATE INDEX IF NOT EXISTS idx_batches_production_date ON batches(production_date);
CREATE INDEX IF NOT EXISTS idx_ai_analyses_batch_id ON ai_analyses(batch_id);
CREATE INDEX IF NOT EXISTS idx_ai_analyses_created_at ON ai_analyses(created_at);
CREATE INDEX IF NOT EXISTS idx_sensory_assessments_batch_id ON sensory_assessments(batch_id);
CREATE INDEX IF NOT EXISTS idx_system_logs_user_id ON system_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs(created_at);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表添加更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_recipes_updated_at BEFORE UPDATE ON recipes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_batches_updated_at BEFORE UPDATE ON batches
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
EOF

    log_success "数据库表结构创建完成"
    
    # 插入初始数据
    log_info "插入初始测试数据..."
    
    docker-compose exec -T postgres psql -U $DB_USER -d $DB_NAME << 'EOF'
-- 插入测试用户
INSERT INTO users (email, password_hash, name, role) VALUES 
('<EMAIL>', '$2b$12$LQv3c1yqBw2LeOzMpkDCW.vcgsANpvAF.RQcZPdAVpOcc9IuHOZFm', '管理员', 'admin'),
('<EMAIL>', '$2b$12$LQv3c1yqBw2LeOzMpkDCW.vcgsANpvAF.RQcZPdAVpOcc9IuHOZFm', '普通用户', 'user')
ON CONFLICT (email) DO NOTHING;

-- 插入示例配方
INSERT INTO recipes (user_id, name, description, ingredients, process, fermentation_temperature, fermentation_duration) 
SELECT 
    u.id,
    '经典原味酸奶',
    '使用优质全脂牛奶制作的经典原味酸奶',
    '[
        {"name": "全脂牛奶", "amount": 1000, "unit": "ml"},
        {"name": "酸奶菌粉", "amount": 1, "unit": "包"},
        {"name": "奶粉", "amount": 30, "unit": "g"}
    ]'::jsonb,
    '[
        {"step": 1, "description": "牛奶加热至85°C杀菌", "duration": 30, "temperature": 85},
        {"step": 2, "description": "冷却至43°C", "duration": 60, "temperature": 43},
        {"step": 3, "description": "加入菌粉搅拌均匀", "duration": 5},
        {"step": 4, "description": "发酵", "duration": 480, "temperature": 43}
    ]'::jsonb,
    43.0,
    8
FROM users u WHERE u.email = '<EMAIL>'
ON CONFLICT DO NOTHING;
EOF

    log_success "初始数据插入完成"
    
    log_success "数据库设置完成！"
    echo ""
    log_info "数据库信息："
    echo "  - 主机: $DB_HOST"
    echo "  - 端口: $DB_PORT"
    echo "  - 数据库: $DB_NAME"
    echo "  - 用户: $DB_USER"
    echo ""
    log_info "测试账户："
    echo "  - 管理员: <EMAIL> / password"
    echo "  - 普通用户: <EMAIL> / password"
}

main "$@"
