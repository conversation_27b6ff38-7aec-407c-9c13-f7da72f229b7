#!/bin/bash

# =============================================================================
# Yogurt AI QC - 一键启动所有服务脚本
# =============================================================================
# 此脚本会按正确顺序启动所有必要的服务
# =============================================================================

set -e

# 获取脚本所在目录并切换到项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_ROOT"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "=================================="
    echo "    Yogurt AI QC 启动脚本"
    echo "=================================="
    echo -e "${NC}"
    echo "🚀 正在启动所有服务..."
    echo ""
}

# 检查系统要求
check_requirements() {
    log_step "1/7 检查系统要求..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js >= 18.0.0"
        exit 1
    fi
    
    local node_version=$(node -v | sed 's/v//')
    log_info "Node.js 版本: $node_version"
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    local npm_version=$(npm -v)
    log_info "npm 版本: $npm_version"
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi

    # 检查Conda (可选，用于AI服务)
    if command -v conda &> /dev/null; then
        log_info "Conda 已安装: $(conda --version)"

        # 检查AI环境是否存在
        if conda env list | grep -q "yoghurt-ai-qc"; then
            log_success "Conda环境 'yoghurt-ai-qc' 已存在"
        else
            log_warning "Conda环境 'yoghurt-ai-qc' 不存在"
            echo "是否要创建Conda环境? (y/N)"
            read -r response
            if [[ "$response" =~ ^[Yy]$ ]]; then
                log_info "创建Conda环境..."
                if [ -f "environment-latest.yml" ]; then
                    conda env create -f environment-latest.yml
                    log_success "Conda环境创建完成"
                elif [ -f "environment.yml" ]; then
                    conda env create -f environment.yml
                    log_success "Conda环境创建完成"
                else
                    log_warning "环境配置文件不存在，跳过环境创建"
                fi
            fi
        fi
    else
        log_warning "Conda 未安装，AI服务可能无法正常运行"
        log_info "如需使用AI服务，请安装Conda并运行: ./scripts/manage-conda-env.sh create"
    fi

    log_success "系统要求检查通过"
    echo ""
}

# 检查端口占用
check_ports() {
    log_step "2/7 检查端口占用..."
    
    local ports=(3010 6174 6432 6479 6800)
    local occupied_ports=()
    
    for port in "${ports[@]}"; do
        if lsof -i :$port &> /dev/null; then
            occupied_ports+=($port)
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        log_warning "以下端口被占用: ${occupied_ports[*]}"
        echo "是否要清理这些端口? (y/N)"
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            for port in "${occupied_ports[@]}"; do
                log_info "清理端口 $port..."
                lsof -ti :$port | xargs kill -9 2>/dev/null || true
            done
            log_success "端口清理完成"
        else
            log_warning "跳过端口清理，可能会导致启动失败"
        fi
    else
        log_success "所有端口可用"
    fi
    echo ""
}

# 安装依赖
install_dependencies() {
    log_step "3/7 安装项目依赖..."
    
    if [ ! -d "node_modules" ]; then
        log_info "安装根目录依赖..."
        npm install
    else
        log_info "根目录依赖已存在，跳过安装"
    fi
    
    # 检查前端依赖
    if [ ! -d "frontend/node_modules" ]; then
        log_info "安装前端依赖..."
        cd frontend && npm install && cd ..
    else
        log_info "前端依赖已存在，跳过安装"
    fi
    
    # 检查后端依赖
    if [ ! -d "backend/node_modules" ]; then
        log_info "安装后端依赖..."
        cd backend && npm install && cd ..
    else
        log_info "后端依赖已存在，跳过安装"
    fi
    
    log_success "依赖安装完成"
    echo ""
}

# 启动Docker服务
start_docker_services() {
    log_step "4/7 启动Docker服务..."
    
    # 检查Docker是否运行（带重试机制）
    local docker_attempts=5
    local docker_attempt=1

    while [ $docker_attempt -le $docker_attempts ]; do
        if docker info &> /dev/null; then
            log_success "Docker 运行正常"
            break
        fi

        if [ $docker_attempt -eq 1 ]; then
            log_info "检查Docker状态..."
        fi

        log_info "Docker未响应，等待启动... (尝试 $docker_attempt/$docker_attempts)"
        sleep 5
        docker_attempt=$((docker_attempt + 1))
    done

    if [ $docker_attempt -gt $docker_attempts ]; then
        log_error "Docker 未运行，请启动 Docker Desktop"
        log_info "如果Docker Desktop已启动，请等待其完全加载后重试"
        exit 1
    fi
    
    # 启动PostgreSQL和Redis
    log_info "启动 PostgreSQL 和 Redis..."

    # 尝试启动容器，如果失败则重试
    if ! docker-compose up -d postgres redis; then
        log_warning "首次启动失败，清理并重试..."
        docker-compose down postgres redis &> /dev/null
        sleep 3
        docker-compose up -d postgres redis
    fi
    
    # 等待服务启动
    log_info "等待数据库服务启动..."

    # 等待PostgreSQL启动
    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if docker ps | grep yoghurt-postgres | grep -q "healthy\|Up"; then
            log_success "PostgreSQL 启动成功"
            break
        fi

        if [ $attempt -eq 1 ]; then
            log_info "等待PostgreSQL启动..."
        fi

        sleep 2
        attempt=$((attempt + 1))
    done

    if [ $attempt -gt $max_attempts ]; then
        log_error "PostgreSQL 启动超时"
        docker-compose logs postgres
        exit 1
    fi

    # 等待Redis启动
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if docker ps | grep yoghurt-redis | grep -q "healthy\|Up"; then
            log_success "Redis 启动成功"
            break
        fi

        if [ $attempt -eq 1 ]; then
            log_info "等待Redis启动..."
        fi

        sleep 2
        attempt=$((attempt + 1))
    done

    if [ $attempt -gt $max_attempts ]; then
        log_error "Redis 启动超时"
        docker-compose logs redis
        exit 1
    fi
    
    echo ""
}

# 初始化数据库
initialize_database() {
    log_step "5/7 初始化数据库..."
    
    # 检查密码文件
    if [ ! -f ".secrets/db_password" ]; then
        log_warning "数据库密码文件不存在，正在创建..."
        mkdir -p .secrets
        chmod 700 .secrets
        openssl rand -base64 32 | tr -d "=+/" | cut -c1-32 > .secrets/db_password
        chmod 600 .secrets/db_password
        log_success "密码文件已创建"
    fi
    
    # 运行数据库初始化
    log_info "运行数据库迁移和种子数据..."
    cd backend
    
    # 检查数据库连接
    if npm run db:init &> /dev/null; then
        log_success "数据库初始化完成"
    else
        log_warning "数据库可能已经初始化，跳过"
    fi
    
    cd ..
    echo ""
}

# 启动应用服务
start_application_services() {
    log_step "6/7 启动应用服务..."
    
    # 创建日志目录
    mkdir -p logs
    
    log_info "启动后端服务 (端口 3010)..."
    cd backend
    npm run dev > ../logs/backend.log 2>&1 &
    BACKEND_PID=$!
    cd ..
    
    # 等待后端启动
    log_info "等待后端服务启动..."
    sleep 5
    
    # 检查后端是否启动成功
    if curl -s http://localhost:3010/health &> /dev/null; then
        log_success "后端服务启动成功"
    else
        log_warning "后端服务可能仍在启动中..."
    fi
    
    log_info "启动前端服务 (端口 6174)..."
    cd frontend
    npm run dev > ../logs/frontend.log 2>&1 &
    FRONTEND_PID=$!
    cd ..
    
    # 等待前端启动
    log_info "等待前端服务启动..."
    sleep 8
    
    # 检查前端是否启动成功
    if curl -s http://localhost:6174 &> /dev/null; then
        log_success "前端服务启动成功"
    else
        log_warning "前端服务可能仍在启动中..."
    fi
    
    echo ""
}

# 显示启动结果
show_results() {
    log_step "7/7 启动完成！"
    
    echo -e "${GREEN}"
    echo "🎉 所有服务启动成功！"
    echo -e "${NC}"
    
    echo "📊 服务状态:"
    echo "┌─────────────────┬──────┬─────────────────────────────┐"
    echo "│ 服务            │ 端口 │ 访问地址                    │"
    echo "├─────────────────┼──────┼─────────────────────────────┤"
    echo "│ 前端应用        │ 6174 │ http://localhost:6174       │"
    echo "│ 后端API         │ 3010 │ http://localhost:3010       │"
    echo "│ API文档         │ 3010 │ http://localhost:3010/api/docs │"
    echo "│ PostgreSQL      │ 6432 │ localhost:6432              │"
    echo "│ Redis           │ 6479 │ localhost:6479              │"
    echo "└─────────────────┴──────┴─────────────────────────────┘"
    
    echo ""
    echo "🔑 测试账户:"
    echo "  邮箱: <EMAIL>"
    echo "  密码: password123"
    
    echo ""
    echo "📝 有用的命令:"
    echo "  查看日志: tail -f logs/backend.log 或 tail -f logs/frontend.log"
    echo "  停止服务: ./scripts/stop-all-services.sh"
    echo "  重启服务: ./scripts/restart-all-services.sh"
    echo "  健康检查: ./scripts/check-status.sh"
    
    echo ""
    echo "🌐 现在可以访问应用了："
    echo "  👉 http://localhost:6174"
    
    # 保存进程ID
    echo "$BACKEND_PID" > .backend.pid
    echo "$FRONTEND_PID" > .frontend.pid
}

# 清理函数
cleanup() {
    log_info "正在清理..."
    if [ -n "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    if [ -n "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    exit 1
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主函数
main() {
    show_banner
    check_requirements
    check_ports
    install_dependencies
    start_docker_services
    initialize_database
    start_application_services
    show_results
    
    echo ""
    log_info "按 Ctrl+C 停止所有服务"
    
    # 保持脚本运行
    wait
}

# 运行主函数
main "$@"
