# 🔐 RBAC权限系统测试指南

## 📊 系统概览

Yogurt AI QC系统现已实现完整的基于角色的访问控制(RBAC)，支持4种用户角色，每种角色都有差异化的界面和功能。

## 👥 测试账户

| 角色 | 邮箱 | 密码 | 权限级别 |
|------|------|------|----------|
| 🔴 **管理员** | <EMAIL> | password123 | 完整系统权限 |
| 🔵 **质检员** | <EMAIL> | password123 | 酸奶质检操作权限 |
| 🟢 **消费者** | <EMAIL> | password123 | 产品信息查看权限 |

## 🧪 测试步骤

### 1. 管理员角色测试 (<EMAIL>)

**预期界面特征:**
- ✅ 红色角色徽章显示"系统管理员"
- ✅ 皇冠图标头像
- ✅ 完整的侧边栏菜单

**功能测试:**
1. **登录后检查:**
   - 仪表板显示"管理员控制台"
   - 统计卡片：总用户数、活跃批次、今日分析、系统健康度
   - 快速操作：用户管理、审计日志、系统设置

2. **导航菜单验证:**
   - ✅ 仪表板
   - ✅ 配方管理
   - ✅ 批次管理
   - ✅ AI分析
   - ✅ 报告中心
   - ✅ 用户管理 (管理员专用)
   - ✅ 审计日志 (管理员专用)
   - ✅ 数据管理 (管理员专用)
   - ✅ 系统设置 (管理员专用)

3. **用户管理页面测试:**
   - 访问 `/users` 页面
   - 查看用户列表和统计信息
   - 测试新建/编辑用户功能

### 2. 质检员角色测试 (<EMAIL>)

**预期界面特征:**
- ✅ 蓝色角色徽章显示"质检员"
- ✅ 实验图标头像
- ✅ 受限的侧边栏菜单

**功能测试:**
1. **登录后检查:**
   - 仪表板显示"我的工作台"
   - 统计卡片：我的配方、进行中批次、本月分析、平均质量分
   - 快速操作：新建批次、AI分析、我的配方

2. **导航菜单验证:**
   - ✅ 仪表板
   - ✅ 配方管理 (只读)
   - ✅ 批次管理
   - ✅ AI分析
   - ✅ 报告中心
   - ❌ 用户管理 (不可见)
   - ❌ 审计日志 (不可见)
   - ❌ 数据管理 (不可见)
   - ❌ 系统设置 (不可见)

3. **权限边界测试:**
   - 尝试直接访问 `/users` - 应显示权限不足
   - 尝试直接访问 `/audit` - 应显示权限不足

### 3. 消费者角色测试 (<EMAIL>)

**预期界面特征:**
- ✅ 绿色角色徽章显示"消费者"
- ✅ 眼睛图标头像
- ✅ 面向消费者的简化菜单

**功能测试:**
1. **登录后检查:**
   - 仪表板显示"产品质量信息"
   - 统计卡片：在售产品、质量合格率、最新检测
   - 可用操作：查看产品信息、质量检测结果、质量报告

2. **导航菜单验证:**
   - ✅ 仪表板
   - ✅ 产品信息 (消费者专用)
   - ✅ 报告中心 (只读)
   - ❌ 配方管理 (不可见，商业机密)
   - ❌ 批次管理 (不可见，内部信息)
   - ❌ AI分析 (不可见)
   - ❌ 所有管理功能 (不可见)

3. **消费者权限验证:**
   - 只能查看产品质量信息和检测结果
   - 不能查看配方详情、生产工艺等商业机密
   - 不能查看批次管理、用户管理等内部信息



## 🔧 技术验证

### 权限系统组件测试

1. **权限Hook测试:**
```javascript
// 在浏览器控制台中测试
console.log('当前用户权限:', window.__REDUX_STORE__.getState().auth.user)
```

2. **路由保护测试:**
   - 直接访问受限URL
   - 验证权限不足页面显示

3. **组件级权限测试:**
   - 检查按钮和菜单项的条件显示
   - 验证权限守卫组件工作正常

### 数据库验证

```sql
-- 检查用户角色
SELECT id, email, role FROM users ORDER BY role, email;

-- 验证角色枚举
SELECT unnest(enum_range(NULL::user_role));
```

## 🎯 预期结果

### ✅ 成功标准

1. **角色识别:**
   - 每个角色登录后显示正确的徽章和头像
   - 界面主题色彩符合角色设定

2. **功能隔离:**
   - 管理员：完整功能访问
   - 质检员：酸奶质检操作功能
   - 消费者：产品信息查看功能

3. **安全边界:**
   - 无权限访问时显示403页面
   - 直接URL访问受限资源被正确拦截
   - 菜单项根据权限动态显示/隐藏

4. **用户体验:**
   - 角色切换时界面立即更新
   - 权限提示信息友好清晰
   - 导航流畅，无权限冲突

### ❌ 失败情况

如果出现以下情况，说明权限系统有问题：
- 角色徽章显示错误或不显示
- 无权限用户能访问受限功能
- 菜单项显示不正确
- 权限检查失效

## 🚀 快速测试命令

```bash
# 启动服务
./scripts/start-services-stable.sh

# 检查服务状态
./scripts/check-services.sh

# 访问应用
open http://localhost:6174
```

## 📞 问题报告

如果发现权限系统问题，请记录：
1. 使用的测试账户
2. 访问的页面URL
3. 预期行为 vs 实际行为
4. 浏览器控制台错误信息
5. 复现步骤

---

**测试完成后，请确认所有角色都能正常工作，权限边界清晰，用户体验良好！** 🎉
