# 登录和注册界面改进

## 🎯 改进目标

- ✅ 支持用户名和邮箱登录
- ✅ 登录时必须选择咖啡厅
- ✅ 注册时添加用户名字段
- ✅ 用户名在同一咖啡厅内唯一
- ✅ 改进UI和用户体验

## 📊 数据库改进

### 新增字段

```sql
-- 用户表添加用户名字段
ALTER TABLE users 
ADD COLUMN username VARCHAR(50);

-- 创建唯一索引
CREATE UNIQUE INDEX idx_users_username_unique 
ON users(username) WHERE username IS NOT NULL;

-- 咖啡厅内用户名唯一
CREATE UNIQUE INDEX idx_users_cafe_username_unique 
ON users(cafe_id, username) 
WHERE username IS NOT NULL AND cafe_id IS NOT NULL;
```

### 示例测试数据

创建了不同咖啡厅的测试用户：

```sql
-- 海口旗舰店
- 质检员: haikou_inspector / <EMAIL> (密码: password123)
- 消费者: haikou_consumer / <EMAIL> (密码: password123)

-- 三亚分店  
- 质检员: sanya_inspector / <EMAIL> (密码: password123)
- 消费者: sanya_consumer / <EMAIL> (密码: password123)
```

## 🔧 后端API改进

### 注册接口 (`POST /api/auth/register`)

**新的请求体:**
```json
{
  "email": "<EMAIL>",
  "username": "unique_username",
  "name": "用户姓名（可选）",
  "password": "password123",
  "cafeId": "uuid-of-cafe",
  "role": "VIEWER" // 或 "USER"
}
```

**验证规则:**
- 邮箱全局唯一
- 用户名在同一咖啡厅内唯一
- 用户名只能包含字母、数字和下划线
- 用户名长度3-50字符

### 登录接口 (`POST /api/auth/login`)

**新的请求体:**
```json
{
  "identifier": "username_or_email",
  "password": "password123", 
  "cafeId": "uuid-of-cafe"
}
```

**登录逻辑:**
- 支持用户名或邮箱登录
- 必须选择正确的咖啡厅
- 用户必须属于所选咖啡厅
- 用户状态必须是激活的

## 🎨 前端界面改进

### 登录界面

**字段结构:**
1. **用户名/邮箱** - 支持两种方式输入
2. **咖啡厅选择** - 下拉选择所属咖啡厅
3. **密码** - 密码输入框

**特点:**
- 咖啡厅列表动态加载
- 支持搜索咖啡厅
- 显示咖啡厅地址信息
- 智能表单验证

### 注册界面

**字段结构:**
1. **用户名** - 必填，同咖啡厅内唯一
2. **姓名** - 可选
3. **邮箱** - 必填，全局唯一
4. **所属咖啡厅** - 下拉选择
5. **账户类型** - 消费者/质检员
6. **密码** - 设置密码
7. **确认密码** - 密码确认

**验证功能:**
- 用户名格式验证（字母数字下划线）
- 邮箱格式验证
- 密码强度要求
- 密码一致性验证
- 咖啡厅选择验证

## 🔐 安全特性

### 数据隔离

- **用户查找**: 必须在指定咖啡厅内查找用户
- **权限验证**: 用户只能访问所属咖啡厅的数据
- **登录验证**: 严格验证用户-咖啡厅关联关系

### 用户名策略

- **全局建议唯一**: 避免混淆，建议全局唯一
- **咖啡厅内强制唯一**: 同一咖啡厅内绝对不允许重复
- **格式限制**: 只允许安全字符，防止注入攻击

## 🚀 用户体验改进

### 登录流程

1. **选择咖啡厅** → 用户首先选择所属咖啡厅
2. **输入凭据** → 输入用户名/邮箱和密码
3. **身份验证** → 系统验证用户属于该咖啡厅
4. **成功登录** → 进入对应角色的界面

### 注册流程

1. **基本信息** → 填写用户名、邮箱、姓名
2. **选择咖啡厅** → 选择要加入的咖啡厅
3. **选择角色** → 消费者或质检员身份
4. **设置密码** → 设置并确认密码
5. **完成注册** → 自动登录到系统

## 📱 界面示例

### 登录界面布局

```
┌─────────────────────────────────┐
│        🥛 酸奶AI质控系统           │
├─────────────────────────────────┤
│                                 │
│  [登录] [注册]                   │
│                                 │
│  用户名/邮箱: [____________]      │
│  咖啡厅:     [海口旗舰店 ▼]      │
│  密码:       [____________]      │
│                                 │
│           [登录]                │
│                                 │
└─────────────────────────────────┘
```

### 注册界面布局

```
┌─────────────────────────────────┐
│        🥛 酸奶AI质控系统           │
├─────────────────────────────────┤
│                                 │
│  [登录] [注册]                   │
│                                 │
│  用户名:     [____________]      │
│  姓名(可选): [____________]      │
│  邮箱:       [____________]      │
│  咖啡厅:     [请选择咖啡厅 ▼]     │
│                                 │
│  账户类型:                       │
│  ○ 消费者 - 查看产品质量信息        │
│  ○ 质检员 - 管理产品和质量检测     │
│                                 │
│  密码:       [____________]      │
│  确认密码:   [____________]      │
│                                 │
│           [注册]                │
│                                 │
└─────────────────────────────────┘
```

## 🧪 测试场景

### 登录测试

1. **用户名登录**
   - 用户名: `haikou_inspector`
   - 咖啡厅: 海口旗舰店
   - 密码: `password123`

2. **邮箱登录**
   - 邮箱: `<EMAIL>`
   - 咖啡厅: 三亚分店
   - 密码: `password123`

3. **错误场景**
   - 错误咖啡厅: 用海口用户登录三亚咖啡厅
   - 错误密码: 正确用户名但错误密码
   - 不存在用户: 不存在的用户名/邮箱

### 注册测试

1. **成功注册**
   - 新用户名: `test_consumer`
   - 邮箱: `<EMAIL>`
   - 咖啡厅: 海口旗舰店
   - 角色: 消费者

2. **失败场景**
   - 重复用户名: 同咖啡厅内用户名冲突
   - 重复邮箱: 全局邮箱冲突
   - 无效格式: 用户名包含特殊字符

## ✅ 完成状态

- [x] 数据库结构修改（添加用户名字段）
- [x] 后端API更新（登录/注册逻辑）
- [x] 前端界面改进（表单字段和验证）
- [x] 安全验证（用户名唯一性、咖啡厅关联）
- [x] 测试数据创建（不同咖啡厅用户）
- [x] 用户体验优化（智能表单、友好提示）

## 🎯 用户指南

### 如何登录

1. 访问登录页面
2. 选择您所属的咖啡厅
3. 输入您的用户名或邮箱
4. 输入密码
5. 点击登录

### 如何注册

1. 点击"注册"选项卡
2. 填写用户名（将作为您的登录凭据）
3. 填写邮箱地址
4. 选择您要加入的咖啡厅
5. 选择账户类型（消费者或质检员）
6. 设置密码并确认
7. 点击注册

**改进完成！** 🎉

现在用户可以更灵活地使用用户名或邮箱登录，同时系统保持了严格的多租户数据隔离和安全性。