#!/bin/bash

# =============================================================================
# Yogurt AI QC - 项目环境激活脚本
# =============================================================================
# 
# 使用方法:
# source activate.sh  或  . activate.sh
# 
# 此脚本会:
# 1. 激活yoghurt-ai-qc conda环境
# 2. 设置项目环境变量
# 3. 显示环境信息
# =============================================================================

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🚀 激活 Yogurt AI QC 项目环境...${NC}"

# 检查是否在正确的目录
if [ ! -f "package.json" ] || [ ! -d "ai-service" ]; then
    echo -e "${RED}❌ 错误: 请在项目根目录运行此脚本${NC}"
    return 1 2>/dev/null || exit 1
fi

# 检查conda是否可用
if ! command -v conda >/dev/null 2>&1; then
    echo -e "${RED}❌ 错误: Conda未安装或未在PATH中${NC}"
    return 1 2>/dev/null || exit 1
fi

# 初始化conda（如果还没有初始化）
if [ -z "$CONDA_PREFIX" ]; then
    # 尝试找到conda的初始化脚本
    CONDA_BASE=$(conda info --base 2>/dev/null)
    if [ -n "$CONDA_BASE" ] && [ -f "$CONDA_BASE/etc/profile.d/conda.sh" ]; then
        source "$CONDA_BASE/etc/profile.d/conda.sh"
    fi
fi

# 检查yoghurt-ai-qc环境是否存在
if ! conda env list | grep -q "yoghurt-ai-qc"; then
    echo -e "${RED}❌ 错误: yoghurt-ai-qc conda环境不存在${NC}"
    echo -e "${YELLOW}💡 请运行以下命令创建环境:${NC}"
    echo "   ./scripts/create-latest-env.sh"
    return 1 2>/dev/null || exit 1
fi

# 激活conda环境
echo -e "${BLUE}🐍 激活 yoghurt-ai-qc conda环境...${NC}"

# 初始化conda（如果需要）
if [ -z "$CONDA_PREFIX" ]; then
    # 尝试初始化conda
    if [ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]; then
        source "$HOME/miniconda3/etc/profile.d/conda.sh"
    elif [ -f "$HOME/anaconda3/etc/profile.d/conda.sh" ]; then
        source "$HOME/anaconda3/etc/profile.d/conda.sh"
    elif [ -f "/opt/miniconda3/etc/profile.d/conda.sh" ]; then
        source "/opt/miniconda3/etc/profile.d/conda.sh"
    elif [ -f "/opt/anaconda3/etc/profile.d/conda.sh" ]; then
        source "/opt/anaconda3/etc/profile.d/conda.sh"
    else
        # 尝试从conda info获取路径
        CONDA_BASE=$(conda info --base 2>/dev/null)
        if [ -n "$CONDA_BASE" ] && [ -f "$CONDA_BASE/etc/profile.d/conda.sh" ]; then
            source "$CONDA_BASE/etc/profile.d/conda.sh"
        fi
    fi
fi

# 激活环境
conda activate yoghurt-ai-qc

# 检查激活是否成功
if [ "$CONDA_DEFAULT_ENV" = "yoghurt-ai-qc" ]; then
    echo -e "${GREEN}✅ 成功激活 yoghurt-ai-qc 环境${NC}"
else
    echo -e "${RED}❌ 环境激活失败${NC}"
    return 1 2>/dev/null || exit 1
fi

# 设置项目环境变量
export PROJECT_ROOT="$(pwd)"
export AI_SERVICE_PORT=6800
export FRONTEND_PORT=6174
export BACKEND_PORT=3010

# 加载环境变量文件
if [ -f .env ]; then
    echo -e "${BLUE}📄 加载 .env 文件...${NC}"
    set -a
    source .env
    set +a
fi

if [ -f .env.local ]; then
    echo -e "${BLUE}📄 加载 .env.local 文件...${NC}"
    set -a
    source .env.local
    set +a
fi

# 显示环境信息
echo ""
echo -e "${GREEN}🎉 项目环境已准备就绪！${NC}"
echo ""
echo -e "${BLUE}📊 环境信息:${NC}"
echo "  🐍 Python版本: $(python --version)"
echo "  📦 Conda环境: $CONDA_DEFAULT_ENV"
echo "  📁 环境路径: $CONDA_PREFIX"
echo "  🏠 项目路径: $PROJECT_ROOT"
echo ""
echo -e "${BLUE}🌐 服务端口:${NC}"
echo "  🎨 前端: http://localhost:$FRONTEND_PORT"
echo "  🔧 后端: http://localhost:$BACKEND_PORT"
echo "  🤖 AI服务: http://localhost:$AI_SERVICE_PORT"
echo ""
echo -e "${BLUE}🚀 常用命令:${NC}"
echo "  启动所有服务: npm run dev"
echo "  启动AI服务: npm run dev:ai"
echo "  运行测试: npm test"
echo "  查看环境: conda info"
echo ""

# 验证关键组件
echo -e "${BLUE}🔍 验证关键组件...${NC}"
python -c "
try:
    import fastapi, openai, cv2, numpy
    print('✅ 所有关键组件正常')
except ImportError as e:
    print(f'❌ 组件导入失败: {e}')
" 2>/dev/null

echo -e "${GREEN}🎯 环境激活完成！开始开发吧！${NC}"
