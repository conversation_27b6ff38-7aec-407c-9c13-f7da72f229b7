# 🍎 macOS 快速设置指南

## 🎯 目标

在macOS系统上实现项目环境的自动激活。

## 🚀 推荐方案

### 方案1: 使用简单激活脚本 (推荐)

这是最可靠的方法，适用于所有macOS版本：

```bash
# 每次进入项目时运行
source activate-env.sh
```

**优点**:
- ✅ 100%可靠
- ✅ 无需复杂配置
- ✅ 适用于所有Shell
- ✅ 易于调试

### 方案2: 配置direnv自动激活

如果您想要完全自动化，可以配置direnv：

#### 步骤1: 确保direnv正确配置

```bash
# 检查您的Shell
echo $SHELL

# 如果是zsh (macOS默认)
echo 'eval "$(direnv hook zsh)"' >> ~/.zshrc

# 如果是bash
echo 'eval "$(direnv hook bash)"' >> ~/.bashrc
```

#### 步骤2: 重新加载配置

```bash
# 对于zsh用户
source ~/.zshrc

# 对于bash用户
source ~/.bashrc

# 或者重启终端
```

#### 步骤3: 启用项目环境

```bash
# 在项目根目录运行
direnv allow
```

## 🔧 故障排除

### 问题1: "direnv: 未找到命令"

**原因**: PATH配置问题或Shell配置错误

**解决方案**:
```bash
# 方法1: 使用简单激活脚本
source activate-env.sh

# 方法2: 手动修复PATH
export PATH="/opt/homebrew/bin:$PATH"
eval "$(direnv hook zsh)"  # 或 bash
direnv allow
```

### 问题2: conda环境未激活

**检查环境是否存在**:
```bash
conda env list | grep yoghurt-ai-qc
```

**如果不存在，创建环境**:
```bash
./scripts/create-latest-env.sh
```

### 问题3: Python包缺失

```bash
# 激活环境后安装依赖
conda activate yoghurt-ai-qc
pip install -r ai-service/requirements-slim.txt
```

## 🎉 使用体验

### 方案1使用流程
```bash
# 1. 进入项目目录
cd ~/yoghurt-ai-qc

# 2. 激活环境
source activate-env.sh

# 3. 开始开发
npm run dev
```

### 方案2使用流程 (配置成功后)
```bash
# 1. 进入项目目录 (自动激活环境)
cd ~/yoghurt-ai-qc

# 2. 直接开始开发
npm run dev
```

## 📋 验证设置

### 检查环境激活
```bash
# 检查conda环境
echo $CONDA_DEFAULT_ENV  # 应该显示: yoghurt-ai-qc

# 检查Python版本
python --version  # 应该显示: Python 3.12.11

# 检查关键包
python -c "import fastapi, openai, cv2, numpy; print('✅ 所有组件正常')"
```

### 检查环境变量
```bash
echo "项目根目录: $PROJECT_ROOT"
echo "AI服务端口: $AI_SERVICE_PORT"
echo "前端端口: $FRONTEND_PORT"
echo "后端端口: $BACKEND_PORT"
```

## 🛠️ 高级配置

### 创建别名 (可选)

在您的Shell配置文件中添加别名：

```bash
# 添加到 ~/.zshrc 或 ~/.bashrc
alias yoghurt="cd ~/yoghurt-ai-qc && source activate-env.sh"
alias yoghurt-dev="cd ~/yoghurt-ai-qc && source activate-env.sh && npm run dev"
alias yoghurt-ai="cd ~/yoghurt-ai-qc && source activate-env.sh && npm run dev:ai"
```

使用方法:
```bash
yoghurt      # 进入项目并激活环境
yoghurt-dev  # 进入项目、激活环境并启动开发服务
yoghurt-ai   # 进入项目、激活环境并启动AI服务
```

### VS Code集成

如果您使用VS Code，可以配置工作区设置：

1. 在项目根目录创建 `.vscode/settings.json`:
```json
{
    "python.defaultInterpreterPath": "/Volumes/acasis/miniconda3/miniconda3/envs/yoghurt-ai-qc/bin/python",
    "python.terminal.activateEnvironment": true,
    "terminal.integrated.env.osx": {
        "CONDA_DEFAULT_ENV": "yoghurt-ai-qc",
        "PROJECT_ROOT": "${workspaceFolder}",
        "AI_SERVICE_PORT": "6800",
        "FRONTEND_PORT": "6174",
        "BACKEND_PORT": "3010"
    }
}
```

## 📚 相关文档

- [项目启动指南](STARTUP_GUIDE.md)
- [Conda环境管理](CONDA_ENVIRONMENT_GUIDE.md)
- [通用快速设置](QUICK_SETUP.md)

## 💡 推荐工作流

### 日常开发
```bash
# 1. 打开终端
# 2. 进入项目
cd ~/yoghurt-ai-qc

# 3. 激活环境 (选择一种方法)
source activate-env.sh  # 方案1: 手动激活
# 或者如果direnv配置成功，会自动激活

# 4. 开始开发
npm run dev
```

### 首次设置
```bash
# 1. 创建conda环境
./scripts/create-latest-env.sh

# 2. 测试环境激活
source activate-env.sh

# 3. (可选) 配置direnv自动激活
echo 'eval "$(direnv hook zsh)"' >> ~/.zshrc
source ~/.zshrc
direnv allow
```

---

**在macOS上享受流畅的开发体验！** 🍎✨
