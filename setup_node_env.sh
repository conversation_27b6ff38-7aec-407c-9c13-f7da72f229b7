#!/bin/bash

# =============================================================================
# Node.js 开发环境设置脚本
# =============================================================================

echo "🔧 正在设置Node.js开发环境..."

# 加载NVM
export NVM_DIR="$HOME/.nvm"
if [ -s "$NVM_DIR/nvm.sh" ]; then
    \. "$NVM_DIR/nvm.sh"
    echo "✅ NVM已加载"
else
    echo "❌ NVM未找到，请先安装NVM"
    exit 1
fi

# 加载NVM bash completion
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"

# 检查.nvmrc文件
if [ -f ".nvmrc" ]; then
    echo "📋 发现.nvmrc文件，使用指定版本: $(cat .nvmrc)"
    nvm use
else
    echo "📋 使用默认Node.js版本"
    nvm use default
fi

# 验证安装
echo ""
echo "📊 环境信息:"
echo "   Node.js版本: $(node --version)"
echo "   npm版本: $(npm --version)"
echo "   当前路径: $(pwd)"

# 检查Claude CLI
if command -v claude &> /dev/null; then
    echo "   Claude CLI: $(claude --version)"
else
    echo "   Claude CLI: 未安装"
fi

echo ""
echo "🎉 开发环境设置完成！"
echo "💡 提示: 要在当前shell中使用，请运行 'source setup_node_env.sh'"
echo "💡 提示: 或者重新加载shell配置 'source ~/.zshrc'"