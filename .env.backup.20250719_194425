# =============================================================================
# Yoghurt AI QC - 环境变量配置模板
# =============================================================================
# 复制此文件为 .env 并填入实际值

# =============================================================================
# 应用基础配置
# =============================================================================
NODE_ENV=development
APP_NAME="Yoghurt AI QC"
APP_VERSION=1.0.0
APP_PORT=3010

# =============================================================================
# 数据库配置
# =============================================================================
# PostgreSQL 主数据库
# 注意：DATABASE_URL 包含完整的连接信息
# 密码从 .env.local 文件中读取，避免在代码中硬编码
DATABASE_URL=postgresql://postgres:Xo0Zh2RC2PcykMltA3ZeAFHUr8QeJCdU@localhost:6432/postgres
DB_HOST=localhost
DB_PORT=6432
DB_NAME=postgres
DB_USER=postgres
DB_PASSWORD=Xo0Zh2RC2PcykMltA3ZeAFHUr8QeJCdU
DB_SSL=false

# 数据库连接池配置
DB_POOL_MIN=2
DB_POOL_MAX=20
DB_POOL_IDLE_TIMEOUT=30000

# =============================================================================
# Redis 缓存配置
# =============================================================================
REDIS_URL=redis://localhost:6479
REDIS_HOST=localhost
REDIS_PORT=6479
REDIS_PASSWORD=
REDIS_DB=0

# =============================================================================
# JWT 认证配置
# =============================================================================
JWT_SECRET=YOUR_SECURE_JWT_SECRET_KEY_AT_LEAST_32_CHARS
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# =============================================================================
# AI 服务配置
# =============================================================================
# OpenAI API
OPENAI_API_KEY=YOUR_OPENAI_API_KEY_HERE
OPENAI_MODEL=gpt-4-vision-preview
OPENAI_MAX_TOKENS=4096

# Google Gemini API (备用)
GOOGLE_API_KEY=YOUR_GOOGLE_API_KEY_HERE
GEMINI_MODEL=gemini-pro-vision

# Anthropic Claude API (备用)
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# AI 服务配置
AI_SERVICE_URL=http://localhost:6800
AI_SERVICE_PORT=6800
AI_ANALYSIS_TIMEOUT=30000
AI_MAX_RETRIES=3
AI_BATCH_SIZE=4
AI_MAX_CONCURRENT_ANALYSES=10

# AI 模型配置
MODEL_CACHE_DIR=./models
MODEL_CACHE_SIZE=2048
ENABLE_MODEL_CACHING=true
MIN_CONFIDENCE_THRESHOLD=0.7

# AI 性能配置
ENABLE_GPU=false
GPU_MEMORY_LIMIT=
ENABLE_MIXED_PRECISION=false
ENABLE_RESULT_CACHING=true
RESULT_CACHE_TTL=3600

# =============================================================================
# 文件存储配置
# =============================================================================
# 本地文件存储
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,tiff,bmp

# AWS S3 配置 (生产环境)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-west-2
AWS_S3_BUCKET=yoghurt-qc-images

# =============================================================================
# 邮件服务配置
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=<EMAIL>

# =============================================================================
# 监控和日志配置
# =============================================================================
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE=./logs/app.log

# Prometheus 监控
METRICS_ENABLED=true
METRICS_PORT=6090

# Sentry 错误追踪
SENTRY_DSN=your-sentry-dsn-here

# =============================================================================
# 前端应用配置
# =============================================================================
# Vite 开发服务器
VITE_PORT=6173
VITE_API_URL=http://localhost:3010
VITE_AI_SERVICE_URL=http://localhost:6800
VITE_APP_TITLE="Yogurt AI QC"
VITE_APP_DESCRIPTION="AI驱动的酸奶质量控制系统"

# =============================================================================
# 开发工具配置
# =============================================================================
# 热重载
WATCH_FILES=true
WATCH_EXTENSIONS=js,ts,json

# 调试模式
DEBUG=yoghurt:*
VERBOSE_LOGGING=false

# API 文档
SWAGGER_ENABLED=true
SWAGGER_PATH=/api/docs

# =============================================================================
# 安全配置
# =============================================================================
# CORS 配置
CORS_ORIGIN=http://localhost:6173,http://localhost:3010
CORS_CREDENTIALS=true

# 速率限制
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# 加密配置
ENCRYPTION_KEY=YOUR_32_CHAR_ENCRYPTION_KEY_HERE
HASH_ROUNDS=12

# =============================================================================
# 第三方服务配置
# =============================================================================
# 显微镜设备集成 (未来扩展)
MICROSCOPE_API_URL=
MICROSCOPE_API_KEY=

# 实验室信息管理系统集成 (未来扩展)
LIMS_API_URL=
LIMS_API_KEY=

# =============================================================================
# Docker 和容器化配置
# =============================================================================
# Docker Compose 服务名称
POSTGRES_SERVICE_NAME=postgres
REDIS_SERVICE_NAME=redis
BACKEND_SERVICE_NAME=backend
FRONTEND_SERVICE_NAME=frontend
AI_SERVICE_NAME=ai-service

# 容器内部端口 (通常不需要修改)
CONTAINER_BACKEND_PORT=3000
CONTAINER_FRONTEND_PORT=5173
CONTAINER_AI_SERVICE_PORT=8000
CONTAINER_POSTGRES_PORT=5432
CONTAINER_REDIS_PORT=6379

# =============================================================================
# 生产环境配置
# =============================================================================
# 生产环境域名
PRODUCTION_DOMAIN=yoghurt-qc.com
PRODUCTION_API_URL=https://api.yoghurt-qc.com
PRODUCTION_FRONTEND_URL=https://app.yoghurt-qc.com

# SSL 证书配置
SSL_CERT_PATH=/etc/ssl/certs/yoghurt-qc.crt
SSL_KEY_PATH=/etc/ssl/private/yoghurt-qc.key

# 负载均衡配置
ENABLE_LOAD_BALANCER=false
LOAD_BALANCER_PORT=6080

# =============================================================================
# 备份和恢复配置
# =============================================================================
# 数据库备份
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=yoghurt-qc-backups

# 文件备份
FILE_BACKUP_ENABLED=true
FILE_BACKUP_SCHEDULE="0 3 * * *"
