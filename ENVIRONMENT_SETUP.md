# 🚀 Yogurt AI QC - 环境激活指南

## ✅ 已完成配置

您的项目环境激活已经配置完成！direnv相关问题已解决，现在使用更可靠的zsh钩子方式。

## 🎯 自动激活功能

### 工作原理
- 当您进入项目目录时，会自动检测并激活`yoghurt-ai-qc`环境
- 自动加载项目环境变量
- 设置必要的项目配置

### 激活条件
- 当前目录路径包含`/yoghurt`
- 目录中存在`package.json`文件
- 目录中存在`ai-service`文件夹

## 🔧 使用方法

### 方法1: 自动激活（推荐）
```bash
# 关闭并重新打开终端，或者重新加载配置
source ~/.zshrc

# 进入项目目录，会自动激活
cd /Volumes/acasis/yoghurt
```

### 方法2: 手动激活
```bash
# 使用完整的激活脚本
source ./activate.sh

# 或使用快速激活脚本
source ./quick-activate.sh
```

## 📋 验证激活

激活成功后，您应该看到：
```
🔄 检测到Yogurt AI QC项目，正在激活环境...
✅ 已激活 yoghurt-ai-qc 环境
🐍 Python: Python 3.12.11
📦 环境路径: /path/to/conda/envs/yoghurt-ai-qc
```

检查环境状态：
```bash
echo $CONDA_DEFAULT_ENV    # 应显示: yoghurt-ai-qc
which python               # 应指向conda环境中的python
echo $PROJECT_ROOT         # 应显示项目根目录
```

## 🛠️ 故障排除

### 如果自动激活不工作：

1. **重新加载zsh配置**:
   ```bash
   source ~/.zshrc
   ```

2. **检查conda环境是否存在**:
   ```bash
   conda env list | grep yoghurt-ai-qc
   ```

3. **手动激活**:
   ```bash
   source ./activate.sh
   ```

4. **检查项目目录结构**:
   ```bash
   ls -la  # 确保有package.json和ai-service目录
   ```

### 常见问题

- **环境不存在**: 运行 `./scripts/create-latest-env.sh` 创建环境
- **conda未初始化**: 运行 `conda init zsh` 然后重启终端
- **权限问题**: 确保脚本有执行权限 `chmod +x *.sh`

## 🎉 下一步

环境激活后，您可以：

```bash
# 启动所有服务
npm run dev

# 启动单个服务
npm run dev:backend    # 后端服务
npm run dev:frontend   # 前端服务  
npm run dev:ai         # AI服务

# 运行测试
npm test

# 查看环境信息
conda info
```

## 📝 已清理的文件

以下direnv相关文件已被删除：
- `.envrc`
- `quick-fix-env.sh`
- `setup-auto-activation.sh`
- zsh配置中的direnv hook

现在使用更稳定的zsh钩子方式进行自动激活。
