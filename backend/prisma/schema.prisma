generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Cafe {
  id            String             @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name          String             @unique @db.VarChar(100)
  description   String?
  address       String?
  phone         String?            @db.VarChar(20)
  email         String?            @db.VarChar(100)
  logo_url      String?
  status        String?            @default("active") @db.VarChar(20)
  createdAt     DateTime?          @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt     DateTime?          @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  aiAnalyses    AIAnalysisRecord[]
  batches       Batch[]
  notifications Notification[]
  products      Product[]
  quality_tests quality_tests[]
  recipes       Recipe[]
  users         User[]

  @@map("cafes")
}

model User {
  id                                             String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  email                                          String              @unique @db.Var<PERSON>har(255)
  passwordHash                                   String              @map("password_hash") @db.VarChar(255)
  name                                           String              @db.VarChar(100)
  role                                           UserRole            @default(USER)
  isActive                                       Boolean             @default(true) @map("is_active")
  createdAt                                      DateTime            @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt                                      DateTime            @updatedAt @map("updated_at") @db.Timestamp(6)
  cafeId                                         String?             @map("cafe_id") @db.Uuid
  department                                     String?             @default("general") @db.VarChar(50)
  username                                       String?             @db.VarChar(50)
  aiAnalyses                                     AIAnalysisRecord[]
  batches_batches_creator_idTousers              Batch[]             @relation("batches_creator_idTousers")
  batches                                        Batch[]
  uploadedImages                                 MicroscopeImage[]
  notifications                                  Notification[]
  products                                       Product[]
  qualityReports                                 QualityReport[]
  quality_tests_quality_tests_reviewer_idTousers quality_tests[]     @relation("quality_tests_reviewer_idTousers")
  quality_tests_quality_tests_tester_idTousers   quality_tests[]     @relation("quality_tests_tester_idTousers")
  recipes_recipes_creator_idTousers              Recipe[]            @relation("recipes_creator_idTousers")
  recipes                                        Recipe[]
  sensoryAssessments                             SensoryAssessment[]
  systemLogs                                     SystemLog[]
  cafe                                           Cafe?               @relation(fields: [cafeId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([email])
  @@index([role])
  @@index([isActive])
  @@index([createdAt])
  @@index([cafeId, email], map: "idx_users_cafe_email")
  @@index([cafeId], map: "idx_users_cafe_id")
  @@index([role, cafeId], map: "idx_users_role_cafe")
  @@map("users")
}

model Product {
  id                  String          @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  cafeId              String          @map("cafe_id") @db.Uuid
  creator_id          String          @db.Uuid
  name                String          @db.VarChar(100)
  type                String          @db.VarChar(50)
  description         String?
  price               Decimal?        @db.Decimal(10, 2)
  volume              String?         @db.VarChar(20)
  status              String?         @default("active") @db.VarChar(20)
  ingredients         Json?           @default("[]")
  nutritionPer100g    Json?           @default("{}") @map("nutrition_per_100g")
  benefits            Json?           @default("[]")
  certifications      Json?           @default("[]")
  batch_size          Int?
  shelf_life_days     Int?            @default(21)
  storage_temperature String?         @db.VarChar(20)
  images              Json?           @default("[]")
  documents           Json?           @default("[]")
  createdAt           DateTime?       @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt           DateTime?       @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  cafe                Cafe            @relation(fields: [cafeId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  users               User            @relation(fields: [creator_id], references: [id], onUpdate: NoAction)
  quality_tests       quality_tests[]

  @@index([cafeId], map: "idx_products_cafe_id")
  @@index([creator_id], map: "idx_products_creator_id")
  @@index([status], map: "idx_products_status")
  @@index([type], map: "idx_products_type")
  @@map("products")
}

model Recipe {
  id                              String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId                          String   @map("user_id") @db.Uuid
  name                            String   @db.VarChar(100)
  description                     String?
  ingredients                     Json
  process                         Json
  fermentationTemperature         Decimal? @map("fermentation_temperature") @db.Decimal(4, 2)
  fermentationDuration            Int?     @map("fermentation_duration")
  filtrationDuration              Int?     @map("filtration_duration")
  version                         Int      @default(1)
  isActive                        Boolean  @default(true) @map("is_active")
  createdAt                       DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt                       DateTime @updatedAt @map("updated_at") @db.Timestamp(6)
  cafe_id                         String?  @db.Uuid
  creator_id                      String?  @db.Uuid
  batches                         Batch[]
  cafes                           Cafe?    @relation(fields: [cafe_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  users_recipes_creator_idTousers User?    @relation("recipes_creator_idTousers", fields: [creator_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user                            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([name])
  @@index([isActive])
  @@index([createdAt])
  @@index([userId, isActive])
  @@map("recipes")
}

model Batch {
  id                              String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  batchNumber                     String              @unique @map("batch_number") @db.VarChar(50)
  recipeId                        String              @map("recipe_id") @db.Uuid
  userId                          String              @map("user_id") @db.Uuid
  status                          BatchStatus         @default(PLANNING)
  productionDate                  DateTime            @map("production_date") @db.Date
  quantity                        Decimal?            @db.Decimal(8, 2)
  actualFermentationDuration      Int?                @map("actual_fermentation_duration")
  actualTemperature               Decimal?            @map("actual_temperature") @db.Decimal(4, 2)
  notes                           String?
  createdAt                       DateTime            @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt                       DateTime            @updatedAt @map("updated_at") @db.Timestamp(6)
  cafe_id                         String?             @db.Uuid
  creator_id                      String?             @db.Uuid
  aiAnalyses                      AIAnalysis[]
  cafes                           Cafe?               @relation(fields: [cafe_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  users_batches_creator_idTousers User?               @relation("batches_creator_idTousers", fields: [creator_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  recipe                          Recipe              @relation(fields: [recipeId], references: [id], onDelete: Cascade)
  user                            User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  microscopeImages                MicroscopeImage[]
  qualityReports                  QualityReport[]
  sensoryAssessments              SensoryAssessment[]

  @@index([batchNumber])
  @@index([recipeId])
  @@index([userId])
  @@index([status])
  @@index([productionDate])
  @@index([createdAt])
  @@index([userId, status])
  @@index([recipeId, status])
  @@index([productionDate, status])
  @@map("batches")
}

model SensoryAssessment {
  id           String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  batchId      String   @map("batch_id") @db.Uuid
  assessorId   String   @map("assessor_id") @db.Uuid
  textureScore Int      @map("texture_score") @db.SmallInt
  acidityScore Int      @map("acidity_score") @db.SmallInt
  flavorTags   Json     @map("flavor_tags")
  flavorNotes  String?  @map("flavor_notes")
  overallScore Int      @map("overall_score") @db.SmallInt
  notes        String?
  createdAt    DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt    DateTime @updatedAt @map("updated_at") @db.Timestamp(6)
  assessor     User     @relation(fields: [assessorId], references: [id], onDelete: Cascade)
  batch        Batch    @relation(fields: [batchId], references: [id], onDelete: Cascade)

  @@index([batchId])
  @@index([assessorId])
  @@index([overallScore])
  @@index([createdAt])
  @@index([batchId, createdAt])
  @@map("sensory_assessments")
}

model MicroscopeImage {
  id             String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  batchId        String       @map("batch_id") @db.Uuid
  filename       String       @db.VarChar(255)
  originalName   String       @map("original_name") @db.VarChar(255)
  filePath       String       @map("file_path") @db.VarChar(500)
  fileSize       Int          @map("file_size")
  mimeType       String       @map("mime_type") @db.VarChar(100)
  magnification  Int
  stainingMethod String       @map("staining_method") @db.VarChar(100)
  imageWidth     Int?         @map("image_width")
  imageHeight    Int?         @map("image_height")
  metadata       Json?
  uploadedBy     String       @map("uploaded_by") @db.Uuid
  createdAt      DateTime     @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt      DateTime     @updatedAt @map("updated_at") @db.Timestamp(6)
  aiAnalyses     AIAnalysis[]
  batch          Batch        @relation(fields: [batchId], references: [id], onDelete: Cascade)
  uploader       User         @relation(fields: [uploadedBy], references: [id], onDelete: Cascade)

  @@index([batchId])
  @@index([uploadedBy])
  @@index([magnification])
  @@index([stainingMethod])
  @@index([createdAt])
  @@index([batchId, createdAt])
  @@map("microscope_images")
}

model AIAnalysis {
  id                    String           @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  batchId               String           @map("batch_id") @db.Uuid
  microscopeImageId     String?          @map("microscope_image_id") @db.Uuid
  analysisStatus        AnalysisStatus   @default(PENDING) @map("analysis_status")
  qualityScore          Decimal?         @map("quality_score") @db.Decimal(5, 2)
  bacterialCount        Int?             @map("bacterial_count")
  contaminationDetected Boolean          @default(false) @map("contamination_detected")
  contaminationType     String?          @map("contamination_type") @db.VarChar(100)
  morphologyAnalysis    Json?            @map("morphology_analysis")
  confidenceScore       Decimal?         @map("confidence_score") @db.Decimal(5, 2)
  summaryRecommendation String?          @map("summary_recommendation")
  processingTimeMs      Int?             @map("processing_time_ms")
  modelVersion          String?          @map("model_version") @db.VarChar(50)
  apiProvider           String?          @map("api_provider") @db.VarChar(50)
  rawResponse           Json?            @map("raw_response")
  errorMessage          String?          @map("error_message")
  retryCount            Int              @default(0) @map("retry_count")
  createdAt             DateTime         @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt             DateTime         @updatedAt @map("updated_at") @db.Timestamp(6)
  batch                 Batch            @relation(fields: [batchId], references: [id], onDelete: Cascade)
  microscopeImage       MicroscopeImage? @relation(fields: [microscopeImageId], references: [id])

  @@index([batchId])
  @@index([microscopeImageId])
  @@index([analysisStatus])
  @@index([qualityScore])
  @@index([contaminationDetected])
  @@index([createdAt])
  @@index([batchId, analysisStatus])
  @@index([analysisStatus, createdAt])
  @@map("ai_analyses")
}

model QualityReport {
  id          String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  batchId     String?    @map("batch_id") @db.Uuid
  reportType  ReportType @map("report_type")
  title       String     @db.VarChar(200)
  description String?
  dateRange   Json?      @map("date_range")
  filters     Json?
  reportData  Json       @map("report_data")
  generatedBy String     @map("generated_by") @db.Uuid
  isPublic    Boolean    @default(false) @map("is_public")
  createdAt   DateTime   @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt   DateTime   @updatedAt @map("updated_at") @db.Timestamp(6)
  batch       Batch?     @relation(fields: [batchId], references: [id])
  generator   User       @relation(fields: [generatedBy], references: [id], onDelete: Cascade)

  @@index([batchId])
  @@index([reportType])
  @@index([generatedBy])
  @@index([isPublic])
  @@index([createdAt])
  @@index([reportType, createdAt])
  @@map("quality_reports")
}

model SystemLog {
  id           String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId       String?  @map("user_id") @db.Uuid
  action       String   @db.VarChar(100)
  resourceType String?  @map("resource_type") @db.VarChar(50)
  resourceId   String?  @map("resource_id") @db.Uuid
  details      Json?
  ipAddress    String?  @map("ip_address") @db.Inet
  userAgent    String?  @map("user_agent")
  createdAt    DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  user         User?    @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([action])
  @@index([resourceType])
  @@index([resourceId])
  @@index([createdAt])
  @@index([userId, action])
  @@index([action, createdAt])
  @@map("system_logs")
}

model AIAnalysisRecord {
  id             String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId         String    @map("user_id") @db.Uuid
  cafeId         String    @map("cafe_id") @db.Uuid
  fileName       String    @map("file_name") @db.VarChar(255)
  imageUrl       String    @map("image_url")
  analysisResult String    @map("analysis_result")
  processingTime Int?      @map("processing_time")
  customPrompt   String?   @map("custom_prompt")
  status         String    @default("completed") @db.VarChar(20)
  createdAt      DateTime? @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  cafe           Cafe      @relation(fields: [cafeId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  user           User      @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([cafeId], map: "idx_ai_analysis_records_cafe_id")
  @@index([createdAt], map: "idx_ai_analysis_records_created_at")
  @@index([status], map: "idx_ai_analysis_records_status")
  @@index([userId], map: "idx_ai_analysis_records_user_id")
  @@map("ai_analysis_records")
}

model Notification {
  id        String           @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId    String           @map("user_id") @db.Uuid
  type      NotificationType
  title     String           @db.VarChar(200)
  content   String
  data      String?
  isRead    Boolean          @default(false) @map("is_read")
  createdAt DateTime         @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt DateTime         @updatedAt @map("updated_at") @db.Timestamp(6)
  cafe_id   String?          @db.Uuid
  cafes     Cafe?            @relation(fields: [cafe_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([type])
  @@index([isRead])
  @@index([createdAt])
  @@index([userId, isRead])
  @@index([userId, type])
  @@index([userId, createdAt])
  @@map("notifications")
}

model migrations {
  id          Int       @id @default(dbgenerated("nextval('_migrations_id_seq'::regclass)"))
  name        String    @unique @db.VarChar(255)
  executed_at DateTime? @default(now()) @db.Timestamp(6)

  @@map("_migrations")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model quality_tests {
  id                                     String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  cafe_id                                String    @db.Uuid
  product_id                             String    @db.Uuid
  tester_id                              String    @db.Uuid
  batch_number                           String    @db.VarChar(50)
  test_date                              DateTime  @default(dbgenerated("CURRENT_DATE")) @db.Date
  test_type                              String?   @default("出厂检测") @db.VarChar(30)
  overall_score                          Int?      @default(0)
  status                                 String?   @default("pending") @db.VarChar(20)
  microorganisms                         Json?     @default("{}")
  chemical                               Json?     @default("{}")
  physical                               Json?     @default("{}")
  sensory                                Json?     @default("{}")
  heavy_metals                           Json?     @default("{}")
  additives                              Json?     @default("{}")
  reviewer_id                            String?   @db.Uuid
  reviewed_at                            DateTime? @db.Timestamptz(6)
  notes                                  String?
  created_at                             DateTime? @default(now()) @db.Timestamptz(6)
  updated_at                             DateTime? @default(now()) @db.Timestamptz(6)
  cafes                                  Cafe      @relation(fields: [cafe_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  products                               Product   @relation(fields: [product_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  users_quality_tests_reviewer_idTousers User?     @relation("quality_tests_reviewer_idTousers", fields: [reviewer_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  users_quality_tests_tester_idTousers   User      @relation("quality_tests_tester_idTousers", fields: [tester_id], references: [id], onUpdate: NoAction)

  @@index([batch_number], map: "idx_quality_tests_batch_number")
  @@index([cafe_id], map: "idx_quality_tests_cafe_id")
  @@index([product_id], map: "idx_quality_tests_product_id")
  @@index([test_date], map: "idx_quality_tests_test_date")
  @@index([tester_id], map: "idx_quality_tests_tester_id")
}

enum UserRole {
  ADMIN
  MANAGER
  USER
  VIEWER

  @@map("user_role")
}

enum BatchStatus {
  PLANNING
  IN_PROGRESS
  FERMENTING
  FILTERING
  COMPLETED
  FAILED

  @@map("batch_status")
}

enum AnalysisStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED

  @@map("analysis_status")
}

enum ReportType {
  QUALITY_SUMMARY
  BATCH_ANALYSIS
  TREND_ANALYSIS
  CONTAMINATION_REPORT
  CUSTOM

  @@map("report_type")
}

enum NotificationType {
  BATCH_COMPLETED
  BATCH_FAILED
  QUALITY_ALERT
  RECIPE_UPDATED
  SYSTEM_MAINTENANCE
  USER_MENTION
  REPORT_READY

  @@map("notification_type")
}
