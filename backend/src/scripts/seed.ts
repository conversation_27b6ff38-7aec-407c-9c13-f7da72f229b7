#!/usr/bin/env ts-node

// =============================================================================
// Yoghurt AI QC - 数据库种子数据脚本
// =============================================================================

import { readFileSync } from 'fs'
import { join } from 'path'
import { Pool } from 'pg'
import { config } from '../config'
import { logger } from '../utils/logger'

// 数据库连接配置
const pool = new Pool({
  host: config.database.host,
  port: config.database.port,
  database: config.database.name,
  user: config.database.user,
  password: config.database.password,
  ssl: config.database.ssl ? { rejectUnauthorized: false } : false,
})

// 执行种子数据
const runSeed = async () => {
  try {
    logger.info('🌱 Starting database seeding...')
    
    // 读取种子数据文件
    const seedPath = join(__dirname, '../../prisma/seed.sql')
    const seedSQL = readFileSync(seedPath, 'utf8')
    
    // 开始事务
    const client = await pool.connect()
    
    try {
      await client.query('BEGIN')
      
      // 执行种子数据SQL
      await client.query(seedSQL)
      
      // 提交事务
      await client.query('COMMIT')
      
      logger.info('✅ Database seeding completed successfully')
    } catch (error) {
      // 回滚事务
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  } catch (error) {
    logger.error('❌ Database seeding failed:', error)
    throw error
  }
}

// 清空数据库（谨慎使用）
const clearDatabase = async () => {
  try {
    logger.warn('⚠️  Clearing database...')
    
    const client = await pool.connect()
    
    try {
      await client.query('BEGIN')
      
      // 禁用外键约束检查
      await client.query('SET session_replication_role = replica;')
      
      // 获取所有表名
      const tablesResult = await client.query(`
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename NOT LIKE '_migrations'
      `)
      
      // 清空所有表
      for (const row of tablesResult.rows) {
        const tableName = row.tablename
        await client.query(`TRUNCATE TABLE "${tableName}" RESTART IDENTITY CASCADE`)
        logger.info(`Cleared table: ${tableName}`)
      }
      
      // 重新启用外键约束检查
      await client.query('SET session_replication_role = DEFAULT;')
      
      await client.query('COMMIT')
      
      logger.info('✅ Database cleared successfully')
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  } catch (error) {
    logger.error('❌ Failed to clear database:', error)
    throw error
  }
}

// 重置数据库（清空 + 种子数据）
const resetDatabase = async () => {
  try {
    logger.info('🔄 Resetting database...')
    
    await clearDatabase()
    await runSeed()
    
    logger.info('✅ Database reset completed successfully')
  } catch (error) {
    logger.error('❌ Database reset failed:', error)
    throw error
  }
}

// 检查数据库状态
const checkDatabaseStatus = async () => {
  try {
    logger.info('📊 Database Status:')
    
    const client = await pool.connect()
    
    try {
      // 检查各表的记录数
      const tables = [
        'users',
        'recipes', 
        'batches',
        'sensory_assessments',
        'microscope_images',
        'ai_analyses',
        'quality_reports',
        'system_logs'
      ]
      
      for (const table of tables) {
        try {
          const result = await client.query(`SELECT COUNT(*) FROM ${table}`)
          const count = parseInt(result.rows[0].count)
          logger.info(`  ${table}: ${count} records`)
        } catch (error) {
          logger.warn(`  ${table}: table not found or error`)
        }
      }
    } finally {
      client.release()
    }
  } catch (error) {
    logger.error('Failed to check database status:', error)
    throw error
  }
}

// 创建测试用户
const createTestUsers = async () => {
  try {
    logger.info('👥 Creating additional test users...')
    
    const client = await pool.connect()
    
    try {
      await client.query('BEGIN')
      
      // 创建更多测试用户
      const testUsers = [
        {
          email: '<EMAIL>',
          name: '生产经理',
          role: 'USER'
        },
        {
          email: '<EMAIL>', 
          name: '质控专员',
          role: 'USER'
        },
        {
          email: '<EMAIL>',
          name: '实验室技术员',
          role: 'USER'
        }
      ]
      
      for (const user of testUsers) {
        await client.query(`
          INSERT INTO users (email, password_hash, name, role) 
          VALUES ($1, $2, $3, $4)
          ON CONFLICT (email) DO NOTHING
        `, [
          user.email,
          '$2b$12$LQv3c1yqBw2LeOzMpkDCW.vcgsANpvAF.RQcZPdAVpOcc9IuHOZFm', // password123
          user.name,
          user.role
        ])
        
        logger.info(`Created test user: ${user.email}`)
      }
      
      await client.query('COMMIT')
      
      logger.info('✅ Test users created successfully')
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  } catch (error) {
    logger.error('❌ Failed to create test users:', error)
    throw error
  }
}

// 创建示例数据
const createSampleData = async () => {
  try {
    logger.info('📝 Creating additional sample data...')
    
    const client = await pool.connect()
    
    try {
      await client.query('BEGIN')
      
      // 创建更多示例批次
      const today = new Date().toISOString().split('T')[0]
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      
      // 获取第一个配方和用户
      const recipeResult = await client.query('SELECT id FROM recipes LIMIT 1')
      const userResult = await client.query('SELECT id FROM users WHERE role = $1 LIMIT 1', ['USER'])
      
      if (recipeResult.rows.length > 0 && userResult.rows.length > 0) {
        const recipeId = recipeResult.rows[0].id
        const userId = userResult.rows[0].id
        
        // 创建今天的批次
        await client.query(`
          INSERT INTO batches (batch_number, recipe_id, user_id, status, production_date, quantity, notes)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
          ON CONFLICT (batch_number) DO NOTHING
        `, [
          `${today.replace(/-/g, '')}-003`,
          recipeId,
          userId,
          'IN_PROGRESS',
          today,
          1.2,
          '今日生产批次'
        ])
        
        // 创建昨天的批次
        await client.query(`
          INSERT INTO batches (batch_number, recipe_id, user_id, status, production_date, quantity, notes)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
          ON CONFLICT (batch_number) DO NOTHING
        `, [
          `${yesterday.replace(/-/g, '')}-001`,
          recipeId,
          userId,
          'COMPLETED',
          yesterday,
          1.0,
          '昨日生产批次'
        ])
        
        logger.info('Sample batches created')
      }
      
      await client.query('COMMIT')
      
      logger.info('✅ Sample data created successfully')
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  } catch (error) {
    logger.error('❌ Failed to create sample data:', error)
    throw error
  }
}

// 主函数
const main = async () => {
  const command = process.argv[2]
  
  try {
    switch (command) {
      case 'run':
      case undefined:
        await runSeed()
        break
      case 'clear':
        await clearDatabase()
        break
      case 'reset':
        await resetDatabase()
        break
      case 'status':
        await checkDatabaseStatus()
        break
      case 'test-users':
        await createTestUsers()
        break
      case 'sample-data':
        await createSampleData()
        break
      default:
        logger.info('Usage: npm run seed [run|clear|reset|status|test-users|sample-data]')
        logger.info('  run (default): Run seed data')
        logger.info('  clear: Clear all data from database')
        logger.info('  reset: Clear database and run seed data')
        logger.info('  status: Show database status')
        logger.info('  test-users: Create additional test users')
        logger.info('  sample-data: Create additional sample data')
        process.exit(1)
    }
  } catch (error) {
    logger.error('Seed script failed:', error)
    process.exit(1)
  } finally {
    await pool.end()
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

export { runSeed, clearDatabase, resetDatabase, checkDatabaseStatus }
