import { PrismaClient, NotificationType } from '@prisma/client'

const prisma = new PrismaClient()

async function seedNotifications() {
  try {
    console.log('开始创建示例通知数据...')

    // 获取第一个用户用于测试
    const firstUser = await prisma.user.findFirst()
    if (!firstUser) {
      console.log('没有找到用户，请先创建用户数据')
      return
    }

    console.log(`为用户 ${firstUser.email} 创建通知...`)

    // 创建示例通知
    const notifications = [
      {
        userId: firstUser.id,
        type: NotificationType.BATCH_COMPLETED,
        title: '批次 YG-2025-001 发酵完成',
        content: '经典酸奶配方批次 YG-2025-001 已完成发酵，质量评分 8.5/10，可以进入下一阶段处理。',
        data: JSON.stringify({
          batchId: 'batch-001',
          batchNumber: 'YG-2025-001',
          recipe: '经典酸奶配方',
          qualityScore: 8.5
        }),
        isRead: false
      },
      {
        userId: firstUser.id,
        type: NotificationType.QUALITY_ALERT,
        title: '质量预警：批次 YG-2025-002',
        content: '批次 YG-2025-002 的pH值检测结果异常，请及时检查发酵环境和菌种活性。',
        data: JSON.stringify({
          batchId: 'batch-002',
          batchNumber: 'YG-2025-002',
          alertType: 'pH异常',
          value: 3.8,
          expected: '4.0-4.4'
        }),
        isRead: false
      },
      {
        userId: firstUser.id,
        type: NotificationType.RECIPE_UPDATED,
        title: '配方更新：希腊酸奶配方 v2.1',
        content: '希腊酸奶配方已更新至v2.1版本，优化了发酵温度和时间参数，请查看详细变更。',
        data: JSON.stringify({
          recipeId: 'recipe-002',
          recipeName: '希腊酸奶配方',
          version: '2.1',
          changes: ['发酵温度调整', '发酵时间优化']
        }),
        isRead: true
      },
      {
        userId: firstUser.id,
        type: NotificationType.REPORT_READY,
        title: '月度质量报告已生成',
        content: '2025年1月份的质量分析报告已成功生成，包含31个批次的详细分析数据。',
        data: JSON.stringify({
          reportId: 'report-001',
          reportType: '月度质量报告',
          period: '2025-01',
          batchCount: 31
        }),
        isRead: false
      },
      {
        userId: firstUser.id,
        type: NotificationType.SYSTEM_MAINTENANCE,
        title: '系统维护通知',
        content: '系统将于今晚23:00-01:00进行例行维护，期间可能影响部分功能使用。',
        data: JSON.stringify({
          maintenanceStart: '2025-01-31T23:00:00Z',
          maintenanceEnd: '2025-02-01T01:00:00Z',
          affectedServices: ['AI分析', '报告生成']
        }),
        isRead: true
      }
    ]

    // 创建通知记录
    for (const notification of notifications) {
      await prisma.notification.create({
        data: {
          ...notification,
          createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000), // 过去7天内的随机时间
          updatedAt: new Date()
        }
      })
    }

    console.log(`成功创建 ${notifications.length} 条通知记录`)

    // 显示统计信息
    const unreadCount = notifications.filter(n => !n.isRead).length
    console.log(`其中未读通知: ${unreadCount} 条`)
    console.log('通知数据创建完成!')

  } catch (error) {
    console.error('创建通知数据失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行脚本
if (require.main === module) {
  seedNotifications()
}

export default seedNotifications