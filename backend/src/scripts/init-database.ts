#!/usr/bin/env ts-node

// =============================================================================
// Yoghurt AI QC - 数据库初始化脚本
// =============================================================================

import { logger } from '../utils/logger'
import { runMigrations } from './migrate'
import { runSeed } from './seed'
import { connectPrisma, checkPrismaHealth } from '../lib/prisma'

// 数据库初始化主函数
const initializeDatabase = async () => {
  try {
    logger.info('🚀 Starting database initialization...')
    
    // 1. 检查数据库连接
    logger.info('📡 Checking database connection...')
    await connectPrisma()
    
    const isHealthy = await checkPrismaHealth()
    if (!isHealthy) {
      throw new Error('Database health check failed')
    }
    logger.info('✅ Database connection established')
    
    // 2. 运行数据库迁移
    logger.info('🔄 Running database migrations...')
    await runMigrations()
    logger.info('✅ Database migrations completed')
    
    // 3. 生成 Prisma 客户端
    logger.info('⚙️  Generating Prisma client...')
    const { exec } = require('child_process')
    await new Promise((resolve, reject) => {
      exec('npx prisma generate', (error: any, stdout: any, stderr: any) => {
        if (error) {
          reject(error)
        } else {
          resolve(stdout)
        }
      })
    })
    logger.info('✅ Prisma client generated')
    
    // 4. 运行种子数据
    logger.info('🌱 Running seed data...')
    await runSeed()
    logger.info('✅ Seed data completed')
    
    logger.info('🎉 Database initialization completed successfully!')
    
    // 显示连接信息
    logger.info('\n📊 Database Information:')
    logger.info(`  Host: ${process.env.DB_HOST || 'localhost'}`)
    logger.info(`  Port: ${process.env.DB_PORT || '6432'}`)
    logger.info(`  Database: ${process.env.DB_NAME || 'yoghurt_qc'}`)
    logger.info(`  User: ${process.env.DB_USER || 'yoghurt_user'}`)
    
    logger.info('\n🔑 Default Users Created:')
    logger.info('  Admin: <EMAIL> (password: password123)')
    logger.info('  User: <EMAIL> (password: password123)')
    logger.info('  Alex: <EMAIL> (password: password123)')
    logger.info('  Viewer: <EMAIL> (password: password123)')
    
    logger.info('\n🛠️  Useful Commands:')
    logger.info('  npm run db:studio - Open Prisma Studio')
    logger.info('  npm run db:seed:status - Check database status')
    logger.info('  npm run migrate:status - Check migration status')
    
  } catch (error) {
    logger.error('❌ Database initialization failed:', error)
    process.exit(1)
  }
}

// 重置数据库
const resetDatabase = async () => {
  try {
    logger.warn('⚠️  Resetting database...')
    
    // 确认操作
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Cannot reset database in production environment')
    }
    
    const { clearDatabase } = require('./seed')
    
    // 清空数据库
    await clearDatabase()
    
    // 重新初始化
    await initializeDatabase()
    
    logger.info('✅ Database reset completed')
  } catch (error) {
    logger.error('❌ Database reset failed:', error)
    process.exit(1)
  }
}

// 检查数据库状态
const checkDatabaseStatus = async () => {
  try {
    logger.info('📊 Checking database status...')
    
    // 检查连接
    await connectPrisma()
    const isHealthy = await checkPrismaHealth()
    
    if (isHealthy) {
      logger.info('✅ Database is healthy')
    } else {
      logger.error('❌ Database is not healthy')
      process.exit(1)
    }
    
    // 检查迁移状态
    const { showMigrationStatus } = require('./migrate')
    await showMigrationStatus()
    
    // 检查数据状态
    const { checkDatabaseStatus: checkSeedStatus } = require('./seed')
    await checkSeedStatus()
    
  } catch (error) {
    logger.error('❌ Database status check failed:', error)
    process.exit(1)
  }
}

// 备份数据库
const backupDatabase = async () => {
  try {
    logger.info('💾 Creating database backup...')
    
    const { exec } = require('child_process')
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const backupFile = `backup-${timestamp}.sql`
    
    const dbHost = process.env.DB_HOST || 'localhost'
    const dbPort = process.env.DB_PORT || '6432'
    const dbName = process.env.DB_NAME || 'yoghurt_qc'
    const dbUser = process.env.DB_USER || 'yoghurt_user'
    
    const command = `pg_dump -h ${dbHost} -p ${dbPort} -U ${dbUser} -d ${dbName} -f ${backupFile}`
    
    await new Promise((resolve, reject) => {
      exec(command, (error: any, stdout: any, stderr: any) => {
        if (error) {
          reject(error)
        } else {
          resolve(stdout)
        }
      })
    })
    
    logger.info(`✅ Database backup created: ${backupFile}`)
  } catch (error) {
    logger.error('❌ Database backup failed:', error)
    throw error
  }
}

// 恢复数据库
const restoreDatabase = async (backupFile: string) => {
  try {
    logger.info(`📥 Restoring database from: ${backupFile}`)
    
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Cannot restore database in production environment without confirmation')
    }
    
    const { exec } = require('child_process')
    
    const dbHost = process.env.DB_HOST || 'localhost'
    const dbPort = process.env.DB_PORT || '6432'
    const dbName = process.env.DB_NAME || 'yoghurt_qc'
    const dbUser = process.env.DB_USER || 'yoghurt_user'
    
    const command = `psql -h ${dbHost} -p ${dbPort} -U ${dbUser} -d ${dbName} -f ${backupFile}`
    
    await new Promise((resolve, reject) => {
      exec(command, (error: any, stdout: any, stderr: any) => {
        if (error) {
          reject(error)
        } else {
          resolve(stdout)
        }
      })
    })
    
    logger.info('✅ Database restore completed')
  } catch (error) {
    logger.error('❌ Database restore failed:', error)
    throw error
  }
}

// 主函数
const main = async () => {
  const command = process.argv[2]
  const arg = process.argv[3]
  
  try {
    switch (command) {
      case 'init':
      case undefined:
        await initializeDatabase()
        break
      case 'reset':
        await resetDatabase()
        break
      case 'status':
        await checkDatabaseStatus()
        break
      case 'backup':
        await backupDatabase()
        break
      case 'restore':
        if (!arg) {
          logger.error('Please provide backup file path')
          process.exit(1)
        }
        await restoreDatabase(arg)
        break
      default:
        logger.info('Usage: npm run db:init [init|reset|status|backup|restore <file>]')
        logger.info('  init (default): Initialize database with migrations and seed data')
        logger.info('  reset: Reset database (clear + init)')
        logger.info('  status: Check database status')
        logger.info('  backup: Create database backup')
        logger.info('  restore <file>: Restore database from backup file')
        process.exit(1)
    }
  } catch (error) {
    logger.error('Database initialization script failed:', error)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

export { 
  initializeDatabase, 
  resetDatabase, 
  checkDatabaseStatus, 
  backupDatabase, 
  restoreDatabase 
}
