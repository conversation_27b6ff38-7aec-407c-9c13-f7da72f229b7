#!/usr/bin/env ts-node

// =============================================================================
// Yoghurt AI QC - 数据库迁移脚本
// =============================================================================

import { readFileSync } from 'fs'
import { join } from 'path'
import { Pool } from 'pg'
import { config } from '../config'
import { logger } from '../utils/logger'

// 数据库连接配置
const pool = new Pool({
  host: config.database.host,
  port: config.database.port,
  database: config.database.name,
  user: config.database.user,
  password: config.database.password,
  ssl: config.database.ssl ? { rejectUnauthorized: false } : false,
})

// 迁移文件列表
const migrations = [
  '001_initial_schema.sql',
]

// 创建迁移记录表
const createMigrationsTable = async () => {
  const query = `
    CREATE TABLE IF NOT EXISTS _migrations (
      id SERIAL PRIMARY KEY,
      name VARCHAR(255) UNIQUE NOT NULL,
      executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `
  
  try {
    await pool.query(query)
    logger.info('Migrations table created or already exists')
  } catch (error) {
    logger.error('Failed to create migrations table:', error)
    throw error
  }
}

// 检查迁移是否已执行
const isMigrationExecuted = async (migrationName: string): Promise<boolean> => {
  const query = 'SELECT COUNT(*) FROM _migrations WHERE name = $1'
  
  try {
    const result = await pool.query(query, [migrationName])
    return parseInt(result.rows[0].count) > 0
  } catch (error) {
    logger.error(`Failed to check migration ${migrationName}:`, error)
    throw error
  }
}

// 记录迁移执行
const recordMigration = async (migrationName: string) => {
  const query = 'INSERT INTO _migrations (name) VALUES ($1)'
  
  try {
    await pool.query(query, [migrationName])
    logger.info(`Migration ${migrationName} recorded`)
  } catch (error) {
    logger.error(`Failed to record migration ${migrationName}:`, error)
    throw error
  }
}

// 执行单个迁移文件
const executeMigration = async (migrationName: string) => {
  const migrationPath = join(__dirname, '../../prisma/migrations', migrationName)
  
  try {
    // 读取迁移文件
    const migrationSQL = readFileSync(migrationPath, 'utf8')
    
    // 开始事务
    const client = await pool.connect()
    
    try {
      await client.query('BEGIN')
      
      // 执行迁移SQL
      await client.query(migrationSQL)
      
      // 记录迁移
      await client.query('INSERT INTO _migrations (name) VALUES ($1)', [migrationName])
      
      // 提交事务
      await client.query('COMMIT')
      
      logger.info(`✅ Migration ${migrationName} executed successfully`)
    } catch (error) {
      // 回滚事务
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  } catch (error) {
    logger.error(`❌ Failed to execute migration ${migrationName}:`, error)
    throw error
  }
}

// 执行所有待执行的迁移
const runMigrations = async () => {
  try {
    logger.info('🚀 Starting database migrations...')
    
    // 创建迁移记录表
    await createMigrationsTable()
    
    // 执行每个迁移
    for (const migration of migrations) {
      const isExecuted = await isMigrationExecuted(migration)
      
      if (isExecuted) {
        logger.info(`⏭️  Migration ${migration} already executed, skipping`)
        continue
      }
      
      logger.info(`🔄 Executing migration ${migration}...`)
      await executeMigration(migration)
    }
    
    logger.info('✅ All migrations completed successfully')
  } catch (error) {
    logger.error('❌ Migration failed:', error)
    process.exit(1)
  }
}

// 回滚最后一个迁移（谨慎使用）
const rollbackLastMigration = async () => {
  try {
    logger.warn('⚠️  Rolling back last migration...')
    
    // 获取最后执行的迁移
    const query = 'SELECT name FROM _migrations ORDER BY executed_at DESC LIMIT 1'
    const result = await pool.query(query)
    
    if (result.rows.length === 0) {
      logger.info('No migrations to rollback')
      return
    }
    
    const lastMigration = result.rows[0].name
    logger.warn(`Rolling back migration: ${lastMigration}`)
    
    // 这里需要手动处理回滚逻辑
    // 由于SQL迁移通常不可逆，建议备份数据库
    logger.warn('⚠️  Manual rollback required. Please restore from backup.')
    
    // 删除迁移记录
    await pool.query('DELETE FROM _migrations WHERE name = $1', [lastMigration])
    
    logger.info('Migration record removed')
  } catch (error) {
    logger.error('Rollback failed:', error)
    throw error
  }
}

// 显示迁移状态
const showMigrationStatus = async () => {
  try {
    logger.info('📊 Migration Status:')
    
    // 获取已执行的迁移
    const executedQuery = 'SELECT name, executed_at FROM _migrations ORDER BY executed_at'
    const executedResult = await pool.query(executedQuery)
    
    logger.info('\n✅ Executed migrations:')
    if (executedResult.rows.length === 0) {
      logger.info('  None')
    } else {
      executedResult.rows.forEach(row => {
        logger.info(`  - ${row.name} (${row.executed_at})`)
      })
    }
    
    // 检查待执行的迁移
    const executedNames = executedResult.rows.map(row => row.name)
    const pendingMigrations = migrations.filter(name => !executedNames.includes(name))
    
    logger.info('\n⏳ Pending migrations:')
    if (pendingMigrations.length === 0) {
      logger.info('  None')
    } else {
      pendingMigrations.forEach(name => {
        logger.info(`  - ${name}`)
      })
    }
  } catch (error) {
    logger.error('Failed to show migration status:', error)
    throw error
  }
}

// 主函数
const main = async () => {
  const command = process.argv[2]
  
  try {
    switch (command) {
      case 'up':
      case undefined:
        await runMigrations()
        break
      case 'status':
        await showMigrationStatus()
        break
      case 'rollback':
        await rollbackLastMigration()
        break
      default:
        logger.info('Usage: npm run migrate [up|status|rollback]')
        logger.info('  up (default): Run pending migrations')
        logger.info('  status: Show migration status')
        logger.info('  rollback: Rollback last migration')
        process.exit(1)
    }
  } catch (error) {
    logger.error('Migration script failed:', error)
    process.exit(1)
  } finally {
    await pool.end()
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

export { runMigrations, showMigrationStatus, rollbackLastMigration }
