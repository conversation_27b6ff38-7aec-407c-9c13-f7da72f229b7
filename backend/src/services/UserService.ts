// =============================================================================
// Yoghurt AI QC - 用户服务
// =============================================================================

import bcrypt from 'bcryptjs'
import prisma from '../lib/prisma'
import { BaseService } from './BaseService'
import { User, UserRole } from '@prisma/client'
import { 
  CreateUserRequest, 
  UpdateUserRequest, 
  UserQueryParams
} from '../types/database'
import { logger } from '../utils/logger'

export class UserService extends BaseService<any, CreateUserRequest, UpdateUserRequest, UserQueryParams> {
  protected modelName = 'User'
  protected model = prisma.user

  // 获取默认包含的关联数据
  protected getDefaultInclude() {
    return {
      _count: {
        select: {
          recipes: true,
          batches: true,
          sensoryAssessments: true,
          qualityReports: true,
        },
      },
    }
  }

  // 构建查询条件
  protected buildWhereClause(filters: Partial<UserQueryParams>) {
    const where: any = {}

    if (filters.email) {
      where.email = {
        contains: filters.email,
        mode: 'insensitive',
      }
    }

    if (filters.name) {
      where.name = {
        contains: filters.name,
        mode: 'insensitive',
      }
    }

    if (filters.role) {
      where.role = filters.role
    }

    if (filters.isActive !== undefined) {
      where.isActive = filters.isActive
    }

    return where
  }

  // 构建排序条件
  protected buildOrderByClause(sortBy?: string, sortOrder: 'asc' | 'desc' = 'desc') {
    const validSortFields = ['name', 'email', 'role', 'createdAt', 'updatedAt']
    const field = validSortFields.includes(sortBy || '') ? sortBy : 'createdAt'
    
    return {
      [field as string]: sortOrder,
    }
  }

  // 创建用户
  async create(data: CreateUserRequest): Promise<any> {
    try {
      // 检查邮箱是否已存在
      const existingUser = await this.model.findUnique({
        where: { email: data.email },
      })

      if (existingUser) {
        throw new Error('邮箱已被使用')
      }

      // 加密密码
      const saltRounds = 12
      const passwordHash = await bcrypt.hash(data.password, saltRounds)

      const createData = {
        email: data.email,
        passwordHash,
        name: data.name,
        role: (data.role as UserRole) || 'USER',
        isActive: data.isActive !== undefined ? data.isActive : true,
      }

      const result = await this.model.create({
        data: createData,
        include: this.getDefaultInclude(),
      })

      logger.info('User created', { id: result.id, email: result.email })
      return result as any
    } catch (error) {
      logger.error('Failed to create user:', error)
      throw error
    }
  }

  // 根据邮箱查找用户
  async findByEmail(email: string): Promise<any> {
    try {
      const result = await this.model.findUnique({
        where: { email },
        include: this.getDefaultInclude(),
      })

      return result as any
    } catch (error) {
      logger.error('Failed to find user by email:', error)
      throw error
    }
  }

  // 根据ID查找用户
  async findById(id: string): Promise<any> {
    try {
      const result = await this.model.findUnique({
        where: { id },
        include: this.getDefaultInclude(),
      })

      return result as any
    } catch (error) {
      logger.error('Failed to find user by ID:', error)
      throw error
    }
  }

  // 更新用户
  async updateById(id: string, data: UpdateUserRequest): Promise<any> {
    try {
      const updateData: any = { ...data }

      // 如果要更新密码，需要加密
      if (data.password) {
        const saltRounds = 12
        updateData.passwordHash = await bcrypt.hash(data.password, saltRounds)
        delete updateData.password
      }

      const result = await this.model.update({
        where: { id },
        data: updateData,
        include: this.getDefaultInclude(),
      })

      logger.info('User updated', { id: result.id, email: result.email })
      return result as any
    } catch (error) {
      logger.error('Failed to update user:', error)
      throw error
    }
  }

  // 停用用户
  async deactivateById(id: string): Promise<any> {
    try {
      const result = await this.model.update({
        where: { id },
        data: { isActive: false },
        include: this.getDefaultInclude(),
      })

      logger.info('User deactivated', { id: result.id, email: result.email })
      return result as any
    } catch (error) {
      logger.error('Failed to deactivate user:', error)
      throw error
    }
  }

  // 验证密码
  async validatePassword(email: string, password: string): Promise<any> {
    try {
      const user = await this.model.findUnique({
        where: { email },
      })

      if (!user || !user.isActive) {
        return null
      }

      const isValid = await bcrypt.compare(password, user.passwordHash)
      if (!isValid) {
        return null
      }

      // 返回用户信息（不包含密码）
      return {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        isActive: user.isActive,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      }
    } catch (error) {
      logger.error('Failed to validate password:', error)
      throw error
    }
  }

  // 更改用户角色
  async changeRole(id: string, role: UserRole): Promise<any> {
    try {
      const result = await this.model.update({
        where: { id },
        data: { role },
        include: this.getDefaultInclude(),
      })

      logger.info('User role changed', { id: result.id, role: result.role })
      return result as any
    } catch (error) {
      logger.error('Failed to change user role:', error)
      throw error
    }
  }

  // 获取用户统计信息
  async getUserStatistics(userId: string) {
    try {
      const stats = await prisma.$queryRaw`
        SELECT 
          COUNT(CASE WHEN r.id IS NOT NULL THEN 1 END)::int as total_recipes,
          COUNT(CASE WHEN b.id IS NOT NULL THEN 1 END)::int as total_batches,
          COUNT(CASE WHEN sa.id IS NOT NULL THEN 1 END)::int as total_assessments,
          COUNT(CASE WHEN b.status = 'COMPLETED' THEN 1 END)::int as completed_batches,
          COALESCE(AVG(sa.overall_score), 0)::float as avg_quality_score
        FROM users u
        LEFT JOIN recipes r ON u.id = r.user_id AND r.is_active = true
        LEFT JOIN batches b ON u.id = b.user_id
        LEFT JOIN sensory_assessments sa ON u.id = sa.assessor_id
        WHERE u.id = ${userId}::uuid
        GROUP BY u.id
      `

      return Array.isArray(stats) ? stats[0] : stats
    } catch (error: any) {
      logger.error(`Failed to get user statistics for user ${userId}:`, error)
      throw new Error('获取用户统计信息失败')
    }
  }

  // 获取所有活跃用户
  async getAllActiveUsers(): Promise<any[]> {
    try {
      const result = await this.model.findMany({
        where: { isActive: true },
        include: this.getDefaultInclude(),
        orderBy: { createdAt: 'desc' },
      })

      return result as any
    } catch (error) {
      logger.error('Failed to get all active users:', error)
      throw error
    }
  }

  // 软删除用户（实际上是停用）
  async softDelete(id: string): Promise<any> {
    return this.deactivateById(id)
  }

  // 检查用户权限
  async checkPermission(userId: string, permission: string): Promise<boolean> {
    try {
      const user = await this.findById(userId)
      if (!user || !user.isActive) {
        return false
      }

      // 基于角色的权限检查
      switch (user.role) {
        case 'ADMIN':
          return true // 管理员拥有所有权限
        case 'USER':
          // 用户权限逻辑
          return ['READ_RECIPE', 'CREATE_BATCH', 'VIEW_ANALYSIS'].includes(permission)
        case 'VIEWER':
          // 只读权限
          return ['READ_RECIPE', 'VIEW_ANALYSIS'].includes(permission)
        default:
          return false
      }
    } catch (error) {
      logger.error('Failed to check user permission:', error)
      return false
    }
  }
}

// 导出服务实例
export const userService = new UserService()