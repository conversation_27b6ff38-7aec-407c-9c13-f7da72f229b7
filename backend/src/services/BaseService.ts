// =============================================================================
// Yoghurt AI QC - 基础服务类
// =============================================================================

import prisma from '../lib/prisma'
import { logger } from '../utils/logger'
import { PaginatedResponse, PaginationParams, SortParams } from '../types/database'

export abstract class BaseService<T, CreateT, UpdateT, QueryT> {
  protected abstract modelName: string
  protected abstract model: any

  // 创建记录
  async create(data: CreateT, userId?: string): Promise<T> {
    try {
      const createData = userId ? { ...data, userId } : data
      const result = await this.model.create({
        data: createData,
        include: this.getDefaultInclude(),
      })

      logger.info(`${this.modelName} created`, { id: result.id, userId })
      return result
    } catch (error) {
      logger.error(`Failed to create ${this.modelName}:`, error)
      throw error
    }
  }

  // 根据ID获取记录
  async findById(id: string, include?: any): Promise<T | null> {
    try {
      const result = await this.model.findUnique({
        where: { id },
        include: include || this.getDefaultInclude(),
      })

      return result
    } catch (error) {
      logger.error(`Failed to find ${this.modelName} by id:`, error)
      throw error
    }
  }

  // 更新记录
  async update(id: string, data: UpdateT): Promise<T> {
    try {
      const result = await this.model.update({
        where: { id },
        data,
        include: this.getDefaultInclude(),
      })

      logger.info(`${this.modelName} updated`, { id, data })
      return result
    } catch (error) {
      logger.error(`Failed to update ${this.modelName}:`, error)
      throw error
    }
  }

  // 删除记录
  async delete(id: string): Promise<void> {
    try {
      await this.model.delete({
        where: { id },
      })

      logger.info(`${this.modelName} deleted`, { id })
    } catch (error) {
      logger.error(`Failed to delete ${this.modelName}:`, error)
      throw error
    }
  }

  // 软删除（如果模型支持）
  async softDelete(id: string): Promise<T> {
    try {
      const result = await this.model.update({
        where: { id },
        data: { isActive: false },
        include: this.getDefaultInclude(),
      })

      logger.info(`${this.modelName} soft deleted`, { id })
      return result
    } catch (error) {
      logger.error(`Failed to soft delete ${this.modelName}:`, error)
      throw error
    }
  }

  // 分页查询
  async findMany(params: QueryT & PaginationParams & SortParams): Promise<PaginatedResponse<T>> {
    try {
      const {
        page = 1,
        pageSize = 10,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        ...filters
      } = params

      const skip = (page - 1) * pageSize
      const take = pageSize

      // 构建查询条件
      const where = this.buildWhereClause(filters as any)
      
      // 构建排序条件
      const orderBy = this.buildOrderByClause(sortBy, sortOrder)

      const [data, total] = await Promise.all([
        this.model.findMany({
          where,
          orderBy,
          include: this.getDefaultInclude(),
          skip,
          take,
        }),
        this.model.count({ where }),
      ])

      return {
        data,
        pagination: {
          current: page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize),
        },
      }
    } catch (error) {
      logger.error(`Failed to find ${this.modelName} records:`, error)
      throw error
    }
  }

  // 查询所有记录（不分页）
  async findAll(filters?: Partial<QueryT>): Promise<T[]> {
    try {
      const where = filters ? this.buildWhereClause(filters) : {}
      
      const result = await this.model.findMany({
        where,
        include: this.getDefaultInclude(),
        orderBy: { createdAt: 'desc' },
      })

      return result
    } catch (error) {
      logger.error(`Failed to find all ${this.modelName} records:`, error)
      throw error
    }
  }

  // 统计记录数
  async count(filters?: Partial<QueryT>): Promise<number> {
    try {
      const where = filters ? this.buildWhereClause(filters) : {}
      
      const result = await this.model.count({ where })
      return result
    } catch (error) {
      logger.error(`Failed to count ${this.modelName} records:`, error)
      throw error
    }
  }

  // 检查记录是否存在
  async exists(id: string): Promise<boolean> {
    try {
      const result = await this.model.findUnique({
        where: { id },
        select: { id: true },
      })

      return !!result
    } catch (error) {
      logger.error(`Failed to check if ${this.modelName} exists:`, error)
      throw error
    }
  }

  // 批量创建
  async createMany(data: CreateT[]): Promise<{ count: number }> {
    try {
      const result = await this.model.createMany({
        data,
        skipDuplicates: true,
      })

      logger.info(`${this.modelName} batch created`, { count: result.count })
      return result
    } catch (error) {
      logger.error(`Failed to batch create ${this.modelName}:`, error)
      throw error
    }
  }

  // 批量更新
  async updateMany(filters: Partial<QueryT>, data: Partial<UpdateT>): Promise<{ count: number }> {
    try {
      const where = this.buildWhereClause(filters)
      
      const result = await this.model.updateMany({
        where,
        data,
      })

      logger.info(`${this.modelName} batch updated`, { count: result.count })
      return result
    } catch (error) {
      logger.error(`Failed to batch update ${this.modelName}:`, error)
      throw error
    }
  }

  // 批量删除
  async deleteMany(filters: Partial<QueryT>): Promise<{ count: number }> {
    try {
      const where = this.buildWhereClause(filters)
      
      const result = await this.model.deleteMany({ where })

      logger.info(`${this.modelName} batch deleted`, { count: result.count })
      return result
    } catch (error) {
      logger.error(`Failed to batch delete ${this.modelName}:`, error)
      throw error
    }
  }

  // 抽象方法：子类需要实现
  protected abstract getDefaultInclude(): any
  protected abstract buildWhereClause(filters: Partial<QueryT>): any
  protected abstract buildOrderByClause(sortBy: string, sortOrder: 'asc' | 'desc'): any

  // 通用的日期范围过滤器
  protected buildDateRangeFilter(dateRange?: [Date, Date], field = 'createdAt') {
    if (!dateRange || dateRange.length !== 2) {
      return {}
    }

    return {
      [field]: {
        gte: dateRange[0],
        lte: dateRange[1],
      },
    }
  }

  // 通用的搜索过滤器
  protected buildSearchFilter(search?: string, fields: string[] = ['name']) {
    if (!search) {
      return {}
    }

    if (fields.length === 1) {
      return {
        [fields[0]]: {
          contains: search,
          mode: 'insensitive',
        },
      }
    }

    return {
      OR: fields.map(field => ({
        [field]: {
          contains: search,
          mode: 'insensitive',
        },
      })),
    }
  }

  // 事务支持
  async withTransaction<R>(callback: (tx: any) => Promise<R>): Promise<R> {
    return await prisma.$transaction(callback)
  }
}
