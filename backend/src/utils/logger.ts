import winston from 'winston'
import DailyRotateFile from 'winston-daily-rotate-file'
import { config } from '../config'
import fs from 'fs'
import path from 'path'

// 确保日志目录存在
const logDir = path.dirname(config.logging.file)
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true })
}

// 定义日志级别
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
}

// 定义日志颜色
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
}

// 告诉 winston 使用这些颜色
winston.addColors(colors)

// 定义日志格式
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`,
  ),
)

// 定义传输方式
const transports: winston.transport[] = [
  // 控制台输出
  new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  })
]

// 在生产环境中添加文件日志
if (config.nodeEnv === 'production') {
  transports.push(
    // 错误日志文件
    new DailyRotateFile({
      filename: 'logs/error-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      level: 'error',
      handleExceptions: true,
      json: false,
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '14d'
    }) as winston.transport,

    // 所有日志文件
    new DailyRotateFile({
      filename: 'logs/combined-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      handleExceptions: true,
      json: false,
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '14d'
    }) as winston.transport
  )
}

// 创建 logger 实例
export const logger = winston.createLogger({
  level: config.logging.level,
  levels,
  format,
  transports,
  exitOnError: false,
})

// 开发环境额外配置
if (config.nodeEnv === 'development') {
  logger.level = 'debug'
}

// 创建流对象供 Morgan 使用
export const loggerStream = {
  write: (message: string) => {
    logger.info(message.trim())
  }
}

export default logger
