import { Request, Response, NextFunction } from 'express'
import { logger } from '../utils/logger'
import { config } from '../config'

// 自定义错误类
export class AppError extends Error {
  public statusCode: number
  public isOperational: boolean

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message)
    this.statusCode = statusCode
    this.isOperational = isOperational

    Error.captureStackTrace(this, this.constructor)
  }
}

// 错误处理中间件
export const errorHandler = (
  error: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let statusCode = 500
  let message = 'Internal Server Error'
  let isOperational = false

  // 如果是自定义错误
  if (error instanceof AppError) {
    statusCode = error.statusCode
    message = error.message
    isOperational = error.isOperational
  }

  // 处理特定类型的错误
  if (error.name === 'ValidationError') {
    statusCode = 400
    message = 'Validation Error'
    isOperational = true
  }

  if (error.name === 'CastError') {
    statusCode = 400
    message = 'Invalid ID format'
    isOperational = true
  }

  if (error.name === 'JsonWebTokenError') {
    statusCode = 401
    message = 'Invalid token'
    isOperational = true
  }

  if (error.name === 'TokenExpiredError') {
    statusCode = 401
    message = 'Token expired'
    isOperational = true
  }

  // PostgreSQL 错误处理
  if (error.name === 'QueryFailedError' || (error as any).code) {
    const pgError = error as any
    switch (pgError.code) {
      case '23505': // 唯一约束违反
        statusCode = 409
        message = 'Resource already exists'
        isOperational = true
        break
      case '23503': // 外键约束违反
        statusCode = 400
        message = 'Referenced resource does not exist'
        isOperational = true
        break
      case '23502': // 非空约束违反
        statusCode = 400
        message = 'Required field is missing'
        isOperational = true
        break
      default:
        statusCode = 500
        message = 'Database error'
        isOperational = false
    }
  }

  // 记录错误
  if (!isOperational || statusCode >= 500) {
    logger.error('Error occurred:', {
      message: error.message,
      stack: error.stack,
      statusCode,
      url: req.url,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    })
  } else {
    logger.warn('Operational error:', {
      message: error.message,
      statusCode,
      url: req.url,
      method: req.method,
    })
  }

  // 构建错误响应
  const errorResponse: any = {
    success: false,
    error: {
      message,
      statusCode,
      timestamp: new Date().toISOString(),
    },
  }

  // 在开发环境中包含堆栈跟踪
  if (config.nodeEnv === 'development') {
    errorResponse.error.stack = error.stack
    errorResponse.error.details = error.message
  }

  // 发送错误响应
  res.status(statusCode).json(errorResponse)
}

// 异步错误处理包装器
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}

// 常用错误创建函数
export const createError = {
  badRequest: (message: string = 'Bad Request') => new AppError(message, 400),
  unauthorized: (message: string = 'Unauthorized') => new AppError(message, 401),
  forbidden: (message: string = 'Forbidden') => new AppError(message, 403),
  notFound: (message: string = 'Not Found') => new AppError(message, 404),
  conflict: (message: string = 'Conflict') => new AppError(message, 409),
  unprocessableEntity: (message: string = 'Unprocessable Entity') => new AppError(message, 422),
  tooManyRequests: (message: string = 'Too Many Requests') => new AppError(message, 429),
  internalServerError: (message: string = 'Internal Server Error') => new AppError(message, 500),
  notImplemented: (message: string = 'Not Implemented') => new AppError(message, 501),
  serviceUnavailable: (message: string = 'Service Unavailable') => new AppError(message, 503),
}

export default errorHandler
