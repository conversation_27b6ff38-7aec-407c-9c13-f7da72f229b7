import { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'
import { config } from '../config'
import { AppError } from './errorHandler'
import { logger } from '../utils/logger'

// 扩展 Request 接口以包含用户信息
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string
        email: string
        username: string
        role: string
        cafeId: string | null
        iat?: number
        exp?: number
      }
    }
  }
}

// JWT 载荷接口
interface JwtPayload {
  id: string
  email: string
  username: string
  role: string
  cafeId: string | null
  iat?: number
  exp?: number
}

// 认证中间件
export const authMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // 从请求头获取 token
    const authHeader = req.headers.authorization
    
    if (!authHeader) {
      throw new AppError('Access token is required', 401)
    }

    // 检查 token 格式
    if (!authHeader.startsWith('Bearer ')) {
      throw new AppError('Invalid token format', 401)
    }

    const token = authHeader.substring(7) // 移除 'Bearer ' 前缀

    if (!token) {
      throw new AppError('Access token is required', 401)
    }

    // 验证 token
    const decoded = jwt.verify(token, config.jwt.secret) as JwtPayload

    // 将用户信息添加到请求对象
    req.user = {
      id: decoded.id,
      email: decoded.email,
      username: decoded.username,
      role: decoded.role,
      cafeId: decoded.cafeId,
      iat: decoded.iat,
      exp: decoded.exp,
    }

    logger.debug(`User authenticated: ${decoded.email}`)
    next()
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      next(new AppError('Invalid token', 401))
    } else if (error instanceof jwt.TokenExpiredError) {
      next(new AppError('Token expired', 401))
    } else {
      next(error)
    }
  }
}

// 角色检查中间件
export const requireRole = (roles: string | string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      return next(new AppError('Authentication required', 401))
    }

    const allowedRoles = Array.isArray(roles) ? roles : [roles]
    
    if (!allowedRoles.includes(req.user.role)) {
      return next(new AppError('Insufficient permissions', 403))
    }

    next()
  }
}

// 管理员权限检查
export const requireAdmin = requireRole('ADMIN')

// 用户权限检查（管理员或普通用户）
export const requireUser = requireRole(['ADMIN', 'USER'])

// 管理级权限检查（ADMIN和MANAGER）
export const requireManagerLevel = (req: Request, _res: Response, next: NextFunction): void => {
  if (!req.user) {
    return next(new AppError('Authentication required', 401))
  }

  // ADMIN和MANAGER都有管理权限
  if (req.user.role === 'ADMIN' || req.user.role === 'MANAGER') {
    return next()
  }

  return next(new AppError('需要管理员权限才能执行此操作', 403))
}

// 可选认证中间件（不强制要求认证）
export const optionalAuth = async (
  req: Request,
  _res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      
      if (token) {
        const decoded = jwt.verify(token, config.jwt.secret) as JwtPayload
        req.user = {
          id: decoded.id,
          email: decoded.email,
          username: decoded.username,
          role: decoded.role,
          cafeId: decoded.cafeId,
          iat: decoded.iat,
          exp: decoded.exp,
        }
      }
    }
    
    next()
  } catch (error) {
    // 在可选认证中，忽略 token 错误
    next()
  }
}

// 生成 JWT token
export const generateToken = (payload: { id: string; email: string; role: string }): string => {
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.expiresIn,
  } as jwt.SignOptions)
}

// 生成刷新 token
export const generateRefreshToken = (payload: { id: string; email: string; role: string }): string => {
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.refreshExpiresIn,
  } as jwt.SignOptions)
}

// 验证 token
export const verifyToken = (token: string): JwtPayload => {
  return jwt.verify(token, config.jwt.secret) as JwtPayload
}

export default authMiddleware
