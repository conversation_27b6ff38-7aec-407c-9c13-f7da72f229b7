import { Router, Request, Response } from 'express'
import multer from 'multer'
import path from 'path'
import fs from 'fs'
import prisma from '../lib/prisma'
import { asyncHandler } from '../middleware/errorHandler'
import { authMiddleware } from '../middleware/auth'

const router = Router()

// 应用认证中间件
router.use(authMiddleware)

// 确保上传目录存在
const uploadDir = path.join(process.cwd(), 'uploads', 'images')
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true })
}

// 配置multer用于文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadDir)
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名：时间戳 + 随机字符串 + 原扩展名
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 15)
    const ext = path.extname(file.originalname)
    cb(null, `${timestamp}_${randomString}${ext}`)
  }
})

// 文件过滤器
const fileFilter = (req: any, file: any, cb: any) => {
  // 只允许图片文件
  if (file.mimetype.startsWith('image/')) {
    cb(null, true)
  } else {
    cb(new Error('只允许上传图片文件'), false)
  }
}

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB限制
    files: 5 // 最多5个文件
  }
})

/**
 * @swagger
 * /api/upload/batch-images/{batchId}:
 *   post:
 *     summary: Upload images for a batch
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: batchId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               images:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *               magnification:
 *                 type: integer
 *               stainingMethod:
 *                 type: string
 *     responses:
 *       201:
 *         description: Images uploaded successfully
 *       400:
 *         description: Invalid input or file
 *       404:
 *         description: Batch not found
 */
router.post('/batch-images/:batchId', 
  upload.array('images', 5), 
  asyncHandler(async (req: Request, res: Response) => {
    const { batchId } = req.params
    const userId = (req as any).user.id
    const files = req.files as Express.Multer.File[]
    
    if (!files || files.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的图片文件'
      })
    }

    // 注释掉批次检查，因为AI分析不依赖于特定的批次
    // const batch = await prisma.batch.findUnique({
    //   where: { id: batchId },
    //   select: { id: true, batchNumber: true }
    // })

    // if (!batch) {
    //   // 清理已上传的文件
    //   files.forEach(file => {
    //     fs.unlinkSync(file.path)
    //   })
      
    //   return res.status(404).json({
    //     success: false,
    //     message: '批次不存在'
    //   })
    // }

    const {
      magnification = 100,
      stainingMethod = '未指定'
    } = req.body

    try {
      // 简化返回，不创建数据库记录（AI分析功能独立管理）
      const imageRecords = files.map(file => ({
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        filename: file.filename,
        originalName: file.originalname,
        filePath: file.path,
        fileSize: file.size,
        mimeType: file.mimetype,
        magnification: parseInt(magnification.toString()),
        stainingMethod: stainingMethod.toString(),
        uploadedBy: userId,
        createdAt: new Date()
      }))

      res.status(201).json({
        success: true,
        data: {
          images: imageRecords,
          count: imageRecords.length
        },
        message: `成功上传 ${imageRecords.length} 张图片`
      })

    } catch (error) {
      // 如果出错，清理已上传的文件
      files.forEach(file => {
        try {
          fs.unlinkSync(file.path)
        } catch (e) {
          console.warn('清理文件失败:', file.path, e)
        }
      })
      
      throw error
    }
  })
)

/**
 * @swagger
 * /api/upload/images/{imageId}:
 *   get:
 *     summary: Get image file
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: imageId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Image file
 *         content:
 *           image/*:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: Image not found
 */
router.get('/images/:imageId', asyncHandler(async (req: Request, res: Response) => {
  const { imageId } = req.params

  // 简化实现，不依赖数据库查询
  // 通过imageId构建文件路径（这里假设imageId就是文件名）
  const filePath = path.join(uploadDir, imageId)

  // 检查文件是否存在
  if (!fs.existsSync(filePath)) {
    return res.status(404).json({
      success: false,
      message: '图片文件不存在'
    })
  }

  // 获取文件信息
  const stats = fs.statSync(filePath)
  const ext = path.extname(imageId).toLowerCase()
  
  // 设置MIME类型
  let mimeType = 'application/octet-stream'
  const mimeTypes: { [key: string]: string } = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.bmp': 'image/bmp',
    '.webp': 'image/webp',
    '.tiff': 'image/tiff',
    '.tif': 'image/tiff'
  }
  
  if (mimeTypes[ext]) {
    mimeType = mimeTypes[ext]
  }

  // 设置响应头
  res.set({
    'Content-Type': mimeType,
    'Content-Length': stats.size.toString(),
    'Content-Disposition': `inline; filename="${encodeURIComponent(imageId)}"`
  })

  // 发送文件
  res.sendFile(path.resolve(filePath))
}))

/**
 * @swagger
 * /api/upload/images/{imageId}:
 *   delete:
 *     summary: Delete image
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: imageId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Image deleted successfully
 *       404:
 *         description: Image not found
 *       403:
 *         description: Not authorized to delete this image
 */
router.delete('/images/:imageId', asyncHandler(async (req: Request, res: Response) => {
  const { imageId } = req.params
  const userId = (req as any).user.id
  const userRole = (req as any).user.role

  // 简化实现，直接根据文件名删除文件
  const filePath = path.join(uploadDir, imageId)

  if (!fs.existsSync(filePath)) {
    return res.status(404).json({
      success: false,
      message: '图片文件不存在'
    })
  }

  // 删除文件
  try {
    fs.unlinkSync(filePath)
  } catch (error) {
    console.warn('删除图片文件失败:', filePath, error)
    return res.status(500).json({
      success: false,
      message: '删除文件失败'
    })
  }

  res.json({
    success: true,
    message: '图片删除成功'
  })
}))

/**
 * @swagger
 * /api/upload/batch-images/{batchId}:
 *   get:
 *     summary: Get all images for a batch
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: batchId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Batch images retrieved successfully
 *       404:
 *         description: Batch not found
 */
router.get('/batch-images/:batchId', asyncHandler(async (req: Request, res: Response) => {
  const { batchId } = req.params

  // 简化实现，不依赖批次检查
  // 直接返回上传目录中的所有图片文件
  try {
    const files = fs.readdirSync(uploadDir).filter(file => {
      const ext = path.extname(file).toLowerCase()
      return ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif'].includes(ext)
    })

    const images = files.map(filename => {
      const filePath = path.join(uploadDir, filename)
      const stats = fs.statSync(filePath)
      return {
        id: filename,
        filename,
        originalName: filename,
        filePath,
        fileSize: stats.size,
        mimeType: 'image/' + path.extname(filename).slice(1),
        createdAt: stats.birthtime
      }
    }).sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())

    res.json({
      success: true,
      data: {
        images,
        count: images.length
      }
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取图片列表失败'
    })
  }
}))

/**
 * @swagger
 * /api/upload/ai-analysis-image:
 *   post:
 *     summary: Upload image for AI analysis
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               image:
 *                 type: string
 *                 format: binary
 *     responses:
 *       201:
 *         description: Image uploaded successfully
 *       400:
 *         description: Invalid input or file
 */
router.post('/ai-analysis-image', 
  upload.single('image'), 
  asyncHandler(async (req: Request, res: Response) => {
    const userId = (req as any).user.id
    const file = req.file as Express.Multer.File
    
    if (!file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的图片文件'
      })
    }

    try {
      // 返回文件信息和访问URL
      const imageUrl = `/api/upload/ai-images/${path.basename(file.filename)}`
      
      res.status(201).json({
        success: true,
        data: {
          filename: file.filename,
          originalName: file.originalname,
          imageUrl,
          fileSize: file.size,
          mimeType: file.mimetype
        },
        message: '图片上传成功'
      })

    } catch (error) {
      // 如果出错，清理已上传的文件
      try {
        fs.unlinkSync(file.path)
      } catch (e) {
        console.warn('清理文件失败:', file.path, e)
      }
      
      throw error
    }
  })
)

/**
 * @swagger
 * /api/upload/ai-images/{filename}:
 *   get:
 *     summary: Get AI analysis image file
 *     tags: [Upload]
 *     parameters:
 *       - in: path
 *         name: filename
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Image file
 *         content:
 *           image/*:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: Image not found
 */
router.get('/ai-images/:filename', asyncHandler(async (req: Request, res: Response) => {
  const { filename } = req.params
  const filePath = path.join(uploadDir, filename)

  // 检查文件是否存在
  if (!fs.existsSync(filePath)) {
    return res.status(404).json({
      success: false,
      message: '图片文件不存在'
    })
  }

  // 获取文件信息
  const stats = fs.statSync(filePath)
  const ext = path.extname(filename).toLowerCase()
  
  // 设置MIME类型
  let mimeType = 'application/octet-stream'
  const mimeTypes: { [key: string]: string } = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.bmp': 'image/bmp',
    '.webp': 'image/webp',
    '.tiff': 'image/tiff',
    '.tif': 'image/tiff'
  }
  
  if (mimeTypes[ext]) {
    mimeType = mimeTypes[ext]
  }

  // 设置响应头
  res.set({
    'Content-Type': mimeType,
    'Content-Length': stats.size.toString(),
    'Cache-Control': 'public, max-age=31536000', // 缓存1年
    'Content-Disposition': `inline; filename="${encodeURIComponent(filename)}"`
  })

  // 发送文件
  res.sendFile(path.resolve(filePath))
}))

/**
 * @swagger
 * /api/upload/product-images:
 *   post:
 *     summary: Upload images for product management
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               images:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *     responses:
 *       201:
 *         description: Images uploaded successfully
 *       400:
 *         description: Invalid input or file
 */
router.post('/product-images', 
  upload.array('images', 5), 
  asyncHandler(async (req: Request, res: Response) => {
    const userId = (req as any).user.id
    const files = req.files as Express.Multer.File[]
    
    if (!files || files.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的图片文件'
      })
    }

    try {
      // 返回文件信息和访问URL
      const imageRecords = files.map(file => ({
        id: file.filename,
        filename: file.filename,
        originalName: file.originalname,
        imageUrl: `/api/upload/product-images/${file.filename}`,
        fileSize: file.size,
        mimeType: file.mimetype,
        uploadedBy: userId,
        createdAt: new Date()
      }))

      res.status(201).json({
        success: true,
        data: {
          images: imageRecords,
          count: imageRecords.length
        },
        message: `成功上传 ${imageRecords.length} 张图片`
      })

    } catch (error) {
      // 如果出错，清理已上传的文件
      files.forEach(file => {
        try {
          fs.unlinkSync(file.path)
        } catch (e) {
          console.warn('清理文件失败:', file.path, e)
        }
      })
      
      throw error
    }
  })
)

/**
 * @swagger
 * /api/upload/product-images/{filename}:
 *   get:
 *     summary: Get product image file
 *     tags: [Upload]
 *     parameters:
 *       - in: path
 *         name: filename
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Image file
 *         content:
 *           image/*:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: Image not found
 */
router.get('/product-images/:filename', asyncHandler(async (req: Request, res: Response) => {
  const { filename } = req.params
  const filePath = path.join(uploadDir, filename)

  // 检查文件是否存在
  if (!fs.existsSync(filePath)) {
    return res.status(404).json({
      success: false,
      message: '图片文件不存在'
    })
  }

  // 获取文件信息
  const stats = fs.statSync(filePath)
  const ext = path.extname(filename).toLowerCase()
  
  // 设置MIME类型
  let mimeType = 'application/octet-stream'
  const mimeTypes: { [key: string]: string } = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.bmp': 'image/bmp',
    '.webp': 'image/webp',
    '.tiff': 'image/tiff',
    '.tif': 'image/tiff'
  }
  
  if (mimeTypes[ext]) {
    mimeType = mimeTypes[ext]
  }

  // 设置响应头
  res.set({
    'Content-Type': mimeType,
    'Content-Length': stats.size.toString(),
    'Cache-Control': 'public, max-age=31536000', // 缓存1年
    'Content-Disposition': `inline; filename="${encodeURIComponent(filename)}"`
  })

  // 发送文件
  res.sendFile(path.resolve(filePath))
}))

export default router