import { Router, Request, Response } from 'express'
import bcrypt from 'bcryptjs'
import { body, validationResult, query } from 'express-validator'
import { getDatabase } from '../config/database'
import { authMiddleware } from '../middleware/auth'
import { AppError, asyncHandler } from '../middleware/errorHandler'
import { logger } from '../utils/logger'

const router = Router()

// 所有用户管理路由都需要认证
router.use(authMiddleware)

/**
 * @swagger
 * /api/users:
 *   get:
 *     summary: Get users list with filtering
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *           enum: [ADMIN, MANAGER, USER, VIEWER]
 *         description: Filter by user role
 *       - in: query
 *         name: cafeId
 *         schema:
 *           type: string
 *         description: Filter by cafe ID
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *     responses:
 *       200:
 *         description: Users retrieved successfully
 */
router.get('/', [
  query('role').optional().isIn(['ADMIN', 'MANAGER', 'USER', 'VIEWER']),
  query('cafeId').optional().isUUID(),
  query('isActive').optional().isBoolean(),
], asyncHandler(async (req: Request, res: Response) => {
  const user = (req as any).user
  const db = getDatabase()
  
  const { role, cafeId, isActive } = req.query
  
  // 构建查询条件
  let whereConditions = ['u.id IS NOT NULL']
  let queryParams: any[] = []
  let paramIndex = 1
  
  // 权限控制：非ADMIN用户只能查看自己店面的用户
  if (user.role !== 'ADMIN') {
    if (user.cafeId) {
      whereConditions.push(`u.cafe_id = $${paramIndex}`)
      queryParams.push(user.cafeId)
      paramIndex++
    } else {
      // 如果用户没有关联店面，返回空结果
      return res.json({
        success: true,
        data: {
          users: [],
          total: 0,
          statistics: {
            total: 0,
            admin: 0,
            user: 0,
            viewer: 0,
            active: 0
          }
        }
      })
    }
  }
  
  // 角色过滤
  if (role) {
    whereConditions.push(`u.role = $${paramIndex}`)
    queryParams.push(role)
    paramIndex++
  }
  
  // 店面过滤（仅ADMIN用户可用）
  if (cafeId && user.role === 'ADMIN') {
    whereConditions.push(`u.cafe_id = $${paramIndex}`)
    queryParams.push(cafeId)
    paramIndex++
  }
  
  // 状态过滤
  if (isActive !== undefined) {
    whereConditions.push(`u.is_active = $${paramIndex}`)
    queryParams.push(isActive === 'true')
    paramIndex++
  }
  
  const whereClause = whereConditions.join(' AND ')
  
  // 查询用户列表
  const usersQuery = `
    SELECT 
      u.id,
      u.email,
      u.username,
      u.name,
      u.role,
      u.cafe_id,
      u.is_active,
      u.created_at,
      u.updated_at,
      c.name as cafe_name
    FROM users u
    LEFT JOIN cafes c ON u.cafe_id = c.id
    WHERE ${whereClause}
    ORDER BY u.created_at DESC
  `
  
  const usersResult = await db.query(usersQuery, queryParams)
  
  // 统计数据
  const statsQuery = `
    SELECT 
      COUNT(*) as total,
      COUNT(CASE WHEN role = 'ADMIN' THEN 1 END) as admin_count,
      COUNT(CASE WHEN role = 'USER' THEN 1 END) as user_count,
      COUNT(CASE WHEN role = 'VIEWER' THEN 1 END) as viewer_count,
      COUNT(CASE WHEN is_active = true THEN 1 END) as active_count
    FROM users u
    WHERE ${whereClause}
  `
  
  const statsResult = await db.query(statsQuery, queryParams)
  const stats = statsResult.rows[0]
  
  const users = usersResult.rows.map(row => ({
    id: row.id,
    email: row.email,
    username: row.username,
    name: row.name,
    role: row.role,
    cafeId: row.cafe_id,
    cafeName: row.cafe_name,
    isActive: row.is_active,
    createdAt: row.created_at,
    updatedAt: row.updated_at
  }))
  
  res.json({
    success: true,
    data: {
      users,
      total: parseInt(stats.total),
      statistics: {
        total: parseInt(stats.total),
        admin: parseInt(stats.admin_count),
        user: parseInt(stats.user_count),
        viewer: parseInt(stats.viewer_count),
        active: parseInt(stats.active_count)
      }
    }
  })
}))

/**
 * @swagger
 * /api/users:
 *   post:
 *     summary: Create a new user
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - username
 *               - password
 *               - role
 *               - cafeId
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               username:
 *                 type: string
 *               name:
 *                 type: string
 *               password:
 *                 type: string
 *                 minLength: 6
 *               role:
 *                 type: string
 *                 enum: [USER, VIEWER]
 *               cafeId:
 *                 type: string
 *                 format: uuid
 *     responses:
 *       201:
 *         description: User created successfully
 */
router.post('/', [
  body('email').isEmail().normalizeEmail(),
  body('username').isLength({ min: 3, max: 50 }).trim().matches(/^[a-zA-Z0-9_]+$/).withMessage('用户名只能包含字母、数字和下划线'),
  body('name').optional().isLength({ min: 2 }).trim(),
  body('password').isLength({ min: 6 }),
  body('role').isIn(['MANAGER', 'USER', 'VIEWER']).withMessage('只能创建MANAGER、USER或VIEWER角色'),
  body('cafeId').isUUID().withMessage('请选择有效的店面'),
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    throw new AppError('输入数据无效', 400)
  }
  
  const user = (req as any).user
  const { email, username, name, password, role, cafeId } = req.body
  const db = getDatabase()
  
  // 权限控制：非ADMIN用户只能在自己的店面创建用户
  if (user.role !== 'ADMIN') {
    if (user.cafeId !== cafeId) {
      throw new AppError('您只能在自己的店面创建用户', 403)
    }
  }
  
  logger.info(`Creating user: ${email}/${username} in cafe: ${cafeId} by ${user.email}`)
  
  // 检查邮箱是否已存在
  const existingEmailQuery = 'SELECT id FROM users WHERE email = $1'
  const existingEmailResult = await db.query(existingEmailQuery, [email])
  
  if (existingEmailResult.rows.length > 0) {
    throw new AppError('该邮箱已被使用', 400)
  }
  
  // 检查用户名在该店面内是否已存在
  const existingUsernameQuery = 'SELECT id FROM users WHERE username = $1 AND cafe_id = $2'
  const existingUsernameResult = await db.query(existingUsernameQuery, [username, cafeId])
  
  if (existingUsernameResult.rows.length > 0) {
    throw new AppError('该用户名在此店面内已被使用', 400)
  }
  
  // 验证店面是否存在且有效
  const cafeQuery = 'SELECT id, name FROM cafes WHERE id = $1 AND status = $2'
  const cafeResult = await db.query(cafeQuery, [cafeId, 'active'])
  
  if (cafeResult.rows.length === 0) {
    throw new AppError('选择的店面无效或不可用', 400)
  }
  
  const cafe = cafeResult.rows[0]
  
  // 加密密码
  const hashedPassword = await bcrypt.hash(password, 12)
  
  // 创建用户
  const insertUserQuery = `
    INSERT INTO users (email, username, password_hash, name, role, cafe_id, is_active, created_at, updated_at) 
    VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW()) 
    RETURNING id, email, username, name, role, cafe_id, created_at
  `
  
  const newUserResult = await db.query(insertUserQuery, [
    email,
    username,
    hashedPassword,
    name || username,
    role,
    cafeId,
    true
  ])
  
  const newUser = newUserResult.rows[0]
  
  logger.info(`User created successfully: ${email}/${username} at cafe: ${cafe.name}`)
  
  res.status(201).json({
    success: true,
    data: {
      user: {
        id: newUser.id,
        email: newUser.email,
        username: newUser.username,
        name: newUser.name,
        role: newUser.role,
        cafeId: newUser.cafe_id,
        cafeName: cafe.name,
        isActive: true,
        createdAt: newUser.created_at,
      }
    },
    message: '用户创建成功'
  })
}))

/**
 * @swagger
 * /api/users/{id}:
 *   put:
 *     summary: Update user
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: User updated successfully
 */
router.put('/:id', [
  body('name').optional().isLength({ min: 2 }).trim(),
  body('role').optional().isIn(['MANAGER', 'USER', 'VIEWER']).withMessage('只能设置MANAGER、USER或VIEWER角色'),
  body('isActive').optional().isBoolean(),
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    throw new AppError('输入数据无效', 400)
  }
  
  const user = (req as any).user
  const { id } = req.params
  const { name, role, isActive } = req.body
  const db = getDatabase()
  
  // 查询要更新的用户
  const existingUserQuery = 'SELECT id, cafe_id, role FROM users WHERE id = $1'
  const existingUserResult = await db.query(existingUserQuery, [id])
  
  if (existingUserResult.rows.length === 0) {
    throw new AppError('用户不存在', 404)
  }
  
  const existingUser = existingUserResult.rows[0]
  
  // 权限控制：非ADMIN用户只能修改自己店面的用户
  if (user.role !== 'ADMIN') {
    if (existingUser.cafe_id !== user.cafeId) {
      throw new AppError('您只能修改自己店面的用户', 403)
    }
  }
  
  // 不允许修改ADMIN用户（除非自己是ADMIN）
  if (existingUser.role === 'ADMIN' && user.role !== 'ADMIN') {
    throw new AppError('您没有权限修改管理员用户', 403)
  }
  
  // 构建更新字段
  const updateFields = []
  const updateValues = []
  let paramIndex = 1
  
  if (name !== undefined) {
    updateFields.push(`name = $${paramIndex}`)
    updateValues.push(name)
    paramIndex++
  }
  
  if (role !== undefined && user.role === 'ADMIN') {
    updateFields.push(`role = $${paramIndex}`)
    updateValues.push(role)
    paramIndex++
  }
  
  if (isActive !== undefined) {
    updateFields.push(`is_active = $${paramIndex}`)
    updateValues.push(isActive)
    paramIndex++
  }
  
  if (updateFields.length === 0) {
    throw new AppError('没有要更新的字段', 400)
  }
  
  updateFields.push(`updated_at = NOW()`)
  updateValues.push(id)
  
  const updateQuery = `
    UPDATE users 
    SET ${updateFields.join(', ')}
    WHERE id = $${paramIndex}
    RETURNING id, email, username, name, role, cafe_id, is_active, updated_at
  `
  
  const updateResult = await db.query(updateQuery, updateValues)
  const updatedUser = updateResult.rows[0]
  
  logger.info(`User updated: ${updatedUser.email} by ${user.email}`)
  
  res.json({
    success: true,
    data: {
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        username: updatedUser.username,
        name: updatedUser.name,
        role: updatedUser.role,
        cafeId: updatedUser.cafe_id,
        isActive: updatedUser.is_active,
        updatedAt: updatedUser.updated_at
      }
    },
    message: '用户更新成功'
  })
}))

/**
 * @swagger
 * /api/users/{id}:
 *   delete:
 *     summary: Delete user (soft delete)
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: User deleted successfully
 */
router.delete('/:id', asyncHandler(async (req: Request, res: Response) => {
  const user = (req as any).user
  const { id } = req.params
  const db = getDatabase()
  
  // 查询要删除的用户
  const existingUserQuery = 'SELECT id, email, cafe_id, role FROM users WHERE id = $1 AND is_active = true'
  const existingUserResult = await db.query(existingUserQuery, [id])
  
  if (existingUserResult.rows.length === 0) {
    throw new AppError('用户不存在或已被删除', 404)
  }
  
  const existingUser = existingUserResult.rows[0]
  
  // 权限控制：非ADMIN用户只能删除自己店面的用户
  if (user.role !== 'ADMIN') {
    if (existingUser.cafe_id !== user.cafeId) {
      throw new AppError('您只能删除自己店面的用户', 403)
    }
  }
  
  // 不允许删除ADMIN用户（除非自己是ADMIN）
  if (existingUser.role === 'ADMIN' && user.role !== 'ADMIN') {
    throw new AppError('您没有权限删除管理员用户', 403)
  }
  
  // 不允许删除自己
  if (existingUser.id === user.id) {
    throw new AppError('不能删除自己的账号', 400)
  }
  
  // 软删除用户（设置为不活跃）
  const deleteQuery = `
    UPDATE users 
    SET is_active = false, updated_at = NOW()
    WHERE id = $1
    RETURNING email
  `
  
  const deleteResult = await db.query(deleteQuery, [id])
  
  logger.info(`User deleted: ${existingUser.email} by ${user.email}`)
  
  res.json({
    success: true,
    message: '用户删除成功'
  })
}))

export default router
