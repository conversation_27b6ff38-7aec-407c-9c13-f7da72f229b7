import { Router, Request, Response } from 'express'
import bcrypt from 'bcryptjs'
import { body, validationResult } from 'express-validator'
import { getDatabase } from '../config/database'
import { generateToken, generateRefreshToken, verifyToken } from '../middleware/auth'
import { AppError, as<PERSON><PERSON><PERSON><PERSON> } from '../middleware/errorHandler'
import { logger } from '../utils/logger'

const router = Router()

/**
 * @swagger
 * components:
 *   schemas:
 *     LoginRequest:
 *       type: object
 *       required:
 *         - identifier
 *         - password
 *       properties:
 *         identifier:
 *           type: string
 *           description: 用户名或邮箱
 *         password:
 *           type: string
 *           minLength: 6
 *         cafeId:
 *           type: string
 *           format: uuid
 *           description: 店面ID（总管理员可选，其他角色必填）
 *     LoginResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         data:
 *           type: object
 *           properties:
 *             user:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 email:
 *                   type: string
 *                 role:
 *                   type: string
 *             token:
 *               type: string
 *             refreshToken:
 *               type: string
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     RegisterRequest:
 *       type: object
 *       required:
 *         - email
 *         - password
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *         name:
 *           type: string
 *           minLength: 2
 *         password:
 *           type: string
 *           minLength: 6
 *     RegisterResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         data:
 *           type: object
 *           properties:
 *             user:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 email:
 *                   type: string
 *                 role:
 *                   type: string
 *             token:
 *               type: string
 *             refreshToken:
 *               type: string
 */

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: User registration (simplified)
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/RegisterRequest'
 *     responses:
 *       201:
 *         description: Registration successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/RegisterResponse'
 *       400:
 *         description: Invalid input or user already exists
 */
/**
 * @swagger
 * /api/auth/cafes:
 *   get:
 *     summary: Get list of available cafes for registration
 *     tags: [Authentication]
 *     responses:
 *       200:
 *         description: List of cafes retrieved successfully
 */
router.get('/cafes', asyncHandler(async (req: Request, res: Response) => {
  const db = getDatabase()
  
  const cafesQuery = 'SELECT id, name, description, address FROM cafes WHERE status = $1 ORDER BY display_order, name'
  const cafesResult = await db.query(cafesQuery, ['active'])
  
  res.json({
    success: true,
    data: {
      cafes: cafesResult.rows
    }
  })
}))

router.post('/register', [
  body('email').isEmail().normalizeEmail(),
  body('username').isLength({ min: 3, max: 50 }).trim().matches(/^[a-zA-Z0-9_]+$/).withMessage('用户名只能包含字母、数字和下划线'),
  body('name').optional().isLength({ min: 2 }).trim(),
  body('password').isLength({ min: 6 }),
  body('cafeId').isUUID().withMessage('请选择有效的店面'),
  body('role').optional().isIn(['USER', 'VIEWER']).withMessage('角色必须是USER或VIEWER'),
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    throw new AppError('Invalid input data', 400)
  }

  const { email, username, name, password, cafeId, role = 'VIEWER' } = req.body
  const db = getDatabase()

  logger.info(`Registration attempt for email: ${email}, username: ${username}, cafe: ${cafeId}`)

  // 检查邮箱是否已存在
  const existingEmailQuery = 'SELECT id FROM users WHERE email = $1'
  const existingEmailResult = await db.query(existingEmailQuery, [email])

  if (existingEmailResult.rows.length > 0) {
    logger.warn(`User with email already exists: ${email}`)
    throw new AppError('User with this email already exists', 400)
  }

  // 检查用户名在该咖啡厅内是否已存在
  const existingUsernameQuery = 'SELECT id FROM users WHERE username = $1 AND cafe_id = $2'
  const existingUsernameResult = await db.query(existingUsernameQuery, [username, cafeId])

  if (existingUsernameResult.rows.length > 0) {
    logger.warn(`Username already exists in cafe: ${username}`)
    throw new AppError('Username already exists in this cafe', 400)
  }

  // 验证咖啡厅是否存在且有效
  const cafeQuery = 'SELECT id, name FROM cafes WHERE id = $1 AND status = $2'
  const cafeResult = await db.query(cafeQuery, [cafeId, 'active'])
  
  if (cafeResult.rows.length === 0) {
    throw new AppError('Invalid cafe selection', 400)
  }

  const cafe = cafeResult.rows[0]

  // 加密用户提供的密码
  const hashedPassword = await bcrypt.hash(password, 12)

  // 创建用户
  const insertUserQuery = `
    INSERT INTO users (email, username, password_hash, name, role, cafe_id, is_active, created_at, updated_at) 
    VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW()) 
    RETURNING id, email, username, name, role, cafe_id, created_at
  `
  
  const newUserResult = await db.query(insertUserQuery, [
    email,
    username,
    hashedPassword,
    name || username, // 如果没有提供姓名，使用用户名
    role, // 用户选择的角色
    cafeId,
    true
  ])

  const newUser = newUserResult.rows[0]

  // 生成 tokens
  const tokenPayload = {
    id: newUser.id,
    email: newUser.email,
    username: newUser.username,
    role: newUser.role,
    cafeId: newUser.cafe_id,
  }

  const token = generateToken(tokenPayload)
  const refreshToken = generateRefreshToken(tokenPayload)

  logger.info(`User registered successfully: ${email}/${username} at cafe: ${cafe.name}`)

  res.status(201).json({
    success: true,
    data: {
      user: {
        id: newUser.id,
        email: newUser.email,
        username: newUser.username,
        name: newUser.name,
        role: newUser.role,
        cafeId: newUser.cafe_id,
        cafeName: cafe.name,
      },
      token,
      refreshToken,
    },
    message: '注册成功！'
  })
}))

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: User login
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LoginResponse'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Invalid credentials
 */
router.post('/login', [
  body('identifier').notEmpty().withMessage('请输入用户名或邮箱'),
  body('password').isLength({ min: 6 }),
  body('cafeId').optional().isUUID().withMessage('请选择咖啡厅'),
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    throw new AppError('Invalid input data', 400)
  }

  const { identifier, password, cafeId } = req.body
  const db = getDatabase()

  logger.info(`Login attempt for identifier: ${identifier}, cafe: ${cafeId || 'N/A'}`)

  // 首先查找用户，确定是否为管理员
  const initialUserQuery = `
    SELECT id, email, username, password_hash, name, role, cafe_id, created_at 
    FROM users 
    WHERE (email = $1 OR username = $1) 
    AND is_active = true
  `
  const initialUserResult = await db.query(initialUserQuery, [identifier])

  if (initialUserResult.rows.length === 0) {
    logger.warn(`User not found: ${identifier}`)
    throw new AppError('Invalid credentials', 401)
  }

  const user = initialUserResult.rows[0]
  
  // 只有总管理员(ADMIN)可以不选择店面
  if (user.role === 'ADMIN') {
    // 总管理员登录，不需要验证店面
    logger.info(`Admin login: ${identifier}`)
  } else {
    // 店面管理员、操作员、普通用户必须选择店面
    if (!cafeId) {
      throw new AppError('请选择店面', 400)
    }

    // 验证咖啡厅是否存在且处于活跃状态
    const cafeQuery = 'SELECT id, name FROM cafes WHERE id = $1 AND status = $2'
    const cafeResult = await db.query(cafeQuery, [cafeId, 'active'])
    
    if (cafeResult.rows.length === 0) {
      throw new AppError('选择的店面无效或不可用', 400)
    }

    // 验证用户是否属于该店面（店面用户只能访问自己的店面）
    if (user.cafe_id && user.cafe_id !== cafeId) {
      logger.warn(`User ${identifier} attempted to access cafe ${cafeId} but belongs to ${user.cafe_id}`)
      throw new AppError('您没有权限访问该店面', 403)
    }
  }

  // 验证密码
  const isPasswordValid = await bcrypt.compare(password, user.password_hash)
  logger.info(`Password validation result: ${isPasswordValid}`)

  if (!isPasswordValid) {
    logger.warn(`Invalid password for user: ${identifier}`)
    throw new AppError('Invalid credentials', 401)
  }

  // 获取店面信息（管理员可能没有关联店面）
  let cafeName = null
  if (user.role === 'ADMIN') {
    cafeName = '全部店面'
  } else if (cafeId) {
    const cafeQuery = 'SELECT name FROM cafes WHERE id = $1'
    const cafeResult = await db.query(cafeQuery, [cafeId])
    cafeName = cafeResult.rows[0]?.name || '未知店面'
  }

  // 生成 tokens
  const tokenPayload = {
    id: user.id,
    email: user.email,
    username: user.username,
    role: user.role,
    cafeId: user.role === 'ADMIN' ? null : (cafeId || user.cafe_id),
  }

  const token = generateToken(tokenPayload)
  const refreshToken = generateRefreshToken(tokenPayload)

  // 记录登录
  logger.info(`User logged in: ${identifier} (${user.role}) - ${cafeName}`)

  res.json({
    success: true,
    data: {
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        name: user.name,
        role: user.role,
        cafeId: user.role === 'ADMIN' ? null : (cafeId || user.cafe_id),
        cafeName: cafeName,
      },
      token,
      refreshToken,
    },
  })
}))

/**
 * @swagger
 * /api/auth/refresh:
 *   post:
 *     summary: Refresh access token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refreshToken
 *             properties:
 *               refreshToken:
 *                 type: string
 *     responses:
 *       200:
 *         description: Token refreshed successfully
 *       401:
 *         description: Invalid refresh token
 */
router.post('/refresh', asyncHandler(async (req: Request, res: Response) => {
  const { refreshToken } = req.body

  if (!refreshToken) {
    throw new AppError('Refresh token is required', 401)
  }

  try {
    const decoded = verifyToken(refreshToken)
    
    // 生成新的访问令牌
    const newToken = generateToken({
      id: decoded.id,
      email: decoded.email,
      role: decoded.role,
    })

    res.json({
      success: true,
      data: {
        token: newToken,
      },
    })
  } catch (error) {
    logger.warn('Invalid refresh token:', error)
    throw new AppError('Invalid refresh token', 401)
  }
}))

/**
 * @swagger
 * /api/auth/me:
 *   get:
 *     summary: Get current user info
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User info retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/me', asyncHandler(async (req: Request, res: Response) => {
  // 从请求头获取 token 并验证
  const authHeader = req.headers.authorization

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new AppError('Access token is required', 401)
  }

  const token = authHeader.substring(7)

  try {
    const decoded = verifyToken(token)
    const db = getDatabase()

    // 从数据库获取完整用户信息包括店面信息
    const userQuery = `
      SELECT 
        u.id, u.email, u.username, u.name, u.role, u.cafe_id, u.created_at,
        c.name as cafe_name
      FROM users u
      LEFT JOIN cafes c ON u.cafe_id = c.id
      WHERE u.id = $1
    `
    const userResult = await db.query(userQuery, [decoded.id])

    if (userResult.rows.length === 0) {
      throw new AppError('User not found', 404)
    }

    const user = userResult.rows[0]

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          name: user.name,
          role: user.role,
          cafeId: user.role === 'ADMIN' ? null : user.cafe_id,
          cafeName: user.role === 'ADMIN' ? '全部店面' : user.cafe_name,
          createdAt: user.created_at,
        },
      },
    })
  } catch (error) {
    logger.warn('Invalid token in /me endpoint:', error)
    throw new AppError('Invalid token', 401)
  }
}))


export default router
