import { Router, Request, Response } from 'express'
import prisma from '../lib/prisma'
import { asyncHand<PERSON> } from '../middleware/errorHandler'
import { authMiddleware, requireManagerLevel } from '../middleware/auth'

const router = Router()

// 应用认证中间件
router.use(authMiddleware)

// 批次数据验证
const validateBatchData = (data: any) => {
  const errors: string[] = []
  
  if (!data.batchNumber || typeof data.batchNumber !== 'string') {
    errors.push('批次号是必填项')
  }
  
  if (!data.recipeId || typeof data.recipeId !== 'string') {
    errors.push('配方ID是必填项')
  }
  
  if (!data.productionDate) {
    errors.push('生产日期是必填项')
  }
  
  return errors
}

/**
 * @swagger
 * /api/batches:
 *   get:
 *     summary: Get all batches
 *     tags: [Batches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *       - in: query
 *         name: recipeId
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Batches retrieved successfully
 */
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const page = parseInt(req.query.page as string) || 1
  const limit = parseInt(req.query.limit as string) || 10
  const status = req.query.status as string
  const recipeId = req.query.recipeId as string
  const skip = (page - 1) * limit

  const where: any = {}
  
  if (status) {
    where.status = status
  }
  
  if (recipeId) {
    where.recipeId = recipeId
  }

  const [batches, total] = await Promise.all([
    prisma.batch.findMany({
      where,
      include: {
        recipe: {
          select: { id: true, name: true, version: true }
        },
        user: {
          select: { id: true, name: true, email: true }
        },
        microscopeImages: {
          select: {
            id: true,
            filename: true,
            originalName: true,
            createdAt: true
          }
        },
        _count: {
          select: {
            sensoryAssessments: true,
            aiAnalyses: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit
    }),
    prisma.batch.count({ where })
  ])

  res.json({
    success: true,
    data: {
      batches,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  })
}))

/**
 * @swagger
 * /api/batches/{id}:
 *   get:
 *     summary: Get batch by ID
 *     tags: [Batches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Batch retrieved successfully
 *       404:
 *         description: Batch not found
 */
router.get('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params

  const batch = await prisma.batch.findUnique({
    where: { id },
    include: {
      recipe: true,
      user: {
        select: { id: true, name: true, email: true }
      },
      microscopeImages: {
        include: {
          uploader: {
            select: { id: true, name: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      },
      sensoryAssessments: {
        include: {
          assessor: {
            select: { id: true, name: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      },
      aiAnalyses: {
        orderBy: { createdAt: 'desc' }
      }
    }
  })

  if (!batch) {
    return res.status(404).json({
      success: false,
      message: '批次不存在'
    })
  }

  res.json({
    success: true,
    data: { batch }
  })
}))

/**
 * @swagger
 * /api/batches:
 *   post:
 *     summary: Create a new batch
 *     tags: [Batches]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               batchNumber:
 *                 type: string
 *               recipeId:
 *                 type: string
 *               productionDate:
 *                 type: string
 *                 format: date
 *               quantity:
 *                 type: number
 *               notes:
 *                 type: string
 *     responses:
 *       201:
 *         description: Batch created successfully
 *       400:
 *         description: Invalid input
 */
router.post('/', requireManagerLevel, asyncHandler(async (req: Request, res: Response) => {
  const errors = validateBatchData(req.body)
  
  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: '输入数据无效',
      errors
    })
  }

  const userId = (req as any).user.id
  const { batchNumber, recipeId, productionDate, quantity, notes } = req.body

  // 检查批次号是否已存在
  const existingBatch = await prisma.batch.findUnique({
    where: { batchNumber }
  })

  if (existingBatch) {
    return res.status(400).json({
      success: false,
      message: '批次号已存在'
    })
  }

  // 检查配方是否存在
  const recipe = await prisma.recipe.findUnique({
    where: { id: recipeId }
  })

  if (!recipe) {
    return res.status(400).json({
      success: false,
      message: '配方不存在'
    })
  }

  const batch = await prisma.batch.create({
    data: {
      batchNumber,
      recipeId,
      userId,
      productionDate: new Date(productionDate),
      quantity: quantity ? parseFloat(quantity) : null,
      notes
    },
    include: {
      recipe: {
        select: { id: true, name: true, version: true }
      },
      user: {
        select: { id: true, name: true, email: true }
      }
    }
  })

  res.status(201).json({
    success: true,
    data: { batch },
    message: '批次创建成功'
  })
}))

/**
 * @swagger
 * /api/batches/{id}:
 *   put:
 *     summary: Update batch
 *     tags: [Batches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Batch updated successfully
 *       404:
 *         description: Batch not found
 *       403:
 *         description: Not authorized to update this batch
 */
router.put('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params
  const userId = (req as any).user.id
  const userRole = (req as any).user.role

  const existingBatch = await prisma.batch.findUnique({
    where: { id },
    select: { userId: true }
  })

  if (!existingBatch) {
    return res.status(404).json({
      success: false,
      message: '批次不存在'
    })
  }

  // 检查权限：只有批次创建者或管理员可以编辑
  if (existingBatch.userId !== userId && userRole !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '无权限编辑此批次'
    })
  }

  const updateData: any = { ...req.body }
  
  // 处理日期字段
  if (updateData.productionDate) {
    updateData.productionDate = new Date(updateData.productionDate)
  }
  
  // 处理数值字段
  if (updateData.quantity) {
    updateData.quantity = parseFloat(updateData.quantity)
  }
  
  if (updateData.actualFermentationDuration) {
    updateData.actualFermentationDuration = parseInt(updateData.actualFermentationDuration)
  }
  
  if (updateData.actualTemperature) {
    updateData.actualTemperature = parseFloat(updateData.actualTemperature)
  }

  const batch = await prisma.batch.update({
    where: { id },
    data: updateData,
    include: {
      recipe: {
        select: { id: true, name: true, version: true }
      },
      user: {
        select: { id: true, name: true, email: true }
      }
    }
  })

  res.json({
    success: true,
    data: { batch },
    message: '批次更新成功'
  })
}))

/**
 * @swagger
 * /api/batches/{id}/status:
 *   patch:
 *     summary: Update batch status
 *     tags: [Batches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [PLANNING, IN_PROGRESS, FERMENTING, FILTERING, COMPLETED, FAILED]
 *     responses:
 *       200:
 *         description: Batch status updated successfully
 */
router.patch('/:id/status', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params
  const { status } = req.body
  const userId = (req as any).user.id
  const userRole = (req as any).user.role

  const validStatuses = ['PLANNING', 'IN_PROGRESS', 'FERMENTING', 'FILTERING', 'COMPLETED', 'FAILED']
  
  if (!validStatuses.includes(status)) {
    return res.status(400).json({
      success: false,
      message: '无效的状态值'
    })
  }

  const existingBatch = await prisma.batch.findUnique({
    where: { id },
    select: { userId: true }
  })

  if (!existingBatch) {
    return res.status(404).json({
      success: false,
      message: '批次不存在'
    })
  }

  // 检查权限
  if (existingBatch.userId !== userId && userRole !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '无权限修改此批次状态'
    })
  }

  const batch = await prisma.batch.update({
    where: { id },
    data: { status },
    include: {
      recipe: {
        select: { id: true, name: true }
      }
    }
  })

  res.json({
    success: true,
    data: { batch },
    message: '批次状态更新成功'
  })
}))

/**
 * @swagger
 * /api/batches/{id}:
 *   delete:
 *     summary: Delete batch
 *     tags: [Batches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Batch deleted successfully
 *       404:
 *         description: Batch not found
 *       403:
 *         description: Not authorized to delete this batch
 */
router.delete('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params
  const userId = (req as any).user.id
  const userRole = (req as any).user.role

  const existingBatch = await prisma.batch.findUnique({
    where: { id },
    select: { 
      userId: true, 
      batchNumber: true,
      _count: {
        select: {
          sensoryAssessments: true,
          microscopeImages: true,
          aiAnalyses: true
        }
      }
    }
  })

  if (!existingBatch) {
    return res.status(404).json({
      success: false,
      message: '批次不存在'
    })
  }

  // 检查权限
  if (existingBatch.userId !== userId && userRole !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '无权限删除此批次'
    })
  }

  // 检查是否有关联数据
  const hasData = existingBatch._count.sensoryAssessments > 0 || 
                  existingBatch._count.microscopeImages > 0 || 
                  existingBatch._count.aiAnalyses > 0

  if (hasData) {
    return res.status(400).json({
      success: false,
      message: '无法删除：该批次已有关联的评估数据或图片'
    })
  }

  await prisma.batch.delete({
    where: { id }
  })

  res.json({
    success: true,
    message: '批次删除成功'
  })
}))

export default router
