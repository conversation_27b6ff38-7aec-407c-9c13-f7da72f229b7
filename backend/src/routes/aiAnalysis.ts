import { Router, Request, Response } from 'express'
import { body, query, param, validationResult } from 'express-validator'
import prisma from '../lib/prisma'
import { authMiddleware } from '../middleware/auth'
import { AppError, asyncHandler } from '../middleware/errorHandler'
import { logger } from '../utils/logger'
import axios from 'axios'

const router = Router()

// 本地AI服务配置
const LOCAL_AI_URL = 'http://192.168.1.225:1234'

/**
 * @swagger
 * components:
 *   schemas:
 *     AIAnalysisRecord:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         userId:
 *           type: string
 *         cafeId:
 *           type: string
 *         fileName:
 *           type: string
 *         imageUrl:
 *           type: string
 *         analysisResult:
 *           type: string
 *         processingTime:
 *           type: integer
 *         customPrompt:
 *           type: string
 *         status:
 *           type: string
 *           enum: [analyzing, completed, error]
 *         createdAt:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /api/ai-analysis/health:
 *   get:
 *     summary: Check local AI service health
 *     tags: [AI Analysis]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: AI service status
 */
router.get('/health', [
  authMiddleware,
], asyncHandler(async (req: any, res: Response) => {
  try {
    // 使用 /v1/models 端点来检查服务状态，因为 /health 端点不存在
    const response = await axios.get(`${LOCAL_AI_URL}/v1/models`, { timeout: 5000 })

    res.json({
      success: true,
      data: {
        status: 'online',
        serviceUrl: LOCAL_AI_URL,
        serviceResponse: {
          models: response.data.data || [],
          message: 'AI service is online and models are available'
        }
      }
    })
  } catch (error) {
    res.json({
      success: true,
      data: {
        status: 'offline',
        serviceUrl: LOCAL_AI_URL,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    })
  }
}))

/**
 * @swagger
 * /api/ai-analysis/models:
 *   get:
 *     summary: Get available AI models
 *     tags: [AI Analysis]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Available models list
 */
router.get('/models', [
  authMiddleware,
], asyncHandler(async (req: any, res: Response) => {
  try {
    const response = await axios.get(`${LOCAL_AI_URL}/v1/models`, { timeout: 10000 })
    
    res.json({
      success: true,
      data: {
        models: response.data.data || []
      }
    })
  } catch (error) {
    throw new AppError('Failed to fetch models from AI service', 503)
  }
}))

/**
 * @swagger
 * /api/ai-analysis:
 *   get:
 *     summary: Get user's AI analysis history
 *     tags: [AI Analysis]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Filter by status
 *     responses:
 *       200:
 *         description: Analysis history retrieved successfully
 */
router.get('/', [
  authMiddleware,
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(['analyzing', 'completed', 'error']),
], asyncHandler(async (req: any, res: Response) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    throw new AppError('Invalid query parameters', 400)
  }

  const { page = 1, limit = 20, status } = req.query
  const skip = (page - 1) * limit

  // 构建查询条件
  let where: any = {}

  // 根据用户角色决定数据过滤策略
  if (req.user.role === 'ADMIN') {
    // 管理员可以看到所有分析记录
    where = {}
  } else if (req.user.role === 'MANAGER') {
    // 店面管理员可以看到自己店面的所有分析记录
    where.cafeId = req.user.cafeId
  } else {
    // 普通用户只能看到自己的分析记录
    where.userId = req.user.id
  }

  // 添加状态过滤
  if (status) {
    where.status = status
  }

  const [analyses, total] = await Promise.all([
    prisma.aIAnalysisRecord.findMany({
      where,
      skip,
      take: parseInt(limit),
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: { id: true, name: true, email: true }
        },
        cafe: {
          select: { id: true, name: true }
        }
      }
    }),
    prisma.aIAnalysisRecord.count({ where })
  ])

  const totalPages = Math.ceil(total / limit)

  res.json({
    success: true,
    data: {
      analyses: analyses.map(analysis => ({
        ...analysis,
        userName: analysis.user.name,
        userEmail: analysis.user.email,
        cafeName: analysis.cafe.name
      })),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  })
}))

/**
 * @swagger
 * /api/ai-analysis/{id}:
 *   get:
 *     summary: Get AI analysis record details
 *     tags: [AI Analysis]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Analysis record retrieved successfully
 */
/**
 * @swagger
 * /api/ai-analysis/stats:
 *   get:
 *     summary: Get AI analysis statistics
 *     tags: [AI Analysis]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Analysis statistics retrieved successfully
 */
router.get('/stats', [
  authMiddleware,
], asyncHandler(async (req: any, res: Response) => {
  // 构建查询条件
  let where: any = {}

  // 根据用户角色调整统计范围
  if (req.user.role === 'ADMIN') {
    where = {}
  } else if (req.user.role === 'MANAGER') {
    where.cafeId = req.user.cafeId
  } else {
    where.userId = req.user.id
  }

  // 获取7天前的日期
  const sevenDaysAgo = new Date()
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

  const [
    totalAnalyses,
    completedAnalyses,
    failedAnalyses,
    processingAnalyses,
    recentAnalyses,
    avgProcessingTime
  ] = await Promise.all([
    prisma.aIAnalysisRecord.count({ where }),
    prisma.aIAnalysisRecord.count({ where: { ...where, status: 'completed' } }),
    prisma.aIAnalysisRecord.count({ where: { ...where, status: 'error' } }),
    prisma.aIAnalysisRecord.count({ where: { ...where, status: 'analyzing' } }),
    prisma.aIAnalysisRecord.count({ 
      where: { 
        ...where, 
        createdAt: { gte: sevenDaysAgo } 
      } 
    }),
    prisma.aIAnalysisRecord.aggregate({
      where: { ...where, processingTime: { not: null } },
      _avg: { processingTime: true }
    })
  ])

  res.json({
    success: true,
    data: {
      totalAnalyses,
      completedAnalyses,
      failedAnalyses,
      processingAnalyses,
      avgProcessingTime: avgProcessingTime._avg.processingTime || 0,
      recentAnalyses
    }
  })
}))

router.get('/:id', [
  authMiddleware,
  param('id').isUUID(),
], asyncHandler(async (req: any, res: Response) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    throw new AppError('Invalid analysis ID', 400)
  }

  const { id } = req.params

  // 构建查询条件
  let where: any = { id }

  // 根据用户角色调整权限
  if (req.user.role === 'ADMIN') {
    // 管理员可以查看所有记录
    where = { id }
  } else if (req.user.role === 'MANAGER') {
    // 店面管理员只能查看自己店面的记录
    where = { id, cafeId: req.user.cafeId }
  } else {
    // 普通用户只能查看自己的记录
    where = { id, userId: req.user.id }
  }

  const analysis = await prisma.aIAnalysisRecord.findFirst({
    where,
    include: {
      user: {
        select: { id: true, name: true, email: true }
      },
      cafe: {
        select: { id: true, name: true }
      }
    }
  })

  if (!analysis) {
    throw new AppError('Analysis record not found', 404)
  }

  res.json({
    success: true,
    data: {
      analysis: {
        ...analysis,
        userName: analysis.user.name,
        userEmail: analysis.user.email,
        cafeName: analysis.cafe.name
      }
    }
  })
}))

/**
 * @swagger
 * /api/ai-analysis:
 *   post:
 *     summary: Save AI analysis record
 *     tags: [AI Analysis]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - fileName
 *               - imageUrl
 *               - analysisResult
 *             properties:
 *               fileName:
 *                 type: string
 *               imageUrl:
 *                 type: string
 *               analysisResult:
 *                 type: string
 *               processingTime:
 *                 type: integer
 *               customPrompt:
 *                 type: string
 *               status:
 *                 type: string
 *     responses:
 *       201:
 *         description: Analysis record saved successfully
 */
router.post('/', [
  authMiddleware,
  body('fileName').isLength({ min: 1, max: 255 }).trim(),
  body('imageUrl').isURL(),
  body('analysisResult').isLength({ min: 1 }),
  body('processingTime').optional().isInt({ min: 0 }),
  body('customPrompt').optional().isLength({ max: 2000 }),
  body('status').optional().isIn(['analyzing', 'completed', 'error']),
], asyncHandler(async (req: any, res: Response) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    throw new AppError('Invalid input data', 400)
  }

  const {
    fileName,
    imageUrl,
    analysisResult,
    processingTime = null,
    customPrompt = null,
    status = 'completed'
  } = req.body

  const analysis = await prisma.aIAnalysisRecord.create({
    data: {
      userId: req.user.id,
      cafeId: req.user.cafeId,
      fileName,
      imageUrl,
      analysisResult,
      processingTime,
      customPrompt,
      status
    },
    include: {
      user: {
        select: { id: true, name: true, email: true }
      },
      cafe: {
        select: { id: true, name: true }
      }
    }
  })

  logger.info(`AI analysis record saved: ${fileName} by user ${req.user.email}`)

  res.status(201).json({
    success: true,
    data: {
      analysis: {
        ...analysis,
        userName: analysis.user.name,
        userEmail: analysis.user.email,
        cafeName: analysis.cafe.name
      }
    },
    message: 'Analysis record saved successfully'
  })
}))

/**
 * @swagger
 * /api/ai-analysis/{id}:
 *   delete:
 *     summary: Delete AI analysis record
 *     tags: [AI Analysis]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Analysis record deleted successfully
 */
router.delete('/:id', [
  authMiddleware,
  param('id').isUUID(),
], asyncHandler(async (req: any, res: Response) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    throw new AppError('Invalid analysis ID', 400)
  }

  const { id } = req.params

  // 构建查询条件以检查权限
  let where: any = { id }

  if (req.user.role === 'ADMIN') {
    // 管理员可以删除所有记录
    where = { id }
  } else if (req.user.role === 'MANAGER') {
    // 店面管理员只能删除自己店面的记录
    where = { id, cafeId: req.user.cafeId }
  } else {
    // 普通用户只能删除自己的记录
    where = { id, userId: req.user.id }
  }

  // 先查找记录以获取文件名并验证权限
  const analysis = await prisma.aIAnalysisRecord.findFirst({
    where,
    select: { fileName: true }
  })

  if (!analysis) {
    throw new AppError('Analysis record not found or no permission to delete', 404)
  }

  // 删除记录
  await prisma.aIAnalysisRecord.delete({
    where: { id }
  })

  logger.info(`AI analysis record deleted: ${analysis.fileName} (${id}) by user ${req.user.email}`)

  res.json({
    success: true,
    message: 'Analysis record deleted successfully'
  })
}))

export default router