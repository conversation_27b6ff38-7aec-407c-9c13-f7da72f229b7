import { Router, Request, Response } from 'express'
import { body, query, param, validationResult } from 'express-validator'
import { getDatabase } from '../config/database'
import { authMiddleware } from '../middleware/auth'
import { AppError, asyncHandler } from '../middleware/errorHandler'
import { logger } from '../utils/logger'

const router = Router()

// 权限检查中间件
const checkProductManagementPermission = (req: any, res: Response, next: any) => {
  if (req.user.role !== 'USER' && req.user.role !== 'MANAGER' && req.user.role !== 'ADMIN') {
    throw new AppError('Only managers and operators can manage products', 403)
  }
  next()
}

/**
 * @swagger
 * components:
 *   schemas:
 *     Product:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         cafeId:
 *           type: string
 *         creatorId:
 *           type: string
 *         name:
 *           type: string
 *         type:
 *           type: string
 *         description:
 *           type: string
 *         price:
 *           type: number
 *         volume:
 *           type: string
 *         status:
 *           type: string
 *         ingredients:
 *           type: array
 *         nutritionPer100g:
 *           type: object
 *         benefits:
 *           type: array
 *         certifications:
 *           type: array
 *         shelfLifeDays:
 *           type: integer
 */

/**
 * @swagger
 * /api/products:
 *   get:
 *     summary: Get products list (filtered by user permissions)
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Items per page
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: Product type filter
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Product status filter
 *     responses:
 *       200:
 *         description: Products retrieved successfully
 */
router.get('/', [
  authMiddleware,
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('type').optional().isString(),
  query('status').optional().isIn(['active', 'inactive', 'discontinued']),
], asyncHandler(async (req: any, res: Response) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    throw new AppError('Invalid query parameters', 400)
  }

  const { page = 1, limit = 20, type, status } = req.query
  const offset = (page - 1) * limit
  const db = getDatabase()

  let whereClause = ''
  let queryParams: any[] = []
  let paramIndex = 1

  // 根据用户角色决定数据过滤策略
  if (req.user.role === 'ADMIN') {
    // 管理员可以看到所有产品
    whereClause = 'WHERE 1=1'
  } else if (req.user.role === 'MANAGER') {
    // 店面管理员可以看到自己店面的所有产品
    whereClause = 'WHERE p.cafe_id = $' + paramIndex
    queryParams.push(req.user.cafeId)
    paramIndex++
  } else if (req.user.role === 'USER') {
    // 操作员只能看到自己创建的产品
    whereClause = 'WHERE p.creator_id = $' + paramIndex
    queryParams.push(req.user.id)
    paramIndex++
  } else if (req.user.role === 'VIEWER') {
    // 消费者只能看到自己咖啡厅的产品，且必须是激活状态
    whereClause = 'WHERE p.cafe_id = $' + paramIndex + ' AND p.status = $' + (paramIndex + 1)
    queryParams.push(req.user.cafeId, 'active')
    paramIndex += 2
  }

  // 添加类型过滤
  if (type) {
    whereClause += ` AND p.type = $${paramIndex}`
    queryParams.push(type)
    paramIndex++
  }

  // 添加状态过滤（只对操作员和管理员有效）
  if (status && req.user.role !== 'VIEWER') {
    whereClause += ` AND p.status = $${paramIndex}`
    queryParams.push(status)
    paramIndex++
  }

  const productsQuery = `
    SELECT 
      p.*,
      c.name as cafe_name,
      u.name as creator_name,
      u.email as creator_email
    FROM products p
    LEFT JOIN cafes c ON p.cafe_id = c.id
    LEFT JOIN users u ON p.creator_id = u.id
    ${whereClause}
    ORDER BY p.created_at DESC
    LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
  `

  const countQuery = `
    SELECT COUNT(*) as total
    FROM products p
    ${whereClause}
  `

  queryParams.push(limit, offset)

  const [productsResult, countResult] = await Promise.all([
    db.query(productsQuery, queryParams),
    db.query(countQuery, queryParams.slice(0, -2)) // 移除 limit 和 offset
  ])

  const total = parseInt(countResult.rows[0].total)
  const totalPages = Math.ceil(total / limit)

  res.json({
    success: true,
    data: {
      products: productsResult.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  })
}))

/**
 * @swagger
 * /api/products/{id}:
 *   get:
 *     summary: Get product details
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Product details retrieved successfully
 *       404:
 *         description: Product not found
 */
router.get('/:id', [
  authMiddleware,
  param('id').isUUID(),
], asyncHandler(async (req: any, res: Response) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    throw new AppError('Invalid product ID', 400)
  }

  const { id } = req.params
  const db = getDatabase()

  let whereClause = 'WHERE p.id = $1'
  let queryParams = [id]

  // 根据用户角色添加权限过滤
  if (req.user.role === 'MANAGER') {
    whereClause += ' AND p.cafe_id = $2'
    queryParams.push(req.user.cafeId)
  } else if (req.user.role === 'USER') {
    whereClause += ' AND p.creator_id = $2'
    queryParams.push(req.user.id)
  } else if (req.user.role === 'VIEWER') {
    whereClause += ' AND p.cafe_id = $2 AND p.status = $3'
    queryParams.push(req.user.cafeId, 'active')
  }

  const productQuery = `
    SELECT 
      p.*,
      c.name as cafe_name,
      c.address as cafe_address,
      u.name as creator_name,
      u.email as creator_email
    FROM products p
    LEFT JOIN cafes c ON p.cafe_id = c.id
    LEFT JOIN users u ON p.creator_id = u.id
    ${whereClause}
  `

  const productResult = await db.query(productQuery, queryParams)

  if (productResult.rows.length === 0) {
    throw new AppError('Product not found', 404)
  }

  res.json({
    success: true,
    data: {
      product: productResult.rows[0]
    }
  })
}))

/**
 * @swagger
 * /api/products:
 *   post:
 *     summary: Create new product (Managers and Operators only)
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - type
 *               - price
 *               - volume
 *             properties:
 *               name:
 *                 type: string
 *               type:
 *                 type: string
 *               description:
 *                 type: string
 *               price:
 *                 type: number
 *               volume:
 *                 type: string
 *     responses:
 *       201:
 *         description: Product created successfully
 */
router.post('/', [
  authMiddleware,
  checkProductManagementPermission,
  body('name').isLength({ min: 2, max: 100 }).trim(),
  body('type').isLength({ min: 2, max: 50 }).trim(),
  body('description').optional().isLength({ max: 500 }),
  body('price').isFloat({ min: 0 }),
  body('volume').isLength({ min: 1, max: 20 }),
  body('ingredients').optional().isArray(),
  body('nutritionPer100g').optional().isObject(),
  body('benefits').optional().isArray(),
  body('certifications').optional().isArray(),
  body('shelfLifeDays').optional().isInt({ min: 1, max: 365 }),
], asyncHandler(async (req: any, res: Response) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    throw new AppError('Invalid input data', 400)
  }

  const {
    name,
    type,
    description,
    price,
    volume,
    ingredients = [],
    nutritionPer100g = {},
    benefits = [],
    certifications = [],
    shelfLifeDays = 21
  } = req.body

  const db = getDatabase()

  const insertQuery = `
    INSERT INTO products (
      cafe_id, creator_id, name, type, description, price, volume,
      ingredients, nutrition_per_100g, benefits, certifications, shelf_life_days,
      status, created_at, updated_at
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, NOW(), NOW())
    RETURNING *
  `

  const result = await db.query(insertQuery, [
    req.user.cafeId,
    req.user.id,
    name,
    type,
    description,
    price,
    volume,
    JSON.stringify(ingredients),
    JSON.stringify(nutritionPer100g),
    JSON.stringify(benefits),
    JSON.stringify(certifications),
    shelfLifeDays,
    'active'
  ])

  logger.info(`Product created: ${name} by user ${req.user.email}`)

  res.status(201).json({
    success: true,
    data: {
      product: result.rows[0]
    },
    message: 'Product created successfully'
  })
}))

/**
 * @swagger
 * /api/products/{id}:
 *   put:
 *     summary: Update product (Managers and Operators only)
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Product updated successfully
 */
router.put('/:id', [
  authMiddleware,
  checkProductManagementPermission,
  param('id').isUUID(),
  body('name').optional().isLength({ min: 2, max: 100 }).trim(),
  body('type').optional().isLength({ min: 2, max: 50 }).trim(),
  body('description').optional().isLength({ max: 500 }),
  body('price').optional().isFloat({ min: 0 }),
  body('volume').optional().isLength({ min: 1, max: 20 }),
  body('status').optional().isIn(['active', 'inactive', 'discontinued']),
], asyncHandler(async (req: any, res: Response) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    throw new AppError('Invalid input data', 400)
  }

  const { id } = req.params
  const updates = req.body
  const db = getDatabase()

  // 检查产品是否存在且用户有权限修改
  let checkQuery = 'SELECT id FROM products WHERE id = $1'
  let checkParams = [id]

  if (req.user.role === 'USER') {
    // 操作员只能修改自己创建的产品
    checkQuery += ' AND creator_id = $2'
    checkParams.push(req.user.id)
  } else if (req.user.role === 'MANAGER') {
    // 店面管理员可以修改自己店面内的所有产品
    checkQuery += ' AND cafe_id = $2'
    checkParams.push(req.user.cafeId)
  }

  const checkResult = await db.query(checkQuery, checkParams)

  if (checkResult.rows.length === 0) {
    throw new AppError('Product not found or no permission to update', 404)
  }

  // 构建更新查询
  const updateFields = []
  const updateValues = []
  let paramIndex = 1

  for (const [key, value] of Object.entries(updates)) {
    if (value !== undefined) {
      const dbKey = key === 'nutritionPer100g' ? 'nutrition_per_100g' : 
                    key === 'shelfLifeDays' ? 'shelf_life_days' : key
      
      if (['ingredients', 'nutrition_per_100g', 'benefits', 'certifications', 'images', 'documents'].includes(dbKey)) {
        updateFields.push(`${dbKey} = $${paramIndex}`)
        updateValues.push(JSON.stringify(value))
      } else {
        updateFields.push(`${dbKey} = $${paramIndex}`)
        updateValues.push(value)
      }
      paramIndex++
    }
  }

  if (updateFields.length === 0) {
    throw new AppError('No valid fields to update', 400)
  }

  updateFields.push(`updated_at = NOW()`)
  updateValues.push(id)

  const updateQuery = `
    UPDATE products 
    SET ${updateFields.join(', ')}
    WHERE id = $${paramIndex}
    RETURNING *
  `

  const result = await db.query(updateQuery, updateValues)

  logger.info(`Product updated: ${id} by user ${req.user.email}`)

  res.json({
    success: true,
    data: {
      product: result.rows[0]
    },
    message: 'Product updated successfully'
  })
}))

/**
 * @swagger
 * /api/products/{id}:
 *   delete:
 *     summary: Delete product (Managers and Operators only)
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Product deleted successfully
 */
router.delete('/:id', [
  authMiddleware,
  checkProductManagementPermission,
  param('id').isUUID(),
], asyncHandler(async (req: any, res: Response) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    throw new AppError('Invalid product ID', 400)
  }

  const { id } = req.params
  const db = getDatabase()

  // 检查产品是否存在且用户有权限删除
  let checkQuery = 'SELECT name FROM products WHERE id = $1'
  let checkParams = [id]

  if (req.user.role === 'USER') {
    // 操作员只能删除自己创建的产品
    checkQuery += ' AND creator_id = $2'
    checkParams.push(req.user.id)
  } else if (req.user.role === 'MANAGER') {
    // 店面管理员可以删除自己店面内的所有产品
    checkQuery += ' AND cafe_id = $2'
    checkParams.push(req.user.cafeId)
  }

  const checkResult = await db.query(checkQuery, checkParams)

  if (checkResult.rows.length === 0) {
    throw new AppError('Product not found or no permission to delete', 404)
  }

  const productName = checkResult.rows[0].name

  // 软删除：将状态设为 discontinued
  const updateQuery = `
    UPDATE products 
    SET status = 'discontinued', updated_at = NOW()
    WHERE id = $1
    RETURNING id
  `

  await db.query(updateQuery, [id])

  logger.info(`Product soft-deleted: ${productName} (${id}) by user ${req.user.email}`)

  res.json({
    success: true,
    message: 'Product deleted successfully'
  })
}))

export default router