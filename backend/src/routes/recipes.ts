import { Router, Request, Response } from 'express'
import prisma from '../lib/prisma'
import { asyncHand<PERSON> } from '../middleware/errorHandler'
import { authMiddleware, requireManagerLevel } from '../middleware/auth'

// 简化验证（暂时不使用zod，直接验证）
const validateRecipeData = (data: any) => {
  const errors: string[] = []
  
  if (!data.name || typeof data.name !== 'string' || data.name.length > 100) {
    errors.push('名称是必填项，且长度不能超过100字符')
  }
  
  if (!Array.isArray(data.ingredients) || data.ingredients.length === 0) {
    errors.push('配料列表不能为空')
  }
  
  if (!Array.isArray(data.process) || data.process.length === 0) {
    errors.push('制作流程不能为空')
  }
  
  return errors
}

const router = Router()

// 应用认证中间件
router.use(authMiddleware)


/**
 * @swagger
 * /api/recipes:
 *   get:
 *     summary: Get all recipes
 *     tags: [Recipes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *     responses:
 *       200:
 *         description: Recipes retrieved successfully
 */
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const page = parseInt(req.query.page as string) || 1
  const limit = parseInt(req.query.limit as string) || 10
  const search = req.query.search as string
  const isActive = req.query.isActive ? req.query.isActive === 'true' : undefined
  const skip = (page - 1) * limit

  const where: any = {}
  
  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } }
    ]
  }
  
  if (isActive !== undefined) {
    where.isActive = isActive
  }

  const [recipes, total] = await Promise.all([
    prisma.recipe.findMany({
      where,
      include: {
        user: {
          select: { id: true, name: true, email: true }
        },
        _count: {
          select: { batches: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit
    }),
    prisma.recipe.count({ where })
  ])

  res.json({
    success: true,
    data: {
      recipes,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  })
}))

/**
 * @swagger
 * /api/recipes/{id}:
 *   get:
 *     summary: Get recipe by ID
 *     tags: [Recipes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Recipe retrieved successfully
 *       404:
 *         description: Recipe not found
 */
router.get('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params

  const recipe = await prisma.recipe.findUnique({
    where: { id },
    include: {
      user: {
        select: { id: true, name: true, email: true }
      },
      batches: {
        select: {
          id: true,
          batchNumber: true,
          status: true,
          productionDate: true,
          quantity: true
        },
        orderBy: { createdAt: 'desc' }
      }
    }
  })

  if (!recipe) {
    return res.status(404).json({
      success: false,
      message: '配方不存在'
    })
  }

  res.json({
    success: true,
    data: { recipe }
  })
}))

/**
 * @swagger
 * /api/recipes:
 *   post:
 *     summary: Create a new recipe
 *     tags: [Recipes]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               ingredients:
 *                 type: array
 *               process:
 *                 type: array
 *               fermentationTemperature:
 *                 type: number
 *               fermentationDuration:
 *                 type: integer
 *               filtrationDuration:
 *                 type: integer
 *     responses:
 *       201:
 *         description: Recipe created successfully
 *       400:
 *         description: Invalid input
 */
router.post('/', requireManagerLevel, asyncHandler(async (req: Request, res: Response) => {
  const errors = validateRecipeData(req.body)
  
  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: '输入数据无效',
      errors
    })
  }

  const userId = (req as any).user.id
  const data = req.body

  const recipe = await prisma.recipe.create({
    data: {
      ...data,
      userId
    },
    include: {
      user: {
        select: { id: true, name: true, email: true }
      }
    }
  })

  res.status(201).json({
    success: true,
    data: { recipe },
    message: '配方创建成功'
  })
}))

/**
 * @swagger
 * /api/recipes/{id}:
 *   put:
 *     summary: Update recipe
 *     tags: [Recipes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Recipe updated successfully
 *       404:
 *         description: Recipe not found
 *       403:
 *         description: Not authorized to update this recipe
 */
router.put('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params
  const userId = (req as any).user.id
  const userRole = (req as any).user.role

  // 对于更新，只验证提供的字段
  if (req.body.name && (typeof req.body.name !== 'string' || req.body.name.length > 100)) {
    return res.status(400).json({
      success: false,
      message: '名称格式无效'
    })
  }

  const existingRecipe = await prisma.recipe.findUnique({
    where: { id },
    select: { userId: true }
  })

  if (!existingRecipe) {
    return res.status(404).json({
      success: false,
      message: '配方不存在'
    })
  }

  // 检查权限：只有配方创建者或管理员可以编辑
  if (existingRecipe.userId !== userId && userRole !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '无权限编辑此配方'
    })
  }

  const recipe = await prisma.recipe.update({
    where: { id },
    data: req.body,
    include: {
      user: {
        select: { id: true, name: true, email: true }
      }
    }
  })

  res.json({
    success: true,
    data: { recipe },
    message: '配方更新成功'
  })
}))

/**
 * @swagger
 * /api/recipes/{id}:
 *   delete:
 *     summary: Delete recipe
 *     tags: [Recipes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Recipe deleted successfully
 *       404:
 *         description: Recipe not found
 *       403:
 *         description: Not authorized to delete this recipe
 */
router.delete('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params
  const userId = (req as any).user.id
  const userRole = (req as any).user.role

  const existingRecipe = await prisma.recipe.findUnique({
    where: { id },
    include: {
      _count: {
        select: { batches: true }
      }
    }
  })

  if (!existingRecipe) {
    return res.status(404).json({
      success: false,
      message: '配方不存在'
    })
  }

  // 检查权限
  if (existingRecipe.userId !== userId && userRole !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '无权限删除此配方'
    })
  }

  // 检查是否有关联的批次
  if (existingRecipe._count.batches > 0) {
    return res.status(400).json({
      success: false,
      message: '无法删除：该配方已有关联的生产批次'
    })
  }

  await prisma.recipe.delete({
    where: { id }
  })

  res.json({
    success: true,
    message: '配方删除成功'
  })
}))

/**
 * @swagger
 * /api/recipes/{id}/duplicate:
 *   post:
 *     summary: Duplicate a recipe
 *     tags: [Recipes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       201:
 *         description: Recipe duplicated successfully
 *       404:
 *         description: Recipe not found
 */
router.post('/:id/duplicate', requireManagerLevel, asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params
  const userId = (req as any).user.id

  const originalRecipe = await prisma.recipe.findUnique({
    where: { id },
    select: {
      name: true,
      description: true,
      ingredients: true,
      process: true,
      fermentationTemperature: true,
      fermentationDuration: true,
      filtrationDuration: true
    }
  })

  if (!originalRecipe) {
    return res.status(404).json({
      success: false,
      message: '配方不存在'
    })
  }

  const duplicatedRecipe = await prisma.recipe.create({
    data: {
      name: `${originalRecipe.name} (副本)`,
      description: originalRecipe.description,
      ingredients: originalRecipe.ingredients as any,
      process: originalRecipe.process as any,
      fermentationTemperature: originalRecipe.fermentationTemperature,
      fermentationDuration: originalRecipe.fermentationDuration,
      filtrationDuration: originalRecipe.filtrationDuration,
      userId,
      version: 1
    },
    include: {
      user: {
        select: { id: true, name: true, email: true }
      }
    }
  })

  res.status(201).json({
    success: true,
    data: { recipe: duplicatedRecipe },
    message: '配方复制成功'
  })
}))

export default router
