import express from 'express'
import authMiddleware from '../middleware/auth'
import { PrismaClient } from '@prisma/client'

const router = express.Router()
const prisma = new PrismaClient()

// 通知类型枚举
export enum NotificationType {
  BATCH_COMPLETED = 'BATCH_COMPLETED',
  BATCH_FAILED = 'BATCH_FAILED', 
  QUALITY_ALERT = 'QUALITY_ALERT',
  RECIPE_UPDATED = 'RECIPE_UPDATED',
  SYSTEM_MAINTENANCE = 'SYSTEM_MAINTENANCE',
  USER_MENTION = 'USER_MENTION',
  REPORT_READY = 'REPORT_READY'
}

// 获取用户通知列表
router.get('/', authMiddleware, async (req, res) => {
  try {
    const userId = req.user?.id
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '未授权访问'
      })
    }

    const { page = 1, limit = 20, unreadOnly = false } = req.query

    const where = {
      userId: userId,
      ...(unreadOnly === 'true' ? { isRead: false } : {})
    }

    const notifications = await prisma.notification.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      skip: (Number(page) - 1) * Number(limit),
      take: Number(limit),
      select: {
        id: true,
        type: true,
        title: true,
        content: true,
        isRead: true,
        data: true,
        createdAt: true,
        updatedAt: true
      }
    })

    const total = await prisma.notification.count({ where })
    const unreadCount = await prisma.notification.count({
      where: { userId: userId, isRead: false }
    })

    res.json({
      success: true,
      data: {
        notifications,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages: Math.ceil(total / Number(limit))
        },
        unreadCount
      }
    })
  } catch (error) {
    console.error('获取通知列表失败:', error)
    res.status(500).json({
      success: false,
      message: '获取通知列表失败'
    })
  }
})

// 标记通知为已读
router.patch('/:id/read', authMiddleware, async (req, res) => {
  try {
    const userId = req.user?.id
    const notificationId = req.params.id

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '未授权访问'
      })
    }

    const notification = await prisma.notification.findFirst({
      where: {
        id: notificationId,
        userId: userId
      }
    })

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: '通知不存在'
      })
    }

    await prisma.notification.update({
      where: { id: notificationId },
      data: { isRead: true, updatedAt: new Date() }
    })

    res.json({
      success: true,
      message: '通知已标记为已读'
    })
  } catch (error) {
    console.error('标记通知已读失败:', error)
    res.status(500).json({
      success: false,
      message: '标记通知已读失败'
    })
  }
})

// 批量标记通知为已读
router.patch('/batch/read', authMiddleware, async (req, res) => {
  try {
    const userId = req.user?.id
    const { notificationIds } = req.body

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '未授权访问'
      })
    }

    if (!Array.isArray(notificationIds) || notificationIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供有效的通知ID列表'
      })
    }

    await prisma.notification.updateMany({
      where: {
        id: { in: notificationIds },
        userId: userId
      },
      data: { isRead: true, updatedAt: new Date() }
    })

    res.json({
      success: true,
      message: '通知已批量标记为已读'
    })
  } catch (error) {
    console.error('批量标记通知已读失败:', error)
    res.status(500).json({
      success: false,
      message: '批量标记通知已读失败'
    })
  }
})

// 标记所有通知为已读
router.patch('/all/read', authMiddleware, async (req, res) => {
  try {
    const userId = req.user?.id

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '未授权访问'
      })
    }

    await prisma.notification.updateMany({
      where: {
        userId: userId,
        isRead: false
      },
      data: { isRead: true, updatedAt: new Date() }
    })

    res.json({
      success: true,
      message: '所有通知已标记为已读'
    })
  } catch (error) {
    console.error('标记所有通知已读失败:', error)
    res.status(500).json({
      success: false,
      message: '标记所有通知已读失败'
    })
  }
})

// 删除通知
router.delete('/:id', authMiddleware, async (req, res) => {
  try {
    const userId = req.user?.id
    const notificationId = req.params.id

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '未授权访问'
      })
    }

    const notification = await prisma.notification.findFirst({
      where: {
        id: notificationId,
        userId: userId
      }
    })

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: '通知不存在'
      })
    }

    await prisma.notification.delete({
      where: { id: notificationId }
    })

    res.json({
      success: true,
      message: '通知删除成功'
    })
  } catch (error) {
    console.error('删除通知失败:', error)
    res.status(500).json({
      success: false,
      message: '删除通知失败'
    })
  }
})

// 获取未读通知数量
router.get('/unread-count', authMiddleware, async (req, res) => {
  try {
    const userId = req.user?.id

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '未授权访问'
      })
    }

    const unreadCount = await prisma.notification.count({
      where: {
        userId: userId,
        isRead: false
      }
    })

    res.json({
      success: true,
      data: { unreadCount }
    })
  } catch (error) {
    console.error('获取未读通知数量失败:', error)
    res.status(500).json({
      success: false,
      message: '获取未读通知数量失败'
    })
  }
})

// 创建新通知 (系统内部使用)
export const createNotification = async (
  userId: string,
  type: NotificationType,
  title: string,
  content: string,
  data?: any
) => {
  try {
    await prisma.notification.create({
      data: {
        userId,
        type,
        title,
        content,
        data: data ? JSON.stringify(data) : null,
        isRead: false,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    })
  } catch (error) {
    console.error('创建通知失败:', error)
  }
}

export default router