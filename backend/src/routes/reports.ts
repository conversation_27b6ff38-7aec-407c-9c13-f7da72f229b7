import { Router, Request, Response } from 'express'
import { asyncHand<PERSON> } from '../middleware/errorHandler'

const router = Router()

/**
 * @swagger
 * /api/reports:
 *   get:
 *     summary: Get all reports
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Reports retrieved successfully
 */
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  res.json({
    success: true,
    data: {
      reports: [],
      message: 'Reports endpoint - implementation pending',
    },
  })
}))

export default router
