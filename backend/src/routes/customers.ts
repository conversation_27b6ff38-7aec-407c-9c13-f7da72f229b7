import { Router, Request, Response } from 'express';
import { getDatabase } from '../config/database';
import { authMiddleware } from '../middleware/auth';
import { logger } from '../utils/logger';

const router = Router();

// 益生菌健康指数计算函数
async function calculateProbioticHealthIndex(userId: string, cafeId: string, db: any) {
  try {
    // 获取最近3个月的购买数据
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

    // 1. 购买频率分析 (30%)
    const frequencyQuery = `
      SELECT 
        COUNT(*) as total_orders,
        COUNT(DISTINCT DATE_TRUNC('week', order_date)) as weeks_with_orders,
        MAX(order_date) as last_order_date,
        MIN(order_date) as first_order_date
      FROM orders 
      WHERE customer_id = $1 AND cafe_id = $2 AND status = 'completed'
        AND order_date >= $3
    `;
    const frequencyResult = await db.query(frequencyQuery, [userId, cafeId, threeMonthsAgo]);
    const frequency = frequencyResult.rows[0];
    
    // 计算购买频率得分 (每周购买1-2次为最佳)
    const weeksActive = parseInt(frequency.weeks_with_orders) || 0;
    const totalOrders = parseInt(frequency.total_orders) || 0;
    const avgOrdersPerWeek = weeksActive > 0 ? totalOrders / weeksActive : 0;
    const frequencyScore = Math.min(100, (avgOrdersPerWeek * 50)); // 每周2次=100分

    // 2. 产品多样性分析 (25%)
    const diversityQuery = `
      SELECT COUNT(DISTINCT p.type) as unique_types,
             COUNT(DISTINCT oi.product_id) as unique_products
      FROM order_items oi
      JOIN orders o ON oi.order_id = o.id
      JOIN products p ON oi.product_id = p.id
      WHERE o.customer_id = $1 AND o.cafe_id = $2 AND o.status = 'completed'
        AND o.order_date >= $3
    `;
    const diversityResult = await db.query(diversityQuery, [userId, cafeId, threeMonthsAgo]);
    const diversity = diversityResult.rows[0];
    
    // 计算多样性得分 (尝试3种以上类型为满分)
    const uniqueTypes = parseInt(diversity.unique_types) || 0;
    const uniqueProducts = parseInt(diversity.unique_products) || 0;
    const diversityScore = Math.min(100, (uniqueTypes * 25) + (uniqueProducts * 10));

    // 3. 消费连续性分析 (20%)
    const continuityQuery = `
      SELECT 
        order_date,
        LAG(order_date) OVER (ORDER BY order_date) as prev_order_date
      FROM orders 
      WHERE customer_id = $1 AND cafe_id = $2 AND status = 'completed'
        AND order_date >= $3
      ORDER BY order_date
    `;
    const continuityResult = await db.query(continuityQuery, [userId, cafeId, threeMonthsAgo]);
    
    // 计算连续性得分 (订单间隔越小越好，超过14天扣分)
    let continuityScore = 100;
    if (continuityResult.rows.length > 1) {
      const gaps = continuityResult.rows.slice(1).map((row: any) => {
        if (row.prev_order_date) {
          const daysDiff = Math.floor((new Date(row.order_date).getTime() - new Date(row.prev_order_date).getTime()) / (1000 * 60 * 60 * 24));
          return daysDiff;
        }
        return 0;
      }).filter((gap: number) => gap > 0);
      
      const avgGap = gaps.length > 0 ? gaps.reduce((sum: number, gap: number) => sum + gap, 0) / gaps.length : 0;
      continuityScore = Math.max(0, 100 - (avgGap * 3)); // 每增加1天间隔扣3分
    }

    // 4. 活菌含量偏好分析 (15%)
    const probioticQuery = `
      SELECT 
        COALESCE(AVG(
          CASE 
            WHEN p.name ILIKE '%活菌%' OR p.name ILIKE '%益生菌%' THEN 100
            WHEN p.name ILIKE '%经典%' OR p.name ILIKE '%原味%' THEN 80
            WHEN p.name ILIKE '%发酵%' OR p.name ILIKE '%酸奶%' THEN 70
            ELSE 60
          END
        ), 60) as avg_probiotic_preference
      FROM order_items oi
      JOIN orders o ON oi.order_id = o.id
      JOIN products p ON oi.product_id = p.id
      WHERE o.customer_id = $1 AND o.cafe_id = $2 AND o.status = 'completed'
        AND o.order_date >= $3
    `;
    const probioticResult = await db.query(probioticQuery, [userId, cafeId, threeMonthsAgo]);
    const probioticScore = parseFloat(probioticResult.rows[0].avg_probiotic_preference) || 60;

    // 5. 饮食习惯匹配度 (10%)
    const preferencesQuery = `
      SELECT dietary_restrictions, preferred_size, taste_preferences
      FROM customer_preferences 
      WHERE customer_id = $1 AND cafe_id = $2
    `;
    const preferencesResult = await db.query(preferencesQuery, [userId, cafeId]);
    
    let dietMatchScore = 80; // 默认80分
    if (preferencesResult.rows.length > 0) {
      const restrictions = preferencesResult.rows[0].dietary_restrictions || [];
      const tastePrefs = preferencesResult.rows[0].taste_preferences || {};
      
      // 乳糖敏感用户选择酸奶是很好的选择
      if (restrictions.includes('lactose_sensitive')) {
        dietMatchScore = 95;
      }
      // 素食主义者选择酸奶也很合适
      if (restrictions.includes('vegetarian') || restrictions.includes('vegan')) {
        dietMatchScore = Math.max(dietMatchScore, 90);
      }
      
      // 根据口味偏好调整分数
      if (tastePrefs.acidity === 'medium' && tastePrefs.texture === 'smooth') {
        dietMatchScore = Math.max(dietMatchScore, 95); // 偏好经典酸奶口味
      }
    }

    // 综合计算最终指数
    const finalIndex = Math.round(
      (frequencyScore * 0.30) +
      (diversityScore * 0.25) +
      (continuityScore * 0.20) +
      (probioticScore * 0.15) +
      (dietMatchScore * 0.10)
    );

    // 确保指数在0-100之间
    const normalizedIndex = Math.max(0, Math.min(100, finalIndex));

    // 生成健康建议
    let healthAdvice = '';
    let indexLevel = '';
    let indexColor = '';

    if (normalizedIndex >= 85) {
      indexLevel = '优秀';
      indexColor = '#52c41a';
      healthAdvice = '您的益生菌摄入习惯非常优秀！请保持定期饮用活菌酸奶的好习惯。';
    } else if (normalizedIndex >= 70) {
      indexLevel = '良好';
      indexColor = '#1890ff';
      healthAdvice = '您的益生菌摄入习惯良好，建议增加饮用频率或尝试更多发酵产品种类。';
    } else if (normalizedIndex >= 55) {
      indexLevel = '中等';
      indexColor = '#faad14';
      healthAdvice = '建议您更规律地饮用酸奶，每周2-3次有助于维持肠道健康。';
    } else if (normalizedIndex >= 40) {
      indexLevel = '需改善';
      indexColor = '#fa8c16';
      healthAdvice = '建议您养成定期饮用活菌酸奶的习惯，有益于肠道菌群平衡。';
    } else {
      indexLevel = '较低';
      indexColor = '#f5222d';
      healthAdvice = '建议您开始规律摄入益生菌产品，选择适合的活菌酸奶并坚持饮用。';
    }

    return {
      index: normalizedIndex,
      level: indexLevel,
      color: indexColor,
      advice: healthAdvice,
      breakdown: {
        frequency: Math.round(frequencyScore),
        diversity: Math.round(diversityScore),
        continuity: Math.round(continuityScore),
        probioticPreference: Math.round(probioticScore),
        dietMatch: Math.round(dietMatchScore)
      }
    };

  } catch (error) {
    logger.error('计算益生菌健康指数失败:', error);
    // 返回默认值
    return {
      index: 65,
      level: '中等',
      color: '#faad14',
      advice: '建议您规律饮用活菌酸奶，有助于维持肠道健康。',
      breakdown: {
        frequency: 60,
        diversity: 50,
        continuity: 70,
        probioticPreference: 80,
        dietMatch: 80
      }
    };
  }
}

// 获取顾客统计数据
router.get('/stats', authMiddleware, async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    const cafeId = req.user?.cafeId;

    if (!userId || !cafeId) {
      return res.status(400).json({ error: '用户信息不完整' });
    }

    const db = getDatabase();

    // 获取订单统计
    const orderStatsQuery = `
      SELECT 
        COUNT(*) as total_orders,
        COALESCE(SUM(total_amount), 0) as total_spent,
        COALESCE(AVG(total_amount), 0) as avg_order_value
      FROM orders 
      WHERE customer_id = $1 AND cafe_id = $2 AND status = 'completed'
    `;
    const orderStats = await db.query(orderStatsQuery, [userId, cafeId]);

    // 获取积分余额
    const pointsQuery = `
      SELECT COALESCE(points_balance, 0) as loyalty_points
      FROM loyalty_points 
      WHERE customer_id = $1 AND cafe_id = $2
      ORDER BY created_at DESC
      LIMIT 1
    `;
    const pointsResult = await db.query(pointsQuery, [userId, cafeId]);

    // 获取最喜欢的产品
    const favoriteProductQuery = `
      SELECT p.name, COUNT(*) as purchase_count
      FROM order_items oi
      JOIN orders o ON oi.order_id = o.id
      JOIN products p ON oi.product_id = p.id
      WHERE o.customer_id = $1 AND o.cafe_id = $2 AND o.status = 'completed'
      GROUP BY p.id, p.name
      ORDER BY purchase_count DESC
      LIMIT 1
    `;
    const favoriteProduct = await db.query(favoriteProductQuery, [userId, cafeId]);

    // 获取最近订单
    const recentOrdersQuery = `
      SELECT 
        o.total_amount,
        o.order_date,
        STRING_AGG(p.name || ' x' || oi.quantity, ', ') as items
      FROM orders o
      JOIN order_items oi ON o.id = oi.order_id
      JOIN products p ON oi.product_id = p.id
      WHERE o.customer_id = $1 AND o.cafe_id = $2 AND o.status = 'completed'
      GROUP BY o.id, o.total_amount, o.order_date
      ORDER BY o.order_date DESC
      LIMIT 5
    `;
    const recentOrders = await db.query(recentOrdersQuery, [userId, cafeId]);

    // 获取月度购买统计
    const monthlyStatsQuery = `
      SELECT 
        TO_CHAR(o.order_date, 'YYYY-MM') as month,
        COUNT(*) as count,
        SUM(o.total_amount) as amount
      FROM orders o
      WHERE o.customer_id = $1 AND o.cafe_id = $2 AND o.status = 'completed'
        AND o.order_date >= CURRENT_DATE - INTERVAL '3 months'
      GROUP BY TO_CHAR(o.order_date, 'YYYY-MM')
      ORDER BY month DESC
    `;
    const monthlyStats = await db.query(monthlyStatsQuery, [userId, cafeId]);

    // 格式化数据
    const stats = orderStats.rows[0];
    const points = pointsResult.rows[0];
    const favorite = favoriteProduct.rows[0];

    const recentOrdersFormatted = recentOrders.rows.map(order => {
      const daysDiff = Math.floor((Date.now() - new Date(order.order_date).getTime()) / (1000 * 60 * 60 * 24));
      let dateStr = '今天';
      if (daysDiff === 1) dateStr = '1天前';
      else if (daysDiff > 1) dateStr = `${daysDiff}天前`;
      
      return {
        date: dateStr,
        amount: parseFloat(order.total_amount),
        items: order.items
      };
    });

    const monthlyPurchasesFormatted = monthlyStats.rows.map((month, index) => {
      let monthName = '本月';
      if (index === 1) monthName = '上月';
      else if (index === 2) monthName = '两月前';
      else if (index > 2) monthName = `${index + 1}月前`;
      
      return {
        month: monthName,
        count: parseInt(month.count),
        amount: parseFloat(month.amount)
      };
    });

    // 计算益生菌健康指数
    const probioticHealthIndex = await calculateProbioticHealthIndex(userId, cafeId, db);

    const response = {
      totalOrders: parseInt(stats.total_orders),
      totalSpent: parseFloat(stats.total_spent),
      loyaltyPoints: parseInt(points.loyalty_points),
      favoriteProduct: favorite?.name || '暂无',
      avgOrderValue: parseFloat(stats.avg_order_value),
      recentOrders: recentOrdersFormatted,
      monthlyPurchases: monthlyPurchasesFormatted,
      probioticHealthIndex: probioticHealthIndex
    };

    res.json(response);
  } catch (error) {
    logger.error('获取顾客统计数据失败:', error);
    res.status(500).json({ error: '获取统计数据失败' });
  }
});

// 获取顾客偏好
router.get('/preferences', authMiddleware, async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    const cafeId = req.user?.cafeId;

    if (!userId || !cafeId) {
      return res.status(400).json({ error: '用户信息不完整' });
    }

    const db = getDatabase();
    const query = `
      SELECT 
        favorite_products,
        taste_preferences,
        dietary_restrictions,
        preferred_size,
        preferred_time_slots,
        notes
      FROM customer_preferences 
      WHERE customer_id = $1 AND cafe_id = $2
    `;
    
    const result = await db.query(query, [userId, cafeId]);
    
    if (result.rows.length === 0) {
      return res.json({
        favoriteProducts: [],
        tastePreferences: {},
        dietaryRestrictions: [],
        preferredSize: '',
        preferredTimeSlots: [],
        notes: ''
      });
    }

    const preferences = result.rows[0];
    res.json({
      favoriteProducts: preferences.favorite_products || [],
      tastePreferences: preferences.taste_preferences || {},
      dietaryRestrictions: preferences.dietary_restrictions || [],
      preferredSize: preferences.preferred_size || '',
      preferredTimeSlots: preferences.preferred_time_slots || [],
      notes: preferences.notes || ''
    });
  } catch (error) {
    logger.error('获取顾客偏好失败:', error);
    res.status(500).json({ error: '获取偏好数据失败' });
  }
});

// 获取顾客反馈记录
router.get('/feedback', authMiddleware, async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    const cafeId = req.user?.cafeId;

    if (!userId || !cafeId) {
      return res.status(400).json({ error: '用户信息不完整' });
    }

    const db = getDatabase();
    const query = `
      SELECT 
        cf.id,
        cf.rating,
        cf.feedback_type,
        cf.title,
        cf.content,
        cf.response,
        cf.status,
        cf.created_at,
        p.name as product_name
      FROM customer_feedback cf
      LEFT JOIN products p ON cf.product_id = p.id
      WHERE cf.customer_id = $1 AND cf.cafe_id = $2
      ORDER BY cf.created_at DESC
    `;
    
    const result = await db.query(query, [userId, cafeId]);
    
    const feedback = result.rows.map(row => ({
      id: row.id,
      rating: row.rating,
      feedbackType: row.feedback_type,
      title: row.title,
      content: row.content,
      response: row.response,
      status: row.status,
      createdAt: row.created_at,
      productName: row.product_name
    }));

    res.json(feedback);
  } catch (error) {
    logger.error('获取顾客反馈失败:', error);
    res.status(500).json({ error: '获取反馈数据失败' });
  }
});

// 获取积分记录
router.get('/points', authMiddleware, async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    const cafeId = req.user?.cafeId;

    if (!userId || !cafeId) {
      return res.status(400).json({ error: '用户信息不完整' });
    }

    const db = getDatabase();
    const query = `
      SELECT 
        points_change,
        points_balance,
        transaction_type,
        description,
        created_at
      FROM loyalty_points 
      WHERE customer_id = $1 AND cafe_id = $2
      ORDER BY created_at DESC
      LIMIT 20
    `;
    
    const result = await db.query(query, [userId, cafeId]);
    
    const points = result.rows.map(row => ({
      pointsChange: row.points_change,
      pointsBalance: row.points_balance,
      transactionType: row.transaction_type,
      description: row.description,
      createdAt: row.created_at
    }));

    res.json(points);
  } catch (error) {
    logger.error('获取积分记录失败:', error);
    res.status(500).json({ error: '获取积分记录失败' });
  }
});

// 创建反馈
router.post('/feedback', authMiddleware, async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    const cafeId = req.user?.cafeId;
    const { productId, rating, feedbackType, title, content } = req.body;

    if (!userId || !cafeId) {
      return res.status(400).json({ error: '用户信息不完整' });
    }

    if (!title || !content) {
      return res.status(400).json({ error: '标题和内容不能为空' });
    }

    const db = getDatabase();
    const query = `
      INSERT INTO customer_feedback (
        cafe_id, customer_id, product_id, rating, feedback_type, title, content
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING id
    `;
    
    const result = await db.query(query, [
      cafeId, userId, productId || null, rating || null, feedbackType || 'general', title, content
    ]);

    res.json({ 
      success: true, 
      id: result.rows[0].id,
      message: '反馈提交成功' 
    });
  } catch (error) {
    logger.error('创建反馈失败:', error);
    res.status(500).json({ error: '提交反馈失败' });
  }
});

export default router;