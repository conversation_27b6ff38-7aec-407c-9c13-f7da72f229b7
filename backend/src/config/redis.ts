import Redis from 'ioredis'
import { config } from './index'
import { logger } from '../utils/logger'

let redisClient: Redis | null = null

export const connectRedis = async (): Promise<Redis> => {
  if (redisClient) {
    return redisClient
  }

  try {
    redisClient = new Redis({
      host: config.redis.host,
      port: config.redis.port,
      password: config.redis.password,
      db: config.redis.db,
      enableReadyCheck: true,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      connectTimeout: 10000,
      commandTimeout: 5000,
    })

    // 连接事件监听
    redisClient.on('connect', () => {
      logger.info('Redis connection established')
    })

    redisClient.on('ready', () => {
      logger.info('Redis client ready')
    })

    redisClient.on('error', (error) => {
      logger.error('Redis connection error:', error)
    })

    redisClient.on('close', () => {
      logger.warn('Redis connection closed')
    })

    redisClient.on('reconnecting', () => {
      logger.info('Redis reconnecting...')
    })

    // 建立连接
    await redisClient.connect()

    // 测试连接
    await redisClient.ping()
    logger.info('Redis connection test successful')

    return redisClient
  } catch (error) {
    logger.error('Failed to connect to Redis:', error)
    throw error
  }
}

export const getRedis = (): Redis => {
  if (!redisClient) {
    throw new Error('Redis not connected. Call connectRedis() first.')
  }
  return redisClient
}

export const closeRedis = async (): Promise<void> => {
  if (redisClient) {
    await redisClient.quit()
    redisClient = null
    logger.info('Redis connection closed')
  }
}

// 健康检查
export const checkRedisHealth = async (): Promise<boolean> => {
  try {
    if (!redisClient) {
      return false
    }
    
    const result = await redisClient.ping()
    return result === 'PONG'
  } catch (error) {
    logger.error('Redis health check failed:', error)
    return false
  }
}

// Redis 工具函数
export const redisUtils = {
  // 设置缓存
  setCache: async (key: string, value: any, ttl?: number): Promise<void> => {
    const client = getRedis()
    const serializedValue = JSON.stringify(value)
    
    if (ttl) {
      await client.setex(key, ttl, serializedValue)
    } else {
      await client.set(key, serializedValue)
    }
  },

  // 获取缓存
  getCache: async <T>(key: string): Promise<T | null> => {
    const client = getRedis()
    const value = await client.get(key)
    
    if (!value) {
      return null
    }
    
    try {
      return JSON.parse(value) as T
    } catch (error) {
      logger.error('Failed to parse cached value:', error)
      return null
    }
  },

  // 删除缓存
  deleteCache: async (key: string): Promise<void> => {
    const client = getRedis()
    await client.del(key)
  },

  // 检查键是否存在
  exists: async (key: string): Promise<boolean> => {
    const client = getRedis()
    const result = await client.exists(key)
    return result === 1
  },

  // 设置过期时间
  expire: async (key: string, ttl: number): Promise<void> => {
    const client = getRedis()
    await client.expire(key, ttl)
  },
}

export default {
  connectRedis,
  getRedis,
  closeRedis,
  checkRedisHealth,
  redisUtils,
}
