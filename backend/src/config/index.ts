import dotenv from 'dotenv'
import path from 'path'
import fs from 'fs'

// 加载环境变量
// 首先加载 .env 文件
dotenv.config({ 
  path: path.resolve(__dirname, '../../../.env')
})

// 然后加载 .env.local 文件（如果存在），它会覆盖 .env 中的值
const envLocalPath = path.resolve(__dirname, '../../../.env.local')
if (fs.existsSync(envLocalPath)) {
  const result = dotenv.config({ 
    path: envLocalPath, 
    override: true 
  })
  if (!result.error) {
    console.log('✅ Loaded .env.local file')
  }
} else {
  console.log('⚠️  .env.local file not found, using .env values')
}

// 读取密码文件的辅助函数
const readPasswordFile = (filePath: string): string => {
  try {
    const fullPath = path.resolve(__dirname, '../../../', filePath)
    if (fs.existsSync(fullPath)) {
      const password = fs.readFileSync(fullPath, 'utf8').trim()
      console.log(`✅ Loaded password from file: ${filePath}`)
      return password
    } else {
      console.log(`⚠️  Password file not found: ${filePath}`)
      return ''
    }
  } catch (error) {
    console.error(`❌ Error reading password file ${filePath}:`, error)
    return ''
  }
}

// 构建数据库 URL 的辅助函数
const buildDatabaseUrl = (): string => {
  // 如果有 DATABASE_URL，直接使用
  if (process.env.DATABASE_URL) {
    return process.env.DATABASE_URL
  }

  // 否则从密码文件构建 URL
  const passwordFile = process.env.DB_PASSWORD_FILE || '.secrets/db_password'
  const password = readPasswordFile(passwordFile) || 'default_password'
  const host = process.env.DB_HOST || 'localhost'
  const port = process.env.DB_PORT || '6432'
  const dbName = process.env.DB_NAME || 'postgres'
  const user = process.env.DB_USER || 'postgres'

  return `postgresql://${user}:${password}@${host}:${port}/${dbName}`
}

// 构建数据库 URL
const databaseUrl = buildDatabaseUrl()

// 缓存密码，避免重复读取
const dbPassword = (() => {
  // 从 databaseUrl 中提取密码
  const match = databaseUrl.match(/postgresql:\/\/[^:]+:([^@]+)@/)
  return match ? match[1] : 'default_password'
})()

// 设置 DATABASE_URL 环境变量供 Prisma 使用
if (!process.env.DATABASE_URL) {
  process.env.DATABASE_URL = databaseUrl
  console.log('✅ Set DATABASE_URL environment variable for Prisma')
}

export const config = {
  // 应用基础配置
  nodeEnv: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.APP_PORT || '3010', 10),
  appName: process.env.APP_NAME || 'Yogurt AI QC',
  appVersion: process.env.APP_VERSION || '1.0.0',

  // 数据库配置
  database: {
    url: databaseUrl,
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '6432', 10),
    name: process.env.DB_NAME || 'postgres',
    user: process.env.DB_USER || 'postgres',
    password: dbPassword,
    ssl: process.env.DB_SSL === 'true',
    poolMin: parseInt(process.env.DB_POOL_MIN || '2', 10),
    poolMax: parseInt(process.env.DB_POOL_MAX || '20', 10),
    poolIdleTimeout: parseInt(process.env.DB_POOL_IDLE_TIMEOUT || '30000', 10),
  },

  // Redis 配置
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6479',
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6479', 10),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0', 10),
  },

  // JWT 配置
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
  },

  // AI 服务配置
  ai: {
    openaiApiKey: process.env.OPENAI_API_KEY,
    openaiModel: process.env.OPENAI_MODEL || 'gpt-4-vision-preview',
    openaiMaxTokens: parseInt(process.env.OPENAI_MAX_TOKENS || '4096', 10),
    googleApiKey: process.env.GOOGLE_API_KEY,
    geminiModel: process.env.GEMINI_MODEL || 'gemini-pro-vision',
    anthropicApiKey: process.env.ANTHROPIC_API_KEY,
    serviceUrl: process.env.AI_SERVICE_URL || 'http://localhost:6800',
    analysisTimeout: parseInt(process.env.AI_ANALYSIS_TIMEOUT || '30000', 10),
    maxRetries: parseInt(process.env.AI_MAX_RETRIES || '3', 10),
  },

  // 文件存储配置
  storage: {
    uploadDir: process.env.UPLOAD_DIR || './uploads',
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760', 10), // 10MB
    allowedFileTypes: (process.env.ALLOWED_FILE_TYPES || 'jpg,jpeg,png,tiff,bmp').split(','),
    aws: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.AWS_REGION || 'us-west-2',
      s3Bucket: process.env.AWS_S3_BUCKET,
    },
  },

  // 邮件服务配置
  email: {
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT || '587', 10),
    user: process.env.SMTP_USER,
    password: process.env.SMTP_PASSWORD,
    from: process.env.SMTP_FROM || '<EMAIL>',
  },

  // 安全配置
  security: {
    corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:6173,http://localhost:6000,http://localhost:6174,http://*************:6174',
    corsCredentials: process.env.CORS_CREDENTIALS === 'true',
    rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW || '15', 10),
    rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
    encryptionKey: process.env.ENCRYPTION_KEY || 'your-32-character-encryption-key',
    hashRounds: parseInt(process.env.HASH_ROUNDS || '12', 10),
  },

  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'json',
    file: process.env.LOG_FILE || './logs/app.log',
  },

  // 监控配置
  monitoring: {
    metricsEnabled: process.env.METRICS_ENABLED === 'true',
    metricsPort: parseInt(process.env.METRICS_PORT || '9090', 10),
    sentryDsn: process.env.SENTRY_DSN,
  },

  // 开发配置
  development: {
    watchFiles: process.env.WATCH_FILES === 'true',
    watchExtensions: (process.env.WATCH_EXTENSIONS || 'js,ts,json').split(','),
    debug: process.env.DEBUG,
    verboseLogging: process.env.VERBOSE_LOGGING === 'true',
    swaggerEnabled: process.env.SWAGGER_ENABLED !== 'false',
    swaggerPath: process.env.SWAGGER_PATH || '/api/docs',
  },

  // 第三方服务配置
  external: {
    microscopeApiUrl: process.env.MICROSCOPE_API_URL,
    microscopeApiKey: process.env.MICROSCOPE_API_KEY,
    limsApiUrl: process.env.LIMS_API_URL,
    limsApiKey: process.env.LIMS_API_KEY,
  },
}

// 验证必需的环境变量
const requiredEnvVars = [
  'JWT_SECRET',
]

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar])

if (missingEnvVars.length > 0) {
  console.error('Missing required environment variables:', missingEnvVars.join(', '))
  console.error('Please check your .env file')
  process.exit(1)
}

// 开发环境警告
if (config.nodeEnv === 'development') {
  if (config.jwt.secret === 'your-super-secret-jwt-key') {
    console.warn('⚠️  Warning: Using default JWT secret in development mode')
  }
  
  if (!config.ai.openaiApiKey) {
    console.warn('⚠️  Warning: OpenAI API key not configured')
  }
}

export default config
