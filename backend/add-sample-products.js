const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function addSampleProducts() {
  try {
    // First, let's check if there are any cafes
    const cafes = await prisma.cafe.findMany()
    if (cafes.length === 0) {
      console.log('No cafes found. Creating a sample cafe...')
      await prisma.cafe.create({
        data: {
          name: '示例咖啡店',
          address: '示例地址'
        }
      })
    }
    
    const cafe = await prisma.cafe.findFirst()
    
    // Check if there are any users
    const users = await prisma.user.findMany()
    if (users.length === 0) {
      console.log('No users found. Creating a sample user...')
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Admin User',
          password: 'hashed_password',
          role: 'ADMIN',
          cafe_id: cafe.id
        }
      })
    }
    
    const user = await prisma.user.findFirst()

    // Sample products with our static image URLs
    const sampleProducts = [
      {
        name: '经典原味酸奶',
        type: '原味系列',
        description: '经典原味酸奶，采用优质牛奶发酵制成，口感醇厚，营养丰富',
        price: 12.8,
        volume: '200ml',
        status: 'active',
        ingredients: ['生牛乳', '乳酸菌', '白砂糖'],
        nutrition_per_100g: {
          energy: '315kJ',
          protein: '3.2g',
          fat: '3.1g',
          carbohydrate: '4.8g',
          calcium: '104mg'
        },
        benefits: ['补充益生菌', '促进消化', '增强免疫力'],
        certifications: ['有机认证', 'ISO9001'],
        shelf_life_days: 21,
        images: [
          {
            url: '/images/products/classic-yogurt.svg',
            alt: '经典原味酸奶'
          }
        ],
        cafe_id: cafe.id,
        creator_id: user.id
      },
      {
        name: '蓝莓风味酸奶',
        type: '果味系列', 
        description: '新鲜蓝莓与优质酸奶的完美结合，酸甜可口，富含花青素',
        price: 15.8,
        volume: '200ml',
        status: 'active',
        ingredients: ['生牛乳', '乳酸菌', '蓝莓果粒', '白砂糖'],
        nutrition_per_100g: {
          energy: '335kJ',
          protein: '3.0g',
          fat: '2.8g',
          carbohydrate: '6.2g',
          calcium: '98mg'
        },
        benefits: ['抗氧化', '护眼明目', '美容养颜'],
        certifications: ['绿色食品', 'HACCP'],
        shelf_life_days: 21,
        images: [
          {
            url: '/images/products/blueberry-yogurt.svg',
            alt: '蓝莓风味酸奶'
          }
        ],
        cafe_id: cafe.id,
        creator_id: user.id
      }
    ]

    // Clear existing products first
    await prisma.product.deleteMany()
    console.log('Cleared existing products')

    // Add sample products
    for (const productData of sampleProducts) {
      const product = await prisma.product.create({
        data: productData
      })
      console.log(`Created product: ${product.name}`)
    }

    console.log('Sample products added successfully!')
    
  } catch (error) {
    console.error('Error adding sample products:', error)
  } finally {
    await prisma.$disconnect()
  }
}

addSampleProducts()