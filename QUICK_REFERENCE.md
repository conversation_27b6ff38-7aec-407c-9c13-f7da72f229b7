# ⚡ 快速参考卡片

## 🎯 项目架构一览

```
Yogurt AI QC 混合架构
├── 前端 (React + TS + Vite)      → Node.js环境 (npm)
├── 后端 (Express + TS + Prisma)  → Node.js环境 (npm)
├── AI服务 (FastAPI + Python)     → Conda环境 (conda)
└── 数据库 (PostgreSQL + Redis)   → Docker容器
```

## 🔄 环境自动激活

```bash
cd yoghurt-ai-qc
# 自动激活 yoghurt-ai-qc conda环境
# 设置环境变量
# 验证组件
```

## 🚀 启动命令速查

### 一键启动 (推荐)
```bash
./scripts/start-all-services.sh    # 启动所有服务
./scripts/stop-all-services.sh     # 停止所有服务
./scripts/restart-all-services.sh  # 重启所有服务
```

### 分步启动
```bash
# 1. 数据库
docker-compose up -d postgres redis

# 2. 前后端 (Node.js)
npm run dev:frontend    # 端口 6174
npm run dev:backend     # 端口 3010

# 3. AI服务 (Python/Conda)
npm run dev:ai          # 端口 6800
```

### npm命令
```bash
npm run dev             # 前后端
npm run dev:all         # 所有服务
```

## 🌐 服务地址速查

| 服务 | 端口 | 地址 |
|------|------|------|
| 前端 | 6174 | http://localhost:6174 |
| 后端API | 3010 | http://localhost:3010 |
| API文档 | 3010 | http://localhost:3010/api/docs |
| AI服务 | 6800 | http://localhost:6800 |
| AI文档 | 6800 | http://localhost:6800/docs |
| 数据库管理 | 5555 | http://localhost:5555 |

## 🔧 常用开发命令

### 测试
```bash
npm test                # 所有测试
npm run test:frontend   # 前端测试
npm run test:backend    # 后端测试
```

### 代码检查
```bash
npm run lint            # 所有代码
npm run lint:frontend   # 前端代码
npm run lint:backend    # 后端代码
```

### 数据库
```bash
npm run db:studio       # Prisma Studio
npm run db:seed         # 重新填充数据
npm run db:init         # 初始化数据库
```

## 🐍 Conda环境管理

### 环境操作
```bash
conda activate yoghurt-ai-qc    # 激活环境
conda deactivate                # 停用环境
conda env list                  # 查看所有环境
```

### 环境管理脚本
```bash
./scripts/create-latest-env.sh  # 创建最新环境
./scripts/manage-conda-env.sh   # 环境管理工具
source activate-env.sh          # 手动激活 (备用)
```

## 🔄 Direnv管理

### 基本命令
```bash
direnv allow            # 允许.envrc
direnv reload           # 重新加载
direnv status           # 查看状态
direnv deny             # 禁用.envrc
```

### 配置脚本
```bash
./setup-direnv-complete.sh      # 完整配置direnv
./scripts/fix-macos-direnv.sh   # 修复macOS问题
./scripts/test-auto-activation.sh # 测试自动激活
```

## 🛠️ 故障排除速查

### 端口占用
```bash
lsof -i :6174          # 检查前端端口
lsof -i :3010          # 检查后端端口
lsof -i :6800          # 检查AI服务端口
./scripts/cleanup-ports.sh     # 清理所有端口
```

### 环境问题
```bash
# conda环境未激活
conda activate yoghurt-ai-qc
source activate-env.sh

# direnv问题
direnv allow
direnv reload

# 依赖问题
npm install             # Node.js依赖
pip install -r ai-service/requirements-slim.txt  # Python依赖
```

### 数据库问题
```bash
docker-compose restart postgres redis  # 重启数据库
docker-compose logs postgres           # 查看日志
npm run db:init                        # 重新初始化
```

## 📁 重要文件位置

### 配置文件
```
.envrc                          # direnv自动激活配置
environment-latest.yml          # conda环境定义
ai-service/requirements-slim.txt # Python依赖
package.json                    # Node.js依赖 (根目录)
docker-compose.yml              # Docker服务配置
```

### 启动脚本
```
scripts/start-all-services.sh   # 一键启动
scripts/create-latest-env.sh    # 创建conda环境
setup-direnv-complete.sh        # 配置direnv
activate-env.sh                 # 手动激活环境
```

### 文档
```
README.md                       # 项目主文档
STARTUP_GUIDE.md               # 详细启动指南
FRONTEND_BACKEND_STARTUP.md    # 前后端启动详解
CONDA_ENVIRONMENT_GUIDE.md     # Conda环境管理
DIRENV_FINAL_SETUP.md          # Direnv配置指南
```

## 🎯 测试账户

| 邮箱 | 密码 | 角色 |
|------|------|------|
| <EMAIL> | password123 | ADMIN |
| <EMAIL> | password123 | USER |
| <EMAIL> | password123 | VIEWER |

## 🔍 健康检查

```bash
# 检查所有服务
curl http://localhost:6174        # 前端
curl http://localhost:3010/health # 后端
curl http://localhost:6800/health # AI服务

# 检查数据库
docker exec yoghurt-postgres pg_isready
docker exec yoghurt-redis redis-cli ping

# 检查环境
echo $CONDA_DEFAULT_ENV          # 应该显示: yoghurt-ai-qc
python --version                 # 应该显示: Python 3.12.11
node --version                   # 应该显示: Node.js 18+
```

---

**保存此文档以便快速查阅！** 📌
