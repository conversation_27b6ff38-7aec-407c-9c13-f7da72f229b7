# =============================================================================
# Yogurt AI QC - 最新稳定版Conda环境配置
# =============================================================================
#
# 环境名称: yoghurt-ai-qc
# Python版本: 3.12 (最新稳定版)
# 主要用途: 多模态API调用、图像预处理、Web服务
#
# 设计理念:
# - 使用最新稳定版本的所有依赖
# - 专注于API调用和基础图像处理
# - 移除本地ML框架，使用云端API
# - 优化性能和安全性
#
# 创建环境: conda env create -f environment-slim.yml
# 激活环境: conda activate yoghurt-ai-qc
#
# 管理脚本: ./scripts/manage-conda-env.sh
# 更新时间: 2025年7月
# =============================================================================

name: yoghurt-ai-qc
channels:
  - conda-forge
  - defaults
dependencies:
  # Python 基础环境 (最新稳定版)
  - python=3.12
  - pip
  
  # Web 框架和API
  - fastapi
  - uvicorn[standard]
  - pydantic
  - python-multipart
  
  # HTTP 客户端和工具
  - requests
  - httpx
  - aiohttp
  
  # 基础数据处理
  - numpy
  - pandas
  
  # 基础图像处理
  - pillow
  - opencv
  
  # 数据库连接
  - psycopg2
  - sqlalchemy
  - redis-py
  
  # 环境管理和配置
  - python-dotenv
  - pyyaml
  
  # 日志
  - loguru
  
  # 开发工具（精简版）
  - pytest
  - pytest-asyncio
  - black
  - flake8
  
  # pip 安装的包 (最新稳定版)
  - pip:
    # AI API 客户端 (最新版本)
    - openai>=1.56.0
    - anthropic>=0.40.0
    - google-generativeai>=0.8.0

    # 异步数据库 (最新版本)
    - asyncpg>=0.30.0
    - redis>=5.2.0

    # 文件处理 (最新版本)
    - aiofiles>=24.1.0
    - python-magic>=0.4.27

    # 安全和认证 (最新版本)
    - python-jose[cryptography]>=3.3.0
    - passlib[bcrypt]>=1.7.4
    - cryptography>=43.0.0

    # 时间处理
    - python-dateutil>=2.9.0

    # 数据验证 (最新版本)
    - marshmallow>=3.23.0

    # 监控 (最新版本)
    - prometheus-client>=0.21.0

    # 图像格式支持 (最新版本)
    - imageio>=2.36.0

    # 性能优化
    - orjson>=3.10.0
