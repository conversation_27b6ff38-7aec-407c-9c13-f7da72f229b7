# 🚀 前后端启动完整指南

## 📋 项目架构概览

### 🏗️ 技术栈架构

```
Yogurt AI QC 项目
├── 前端 (React + TypeScript + Vite)     → Node.js环境
├── 后端 (Express + TypeScript + Prisma) → Node.js环境  
├── AI服务 (FastAPI + Python)            → Conda环境
└── 数据库 (PostgreSQL + Redis)          → Docker容器
```

### 🔄 环境管理策略

#### Node.js环境 (前端 + 后端)
- **管理方式**: npm workspaces
- **依赖安装**: `npm install` (根目录统一安装)
- **共享依赖**: TypeScript、开发工具等
- **独立依赖**: 各自的业务依赖

#### Python环境 (AI服务)
- **管理方式**: conda环境 `yoghurt-ai-qc`
- **自动激活**: direnv (进入项目目录自动激活)
- **依赖管理**: `environment-latest.yml` + `requirements-slim.txt`
- **版本**: Python 3.12.11

#### 数据库环境
- **管理方式**: Docker Compose
- **服务**: PostgreSQL (端口6432) + Redis (端口6479)
- **数据持久化**: Docker volumes

## 🎯 启动流程详解

### 🔄 自动环境激活

当您进入项目目录时，direnv会自动：

```bash
cd yoghurt-ai-qc
# 自动执行以下操作：
# 1. 激活 yoghurt-ai-qc conda环境
# 2. 设置环境变量 (端口、路径等)
# 3. 加载 .env 和 .env.local 文件
# 4. 验证关键Python组件
```

**预期输出**:
```
✅ 已激活 yoghurt-ai-qc conda环境
🐍 Python版本: Python 3.12.11
📦 环境路径: /Volumes/acasis/miniconda3/miniconda3/envs/yoghurt-ai-qc
📄 加载 .env 文件
📄 加载 .env.local 文件
🔍 验证关键组件...
✅ 所有关键组件验证通过
🚀 Yogurt AI QC 项目环境已准备就绪
```

### 🚀 启动方式对比

| 启动方式 | 适用场景 | 优点 | 缺点 |
|----------|----------|------|------|
| 一键启动脚本 | 快速演示、生产部署 | 全自动、无需手动操作 | 难以调试单个服务 |
| 分步启动 | 开发调试 | 可控制单个服务、便于调试 | 需要多个命令 |
| npm命令 | 日常开发 | 简单快捷、符合习惯 | 需要手动启动数据库 |

## 🔧 详细启动步骤

### 方法1: 一键启动 (推荐新手)

```bash
# 1. 进入项目目录
cd yoghurt-ai-qc
# → 自动激活conda环境

# 2. 运行一键启动脚本
./scripts/start-all-services.sh
# → 自动启动所有服务
```

**脚本执行流程**:
1. ✅ 检查系统要求 (Node.js, Docker, Conda)
2. ✅ 检查端口占用情况
3. ✅ 安装Node.js依赖
4. ✅ 启动Docker服务 (PostgreSQL + Redis)
5. ✅ 初始化数据库 (迁移 + 种子数据)
6. ✅ 启动后端服务 (端口3010)
7. ✅ 启动前端服务 (端口6174)
8. ✅ 显示服务状态和访问地址

### 方法2: 分步启动 (推荐开发者)

#### 步骤1: 环境准备
```bash
# 进入项目目录 (自动激活conda环境)
cd yoghurt-ai-qc

# 验证环境
echo "Node.js: $(node --version)"
echo "Python: $(python --version)"
echo "Conda环境: $CONDA_DEFAULT_ENV"
```

#### 步骤2: 启动数据库
```bash
# 启动PostgreSQL和Redis
docker-compose up -d postgres redis

# 验证数据库启动
docker ps | grep -E "(postgres|redis)"

# 初始化数据库 (首次运行)
cd backend && npm run db:init && cd ..
```

#### 步骤3: 启动后端服务
```bash
# 方式A: 使用npm脚本
npm run dev:backend

# 方式B: 手动启动
cd backend
npm run dev
cd ..

# 验证后端启动
curl http://localhost:3010/health
```

#### 步骤4: 启动前端服务
```bash
# 方式A: 使用npm脚本
npm run dev:frontend

# 方式B: 手动启动
cd frontend
npm run dev
cd ..

# 验证前端启动
curl http://localhost:6174
```

#### 步骤5: 启动AI服务
```bash
# 方式A: 使用npm脚本 (推荐)
npm run dev:ai

# 方式B: 手动启动
cd ai-service
python -m uvicorn src.main:app --reload --port 6800
cd ..

# 验证AI服务启动
curl http://localhost:6800/health
```

### 方法3: npm统一命令

#### 启动前后端
```bash
# 同时启动前端和后端 (不包括AI服务)
npm run dev
```

#### 启动所有服务
```bash
# 启动前端、后端、AI服务 (需要手动启动数据库)
npm run dev:all
```

## 🌐 服务端口和访问

### 🔗 服务地址

| 服务 | 端口 | 访问地址 | 说明 |
|------|------|----------|------|
| **前端应用** | 6174 | http://localhost:6174 | React应用主界面 |
| **后端API** | 3010 | http://localhost:3010 | Express API服务 |
| **API文档** | 3010 | http://localhost:3010/api/docs | Swagger API文档 |
| **AI服务** | 6800 | http://localhost:6800 | FastAPI AI服务 |
| **AI文档** | 6800 | http://localhost:6800/docs | FastAPI文档 |
| **PostgreSQL** | 6432 | localhost:6432 | 主数据库 |
| **Redis** | 6479 | localhost:6479 | 缓存数据库 |
| **Prisma Studio** | 5555 | http://localhost:5555 | 数据库管理界面 |

### 🔍 健康检查

```bash
# 检查所有服务状态
curl http://localhost:6174        # 前端
curl http://localhost:3010/health # 后端
curl http://localhost:6800/health # AI服务

# 检查数据库连接
docker exec yoghurt-postgres pg_isready
docker exec yoghurt-redis redis-cli ping
```

## 🛠️ 开发工作流

### 📝 日常开发流程

```bash
# 1. 进入项目 (自动激活环境)
cd yoghurt-ai-qc

# 2. 拉取最新代码
git pull

# 3. 更新依赖 (如果有变化)
npm install                    # Node.js依赖
conda env update -f environment-latest.yml  # Python依赖

# 4. 启动开发服务
npm run dev                    # 前后端
npm run dev:ai                 # AI服务 (另一个终端)

# 5. 开始开发
# 前端: http://localhost:6174
# 后端API: http://localhost:3010/api/docs
# AI服务: http://localhost:6800/docs
```

### 🔄 热重载说明

| 服务 | 热重载 | 说明 |
|------|--------|------|
| **前端** | ✅ 自动 | Vite提供快速热重载 |
| **后端** | ✅ 自动 | nodemon监听文件变化 |
| **AI服务** | ✅ 自动 | uvicorn --reload模式 |

### 🧪 测试和调试

```bash
# 运行测试
npm run test                   # 所有测试
npm run test:frontend         # 前端测试
npm run test:backend          # 后端测试

# 代码检查
npm run lint                  # 所有代码
npm run lint:frontend        # 前端代码
npm run lint:backend         # 后端代码

# 数据库管理
npm run db:studio             # 打开Prisma Studio
npm run db:seed               # 重新填充测试数据
```

## 🚨 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 检查端口占用
lsof -i :6174  # 前端
lsof -i :3010  # 后端
lsof -i :6800  # AI服务

# 清理端口
./scripts/cleanup-ports.sh
```

#### 2. conda环境未激活
```bash
# 手动激活
conda activate yoghurt-ai-qc

# 或使用备用脚本
source activate-env.sh

# 重新配置direnv
direnv allow
```

#### 3. 数据库连接失败
```bash
# 重启数据库服务
docker-compose restart postgres redis

# 检查数据库状态
docker-compose logs postgres
docker-compose logs redis
```

#### 4. 依赖安装问题
```bash
# 清理并重新安装Node.js依赖
rm -rf node_modules frontend/node_modules backend/node_modules
npm install

# 重新创建conda环境
conda env remove -n yoghurt-ai-qc
./scripts/create-latest-env.sh
```

## 📚 相关文档

- [项目启动指南](STARTUP_GUIDE.md) - 完整的项目启动说明
- [Conda环境管理](CONDA_ENVIRONMENT_GUIDE.md) - Python环境详细管理
- [AI服务文档](ai-service/README.md) - AI服务专门文档
- [数据库设计](backend/DATABASE_DESIGN.md) - 数据库结构说明

---

**享受高效的全栈开发体验！** 🚀✨
