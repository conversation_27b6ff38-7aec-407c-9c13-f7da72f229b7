# 🐍 Conda环境管理指南 - Yogurt AI QC

## 📋 概述

本项目使用Conda管理Python AI服务的依赖环境。Conda环境包含了所有必要的机器学习、图像处理和Web服务依赖。

## 🏗️ 环境结构

### 环境名称
- **名称**: `yoghurt-ai-qc`
- **Python版本**: 3.11
- **主要用途**: AI服务、图像分析、机器学习模型

### 核心依赖分类

#### 🌐 Web框架和API
- **FastAPI**: 现代高性能Web框架
- **Uvicorn**: ASGI服务器
- **Pydantic**: 数据验证和序列化

#### 🧠 AI/ML框架
- **TensorFlow**: 深度学习框架
- **PyTorch**: 深度学习框架
- **Transformers**: 预训练模型库
- **Scikit-learn**: 机器学习库

#### 🖼️ 图像处理
- **OpenCV**: 计算机视觉库
- **Pillow**: 图像处理库
- **Scikit-image**: 图像分析
- **Matplotlib/Seaborn**: 数据可视化

#### 🗄️ 数据库和缓存
- **PostgreSQL**: 主数据库连接 (psycopg2, asyncpg)
- **Redis**: 缓存数据库连接
- **SQLAlchemy**: ORM框架

#### 🔧 开发工具
- **Jupyter**: 交互式开发环境
- **Pytest**: 测试框架
- **Black/Flake8**: 代码格式化和检查

## 🚀 快速开始

### 方法1: 使用管理脚本 (推荐)

```bash
# 创建环境
./scripts/manage-conda-env.sh create

# 查看环境信息
./scripts/manage-conda-env.sh info

# 更新环境
./scripts/manage-conda-env.sh update
```

### 方法2: 手动操作

```bash
# 创建环境
conda env create -f environment.yml

# 激活环境
conda activate yoghurt-ai-qc

# 安装额外依赖
pip install -r ai-service/requirements.txt
```

## 📦 环境管理

### 创建环境
```bash
# 使用脚本创建
./scripts/manage-conda-env.sh create

# 或手动创建
conda env create -f environment.yml
```

### 激活环境
```bash
conda activate yoghurt-ai-qc
```

### 更新环境
```bash
# 使用脚本更新
./scripts/manage-conda-env.sh update

# 或手动更新
conda env update -f environment.yml --prune
pip install -r ai-service/requirements.txt --upgrade
```

### 删除环境
```bash
# 使用脚本删除
./scripts/manage-conda-env.sh remove

# 或手动删除
conda env remove -n yoghurt-ai-qc
```

## 🔧 开发工作流

### 1. 环境准备
```bash
# 激活环境
conda activate yoghurt-ai-qc

# 验证安装
python -c "import fastapi, torch, cv2; print('环境就绪')"
```

### 2. 启动AI服务
```bash
# 方法1: 直接启动
cd ai-service
python -m uvicorn src.main:app --reload --port 6800

# 方法2: 使用npm脚本
npm run dev:ai
```

### 3. Jupyter开发
```bash
# 启动Jupyter Lab
jupyter lab --port 8888

# 或启动传统Notebook
jupyter notebook
```

### 4. 运行测试
```bash
# 在ai-service目录下
cd ai-service
pytest tests/ -v

# 带覆盖率报告
pytest tests/ --cov=src --cov-report=html
```

## 📊 环境信息

### 系统要求
- **操作系统**: Windows, macOS, Linux
- **内存**: 建议 8GB+ (AI模型需要较多内存)
- **存储**: 环境大小约 3-5GB
- **GPU**: 可选，支持CUDA (用于深度学习加速)

### 主要包版本
| 包名 | 版本 | 用途 |
|------|------|------|
| Python | 3.11 | 基础运行环境 |
| FastAPI | 0.104.1 | Web API框架 |
| TensorFlow | 2.14.0 | 深度学习 |
| PyTorch | 2.1.1 | 深度学习 |
| OpenCV | 4.8.1 | 图像处理 |
| NumPy | 1.24.3 | 数值计算 |
| Pandas | 2.1.3 | 数据处理 |

### 环境大小估算
- **基础环境**: ~2GB
- **包含所有依赖**: ~4GB
- **缓存和临时文件**: ~1GB

## 🛠️ 故障排除

### 常见问题

#### 1. 环境创建失败
```bash
# 清理conda缓存
conda clean --all

# 重新创建环境
conda env remove -n yoghurt-ai-qc
conda env create -f environment.yml
```

#### 2. 包冲突问题
```bash
# 使用mamba替代conda (更快的依赖解析)
conda install mamba -c conda-forge
mamba env create -f environment.yml
```

#### 3. GPU支持问题
```bash
# 检查CUDA是否可用
python -c "import torch; print(torch.cuda.is_available())"

# 安装CUDA版本的PyTorch
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia
```

#### 4. 内存不足
```bash
# 监控内存使用
python -c "import psutil; print(f'内存使用: {psutil.virtual_memory().percent}%')"

# 清理不必要的包
conda clean --packages --tarballs
```

### 性能优化

#### 1. 使用Mamba
```bash
# 安装mamba
conda install mamba -c conda-forge

# 使用mamba管理环境
mamba env create -f environment.yml
mamba env update -f environment.yml
```

#### 2. 配置conda通道优先级
```bash
# 设置通道优先级
conda config --add channels conda-forge
conda config --set channel_priority strict
```

#### 3. 启用并行下载
```bash
# 配置并行下载
conda config --set remote_max_retries 3
conda config --set remote_backoff_factor 2
```

## 📚 相关文档

### 环境配置文件
- [`environment.yml`](environment.yml) - Conda环境定义
- [`ai-service/requirements.txt`](ai-service/requirements.txt) - 额外pip依赖

### 管理脚本
- [`scripts/manage-conda-env.sh`](scripts/manage-conda-env.sh) - 环境管理脚本
- [`scripts/setup-environment.sh`](scripts/setup-environment.sh) - 完整环境设置

### 相关指南
- [项目启动指南](STARTUP_GUIDE.md)
- [AI服务文档](ai-service/README.md)
- [开发环境设置](GETTING_STARTED.md)

## 🔄 环境维护

### 定期维护任务

#### 1. 更新依赖 (每月)
```bash
./scripts/manage-conda-env.sh update
```

#### 2. 清理缓存 (每周)
```bash
conda clean --all
pip cache purge
```

#### 3. 导出环境 (发布前)
```bash
./scripts/manage-conda-env.sh export
```

#### 4. 安全检查 (每月)
```bash
# 检查已知漏洞
pip-audit

# 更新安全补丁
conda update --all
```

### 版本控制

#### 环境版本管理
- `environment.yml` - 主要依赖版本
- `requirements.txt` - 精确版本锁定
- 定期导出完整环境快照

#### 兼容性测试
- 在不同操作系统上测试环境创建
- 验证关键功能在新环境中正常工作
- 记录已知的兼容性问题

## 🆘 获取帮助

### 命令行帮助
```bash
# 查看管理脚本帮助
./scripts/manage-conda-env.sh help

# Conda帮助
conda --help
conda env --help
```

### 常用资源
- [Conda官方文档](https://docs.conda.io/)
- [Conda-forge社区](https://conda-forge.org/)
- [环境管理最佳实践](https://docs.conda.io/projects/conda/en/latest/user-guide/tasks/manage-environments.html)

---

**环境管理愉快！** 🐍✨
