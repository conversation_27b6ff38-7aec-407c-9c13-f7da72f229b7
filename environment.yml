# =============================================================================
# Yogurt AI QC - Conda环境配置 (最新稳定版)
# =============================================================================
#
# 环境名称: yoghurt-ai-qc
# Python版本: 3.12 (最新稳定版)
# 主要用途: 多模态API调用、图像预处理、Web服务
#
# 注意: 这是精简版配置，专注于API调用而非本地ML模型
#
# 创建环境: conda env create -f environment.yml
# 激活环境: conda activate yoghurt-ai-qc
# 更新环境: conda env update -f environment.yml --prune
#
# 管理脚本: ./scripts/manage-conda-env.sh
# 更新时间: 2025年7月
# =============================================================================

name: yoghurt-ai-qc
channels:
  - conda-forge
  - defaults
dependencies:
  # Python 基础环境
  - python=3.12
  - pip
  
  # Web 框架和API
  - fastapi
  - uvicorn[standard]
  - pydantic
  - python-multipart
  
  # 数据处理和科学计算
  - numpy
  - pandas
  - scipy
  - scikit-learn
  
  # 图像处理和计算机视觉
  - opencv
  - pillow
  - matplotlib
  - seaborn
  
  # AI/ML 框架
  - tensorflow
  - pytorch
  - torchvision
  - transformers
  
  # 数据库连接
  - psycopg2
  - sqlalchemy
  - alembic
  
  # 缓存和消息队列
  - redis-py
  - celery
  
  # HTTP 客户端和工具
  - requests
  - httpx
  - aiohttp
  
  # 开发工具
  - pytest
  - pytest-asyncio
  - pytest-cov
  - black
  - flake8
  - mypy
  - pre-commit
  
  # 环境管理
  - python-dotenv
  - pyyaml
  
  # 日志和监控
  - loguru
  - prometheus_client
  
  # Jupyter 开发环境
  - jupyter
  - jupyterlab
  - ipykernel
  
  # pip 安装的包
  - pip:
    - openai>=1.0.0
    - anthropic
    - google-generativeai
    - python-jose[cryptography]
    - passlib[bcrypt]
    - python-multipart
    - slowapi
    - asyncpg
    - aiofiles
    - jinja2
    - python-socketio
    - websockets
    - streamlit
    - gradio
