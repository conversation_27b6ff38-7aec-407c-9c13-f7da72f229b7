const fs = require('fs');
const path = require('path');

// 测试 LM Studio 的流式输出
async function testLMStudioStream() {
  console.log('🧪 测试 LM Studio 流式输出...');
  
  // 读取测试图片并转换为base64
  const imagePath = path.join(__dirname, 'docs/images/architecture-overview.png');
  const imageBuffer = fs.readFileSync(imagePath);
  const imageBase64 = `data:image/png;base64,${imageBuffer.toString('base64')}`;
  
  const request = {
    model: 'google/gemma-3-27b',
    messages: [
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: '请分析这张图片，描述你看到的内容。'
          },
          {
            type: 'image_url',
            image_url: {
              url: imageBase64
            }
          }
        ]
      }
    ],
    stream: true,
    max_tokens: 500,
    temperature: 0.7
  };

  try {
    const response = await fetch('http://192.168.1.225:1234/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    console.log('✅ 连接成功，开始接收流式数据...\n');

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let fullContent = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk.split('\n');

      for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine.startsWith('data: ')) {
          const data = trimmedLine.slice(6).trim();
          
          if (data === '[DONE]') {
            console.log('\n\n🎉 流式输出完成！');
            console.log('📝 完整内容:');
            console.log(fullContent);
            return fullContent;
          }

          if (data) {
            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices[0]?.delta?.content;
              if (content) {
                fullContent += content;
                process.stdout.write(content);
              }
            } catch (e) {
              console.warn('\n⚠️ 解析数据失败:', e.message, '数据:', data);
            }
          }
        }
      }
    }
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    throw error;
  }
}

// 运行测试
testLMStudioStream().catch(console.error);
