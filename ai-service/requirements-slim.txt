# =============================================================================
# Yoghurt AI QC - AI Service Dependencies (最新稳定版)
# =============================================================================
#
# 设计理念:
# - 使用最新稳定版本的所有依赖
# - 专注于多模态API调用和基础图像处理
# - 移除本地ML框架，使用云端API
# - 优化性能、安全性和兼容性
#
# Python版本要求: >=3.12
# 更新时间: 2025年7月
# =============================================================================

# =============================================================================
# Web 框架核心 (最新稳定版)
# =============================================================================
fastapi==0.115.6
uvicorn[standard]==0.32.1
pydantic==2.10.3
python-multipart==0.0.17

# =============================================================================
# HTTP 客户端 (最新稳定版)
# =============================================================================
httpx==0.28.1
requests==2.32.3
aiohttp==3.11.10

# =============================================================================
# AI API 客户端 (最新稳定版)
# =============================================================================
openai==1.56.2
anthropic==0.40.0
google-generativeai==0.8.3

# =============================================================================
# 基础图像处理 (最新稳定版)
# =============================================================================
opencv-python==*********
Pillow==11.0.0
imageio==2.36.0
numpy==2.2.1

# =============================================================================
# 基础数据处理 (最新稳定版)
# =============================================================================
pandas==2.2.3

# =============================================================================
# 数据库连接 (最新稳定版)
# =============================================================================
asyncpg==0.30.0
redis==5.2.0
sqlalchemy==2.0.36

# =============================================================================
# 配置和环境 (最新稳定版)
# =============================================================================
python-dotenv==1.0.1
pyyaml==6.0.2

# =============================================================================
# 日志 (最新稳定版)
# =============================================================================
loguru==0.7.3

# =============================================================================
# 文件处理 (最新稳定版)
# =============================================================================
aiofiles==24.1.0
python-magic==0.4.27

# =============================================================================
# 时间处理 (最新稳定版)
# =============================================================================
python-dateutil==2.9.0.post0

# =============================================================================
# 安全和认证 (最新稳定版)
# =============================================================================
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
cryptography==43.0.3

# =============================================================================
# 数据验证和序列化 (最新稳定版)
# =============================================================================
marshmallow==3.23.1

# =============================================================================
# 监控 (最新稳定版)
# =============================================================================
prometheus-client==0.21.0

# =============================================================================
# 性能优化 (最新稳定版)
# =============================================================================
orjson==3.10.12

# =============================================================================
# 开发和测试工具 (最新稳定版)
# =============================================================================
pytest==8.3.4
pytest-asyncio==0.24.0
pytest-cov==6.0.0
black==24.10.0
flake8==7.1.1
mypy==1.13.0
