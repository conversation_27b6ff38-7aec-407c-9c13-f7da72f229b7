# 🤖 AI服务 - Yogurt AI QC

## 📋 概述

AI服务是Yogurt AI QC系统的核心组件，负责图像分析、质量检测和智能决策。基于FastAPI构建，提供高性能的RESTful API接口。

## 🏗️ 技术架构

### 核心框架
- **FastAPI**: 现代高性能Web框架
- **Uvicorn**: ASGI服务器
- **Pydantic**: 数据验证和序列化

### AI/ML技术栈
- **TensorFlow 2.14**: 深度学习主框架
- **PyTorch 2.1**: 深度学习辅助框架
- **OpenCV**: 计算机视觉处理
- **Scikit-learn**: 传统机器学习
- **Transformers**: 预训练模型库

### 图像处理
- **OpenCV**: 图像预处理和特征提取
- **Pillow**: 图像格式转换
- **Scikit-image**: 高级图像分析
- **Matplotlib**: 结果可视化

## 🚀 快速开始

### 环境准备

#### 1. 创建Conda环境
```bash
# 使用管理脚本 (推荐)
./scripts/manage-conda-env.sh create

# 或手动创建
conda env create -f environment.yml
conda activate yoghurt-ai-qc
pip install -r ai-service/requirements.txt
```

#### 2. 激活环境
```bash
conda activate yoghurt-ai-qc
```

### 启动服务

#### 方法1: 使用项目脚本
```bash
# 从项目根目录
npm run dev:ai
```

#### 方法2: 直接启动
```bash
# 进入AI服务目录
cd ai-service

# 启动开发服务器
python -m uvicorn src.main:app --reload --port 6800

# 或使用生产模式
python -m uvicorn src.main:app --host 0.0.0.0 --port 6800
```

### 验证安装
```bash
# 检查服务状态
curl http://localhost:6800/health

# 查看API文档
open http://localhost:6800/docs
```

## 📁 项目结构

```
ai-service/
├── src/
│   ├── main.py              # FastAPI应用入口
│   ├── config.py            # 配置管理
│   ├── models/              # AI模型定义
│   ├── services/            # 业务逻辑服务
│   ├── utils/               # 工具函数
│   └── api/                 # API路由
├── tests/                   # 测试文件
├── models/                  # 预训练模型存储
├── requirements.txt         # Python依赖
└── README.md               # 本文档
```

## 🔧 开发指南

### 本地开发

#### 1. 环境激活
```bash
conda activate yoghurt-ai-qc
cd ai-service
```

#### 2. 安装开发依赖
```bash
pip install -r requirements.txt
```

#### 3. 运行测试
```bash
# 运行所有测试
pytest tests/ -v

# 运行特定测试
pytest tests/test_image_analysis.py -v

# 生成覆盖率报告
pytest tests/ --cov=src --cov-report=html
```

#### 4. 代码格式化
```bash
# 格式化代码
black src/ tests/

# 检查代码风格
flake8 src/ tests/

# 类型检查
mypy src/
```

### API开发

#### 添加新的API端点
1. 在 `src/api/` 目录下创建新的路由文件
2. 定义Pydantic模型用于请求/响应验证
3. 实现业务逻辑
4. 添加相应的测试

#### 示例API端点
```python
from fastapi import APIRouter, UploadFile, File
from src.services.image_analyzer import ImageAnalyzer

router = APIRouter()
analyzer = ImageAnalyzer()

@router.post("/analyze")
async def analyze_image(file: UploadFile = File(...)):
    """分析上传的图像"""
    result = await analyzer.analyze(file)
    return {"status": "success", "result": result}
```

## 🧠 AI模型管理

### 模型存储
- **位置**: `ai-service/models/`
- **格式**: TensorFlow SavedModel, PyTorch .pth, ONNX
- **版本控制**: 使用MLflow或DVC管理模型版本

### 模型加载
```python
import tensorflow as tf
from pathlib import Path

class ModelManager:
    def __init__(self):
        self.models_dir = Path("models")
        self.loaded_models = {}
    
    def load_model(self, model_name: str):
        model_path = self.models_dir / model_name
        if model_name not in self.loaded_models:
            self.loaded_models[model_name] = tf.keras.models.load_model(model_path)
        return self.loaded_models[model_name]
```

### 支持的模型类型
- **图像分类**: 酸奶质量等级分类
- **目标检测**: 缺陷区域检测
- **图像分割**: 精确的缺陷边界
- **异常检测**: 未知质量问题识别

## 📊 性能监控

### 指标收集
- **响应时间**: API请求处理时间
- **吞吐量**: 每秒处理的图像数量
- **准确率**: 模型预测准确性
- **资源使用**: CPU、内存、GPU使用率

### 监控工具
```python
from prometheus_client import Counter, Histogram, generate_latest

# 定义指标
REQUEST_COUNT = Counter('api_requests_total', 'Total API requests')
REQUEST_DURATION = Histogram('api_request_duration_seconds', 'API request duration')

@app.middleware("http")
async def monitor_requests(request, call_next):
    start_time = time.time()
    response = await call_next(request)
    REQUEST_COUNT.inc()
    REQUEST_DURATION.observe(time.time() - start_time)
    return response
```

## 🔒 安全考虑

### 输入验证
- 文件类型检查
- 文件大小限制
- 图像格式验证
- 恶意内容检测

### API安全
```python
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer

security = HTTPBearer()

async def verify_token(token: str = Depends(security)):
    if not validate_token(token.credentials):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token"
        )
    return token
```

## 🚀 部署指南

### Docker部署
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY src/ ./src/
COPY models/ ./models/

CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "6800"]
```

### 生产环境配置
```bash
# 使用Gunicorn + Uvicorn
gunicorn src.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:6800

# 或使用Uvicorn
uvicorn src.main:app --host 0.0.0.0 --port 6800 --workers 4
```

## 📚 API文档

### 自动生成文档
- **Swagger UI**: http://localhost:6800/docs
- **ReDoc**: http://localhost:6800/redoc
- **OpenAPI JSON**: http://localhost:6800/openapi.json

### 主要API端点

#### 健康检查
```
GET /health
```

#### 图像分析
```
POST /api/v1/analyze
Content-Type: multipart/form-data
Body: image file
```

#### 批量处理
```
POST /api/v1/batch-analyze
Content-Type: multipart/form-data
Body: multiple image files
```

## 🛠️ 故障排除

### 常见问题

#### 1. 环境问题
```bash
# 检查Python版本
python --version

# 检查包安装
pip list | grep fastapi

# 重新安装依赖
pip install -r requirements.txt --force-reinstall
```

#### 2. 模型加载失败
```bash
# 检查模型文件
ls -la models/

# 验证模型格式
python -c "import tensorflow as tf; print(tf.keras.models.load_model('models/your_model'))"
```

#### 3. 内存不足
```bash
# 监控内存使用
python -c "import psutil; print(f'Memory: {psutil.virtual_memory().percent}%')"

# 优化模型加载
# 使用模型量化或剪枝技术
```

### 性能优化

#### 1. 模型优化
- 使用TensorRT或ONNX Runtime
- 模型量化和剪枝
- 批处理推理

#### 2. 缓存策略
```python
from functools import lru_cache

@lru_cache(maxsize=100)
def preprocess_image(image_hash: str):
    # 缓存预处理结果
    pass
```

## 📖 相关文档

- [Conda环境管理指南](../CONDA_ENVIRONMENT_GUIDE.md)
- [项目启动指南](../STARTUP_GUIDE.md)
- [API接口文档](http://localhost:6800/docs)
- [FastAPI官方文档](https://fastapi.tiangolo.com/)

---

**AI服务开发愉快！** 🤖✨
