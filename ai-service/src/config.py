"""
AI Service Configuration
AI服务配置管理
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """应用配置"""
    
    # 应用基础配置
    app_name: str = Field(default="Yogurt AI QC - Analysis Service", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    
    # 服务配置
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=6800, env="PORT")
    workers: int = Field(default=1, env="WORKERS")
    
    # Redis 配置
    redis_url: str = Field(default="redis://localhost:6479", env="REDIS_URL")
    redis_host: str = Field(default="localhost", env="REDIS_HOST")
    redis_port: int = Field(default=6479, env="REDIS_PORT")
    redis_password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    redis_db: int = Field(default=0, env="REDIS_DB")
    
    # AI API 配置
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    openai_model: str = Field(default="gpt-4-vision-preview", env="OPENAI_MODEL")
    openai_max_tokens: int = Field(default=4096, env="OPENAI_MAX_TOKENS")
    openai_temperature: float = Field(default=0.1, env="OPENAI_TEMPERATURE")
    
    google_api_key: Optional[str] = Field(default=None, env="GOOGLE_API_KEY")
    gemini_model: str = Field(default="gemini-pro-vision", env="GEMINI_MODEL")
    
    anthropic_api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    claude_model: str = Field(default="claude-3-sonnet-20240229", env="CLAUDE_MODEL")
    
    # 分析配置
    analysis_timeout: int = Field(default=30, env="ANALYSIS_TIMEOUT")  # 秒
    max_retries: int = Field(default=3, env="MAX_RETRIES")
    batch_size: int = Field(default=4, env="BATCH_SIZE")
    max_concurrent_analyses: int = Field(default=10, env="MAX_CONCURRENT_ANALYSES")
    
    # 图像处理配置
    max_image_size: int = Field(default=10 * 1024 * 1024, env="MAX_IMAGE_SIZE")  # 10MB
    supported_formats: List[str] = Field(
        default=["jpg", "jpeg", "png", "tiff", "bmp"],
        env="SUPPORTED_FORMATS"
    )
    image_quality: int = Field(default=95, env="IMAGE_QUALITY")
    max_image_dimension: int = Field(default=2048, env="MAX_IMAGE_DIMENSION")
    
    # 模型配置
    model_cache_dir: str = Field(default="./models", env="MODEL_CACHE_DIR")
    model_cache_size: int = Field(default=2048, env="MODEL_CACHE_SIZE")  # MB
    enable_model_caching: bool = Field(default=True, env="ENABLE_MODEL_CACHING")
    
    # 安全配置
    api_key: Optional[str] = Field(default=None, env="API_KEY")
    cors_origins: List[str] = Field(
        default=["http://localhost:6000", "http://localhost:6173"],
        env="CORS_ORIGINS"
    )
    
    # 速率限制配置
    rate_limit_requests: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    rate_limit_window: int = Field(default=3600, env="RATE_LIMIT_WINDOW")  # 秒
    
    # 日志配置
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(default="json", env="LOG_FORMAT")
    log_file: Optional[str] = Field(default=None, env="LOG_FILE")
    
    # 监控配置
    enable_metrics: bool = Field(default=True, env="ENABLE_METRICS")
    metrics_port: int = Field(default=9090, env="METRICS_PORT")
    
    # Sentry 错误追踪
    sentry_dsn: Optional[str] = Field(default=None, env="SENTRY_DSN")
    
    # 存储配置
    temp_dir: str = Field(default="./temp", env="TEMP_DIR")
    upload_dir: str = Field(default="./uploads", env="UPLOAD_DIR")
    
    # AWS S3 配置（可选）
    aws_access_key_id: Optional[str] = Field(default=None, env="AWS_ACCESS_KEY_ID")
    aws_secret_access_key: Optional[str] = Field(default=None, env="AWS_SECRET_ACCESS_KEY")
    aws_region: str = Field(default="us-west-2", env="AWS_REGION")
    s3_bucket: Optional[str] = Field(default=None, env="S3_BUCKET")
    
    # 数据库配置（用于结果存储）
    database_url: Optional[str] = Field(default=None, env="DATABASE_URL")
    
    # 实验配置
    enable_experimental_features: bool = Field(default=False, env="ENABLE_EXPERIMENTAL_FEATURES")
    enable_model_ensemble: bool = Field(default=False, env="ENABLE_MODEL_ENSEMBLE")
    enable_result_caching: bool = Field(default=True, env="ENABLE_RESULT_CACHING")
    result_cache_ttl: int = Field(default=3600, env="RESULT_CACHE_TTL")  # 秒
    
    # 性能配置
    enable_gpu: bool = Field(default=False, env="ENABLE_GPU")
    gpu_memory_limit: Optional[int] = Field(default=None, env="GPU_MEMORY_LIMIT")  # MB
    enable_mixed_precision: bool = Field(default=False, env="ENABLE_MIXED_PRECISION")
    
    # 质量控制配置
    min_confidence_threshold: float = Field(default=0.7, env="MIN_CONFIDENCE_THRESHOLD")
    enable_quality_checks: bool = Field(default=True, env="ENABLE_QUALITY_CHECKS")
    enable_result_validation: bool = Field(default=True, env="ENABLE_RESULT_VALIDATION")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# 创建全局设置实例
settings = Settings()

# 验证配置
def validate_settings():
    """验证配置的有效性"""
    errors = []
    
    # 检查必需的 API 密钥
    if not any([settings.openai_api_key, settings.google_api_key, settings.anthropic_api_key]):
        errors.append("At least one AI API key must be configured")
    
    # 检查目录
    import os
    for directory in [settings.model_cache_dir, settings.temp_dir, settings.upload_dir]:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory, exist_ok=True)
            except Exception as e:
                errors.append(f"Cannot create directory {directory}: {e}")
    
    # 检查 GPU 配置
    if settings.enable_gpu:
        try:
            import torch
            if not torch.cuda.is_available():
                errors.append("GPU enabled but CUDA not available")
        except ImportError:
            errors.append("GPU enabled but PyTorch not installed")
    
    if errors:
        raise ValueError(f"Configuration errors: {'; '.join(errors)}")


# 在导入时验证配置
try:
    validate_settings()
except ValueError as e:
    print(f"Configuration validation failed: {e}")
    # 在开发环境中可能需要继续运行
    if not settings.debug:
        raise


# 导出常用配置
__all__ = ["settings", "validate_settings"]
