"""
Yogurt AI QC - AI Analysis Service
AI驱动的酸奶质量控制分析服务
"""

import os
import sys
import asyncio
from contextlib import asynccontextmanager
from typing import List, Optional

import uvicorn
from fastapi import FastAPI, HTTPException, UploadFile, File, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from loguru import logger
import redis.asyncio as redis

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.config import settings
from src.services.image_analyzer import ImageAnalyzer
from src.services.model_manager import ModelManager
from src.utils.image_processor import ImageProcessor
from src.utils.logger import setup_logger
from src.middleware.auth import verify_api_key
from src.middleware.rate_limit import RateLimitMiddleware
from src.models.analysis import AnalysisRequest, AnalysisResult, AnalysisStatus

# 设置日志
setup_logger()

# 全局变量
image_analyzer: Optional[ImageAnalyzer] = None
model_manager: Optional[ModelManager] = None
image_processor: Optional[ImageProcessor] = None
redis_client: Optional[redis.Redis] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global image_analyzer, model_manager, image_processor, redis_client
    
    logger.info("🚀 Starting AI Analysis Service...")
    
    try:
        # 初始化 Redis 连接
        redis_client = redis.from_url(
            settings.redis_url,
            encoding="utf-8",
            decode_responses=True
        )
        await redis_client.ping()
        logger.info("✅ Redis connected successfully")
        
        # 初始化服务组件
        model_manager = ModelManager()
        await model_manager.initialize()
        logger.info("✅ Model Manager initialized")
        
        image_processor = ImageProcessor()
        logger.info("✅ Image Processor initialized")
        
        image_analyzer = ImageAnalyzer(model_manager, image_processor)
        await image_analyzer.initialize()
        logger.info("✅ Image Analyzer initialized")
        
        logger.info("🎉 AI Analysis Service started successfully!")
        
        yield
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize service: {e}")
        raise
    finally:
        # 清理资源
        logger.info("🔄 Shutting down AI Analysis Service...")
        
        if redis_client:
            await redis_client.close()
            logger.info("✅ Redis connection closed")
        
        if model_manager:
            await model_manager.cleanup()
            logger.info("✅ Model Manager cleaned up")
        
        logger.info("👋 AI Analysis Service shutdown complete")


# 创建 FastAPI 应用
app = FastAPI(
    title="Yogurt AI QC - Analysis Service",
    description="AI-powered yogurt quality control analysis service",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(RateLimitMiddleware)

# Pydantic 模型
class HealthResponse(BaseModel):
    status: str = "healthy"
    version: str = "1.0.0"
    timestamp: str
    services: dict
    models_loaded: List[str]


class AnalysisTaskResponse(BaseModel):
    task_id: str
    status: AnalysisStatus
    message: str


class BatchAnalysisRequest(BaseModel):
    batch_id: str
    images: List[str] = Field(..., description="Base64 encoded images or image URLs")
    magnification: int = Field(..., ge=100, le=2000, description="Microscope magnification")
    staining_method: str = Field(..., description="Staining method used")
    metadata: Optional[dict] = None


# API 路由
@app.get("/", response_model=dict)
async def root():
    """根路径信息"""
    return {
        "name": "Yogurt AI QC - Analysis Service",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs",
        "health": "/health"
    }


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查端点"""
    from datetime import datetime
    
    services_status = {}
    
    # 检查 Redis 连接
    try:
        if redis_client:
            await redis_client.ping()
            services_status["redis"] = "healthy"
        else:
            services_status["redis"] = "not_initialized"
    except Exception as e:
        services_status["redis"] = f"error: {str(e)}"
    
    # 检查模型管理器
    if model_manager:
        services_status["model_manager"] = "healthy"
        loaded_models = model_manager.get_loaded_models()
    else:
        services_status["model_manager"] = "not_initialized"
        loaded_models = []
    
    # 检查图像处理器
    services_status["image_processor"] = "healthy" if image_processor else "not_initialized"
    
    # 检查图像分析器
    services_status["image_analyzer"] = "healthy" if image_analyzer else "not_initialized"
    
    return HealthResponse(
        timestamp=datetime.utcnow().isoformat(),
        services=services_status,
        models_loaded=loaded_models
    )


@app.post("/analyze", response_model=AnalysisResult)
async def analyze_images(
    files: List[UploadFile] = File(...),
    magnification: int = Field(..., ge=100, le=2000),
    staining_method: str = Field(...),
    batch_id: Optional[str] = None,
    api_key: str = Depends(verify_api_key)
):
    """
    同步图像分析端点
    上传图像文件进行即时分析
    """
    if not image_analyzer:
        raise HTTPException(status_code=503, detail="Image analyzer not initialized")
    
    if len(files) == 0:
        raise HTTPException(status_code=400, detail="No images provided")
    
    if len(files) > 5:
        raise HTTPException(status_code=400, detail="Too many images (max 5)")
    
    try:
        logger.info(f"Starting analysis for {len(files)} images")
        
        # 读取和预处理图像
        images = []
        for file in files:
            if not file.content_type or not file.content_type.startswith('image/'):
                raise HTTPException(status_code=400, detail=f"Invalid file type: {file.filename}")
            
            content = await file.read()
            processed_image = await image_processor.preprocess_image(content)
            images.append(processed_image)
        
        # 执行分析
        result = await image_analyzer.analyze_batch(
            images=images,
            magnification=magnification,
            staining_method=staining_method,
            batch_id=batch_id
        )
        
        logger.info(f"Analysis completed successfully for batch {batch_id}")
        return result
        
    except Exception as e:
        logger.error(f"Analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@app.post("/analyze/async", response_model=AnalysisTaskResponse)
async def analyze_images_async(
    background_tasks: BackgroundTasks,
    request: BatchAnalysisRequest,
    api_key: str = Depends(verify_api_key)
):
    """
    异步图像分析端点
    提交分析任务，返回任务ID
    """
    if not image_analyzer:
        raise HTTPException(status_code=503, detail="Image analyzer not initialized")
    
    try:
        # 生成任务ID
        import uuid
        task_id = str(uuid.uuid4())
        
        # 将任务添加到后台处理
        background_tasks.add_task(
            process_analysis_task,
            task_id=task_id,
            request=request
        )
        
        # 在 Redis 中存储任务状态
        if redis_client:
            await redis_client.setex(
                f"task:{task_id}",
                3600,  # 1小时过期
                "processing"
            )
        
        logger.info(f"Async analysis task {task_id} submitted for batch {request.batch_id}")
        
        return AnalysisTaskResponse(
            task_id=task_id,
            status=AnalysisStatus.PROCESSING,
            message="Analysis task submitted successfully"
        )
        
    except Exception as e:
        logger.error(f"Failed to submit async analysis task: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to submit task: {str(e)}")


@app.get("/analyze/status/{task_id}")
async def get_analysis_status(
    task_id: str,
    api_key: str = Depends(verify_api_key)
):
    """获取异步分析任务状态"""
    if not redis_client:
        raise HTTPException(status_code=503, detail="Redis not available")
    
    try:
        # 从 Redis 获取任务状态
        status = await redis_client.get(f"task:{task_id}")
        if not status:
            raise HTTPException(status_code=404, detail="Task not found")
        
        # 如果任务完成，获取结果
        result = None
        if status == "completed":
            result_data = await redis_client.get(f"result:{task_id}")
            if result_data:
                import json
                result = json.loads(result_data)
        
        return {
            "task_id": task_id,
            "status": status,
            "result": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get task status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get status: {str(e)}")


async def process_analysis_task(task_id: str, request: BatchAnalysisRequest):
    """处理异步分析任务"""
    try:
        logger.info(f"Processing async analysis task {task_id}")
        
        # 处理图像数据
        images = []
        for image_data in request.images:
            if image_data.startswith('data:image'):
                # Base64 编码的图像
                import base64
                header, data = image_data.split(',', 1)
                image_bytes = base64.b64decode(data)
            else:
                # 图像 URL
                import httpx
                async with httpx.AsyncClient() as client:
                    response = await client.get(image_data)
                    image_bytes = response.content
            
            processed_image = await image_processor.preprocess_image(image_bytes)
            images.append(processed_image)
        
        # 执行分析
        result = await image_analyzer.analyze_batch(
            images=images,
            magnification=request.magnification,
            staining_method=request.staining_method,
            batch_id=request.batch_id
        )
        
        # 存储结果
        if redis_client:
            import json
            await redis_client.setex(
                f"result:{task_id}",
                3600,  # 1小时过期
                json.dumps(result.dict())
            )
            await redis_client.setex(f"task:{task_id}", 3600, "completed")
        
        logger.info(f"Async analysis task {task_id} completed successfully")
        
    except Exception as e:
        logger.error(f"Async analysis task {task_id} failed: {str(e)}")
        
        # 更新任务状态为失败
        if redis_client:
            await redis_client.setex(f"task:{task_id}", 3600, "failed")


# 错误处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    logger.error(f"Global exception: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=6800,
        reload=True,
        log_level="info"
    )
