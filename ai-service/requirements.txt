# =============================================================================
# Yoghurt AI QC - AI Service Dependencies
# =============================================================================

# Web 框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.4.2
python-multipart==0.0.6

# HTTP 客户端
httpx==0.25.2
requests==2.31.0
aiohttp==3.9.0

# 图像处理
opencv-python==********
Pillow==10.0.1
scikit-image==0.22.0
imageio==2.31.6

# 科学计算
numpy==1.24.3
scipy==1.11.4
pandas==2.1.3

# 机器学习
scikit-learn==1.3.2
tensorflow==2.14.0
torch==2.1.1
torchvision==0.16.1
transformers==4.35.2

# 图像分析和计算机视觉
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# AI API 客户端
openai>=1.0.0
anthropic==0.7.7
google-generativeai==0.3.1

# 数据库
asyncpg==0.29.0
redis==5.0.1
sqlalchemy==2.0.23
alembic==1.12.1

# 异步任务处理
celery==5.3.4
kombu==5.3.4

# 配置和环境
python-dotenv==1.0.0
pyyaml==6.0.1
toml==0.10.2

# 日志和监控
loguru==0.7.2
prometheus-client==0.19.0
sentry-sdk==1.38.0

# 数据验证和序列化
marshmallow==3.20.1
cerberus==1.3.5

# 文件处理
aiofiles==23.2.1
python-magic==0.4.27

# 时间处理
python-dateutil==2.8.2
pytz==2023.3

# 加密和安全
cryptography==41.0.7
passlib==1.7.4
python-jose[cryptography]==3.3.0

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
httpx==0.25.2

# 代码质量
black==23.11.0
flake8==6.1.0
mypy==1.7.1
isort==5.12.0
pre-commit==3.5.0

# 性能分析
memory-profiler==0.61.0
line-profiler==4.1.1
py-spy==0.3.14

# 数据格式
jsonschema==4.20.0
xmltodict==0.13.0
openpyxl==3.1.2

# 网络和通信
websockets==12.0
python-socketio==5.10.0

# 缓存
cachetools==5.3.2
diskcache==5.6.3

# 工具库
click==8.1.7
rich==13.7.0
tqdm==4.66.1
more-itertools==10.1.0

# 开发工具
ipython==8.17.2
jupyter==1.0.0
notebook==7.0.6

# 模型服务
mlflow==2.8.1
bentoml==1.1.10

# 图像增强和预处理
albumentations==1.3.1
imgaug==0.4.0

# 深度学习工具
timm==0.9.12
torchmetrics==1.2.0
lightning==2.1.2

# 自然语言处理（用于结果描述生成）
spacy==3.7.2
nltk==3.8.1

# 数据可视化
bokeh==3.3.0
altair==5.1.2

# 并发和异步
asyncio-mqtt==0.16.1
aioredis==2.0.1

# 系统监控
psutil==5.9.6
GPUtil==1.4.0

# 模型优化
onnx==1.15.0
onnxruntime==1.16.3
tensorrt==8.6.1.post1

# 实验跟踪
wandb==0.16.0
tensorboard==2.15.1

# 数据管道
dask==2023.11.0
joblib==1.3.2

# API 文档
fastapi-users==12.1.2
fastapi-pagination==0.12.13
