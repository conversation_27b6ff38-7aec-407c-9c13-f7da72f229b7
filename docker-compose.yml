services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: yoghurt-postgres
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    ports:
      - "6432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - yoghurt-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: yoghurt-redis
    ports:
      - "6479:6379"
    volumes:
      - redis_data:/data
    networks:
      - yoghurt-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 后端 API 服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: yoghurt-backend
    ports:
      - "6000:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=postgresql://postgres:${DB_PASSWORD}@postgres:5432/postgres
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - AI_SERVICE_URL=http://ai-service:8000
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - yoghurt-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: npm run dev

  # AI 分析服务
  ai-service:
    build:
      context: ./ai-service
      dockerfile: Dockerfile.dev
    container_name: yoghurt-ai-service
    ports:
      - "6800:8000"
    environment:
      - PYTHONPATH=/app
      - REDIS_URL=redis://redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - MODEL_CACHE_DIR=/app/models
    volumes:
      - ./ai-service:/app
      - ai_models:/app/models
    networks:
      - yoghurt-network
    depends_on:
      redis:
        condition: service_healthy
    command: uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload

  # 前端开发服务器
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: yoghurt-frontend
    ports:
      - "6173:5173"
    environment:
      - VITE_API_URL=http://localhost:6000
      - VITE_AI_SERVICE_URL=http://localhost:6800
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - yoghurt-network
    depends_on:
      - backend
    command: npm run dev -- --host 0.0.0.0

volumes:
  postgres_data:
  redis_data:
  ai_models:

networks:
  yoghurt-network:
    driver: bridge
