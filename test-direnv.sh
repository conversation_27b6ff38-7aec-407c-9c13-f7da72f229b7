#!/bin/zsh

# =============================================================================
# 测试direnv配置
# =============================================================================

echo "🧪 测试direnv配置..."

# 设置PATH
export PATH="/opt/homebrew/bin:$PATH"

# 检查direnv
echo "1. 检查direnv:"
which direnv
direnv version

echo ""
echo "2. 检查.envrc文件:"
ls -la .envrc

echo ""
echo "3. 允许.envrc:"
direnv allow

echo ""
echo "4. 检查direnv状态:"
direnv status

echo ""
echo "5. 初始化direnv hook:"
eval "$(direnv hook zsh)"

echo ""
echo "6. 重新加载环境:"
direnv reload

echo ""
echo "7. 检查环境变量:"
echo "CONDA_DEFAULT_ENV: $CONDA_DEFAULT_ENV"
echo "PROJECT_ROOT: $PROJECT_ROOT"
echo "AI_SERVICE_PORT: $AI_SERVICE_PORT"

echo ""
echo "✅ 测试完成！"
