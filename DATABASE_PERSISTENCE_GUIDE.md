# 🗄️ Yogurt AI QC - 数据库持久化存储指南

## ✅ 当前配置状态

您的项目已经正确配置了数据持久化存储！

### 📊 持久化配置概览

| 服务 | 数据卷 | 挂载路径 | 状态 |
|------|--------|----------|------|
| PostgreSQL | `postgres_data` | `/var/lib/postgresql/data` | ✅ 已配置 |
| Redis | `redis_data` | `/data` | ✅ 已配置 |
| AI模型 | `ai_models` | `/app/models` | ✅ 已配置 |

## 🔧 技术实现

### Docker Compose配置
```yaml
services:
  postgres:
    volumes:
      - postgres_data:/var/lib/postgresql/data  # PostgreSQL数据目录
  
  redis:
    volumes:
      - redis_data:/data  # Redis数据目录
  
  ai-service:
    volumes:
      - ai_models:/app/models  # AI模型缓存目录

volumes:
  postgres_data:    # 命名卷，由Docker管理
  redis_data:       # 命名卷，由Docker管理
  ai_models:        # 命名卷，由Docker管理
```

### 数据存储位置
- **PostgreSQL数据**: `/var/lib/docker/volumes/yoghurt_postgres_data/_data`
- **Redis数据**: `/var/lib/docker/volumes/yoghurt_redis_data/_data`
- **AI模型**: `/var/lib/docker/volumes/yoghurt_ai_models/_data`

## 🛡️ 数据持久化验证

### 验证数据持久化
```bash
# 1. 创建测试数据
docker exec yoghurt-postgres psql -U postgres -d postgres -c "CREATE TABLE test_persistence (id SERIAL, data TEXT);"
docker exec yoghurt-postgres psql -U postgres -d postgres -c "INSERT INTO test_persistence (data) VALUES ('持久化测试数据');"

# 2. 停止容器
docker-compose down

# 3. 重新启动容器
docker-compose up -d postgres redis

# 4. 验证数据是否存在
docker exec yoghurt-postgres psql -U postgres -d postgres -c "SELECT * FROM test_persistence;"

# 5. 清理测试数据
docker exec yoghurt-postgres psql -U postgres -d postgres -c "DROP TABLE test_persistence;"
```

## 📋 数据管理命令

### 查看数据卷
```bash
# 查看所有项目数据卷
docker volume ls | grep yoghurt

# 查看特定卷的详细信息
docker volume inspect yoghurt_postgres_data
docker volume inspect yoghurt_redis_data

# 查看卷的使用情况
docker system df -v
```

### 数据备份
```bash
# PostgreSQL备份
docker exec yoghurt-postgres pg_dump -U postgres postgres > backup_$(date +%Y%m%d_%H%M%S).sql

# Redis备份
docker exec yoghurt-redis redis-cli BGSAVE
docker cp yoghurt-redis:/data/dump.rdb redis_backup_$(date +%Y%m%d_%H%M%S).rdb
```

### 数据恢复
```bash
# PostgreSQL恢复
docker exec -i yoghurt-postgres psql -U postgres postgres < backup_file.sql

# Redis恢复
docker cp redis_backup.rdb yoghurt-redis:/data/dump.rdb
docker restart yoghurt-redis
```

## ⚠️ 重要注意事项

### 数据安全
1. **定期备份**: 建议每日自动备份重要数据
2. **版本控制**: 不要将数据卷加入Git版本控制
3. **权限管理**: 确保数据卷有正确的访问权限

### 容器操作
- ✅ **安全操作**: `docker-compose down` - 停止容器但保留数据
- ✅ **安全操作**: `docker-compose up -d` - 重启容器，数据保持
- ⚠️ **谨慎操作**: `docker-compose down -v` - 会删除数据卷！
- ❌ **危险操作**: `docker volume rm yoghurt_postgres_data` - 永久删除数据！

## 🔄 数据迁移

### 迁移到新环境
```bash
# 1. 备份当前数据
./scripts/backup-database.sh

# 2. 在新环境中启动服务
docker-compose up -d postgres redis

# 3. 恢复数据
./scripts/restore-database.sh backup_file.sql
```

## 🛠️ 故障排除

### 数据丢失排查
1. **检查卷是否存在**: `docker volume ls | grep yoghurt`
2. **检查卷挂载**: `docker inspect yoghurt-postgres | grep Mounts -A 10`
3. **检查数据目录**: `docker exec yoghurt-postgres ls -la /var/lib/postgresql/data`

### 常见问题
- **权限问题**: 确保容器有读写数据卷的权限
- **空间不足**: 定期清理不需要的数据和日志
- **卷损坏**: 使用备份恢复数据

## 📈 最佳实践

1. **自动备份**: 设置定时任务进行数据备份
2. **监控存储**: 监控数据卷的使用情况
3. **测试恢复**: 定期测试备份恢复流程
4. **文档记录**: 记录重要的数据操作和变更

## 🛠️ 管理工具

项目提供了完整的数据管理工具集：

### 数据卷管理
```bash
./scripts/manage-volumes.sh list     # 列出数据卷
./scripts/manage-volumes.sh usage    # 查看存储使用
./scripts/manage-volumes.sh info     # 详细信息
```

### 数据备份和恢复
```bash
./scripts/backup-database.sh         # 备份数据库
./scripts/restore-database.sh backup.sql.gz  # 恢复数据库
```

### 持久化测试
```bash
./scripts/test-persistence.sh        # 测试数据持久化
```

## 🎯 总结

您的数据持久化配置已经正确设置并通过测试：
- ✅ 使用Docker命名卷确保数据持久化
- ✅ 数据在容器重启后保持不变
- ✅ 支持数据备份和恢复
- ✅ 提供完整的管理工具
- ✅ 通过自动化测试验证
- ✅ 遵循Docker最佳实践

数据安全有保障！🛡️

### 测试结果
- ✅ PostgreSQL数据持久化测试通过
- ✅ Redis数据持久化测试通过
- ✅ 容器重启后数据完整保留
- ✅ 数据卷正常工作
