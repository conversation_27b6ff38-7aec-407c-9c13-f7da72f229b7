# 多租户酸奶质控系统架构实现

## 🏗️ 系统架构概述

实现了基于咖啡厅的多租户架构，每个咖啡厅作为一个独立的租户，数据完全隔离。

### 用户角色体系

1. **管理员 (ADMIN)**
   - 可以看到所有咖啡厅的数据
   - 管理用户和系统设置
   - 跨租户权限

2. **质检员 (USER)** 
   - 每个质检员隶属于特定咖啡厅
   - 只能看到和管理自己创建的产品
   - 负责产品上架和质量检测

3. **消费者 (VIEWER)**
   - 隶属于特定咖啡厅
   - 只能查看所属咖啡厅的产品信息
   - 查看质量检测结果

## 📊 数据库设计

### 核心表结构

```sql
-- 咖啡厅表（租户表）
CREATE TABLE cafes (
    id UUID PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    status VARCHAR(20) DEFAULT 'active'
);

-- 用户表（添加咖啡厅关联）
ALTER TABLE users 
ADD COLUMN cafe_id UUID REFERENCES cafes(id);

-- 产品表（租户隔离）
CREATE TABLE products (
    id UUID PRIMARY KEY,
    cafe_id UUID NOT NULL REFERENCES cafes(id),
    creator_id UUID NOT NULL REFERENCES users(id),
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    price DECIMAL(10, 2),
    -- 其他产品字段...
);

-- 质量检测表（租户隔离）
CREATE TABLE quality_tests (
    id UUID PRIMARY KEY,
    cafe_id UUID NOT NULL REFERENCES cafes(id),
    product_id UUID NOT NULL REFERENCES products(id),
    tester_id UUID NOT NULL REFERENCES users(id),
    -- 检测数据字段...
);
```

### 数据隔离策略

1. **管理员**: 无限制，可查看所有数据
2. **质检员**: `WHERE creator_id = $user_id`
3. **消费者**: `WHERE cafe_id = $user_cafe_id AND status = 'active'`

## 🔐 权限控制实现

### 后端API权限

```typescript
// 产品API权限检查示例
router.get('/products', authMiddleware, async (req, res) => {
  let whereClause = ''
  let queryParams = []

  if (req.user.role === 'ADMIN') {
    // 管理员看所有
    whereClause = 'WHERE 1=1'
  } else if (req.user.role === 'USER') {
    // 质检员只看自己的
    whereClause = 'WHERE creator_id = $1'
    queryParams.push(req.user.id)
  } else if (req.user.role === 'VIEWER') {
    // 消费者只看所属咖啡厅的激活产品
    whereClause = 'WHERE cafe_id = $1 AND status = $2'
    queryParams.push(req.user.cafeId, 'active')
  }
})
```

### 前端路由权限

```typescript
// 菜单权限配置
const menuItems = [
  {
    key: '/product-management',
    label: '产品管理',
    roles: [UserRole.USER], // 只有质检员可见
  },
  {
    key: '/products',
    label: '产品信息', 
    roles: [UserRole.VIEWER], // 只有消费者可见
  }
]
```

## 🚀 功能实现

### 1. 用户注册流程

- **咖啡厅选择**: 用户注册时必须选择所属咖啡厅
- **角色选择**: 可选择"消费者"或"质检员"身份
- **数据关联**: 自动建立用户与咖啡厅的关联关系

### 2. 产品管理（质检员）

- **创建产品**: 质检员可以为自己的咖啡厅创建产品
- **编辑产品**: 只能编辑自己创建的产品
- **产品状态**: 控制产品的上架/下架状态
- **营养信息**: 管理详细的产品营养成分

### 3. 产品查看（消费者）

- **店铺产品**: 只能查看所属咖啡厅的在售产品
- **质量信息**: 查看产品的质量检测结果
- **透明信息**: 配料表、营养成分、认证信息

### 4. 数据隔离

- **完全隔离**: 不同咖啡厅的数据完全隔离
- **权限验证**: API层面严格验证访问权限
- **安全防护**: 防止数据泄露和越权访问

## 📁 文件结构

```
backend/
├── migrations/
│   └── 002_multi_tenant_schema.sql     # 多租户数据库迁移
├── routes/
│   ├── auth.ts                         # 认证路由（含咖啡厅选择）
│   └── products.ts                     # 产品管理API
└── middleware/
    └── auth.ts                         # 权限验证中间件

frontend/
├── pages/
│   ├── Login.tsx                       # 登录页（含咖啡厅选择）
│   ├── Products.tsx                    # 产品展示页（消费者）
│   ├── ProductManagement.tsx           # 产品管理页（质检员）
│   └── QualityResults.tsx              # 质量检测结果页
├── components/
│   └── Layout/index.tsx                # 导航菜单权限控制
└── types/
    └── permissions.ts                  # 权限定义
```

## 🎯 用户体验

### 消费者视角
- ✅ 查看所属咖啡厅的产品目录
- ✅ 了解产品质量检测结果
- ✅ 获得透明的营养和认证信息
- ❌ 无法看到其他咖啡厅的产品
- ❌ 无法进行任何管理操作

### 质检员视角
- ✅ 管理自己创建的产品
- ✅ 添加新产品到咖啡厅目录
- ✅ 控制产品的上架/下架状态
- ✅ 进行质量检测和记录
- ❌ 无法看到其他质检员的产品
- ❌ 无法访问其他咖啡厅数据

### 管理员视角
- ✅ 查看所有咖啡厅和产品
- ✅ 管理用户和系统设置
- ✅ 跨租户数据分析
- ✅ 系统维护和监控

## 📊 数据示例

### 示例咖啡厅
- **海口旗舰店**: 海南省海口市龙华区
- **三亚分店**: 海南省三亚市天涯区  
- **琼海分店**: 海南省琼海市嘉积镇

### 示例产品
- **海口旗舰店**:
  - 海口经典原味酸奶 (¥12.80)
  - 椰香酸奶 (¥15.80)
- **三亚分店**:
  - 三亚芒果酸奶 (¥18.80)

## 🔧 技术特点

1. **数据安全**: 严格的租户数据隔离
2. **权限精细**: 基于角色和租户的双重权限控制
3. **扩展性强**: 可轻松添加新的咖啡厅租户
4. **用户友好**: 不同角色看到不同的界面和功能
5. **代码复用**: 同一套代码支持多租户

## ✅ 实现状态

- [x] 多租户数据库架构设计
- [x] 用户注册流程（咖啡厅选择）
- [x] 产品管理API和界面
- [x] 数据权限隔离逻辑
- [x] 前端菜单权限控制
- [x] 消费者产品查看功能
- [x] 质检员产品管理功能

**多租户架构已完整实现** ✅

每个咖啡厅现在是一个独立的租户，质检员负责产品管理，消费者只能查看所属咖啡厅的产品，管理员可以管理所有数据。