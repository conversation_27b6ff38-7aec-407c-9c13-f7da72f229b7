# 🚀 Yogurt AI QC 启动指南

> **文档生成日期**: 2025年7月31日  
> **版本**: v1.0.0  
> **状态**: 生产就绪  

## 📋 目录

- [系统要求](#系统要求)
- [快速启动](#快速启动)
- [启动选项](#启动选项)
- [服务访问](#服务访问)
- [常见问题](#常见问题)
- [管理命令](#管理命令)
- [开发环境配置](#开发环境配置)

---

## ⚡ 快速启动

如果您是第一次使用，请按以下步骤操作：

```bash
# 1. 进入项目目录
cd /Volumes/acasis/yoghurt

# 2. 启动所有服务（推荐）
./scripts/start-services-with-logs.sh all
```

**就这么简单！** 🎉

---

## 🔧 系统要求

### 必需组件
- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0
- **Docker**: >= 20.0.0
- **Docker Compose**: >= 2.0.0

### 可选组件
- **Conda**: 用于AI服务（如需要）

### 端口要求
确保以下端口未被占用：
- `3010` - 后端API服务
- `6174` - 前端应用
- `6432` - PostgreSQL数据库
- `6479` - Redis缓存
- `6800` - AI服务（可选）

---

## 🚀 启动选项

### 选项1：完整启动（推荐新用户）
```bash
./scripts/start-all-services.sh
```

**特点**：
- ✅ 自动检查系统要求
- ✅ 自动安装依赖
- ✅ 自动启动Docker服务
- ✅ 自动初始化数据库
- ✅ 前台运行，实时显示日志
- ✅ Ctrl+C 停止所有服务

### 选项2：带日志启动（推荐开发者）
```bash
./scripts/start-services-with-logs.sh all
```

**特点**：
- ✅ 快速启动
- ✅ 实时日志显示
- ✅ 交互式选择启动模式
- ✅ 智能依赖检查

### 选项3：后台稳定启动
```bash
./scripts/start-services-stable.sh
```

**特点**：
- ✅ 后台运行
- ✅ 服务持久化
- ✅ 适合生产环境

---

## 🌐 服务访问

启动成功后，您可以访问以下服务：

| 服务 | 端口 | 访问地址 | 说明 |
|------|------|----------|------|
| 🖥️ 前端应用 | 6174 | http://localhost:6174 | 主要用户界面 |
| 🔧 后端API | 3010 | http://localhost:3010 | REST API服务 |
| 📚 API文档 | 3010 | http://localhost:3010/api/docs | Swagger文档 |
| ❤️ 健康检查 | 3010 | http://localhost:3010/health | 服务状态检查 |
| 🗄️ PostgreSQL | 6432 | localhost:6432 | 数据库服务 |
| 🔥 Redis | 6479 | localhost:6479 | 缓存服务 |

---

## 🔑 默认测试账户

```
📧 邮箱: <EMAIL>
🔒 密码: password123
👤 角色: 管理员
```

---

## 🎯 分别启动服务

### 只启动后端
```bash
./scripts/start-services-with-logs.sh backend
```

### 只启动前端
```bash
./scripts/start-services-with-logs.sh frontend
```

### 手动启动（开发调试）
```bash
# 启动后端
cd backend && npm run dev

# 启动前端（新终端）
cd frontend && npm run dev
```

---

## 📝 管理命令

### 查看日志
```bash
# 查看后端日志
tail -f logs/backend.log

# 查看前端日志
tail -f logs/frontend.log

# 查看实时日志
./scripts/start-services-with-logs.sh all
```

### 停止服务
```bash
# 停止所有服务
./scripts/stop-services.sh

# 或者在启动脚本中按 Ctrl+C
```

### 重启服务
```bash
# 重启所有服务
./scripts/restart-all-services.sh

# 或者先停止再启动
./scripts/stop-services.sh
./scripts/start-services-with-logs.sh all
```

### 健康检查
```bash
# 检查服务状态
./scripts/check-services.sh

# 或者直接访问健康检查端点
curl http://localhost:3010/health
```

---

## 🔧 开发环境配置

### 数据库管理
```bash
# 数据库迁移
cd backend && npm run db:migrate

# 重置数据库
cd backend && npm run db:reset

# 查看数据库（Prisma Studio）
cd backend && npm run db:studio
```

### 构建项目
```bash
# 构建后端
cd backend && npm run build

# 构建前端
cd frontend && npm run build
```

### 代码检查
```bash
# 后端代码检查
cd backend && npm run lint

# 前端代码检查
cd frontend && npm run lint
```

---

## ❓ 常见问题

### Q: 端口被占用怎么办？
```bash
# 查看端口占用
lsof -i :3010
lsof -i :6174

# 杀死占用进程
./scripts/cleanup-ports.sh
```

### Q: Docker服务启动失败？
```bash
# 检查Docker状态
docker info

# 重启Docker容器
docker-compose down
docker-compose up -d
```

### Q: 数据库连接失败？
```bash
# 检查数据库状态
docker ps | grep postgres

# 重启数据库
docker-compose restart postgres
```

### Q: 前端页面打不开？
1. 检查前端服务是否启动：`curl http://localhost:6174`
2. 查看前端日志：`tail -f logs/frontend.log`
3. 检查依赖：`cd frontend && npm install`

### Q: API请求失败？
1. 检查后端服务：`curl http://localhost:3010/health`
2. 查看后端日志：`tail -f logs/backend.log`
3. 检查数据库连接
4. 验证JWT token是否有效

---

## 🚨 故障排除

### 完整重置
如果遇到严重问题，可以执行完整重置：

```bash
# 1. 停止所有服务
./scripts/stop-services.sh

# 2. 清理Docker容器
docker-compose down -v

# 3. 清理node_modules
rm -rf node_modules frontend/node_modules backend/node_modules

# 4. 重新安装依赖
npm install
cd frontend && npm install && cd ..
cd backend && npm install && cd ..

# 5. 重新启动
./scripts/start-all-services.sh
```

### 日志调试
```bash
# 开启详细日志
DEBUG=* ./scripts/start-services-with-logs.sh all

# 或者查看特定服务日志
docker-compose logs postgres
docker-compose logs redis
```

---

## 📚 功能指南

### 🧪 配方管理
1. 访问 http://localhost:6174/recipes
2. 点击"新建配方"创建配方
3. 支持配方编辑、复制、删除
4. 支持配方版本管理

### 🥛 批次管理
1. 访问 http://localhost:6174/batches
2. 创建新批次并关联配方
3. 跟踪批次状态：计划中 → 进行中 → 发酵中 → 过滤中 → 完成
4. 上传发酵过程图片

### 📸 图片上传
1. 在批次详情页面上传图片
2. 支持多文件拖拽上传
3. 自动记录图片元数据
4. 支持图片预览和管理

---

## 🛡️ 安全说明

- 🔐 所有API请求需要JWT认证
- 👥 基于角色的权限控制（ADMIN/USER/VIEWER）
- 🗄️ 数据库密码自动生成并安全存储
- 📁 文件上传安全验证
- 🔒 HTTPS推荐用于生产环境

---

## 📞 获取帮助

如果您遇到问题，请：

1. 📖 查看此启动指南
2. 🔍 查看项目文档：`docs/` 目录
3. 📋 查看日志文件：`logs/` 目录
4. 🏥 运行健康检查：`./scripts/check-services.sh`

---

## ✅ 启动检查清单

在启动服务前，请确认：

- [ ] Docker Desktop 已启动并运行
- [ ] 所需端口未被占用
- [ ] 项目依赖已安装
- [ ] 环境变量已配置
- [ ] 数据库服务正常

---

**🎉 祝您使用愉快！**

> 最后更新：2025年7月31日  
> 如有问题，请查看项目文档或联系开发团队。