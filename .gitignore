# =============================================================================
# Yoghurt AI QC - Git 忽略文件配置
# =============================================================================

# =============================================================================
# 环境变量和配置文件
# =============================================================================
# 保留.env作为配置模板，排除包含敏感信息的环境文件
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production

# =============================================================================
# Node.js 相关
# =============================================================================
# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
*.lcov

# nyc 测试覆盖率
.nyc_output

# Grunt 中间存储
.grunt

# Bower 依赖目录
bower_components

# node-waf 配置
.lock-wscript

# 编译的二进制插件
build/Release

# TypeScript 缓存
*.tsbuildinfo

# 可选的 npm 缓存目录
.npm

# 可选的 eslint 缓存
.eslintcache

# 可选的 REPL 历史
.node_repl_history

# 输出的 npm 包
*.tgz

# Yarn 完整性文件
.yarn-integrity

# parcel-bundler 缓存
.cache
.parcel-cache

# Next.js 构建输出
.next

# Nuxt.js 构建/生成输出
.nuxt
dist

# Gatsby 文件
.cache/
public

# Vite 构建输出
dist/
dist-ssr/

# Rollup.js 默认构建输出
/dist/

# =============================================================================
# Python 相关
# =============================================================================
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# =============================================================================
# Conda 环境
# =============================================================================
# Conda 环境文件
.conda/
conda-meta/

# =============================================================================
# 数据库文件
# =============================================================================
*.db
*.sqlite
*.sqlite3

# =============================================================================
# 日志文件
# =============================================================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# =============================================================================
# 上传文件和临时文件
# =============================================================================
uploads/
temp/
tmp/
*.tmp
*.temp

# =============================================================================
# AI 模型和数据文件
# =============================================================================
models/
*.h5
*.pkl
*.joblib
*.model
*.weights
*.ckpt
*.pb

# 大型数据文件
*.csv
*.json
*.parquet
*.hdf5

# =============================================================================
# Docker 相关
# =============================================================================
# Docker 构建缓存
.dockerignore

# =============================================================================
# IDE 和编辑器
# =============================================================================
# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# 操作系统生成的文件
# =============================================================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# 测试和覆盖率报告
# =============================================================================
test-results/
coverage/
.nyc_output/
junit.xml

# =============================================================================
# 构建和部署相关
# =============================================================================
# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Kubernetes
*.kubeconfig

# =============================================================================
# 安全相关
# =============================================================================
# 私钥和证书
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx

# API 密钥文件
secrets/
.secrets/

# 环境变量备份文件
.env.backup
*.env.backup
.env.bak
*.env.bak

# 配置文件中的敏感信息
config/secrets.json
config/production.json
config/local.json

# 临时敏感文件
*.tmp.env
*.temp.env
.env.temp
.env.tmp

# IDE 和编辑器的敏感配置
.vscode/settings.json
.idea/workspace.xml

# 数据库转储文件
*.sql
*.dump
*.backup
database_backup/

# =============================================================================
# 数据库备份目录
# =============================================================================
# 备份目录包含敏感数据和大文件，应排除在版本控制之外
backups/
*.sql.gz
*.rdb.gz
backup_info_*.txt

# 但保留备份目录的README文档
!backups/README.md

# 日志文件可能包含敏感信息
*.log
logs/

# 安全扫描报告
security-report.*
vulnerability-report.*

# =============================================================================
# 项目特定文件
# =============================================================================
# PID文件
.backend.pid
.frontend.pid

# 测试文件
test-frontend-auth.html
clear-localStorage.js

# 密钥文件夹
.secrets/

# 日志目录
logs/
