#!/bin/zsh

# =============================================================================
# Yogurt AI QC - 完整的direnv配置脚本
# =============================================================================

echo "🚀 开始配置direnv自动环境激活..."

# 1. 确保在正确的目录
if [[ ! -f "package.json" ]]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

echo "✅ 在正确的项目目录"

# 2. 检查direnv
if [[ ! -f "/opt/homebrew/bin/direnv" ]]; then
    echo "❌ direnv未安装"
    exit 1
fi

echo "✅ direnv已安装: $(/opt/homebrew/bin/direnv version)"

# 3. 检查conda环境
if ! conda env list | grep -q "yoghurt-ai-qc"; then
    echo "❌ yoghurt-ai-qc conda环境不存在"
    echo "请先运行: ./scripts/create-latest-env.sh"
    exit 1
fi

echo "✅ yoghurt-ai-qc conda环境存在"

# 4. 配置zsh
echo "🔧 配置zsh..."

# 检查并添加direnv hook到.zshrc
if ! grep -q 'eval "$(direnv hook zsh)"' ~/.zshrc 2>/dev/null; then
    echo 'eval "$(direnv hook zsh)"' >> ~/.zshrc
    echo "✅ 已添加direnv hook到 ~/.zshrc"
else
    echo "✅ direnv hook已存在于 ~/.zshrc"
fi

# 5. 设置PATH并初始化direnv
export PATH="/opt/homebrew/bin:$PATH"
eval "$(direnv hook zsh)"

echo "✅ direnv已初始化"

# 6. 允许.envrc
echo "🔧 配置项目环境..."
direnv allow

echo "✅ 已允许.envrc文件"

# 7. 测试环境激活
echo "🧪 测试环境激活..."

# 重新加载环境
direnv reload

# 检查状态
echo ""
echo "📊 Direnv状态:"
direnv status

echo ""
echo "🔍 环境变量检查:"
echo "CONDA_DEFAULT_ENV: ${CONDA_DEFAULT_ENV:-未设置}"
echo "PROJECT_ROOT: ${PROJECT_ROOT:-未设置}"
echo "AI_SERVICE_PORT: ${AI_SERVICE_PORT:-未设置}"

# 8. 模拟目录切换测试
echo ""
echo "🔄 测试目录切换..."
current_dir=$(pwd)
cd ..
echo "离开项目目录..."
cd "$current_dir"
echo "重新进入项目目录..."

# 再次检查环境
echo ""
echo "🔍 重新检查环境变量:"
echo "CONDA_DEFAULT_ENV: ${CONDA_DEFAULT_ENV:-未设置}"
echo "PROJECT_ROOT: ${PROJECT_ROOT:-未设置}"

# 9. 显示结果
echo ""
if [[ "$CONDA_DEFAULT_ENV" == "yoghurt-ai-qc" ]]; then
    echo "🎉 成功！direnv自动环境激活已配置完成！"
    echo ""
    echo "📝 使用说明:"
    echo "1. 重新启动终端或运行: source ~/.zshrc"
    echo "2. 每次进入项目目录时会自动激活conda环境"
    echo "3. 离开项目目录时会自动清理环境变量"
    echo ""
    echo "🧪 测试命令:"
    echo "   cd .."
    echo "   cd $(basename $(pwd))"
    echo "   echo \$CONDA_DEFAULT_ENV"
else
    echo "⚠️  配置可能不完整，请尝试以下步骤:"
    echo "1. 重新启动终端"
    echo "2. 运行: source ~/.zshrc"
    echo "3. 进入项目目录: cd $(pwd)"
    echo "4. 如果仍有问题，使用备用方案: source activate-env.sh"
fi

echo ""
echo "✅ 配置脚本执行完成！"
