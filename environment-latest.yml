# =============================================================================
# Yogurt AI QC - 最新稳定版Conda环境配置
# =============================================================================
# 
# 环境名称: yoghurt-ai-qc
# Python版本: 3.12 (最新稳定版)
# 主要用途: 多模态API调用、图像预处理、Web服务
# 
# 设计理念: 
# - 使用2025年最新稳定版本
# - 专注于API调用和基础图像处理
# - 移除本地ML框架，使用云端API
# - 优化性能、安全性和兼容性
# 
# 创建环境: conda env create -f environment-latest.yml
# 激活环境: conda activate yoghurt-ai-qc
# 更新环境: conda env update -f environment-latest.yml --prune
# 
# 管理脚本: ./scripts/manage-conda-env.sh
# 更新时间: 2025年7月
# =============================================================================

name: yoghurt-ai-qc
channels:
  - conda-forge
  - defaults
dependencies:
  # Python 基础环境 (最新稳定版)
  - python=3.12
  - pip
  
  # Web 框架和API (最新稳定版)
  - fastapi
  - uvicorn
  - pydantic
  - python-multipart
  
  # 基础数据处理 (最新稳定版)
  - numpy
  - pandas
  
  # 基础图像处理 (最新稳定版)
  - opencv
  - pillow
  
  # 数据库连接 (最新稳定版)
  - psycopg2
  - sqlalchemy
  - redis-py
  
  # HTTP 客户端 (最新稳定版)
  - requests
  - httpx
  - aiohttp
  
  # 配置和环境 (最新稳定版)
  - python-dotenv
  - pyyaml
  
  # 日志 (最新稳定版)
  - loguru
  
  # 开发工具 (最新稳定版)
  - pytest
  - pytest-asyncio
  - black
  - flake8
  - mypy
  
  # pip 安装的包 (最新稳定版)
  - pip:
    # AI API 客户端 (最新版本)
    - openai==1.56.2
    - anthropic==0.40.0
    - google-generativeai==0.8.3
    
    # 异步数据库 (最新版本)
    - asyncpg==0.30.0
    - redis==5.2.0
    
    # 文件处理 (最新版本)
    - aiofiles==24.1.0
    - python-magic==0.4.27
    
    # 安全和认证 (最新版本)
    - python-jose[cryptography]==3.3.0
    - passlib[bcrypt]==1.7.4
    - cryptography==43.0.3
    
    # 时间处理 (最新版本)
    - python-dateutil==2.9.0.post0
    
    # 数据验证 (最新版本)
    - marshmallow==3.23.1
    
    # 监控 (最新版本)
    - prometheus-client==0.21.0
    
    # 图像格式支持 (最新版本)
    - imageio==2.36.0
    
    # 性能优化 (最新版本)
    - orjson==3.10.12
