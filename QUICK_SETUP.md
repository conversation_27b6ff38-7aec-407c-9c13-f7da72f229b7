# ⚡ 快速设置 - 自动激活Conda环境

## 🎯 目标

实现打开项目终端时自动激活`yoghurt-ai-qc` conda环境。

## 🚀 一键设置

### 方法1: 自动安装脚本 (推荐)

```bash
# 运行自动安装脚本
./scripts/install-direnv.sh
```

这个脚本会：
1. ✅ 自动检测您的操作系统
2. ✅ 安装direnv
3. ✅ 配置Shell集成
4. ✅ 设置项目环境
5. ✅ 验证安装

### 方法2: 手动安装

#### 步骤1: 安装direnv
```bash
# macOS
brew install direnv

# Ubuntu/Debian
sudo apt install direnv
```

#### 步骤2: 配置Shell
```bash
# Bash用户
echo 'eval "$(direnv hook bash)"' >> ~/.bashrc
source ~/.bashrc

# Zsh用户
echo 'eval "$(direnv hook zsh)"' >> ~/.zshrc
source ~/.zshrc
```

#### 步骤3: 启用项目环境
```bash
# 在项目根目录运行
direnv allow
```

## ✅ 验证设置

### 测试自动激活
```bash
# 离开项目目录
cd ..

# 重新进入项目目录
cd yoghurt-ai-qc
```

### 预期输出
```
direnv: loading ~/yoghurt-ai-qc/.envrc
✅ 已激活 yoghurt-ai-qc conda环境
🐍 Python版本: Python 3.12.11
📦 环境路径: /path/to/miniconda3/envs/yoghurt-ai-qc
📄 加载 .env 文件
✅ 关键组件验证通过
🚀 Yogurt AI QC 项目环境已准备就绪
direnv: export +AI_SERVICE_PORT +BACKEND_PORT +CONDA_DEFAULT_ENV +CONDA_PREFIX +FRONTEND_PORT +PROJECT_ROOT
```

## 🔧 工作原理

### 自动激活流程
1. **进入项目目录** → direnv检测到`.envrc`文件
2. **加载.envrc** → 激活conda环境和设置变量
3. **环境就绪** → 可以直接使用Python和项目工具
4. **离开目录** → 自动清理环境变量

### 环境变量设置
```bash
CONDA_DEFAULT_ENV=yoghurt-ai-qc
PROJECT_ROOT=/path/to/yoghurt-ai-qc
AI_SERVICE_PORT=6800
FRONTEND_PORT=6174
BACKEND_PORT=3010
```

## 🎉 使用体验

### 开发工作流
```bash
# 1. 打开终端，进入项目
cd ~/yoghurt-ai-qc
# → 自动激活conda环境

# 2. 直接使用Python和工具
python --version  # Python 3.12.11
pip list          # 显示项目依赖

# 3. 启动服务
npm run dev       # 启动前后端
npm run dev:ai    # 启动AI服务

# 4. 离开项目
cd ..
# → 自动清理环境
```

### IDE集成
- **VS Code**: 自动检测环境变量
- **PyCharm**: 识别conda环境
- **终端**: 任何终端都支持

## 🛠️ 故障排除

### 问题1: direnv未安装
```bash
# 运行安装脚本
./scripts/install-direnv.sh

# 或手动安装
brew install direnv  # macOS
```

### 问题2: Shell集成未生效
```bash
# 重新配置
echo 'eval "$(direnv hook bash)"' >> ~/.bashrc
source ~/.bashrc
```

### 问题3: 环境未激活
```bash
# 重新允许
direnv allow

# 检查.envrc文件
cat .envrc
```

### 问题4: conda环境不存在
```bash
# 创建环境
./scripts/create-latest-env.sh

# 验证环境
conda env list | grep yoghurt-ai-qc
```

### 问题5: macOS上direnv PATH问题
```bash
# 修复PATH问题
./scripts/fix-direnv-path.sh

# 重新启动终端或重新加载配置
source ~/.bashrc  # 或 source ~/.zshrc
```

### 问题6: 环境激活不完整
```bash
# 测试自动激活
./scripts/test-auto-activation.sh

# 重新允许.envrc
direnv allow

# 重新加载环境
direnv reload
```

## 📚 相关文档

- [详细设置指南](DIRENV_SETUP_GUIDE.md)
- [Conda环境管理](CONDA_ENVIRONMENT_GUIDE.md)
- [项目启动指南](STARTUP_GUIDE.md)

## 🎯 下一步

设置完成后，您可以：

1. **开始开发**: 环境已自动激活
2. **启动服务**: `npm run dev`
3. **运行AI服务**: `npm run dev:ai`
4. **查看文档**: 访问 http://localhost:6800/docs

---

**享受自动化的开发体验！** ⚡✨
