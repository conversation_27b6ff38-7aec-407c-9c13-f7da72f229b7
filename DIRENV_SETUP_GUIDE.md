# 🔄 Direnv自动环境激活设置指南

## 📋 概述

Direnv是一个环境变量管理工具，可以在进入项目目录时自动激活conda环境和加载环境变量。

## 🚀 快速设置

### 步骤1: 安装direnv

#### macOS (推荐使用Homebrew)
```bash
brew install direnv
```

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install direnv
```

#### 其他Linux发行版
```bash
# 使用包管理器安装
# CentOS/RHEL: sudo yum install direnv
# Arch Linux: sudo pacman -S direnv
# 或从源码安装: https://github.com/direnv/direnv
```

### 步骤2: 配置Shell集成

根据您使用的Shell添加相应配置：

#### Bash
```bash
echo 'eval "$(direnv hook bash)"' >> ~/.bashrc
source ~/.bashrc
```

#### Zsh
```bash
echo 'eval "$(direnv hook zsh)"' >> ~/.zshrc
source ~/.zshrc
```

#### Fish
```bash
echo 'direnv hook fish | source' >> ~/.config/fish/config.fish
```

### 步骤3: 在项目中启用direnv

```bash
# 进入项目目录
cd /path/to/yoghurt-ai-qc

# 允许direnv在此目录工作
direnv allow
```

## ✅ 验证安装

### 测试direnv是否工作
```bash
# 离开项目目录
cd ..

# 重新进入项目目录
cd yoghurt-ai-qc

# 应该看到环境自动激活的消息
```

### 预期输出
```
direnv: loading ~/yoghurt-ai-qc/.envrc
✅ 已激活 yoghurt-ai-qc conda环境
🐍 Python版本: Python 3.12.11
📦 环境路径: /path/to/miniconda3/envs/yoghurt-ai-qc
📄 加载 .env 文件
✅ 关键组件验证通过
🚀 Yogurt AI QC 项目环境已准备就绪
direnv: export +AI_SERVICE_PORT +BACKEND_PORT +CONDA_DEFAULT_ENV +CONDA_PREFIX +FRONTEND_PORT +PROJECT_ROOT
```

## 🔧 工作原理

### .envrc文件功能
1. **自动激活conda环境**: 进入目录时激活`yoghurt-ai-qc`环境
2. **设置项目变量**: 配置端口号和项目路径
3. **加载环境文件**: 自动加载`.env`和`.env.local`
4. **验证组件**: 检查关键Python包是否可用

### 环境变量设置
```bash
PROJECT_ROOT=/path/to/yoghurt-ai-qc
AI_SERVICE_PORT=6800
FRONTEND_PORT=6174
BACKEND_PORT=3010
```

## 🛠️ 故障排除

### 问题1: direnv命令未找到
```bash
# 检查安装
which direnv

# 重新安装
brew reinstall direnv  # macOS
sudo apt reinstall direnv  # Ubuntu
```

### 问题2: Shell集成未生效
```bash
# 检查shell配置
echo $SHELL

# 重新添加hook
echo 'eval "$(direnv hook bash)"' >> ~/.bashrc
source ~/.bashrc
```

### 问题3: conda环境未激活
```bash
# 检查conda环境是否存在
conda env list | grep yoghurt-ai-qc

# 如果不存在，创建环境
./scripts/create-latest-env.sh
```

### 问题4: 权限被拒绝
```bash
# 重新允许direnv
direnv allow

# 检查.envrc权限
ls -la .envrc
chmod +x .envrc  # 如果需要
```

### 问题5: Python包导入失败
```bash
# 激活环境并检查
conda activate yoghurt-ai-qc
python -c "import fastapi, openai, cv2, numpy"

# 如果失败，重新安装依赖
pip install -r ai-service/requirements-slim.txt
```

## 🎯 使用技巧

### 1. 查看当前环境状态
```bash
direnv status
```

### 2. 重新加载.envrc
```bash
direnv reload
```

### 3. 临时禁用direnv
```bash
direnv deny
```

### 4. 重新启用direnv
```bash
direnv allow
```

### 5. 查看环境变量变化
```bash
direnv diff
```

## 🔄 自动化工作流

### 开发流程
1. **打开终端** → 自动激活conda环境
2. **进入项目目录** → 自动加载环境变量
3. **开始开发** → 所有工具和依赖已准备就绪
4. **离开项目** → 自动清理环境变量

### IDE集成
大多数现代IDE都支持direnv：
- **VS Code**: 安装direnv扩展
- **PyCharm**: 自动检测环境变量
- **Vim/Neovim**: 使用direnv插件

## 📚 高级配置

### 自定义.envrc
您可以根据需要修改`.envrc`文件：

```bash
# 添加自定义环境变量
export CUSTOM_VAR="value"

# 添加PATH
PATH_add "custom/bin"

# 使用不同的conda环境
layout conda my-other-env
```

### 项目特定配置
```bash
# 开发环境
export NODE_ENV=development
export DEBUG=true

# API密钥（从安全文件加载）
if [ -f .secrets/api_keys ]; then
    source .secrets/api_keys
fi
```

## 🔒 安全注意事项

### 1. 不要在.envrc中存储敏感信息
```bash
# ❌ 错误做法
export API_KEY="secret-key"

# ✅ 正确做法
if [ -f .secrets/api_key ]; then
    export API_KEY=$(cat .secrets/api_key)
fi
```

### 2. 使用.gitignore排除敏感文件
```gitignore
.secrets/
.env.local
```

### 3. 定期审查.envrc内容
```bash
# 检查.envrc内容
cat .envrc

# 检查加载的环境变量
direnv export json
```

## 📖 相关资源

- [Direnv官方文档](https://direnv.net/)
- [Direnv GitHub仓库](https://github.com/direnv/direnv)
- [Conda环境管理指南](CONDA_ENVIRONMENT_GUIDE.md)
- [项目启动指南](STARTUP_GUIDE.md)

---

**享受自动化的开发环境！** 🚀✨
