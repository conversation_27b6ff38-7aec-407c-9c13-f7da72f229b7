产品需求文档：Yogurt-AI-QC (酸奶AI质控系统)
1. 项目概述与愿景 (Project Overview & Vision)
产品名称: Yogurt-AI-QC
产品愿景: 打造一款专为精品咖啡厅设计的、结合了传统工艺记录与AI显微分析的酸奶品质控制系统。它旨在将酸奶制作从“经验艺术”提升为“数据科学”，确保每一批产品的极致稳定性、安全性与独特性，最终赋能品牌创新和顾客信任。
核心价值:
品质一致性: 通过数据化记录和AI反馈，消除因人员、环境等变量导致的品质波动。
食品安全保障: 利用AI辅助识别潜在的微生物污染，建立第一道安全防线。
研发创新加速: 数据化追踪配方与成品口感、菌群的关联，为开发新风味提供科学依据。
品牌故事塑造: “我们的每一杯酸奶，都经过AI显微镜的品质把关”，这是一个极具吸引力的营销点。
2. 目标用户 (Target Users)
主要用户: 咖啡厅老板、饮品研发负责人、资深咖啡师/酸奶制作者。
用户特征:
追求极致的产品品质和一致性。
对新生事物和技术有好奇心和探索欲。
熟悉基本的电脑操作，可以使用显微镜。
工作节奏快，需要一个操作简便、直观高效的工具。
3. 功能需求 (Functional Requirements)
模块一：生产批次管理 (Production Batch Management)
这是系统的基础，用于记录一切。

3.1. 配方库 (Recipe Library)
【创建/编辑配方】: 用户可以创建和保存不同的酸奶配方。
【配方字段】:
配方名称 (如: "经典低温10小时配方")
牛奶品牌/类型 (如: "朝日唯品全脂鲜奶")
基础菌种 (如: "川秀15菌菌粉")
添加物及其用量 (如: "奶粉30g", "淡奶油50g")
发酵温度 (°C)
预设发酵时长 (小时)
希腊酸奶过滤时长 (小时)
备注
3.2. 批次日志 (Batch Log)
【创建批次】: 用户开始新一批制作时，可选择一个配方快速创建日志。
【批次字段】:
批次ID (系统自动生成，如: YYYYMMDD-01)
生产日期
关联配方
制作数量 (L / kg)
实际发酵时长
[核心] 感官评估 (Sensory Evaluation):
质地 (Texture): 1-5分制滑块 (稀薄 → 细腻 → 浓稠 → 固态)
酸度 (Acidity): 1-5分制滑块 (温和 → 微酸 → 酸爽 → 尖酸)
风味 (Aroma/Flavor): 标签选择 (如: 奶香纯正, 有发酵香, 果香, 轻微酒味, 异味) + 文本框补充
综合评分 (Overall Score): 1-5颗星
品控师备注: 自由文本输入区。
[核心] 显微镜分析上传区: 见模块二。
模块二：AI显微分析 (AI Microscopic Analysis)
这是系统的灵魂，实现智能质控。

3.3. 图像采集与上传
【上传接口】: 在每个批次日志中，提供一个“上传显微镜图像”的按钮。
【多图上传】: 允许一次上传1-3张来自不同视野的图片，以提高分析准确性。
【元数据记录】: 上传时需记录/选择关键元数据：
放大倍数: (必填，如: 400x, 1000x) - 这是AI正确判断尺寸和形态的前提。
染色方法: (如: 未染色, 革兰氏染色) - 影响AI的分析模型。
3.4. AI分析请求与反馈
【API调用】: 用户点击“开始分析”后，App将图像和元数据打包，通过API发送给指定的多模态大模型（如GPT-4V, Gemini等）。
【结果展示】: 在前端以清晰、结构化的卡片形式展示API返回的分析结果。
【AI分析结果需求 (对多模态模型的要求)】:
菌种形态识别 (Morphology ID): 识别并计数视野中的主要菌种形态，如“杆状 (Lactobacillus)”和“球状链式 (Streptococcus)”。
菌群平衡度评估 (Balance Ratio): 估算杆菌与球菌的大致比例。并给出参考意见，如“球菌比例较高，风味可能偏温和；杆菌比例高，则酸度更突出”。
活性评估 (Activity Assessment): 基于菌体的完整性和分布，初步判断菌群活性（如：“菌体饱满，分布均匀，活性良好”）。
[安全核心] 异常/污染检测 (Contamination Detection): 识别视野中是否存在非目标物体，如酵母菌细胞 (Yeast)、**霉菌菌丝 (Mold Hyphae)**或其他形态的杂菌。并进行高亮预警。
综合健康评分与建议 (Overall Health Score & Advice):
生成一个综合健康分 (1-100)。
输出一段自然语言总结，例如：“本次菌群健康度92分。杆菌与球菌比例均衡，活性良好。未发现明显污染。此状态预计能产出口感顺滑、风味平衡的优质酸奶。”
或预警：“警告！在视野右下角发现疑似酵母菌污染（已标记）。健康度45分。建议复查所有工具的消毒流程。此批次产品有发酵过度、产生酒味的风险，不建议出品。”
模块三：数据看板与洞察 (Dashboard & Insights)
3.5. 历史数据查询与对比
用户可以按日期、配方、综合评分等条件筛选和搜索历史批次。
支持并排比较两个不同批次的日志，直观看到配方、感官、菌群的差异。
3.6. 数据可视化
生成简单的趋势图表，例如：
“发酵时长”与“酸度评分”的关系图。
“菌群健康分”与“综合评分”的相关性图。
4. 非功能性需求 (Non-Functional Requirements)
4.1. 用户体验 (UX/UI): 界面简洁、直观，符合咖啡厅快节奏工作环境。数据录入流程需尽可能简化。
4.2. 性能 (Performance): 图像上传和AI分析响应时间应在15秒内完成，避免长时间等待。
4.3. 兼容性 (Compatibility):
主要为桌面端应用（Windows/macOS），以便连接显微镜并处理图像。可使用Electron等跨平台框架开发。
未来可开发一个移动端App，用于随时查看数据和录入感官评估。
4.4. 安全性 (Security): 用户数据、配方和API密钥需安全存储，做好访问控制。
5. 技术栈与集成建议 (Tech Stack & Integration)
前端: Vue.js 或 React.js (包裹在Electron中实现桌面应用)。
后端: Python (Flask/Django) - Python强大的数据处理和AI生态系统是首选。
数据库: PostgreSQL - 适合结构化数据存储和复杂查询。
显微镜集成: App不直接控制显微镜。工作流为：显微镜自带软件捕获图像并保存到电脑 -> 用户从App内选择该图像文件上传。
多模态模型API:
初期可使用 OpenAI GPT-4V 或 Google Gemini Pro Vision 的API。它们具备强大的通用视觉分析能力，能快速实现MVP。
[进阶]: 长期来看，可将用户反馈（如：“AI这次判断错了，这其实不是酵母菌”）和标注过的图像数据收集起来，用于未来微调（Fine-tune）一个专属的、更精准的酸奶菌群分析模型。
6. 开发阶段规划 (Development Phases)
Phase 1: 最小可行产品 (MVP)
目标: 验证核心流程，开始数据积累。
功能:
完整的配方库和批次日志功能（含感官评估）。
手动上传显微镜图像。
集成通用多模态模型API，并直接展示其返回的文本结果。
最基本的数据列表和查看功能。
Phase 2: 增强与优化
目标: 提升用户体验和分析精度。
功能:
开发数据看板和可视化图表。
优化AI结果的展示界面（结构化卡片、高亮标记等）。
引入用户反馈机制，允许用户“纠正”AI的判断。
Phase 3: 智能化与专业化
目标: 打造真正的智能决策系统。
功能:
利用积累的数据微调一个专有AI模型，大幅提升准确性。
开发预测性功能，如“根据此配方，预测成品酸度将在4.2分左右”。
增加用户权限管理等高级功能。
7. 用户故事 (User Stories)
用户故事以用户的视角描述了他们希望系统能为他们做什么，这有助于开发团队更好地理解需求背后的动机。

用户角色：Alex，咖啡厅主理人兼研发师
故事1: 创建与复用配方
As an 研发师 Alex,
I want to 创建并保存我的黄金酸奶配方，包括牛奶、菌种、温度和时间等所有细节,
so that 我或我的员工在开始新一批制作时，可以一键调用，确保每次操作的标准化。
故事2: 记录生产与感官数据
As an 研发师 Alex,
I want to 在每一批酸奶制作完成后，快速记录下它的感官评分（口感、酸度）和我的主观评价,
so that 我可以追踪不同批次间的细微差别，并与后续的显微镜数据进行关联分析。
故事3: 获得即时的AI质控反馈
As a 咖啡厅主理人 Alex,
I want to 上传刚做好的酸奶样本的显微镜照片，并立即获得一份关于菌群健康状况、平衡度和污染风险的AI分析报告,
so that 我能在决定是否将这批酸奶出品前，得到一个客观、科学的第二意见，保障食品安全和顾客体验。
故事4: 诊断生产问题
As an 研发师 Alex,
I want to 当一批酸奶口感不佳时，能够调出它的生产日志，并对比AI菌群分析报告和历史成功批次的报告,
so that 我能快速定位问题根源，究竟是发酵时间长了，还是菌群本身出了问题。
故事5: 数据驱动的创新
As a 创新者 Alex,
I want to 查看不同配方（比如换了一种牛奶）与最终菌群状态、口感评分之间的关系图表,
so that 我在研发新口味时，不再仅仅依赖试错，而是能基于数据做出更明智的决策。
8. API 交互设计与 Prompt 工程
这是实现“AI显微分析”功能的技术核心。我们需要精心设计发送给多模态模型的请求（Prompt）。

API Endpoint: POST /api/v1/analyze_yogurt_image
Request Body (JSON):
Generated json
{
  "image_base64": "...", // 图像的Base64编码字符串
  "metadata": {
    "magnification": 1000, // 放大倍数 (e.g., 400, 1000)
    "staining_method": "Gram Staining", // 染色方法 ("None", "Gram Staining", "Methylene Blue")
    "base_recipe": { // 关联的配方信息，给AI更多上下文
      "main_bacteria": ["Lactobacillus", "Streptococcus", "Bifidobacterium"],
      "milk_type": "Whole Milk"
    }
  },
  "output_format": "json" // 要求AI以JSON格式返回，便于程序解析
}
Use code with caution.
Json
Prompt 设计 (发送给多模态模型的指令):
这是一个关键的“咒语”，需要不断优化。
基础版 Prompt:
"You are a professional microbiology lab assistant specializing in yogurt fermentation. Analyze the provided microscope image of a yogurt sample. The image was taken at {magnification}x magnification using {staining_method} staining. The expected primary bacteria are {base_recipe.main_bacteria}.
Please analyze the image and respond ONLY with a valid JSON object with the following structure:
{
"quality_score": <An integer score from 0 to 100>,
"key_findings": {
"bacterial_morphology": <Describe the shapes of the dominant bacteria, e.g., "Clear presence of both rod-shaped (Lactobacillus) and chain-like cocci (Streptococcus).">,
"balance_ratio": <Estimate the ratio, e.g., "Lactobacillus and Streptococcus appear to be in a balanced 1:1 ratio.">,
"activity_level": <Assess the vitality, e.g., "High. Most cells appear intact and well-defined.">
},
"contamination_alert": {
"detected": <true or false>,
"type": <If true, specify the type, e.g., "Yeast", "Mold", "Unknown contaminant">,
"description": <A brief description of the alert, e.g., "Several budding yeast cells observed in the upper-left quadrant.">
},
"summary_and_recommendation": <A concise, actionable summary in natural language for a cafe owner.>
}"
为什么要这样做？
角色扮演 (Role-playing): "You are a professional..." 能引导模型以更专业的口吻和知识库进行回答。
提供上下文 (Context): 告知放大倍数、菌种等信息，能极大提高分析的准确性。
强制格式化输出 (Forced-JSON Output): 要求返回JSON格式，使得App后端可以轻松解析数据并展示在前端，而不是处理一段杂乱的文本。
9. 系统工作流程图 (System Workflow)
Generated mermaid
graph TD
    A[用户: 开始新批次] --> B(App: 创建批次日志);
    B --> C{选择或创建配方};
    C --> D[用户: 按照配方制作酸奶];
    D --> E[用户: 制作完成, 感官评估];
    E --> F(App: 记录感官数据);

    subgraph 显微分析
        G[用户: 制备样本并用显微镜拍照] --> H(App: 上传图像);
        H --> I{API: 发送图像和Prompt至多模态模型};
        I --> J(AI模型: 分析图像);
        J --> K{API: 返回JSON格式的分析结果};
        K --> L(App: 解析JSON并在UI上展示报告);
    end

    F & L --> M(App: 将所有数据存入数据库);
    M --> N[数据看板: 用户可随时查看/对比历史数据];
Use code with caution.
Mermaid
10. 商业化与长期发展 (Monetization & Long-term Vision)
虽然初期是为自用，但这个系统有巨大的商业潜力。

SaaS (软件即服务) 模式:
目标客户: 其他精品咖啡馆、酸奶吧、小型食品研发工作室。
定价策略:
免费版/试用版: 限制每月AI分析次数（如10次），仅提供基础的日志功能。
专业版 (月费/年费): 无限次AI分析、数据看板、配方对比等高级功能。
企业版: 针对连锁品牌，提供多店管理、员工权限控制、专属AI模型微调服务。
数据驱动的增值服务:
行业洞察报告: 在获得用户授权并匿名化处理数据后，可以发布行业报告，例如“本季度最受欢迎的益生菌组合趋势”、“不同奶源对酸奶风味影响的数据分析”。
原料推荐引擎: “数据显示，使用XX品牌的牛奶搭配YY菌种，用户满意度平均提高15%”。这可以与原料供应商建立合作。
长期愿景: 成为精品发酵食品行业的“智能质控大脑”。
将技术扩展到其他发酵产品，如康普茶 (Kombucha)、酸面包 (Sourdough)、**精酿啤酒 (Craft Beer)**等。这些产品的核心都在于微生物的控制，技术可以平移。
建立一个庞大的、专业的发酵微生物图像数据库，训练出全球领先的AI模型，成为行业标准。
这个项目从一个咖啡馆的内在需求出发，通过技术赋能，完全有潜力成长为一个独立的、具有高技术壁垒的科技公司。祝您的创新之路顺利！