#!/bin/bash

# =============================================================================
# Yogurt AI QC - 快速环境激活脚本
# =============================================================================
# 
# 使用方法: source ./quick-activate.sh
# 
# 这是一个简化版的激活脚本，用于快速激活项目环境
# =============================================================================

echo "🚀 快速激活 Yogurt AI QC 环境..."

# 检查是否在项目目录
if [ ! -f "package.json" ] || [ ! -d "ai-service" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    return 1 2>/dev/null || exit 1
fi

# 激活conda环境
if command -v conda >/dev/null 2>&1; then
    # 初始化conda（如果需要）
    if [ -z "$CONDA_PREFIX" ]; then
        CONDA_BASE=$(conda info --base 2>/dev/null)
        if [ -n "$CONDA_BASE" ] && [ -f "$CONDA_BASE/etc/profile.d/conda.sh" ]; then
            source "$CONDA_BASE/etc/profile.d/conda.sh"
        fi
    fi
    
    # 激活环境
    conda activate yoghurt-ai-qc
    
    if [ "$CONDA_DEFAULT_ENV" = "yoghurt-ai-qc" ]; then
        echo "✅ 环境激活成功"
        
        # 设置环境变量
        export PROJECT_ROOT="$(pwd)"
        export AI_SERVICE_PORT=6800
        export FRONTEND_PORT=6174
        export BACKEND_PORT=3010
        
        # 加载.env文件
        if [ -f .env ]; then
            set -a
            source .env
            set +a
        fi
        
        echo "🐍 Python: $(python --version)"
        echo "📦 环境: $CONDA_DEFAULT_ENV"
        echo "🎯 项目已准备就绪！"
    else
        echo "❌ 环境激活失败"
        return 1 2>/dev/null || exit 1
    fi
else
    echo "❌ conda 未找到"
    return 1 2>/dev/null || exit 1
fi
