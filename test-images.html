<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Product Images</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .image-test {
            margin: 20px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        img {
            max-width: 200px;
            max-height: 200px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Product Image Test</h1>
    
    <div class="image-test">
        <h3>Classic Yogurt</h3>
        <img src="http://localhost:3010/images/products/classic-yogurt.svg" alt="Classic Yogurt" 
             onerror="this.style.border='2px solid red'; this.alt='Failed to load: classic-yogurt.svg'">
        <p>URL: http://localhost:3010/images/products/classic-yogurt.svg</p>
    </div>
    
    <div class="image-test">
        <h3>Blueberry Yogurt</h3>
        <img src="http://localhost:3010/images/products/blueberry-yogurt.svg" alt="Blueberry Yogurt"
             onerror="this.style.border='2px solid red'; this.alt='Failed to load: blueberry-yogurt.svg'">
        <p>URL: http://localhost:3010/images/products/blueberry-yogurt.svg</p>
    </div>
    
    <div class="image-test">
        <h3>Test Fallback SVG</h3>
        <img src="data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='40' height='40' fill='%23f0f8ff' stroke='%23e0e0e0' stroke-dasharray='2,2' rx='4'/%3E%3Ctext x='20' y='26' text-anchor='middle' font-size='18'%3E🥛%3C/text%3E%3C/svg%3E" 
             alt="Fallback Image">
        <p>This is the fallback SVG that should display when images fail to load</p>
    </div>
</body>
</html>