# 🚀 快速开始指南

## 📋 前置要求

确保您的系统已安装以下工具：

- **Node.js** 18+ 
- **npm** 9+
- **Docker** & **Docker Compose**
- **Git**

## ⚡ 一键启动

### 方法1: 使用快速启动脚本（推荐）

```bash
# 运行快速启动脚本
bash scripts/quick-start.sh
```

这个脚本会自动：
- ✅ 检查系统要求
- ✅ 检查端口占用
- ✅ 安装所有依赖
- ✅ 启动数据库服务
- ✅ 初始化数据库
- ✅ 启动所有开发服务

### 方法2: 手动启动

```bash
# 1. 安装依赖
npm install

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，添加您的 OpenAI API 密钥

# 3. 启动数据库服务
docker-compose up -d postgres redis

# 4. 等待数据库启动并初始化
bash scripts/setup-database.sh

# 5. 启动所有开发服务
npm run dev
```

## 🔧 配置说明

### 环境变量配置

编辑 `.env` 文件，重要配置项：

```env
# OpenAI API 密钥（必需）
OPENAI_API_KEY=sk-your-openai-api-key-here

# 应用端口（已配置为6开头避免冲突）
APP_PORT=6000
VITE_PORT=6173
AI_SERVICE_PORT=6800
DB_PORT=6432
REDIS_PORT=6479

# 数据库配置（已设置安全密码）
DATABASE_URL=postgresql://yoghurt_user:R4t7cjrp@localhost:6432/yoghurt_qc
DB_PASSWORD=R4t7cjrp

# JWT 密钥（开发环境已设置）
JWT_SECRET=yoghurt-dev-jwt-secret-key-2024-change-in-production
```

## 🌐 访问地址

启动成功后，您可以访问：

| 服务 | 地址 | 说明 |
|------|------|------|
| **前端应用** | http://localhost:6173 | React 用户界面 |
| **后端API** | http://localhost:6000 | REST API 服务 |
| **API文档** | http://localhost:6000/api/docs | Swagger 文档 |
| **AI服务** | http://localhost:6800 | AI 分析服务 |
| **AI文档** | http://localhost:6800/docs | FastAPI 文档 |

## 🔍 状态检查

运行状态检查脚本来验证所有配置：

```bash
bash scripts/check-status.sh
```

## 🛠️ 常用命令

```bash
# 启动所有服务
npm run dev

# 分别启动服务
npm run dev:frontend    # 前端 (6173)
npm run dev:backend     # 后端 (6000)  
npm run dev:ai          # AI服务 (6800)

# Docker 服务管理
npm run docker:up       # 启动数据库
npm run docker:down     # 停止数据库
npm run docker:logs     # 查看日志

# 数据库管理
npm run migrate         # 运行迁移
npm run db:seed         # 填充测试数据
npm run db:reset        # 重置数据库

# 代码质量
npm run lint            # 代码检查
npm run format          # 代码格式化
npm run test            # 运行测试
```

## 🐛 故障排除

### 端口冲突

如果遇到端口冲突，检查占用情况：

```bash
# 检查端口占用
lsof -i :6000
lsof -i :6173
lsof -i :6432
lsof -i :6479
lsof -i :6800

# 停止占用端口的进程
kill -9 <PID>
```

### 数据库连接问题

```bash
# 检查数据库状态
docker-compose ps postgres

# 重启数据库
docker-compose restart postgres

# 查看数据库日志
docker-compose logs postgres
```

### 依赖安装问题

```bash
# 清理并重新安装
rm -rf node_modules package-lock.json
npm install

# 前端依赖问题
cd frontend && rm -rf node_modules package-lock.json && npm install

# 后端依赖问题
cd backend && rm -rf node_modules package-lock.json && npm install
```

### AI服务问题

```bash
# 检查Python环境
conda activate yoghurt-ai-qc
python --version

# 重新安装Python依赖
cd ai-service && pip install -r requirements.txt
```

## 📝 开发注意事项

1. **API密钥**: 请确保在 `.env` 文件中配置有效的 OpenAI API 密钥
2. **端口配置**: 所有端口都配置为6开头，避免与其他项目冲突
3. **数据库密码**: 已设置为安全密码 `R4t7cjrp`
4. **热重载**: 前后端都支持热重载，修改代码后自动刷新
5. **日志查看**: 使用 `docker-compose logs -f` 查看服务日志

## 🎯 下一步

1. **配置API密钥**: 在 `.env` 文件中添加您的 OpenAI API 密钥
2. **测试功能**: 访问前端应用，创建第一个配方
3. **查看文档**: 浏览 API 文档了解接口详情
4. **开始开发**: 根据需求修改和扩展功能

## 📞 获取帮助

- **项目文档**: 查看 `docs/` 目录下的详细文档
- **端口配置**: 参考 `docs/PORT_CONFIGURATION.md`
- **API文档**: 访问 http://localhost:6000/api/docs
- **状态检查**: 运行 `bash scripts/check-status.sh`

---

**祝您开发愉快！** 🎉
