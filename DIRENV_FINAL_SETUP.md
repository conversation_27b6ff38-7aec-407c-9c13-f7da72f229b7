# 🔄 Direnv自动激活 - 最终配置指南

## 🎯 目标

实现进入项目目录时自动激活`yoghurt-ai-qc` conda环境。

## 🚀 一键配置 (推荐)

### 步骤1: 运行配置脚本

```bash
# 在项目根目录运行
./setup-direnv-complete.sh
```

这个脚本会：
1. ✅ 检查系统环境
2. ✅ 配置zsh集成
3. ✅ 允许.envrc文件
4. ✅ 测试环境激活
5. ✅ 验证配置结果

### 步骤2: 重新启动终端

**重要**: 配置完成后，请重新启动终端应用或运行：

```bash
source ~/.zshrc
```

### 步骤3: 测试自动激活

```bash
# 离开项目目录
cd ..

# 重新进入项目目录
cd yoghurt-ai-qc

# 应该看到环境自动激活的消息
```

## 🔧 手动配置 (如果自动配置失败)

### 步骤1: 配置zsh

```bash
# 添加direnv hook到zsh配置
echo 'eval "$(direnv hook zsh)"' >> ~/.zshrc

# 重新加载配置
source ~/.zshrc
```

### 步骤2: 允许项目环境

```bash
# 在项目根目录运行
direnv allow
```

### 步骤3: 验证配置

```bash
# 检查direnv状态
direnv status

# 测试环境变量
echo $CONDA_DEFAULT_ENV
```

## ✅ 验证成功

配置成功后，您应该看到：

### 进入项目目录时的输出
```
direnv: loading ~/yoghurt-ai-qc/.envrc
✅ 已激活 yoghurt-ai-qc conda环境
🐍 Python版本: Python 3.12.11
📦 环境路径: /Volumes/acasis/miniconda3/miniconda3/envs/yoghurt-ai-qc
📄 加载 .env 文件
📄 加载 .env.local 文件
🔍 验证关键组件...
✅ 所有关键组件验证通过
🚀 Yogurt AI QC 项目环境已准备就绪
direnv: export +AI_SERVICE_PORT +BACKEND_PORT +CONDA_DEFAULT_ENV +CONDA_PREFIX +FRONTEND_PORT +PROJECT_ROOT
```

### 环境变量检查
```bash
echo $CONDA_DEFAULT_ENV    # 输出: yoghurt-ai-qc
echo $PROJECT_ROOT         # 输出: /Volumes/acasis/yoghurt
echo $AI_SERVICE_PORT      # 输出: 6800
python --version           # 输出: Python 3.12.11
```

## 🛠️ 故障排除

### 问题1: direnv hook未生效

**症状**: 进入目录时没有自动激活环境

**解决方案**:
```bash
# 检查.zshrc中是否有direnv hook
grep "direnv hook" ~/.zshrc

# 如果没有，手动添加
echo 'eval "$(direnv hook zsh)"' >> ~/.zshrc
source ~/.zshrc
```

### 问题2: .envrc未被允许

**症状**: 显示"direnv: error .envrc is blocked"

**解决方案**:
```bash
# 重新允许.envrc
direnv allow

# 检查允许状态
direnv status
```

### 问题3: conda环境未激活

**症状**: 环境变量设置了但conda环境不对

**解决方案**:
```bash
# 检查conda环境是否存在
conda env list | grep yoghurt-ai-qc

# 如果不存在，创建环境
./scripts/create-latest-env.sh

# 重新允许direnv
direnv allow
```

### 问题4: 权限问题

**症状**: 无法写入配置文件

**解决方案**:
```bash
# 检查文件权限
ls -la ~/.zshrc
ls -la .envrc

# 修复权限
chmod 644 ~/.zshrc
chmod 644 .envrc
```

## 🎉 使用体验

### 自动化工作流
```bash
# 1. 打开终端
# 2. 进入项目目录
cd ~/yoghurt-ai-qc
# → 自动激活conda环境和设置环境变量

# 3. 直接开始开发
npm run dev      # 启动前后端
npm run dev:ai   # 启动AI服务

# 4. 离开项目
cd ..
# → 自动清理环境变量
```

### 常用direnv命令
```bash
direnv status    # 查看当前状态
direnv reload    # 重新加载.envrc
direnv allow     # 允许.envrc执行
direnv deny      # 禁用.envrc
direnv edit      # 编辑.envrc文件
```

## 🔄 备用方案

如果direnv配置仍有问题，您可以使用备用的手动激活脚本：

```bash
# 每次进入项目时运行
source activate-env.sh
```

## 📚 相关文档

- [macOS设置指南](MACOS_SETUP.md)
- [通用快速设置](QUICK_SETUP.md)
- [详细direnv指南](DIRENV_SETUP_GUIDE.md)
- [项目启动指南](STARTUP_GUIDE.md)

## 💡 提示

1. **重启终端很重要**: 配置后一定要重启终端或重新加载配置
2. **检查Shell类型**: 确保使用正确的Shell配置文件 (.zshrc vs .bashrc)
3. **权限问题**: 如果遇到权限问题，检查文件权限设置
4. **备用方案**: 如果自动激活有问题，随时可以使用 `source activate-env.sh`

---

**享受自动化的开发环境！** 🚀✨
