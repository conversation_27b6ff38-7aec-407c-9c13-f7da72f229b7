import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Typography,
  Table,
  Tag,
  Space,
  Button,
  Divider,
  Progress,
  Statistic,
  Alert,
  Badge,
  Tooltip,
  Timeline,
  Tabs,
  DatePicker,
  Select
} from 'antd'
import {
  BarChartOutlined,
  SafetyOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  TrophyOutlined,
  CalendarOutlined,
  ExperimentOutlined,
  EyeOutlined,
  FilterOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { RangePicker } = DatePicker
const { Option } = Select

// 模拟质量检测数据
const qualityData = [
  {
    id: '1',
    batchNumber: 'YG20250130001',
    productName: '经典原味酸奶',
    testDate: '2025-01-30',
    testType: '出厂检测',
    overallScore: 98,
    status: 'pass',
    tests: {
      microorganisms: { score: 99, status: 'pass', details: '乳酸菌计数: 1.2×10⁸ CFU/g' },
      chemical: { score: 97, status: 'pass', details: '蛋白质含量: 3.2g/100g' },
      physical: { score: 98, status: 'pass', details: '酸度: 85°T, pH: 4.2' },
      sensory: { score: 98, status: 'pass', details: '色泽纯正，口感醇厚' },
      heavy_metals: { score: 100, status: 'pass', details: '铅、汞等重金属均未检出' },
      additives: { score: 99, status: 'pass', details: '无非法添加剂' }
    },
    tester: 'AI系统',
    reviewer: '张质检员'
  },
  {
    id: '2',
    batchNumber: 'YG20250130002',
    productName: '蓝莓果粒酸奶',
    testDate: '2025-01-30',
    testType: '出厂检测',
    overallScore: 96,
    status: 'pass',
    tests: {
      microorganisms: { score: 98, status: 'pass', details: '乳酸菌计数: 1.1×10⁸ CFU/g' },
      chemical: { score: 95, status: 'pass', details: '蛋白质含量: 3.0g/100g' },
      physical: { score: 96, status: 'pass', details: '酸度: 88°T, pH: 4.1' },
      sensory: { score: 95, status: 'pass', details: '果香浓郁，颗粒饱满' },
      heavy_metals: { score: 100, status: 'pass', details: '重金属含量符合标准' },
      additives: { score: 97, status: 'pass', details: '天然果汁，无人工色素' }
    },
    tester: 'AI系统',
    reviewer: '李质检员'
  },
  {
    id: '3',
    batchNumber: 'YG20250129001',
    productName: '希腊式浓稠酸奶',
    testDate: '2025-01-29',
    testType: '抽检',
    overallScore: 99,
    status: 'pass',
    tests: {
      microorganisms: { score: 100, status: 'pass', details: '乳酸菌计数: 1.5×10⁸ CFU/g' },
      chemical: { score: 99, status: 'pass', details: '蛋白质含量: 6.8g/100g' },
      physical: { score: 99, status: 'pass', details: '酸度: 82°T, pH: 4.3' },
      sensory: { score: 98, status: 'pass', details: '质地浓稠，口感顺滑' },
      heavy_metals: { score: 100, status: 'pass', details: '重金属未检出' },
      additives: { score: 99, status: 'pass', details: '纯天然发酵' }
    },
    tester: 'AI系统',
    reviewer: '王质检员'
  }
]

const QualityResults: React.FC = () => {
  const [filteredData, setFilteredData] = useState(qualityData)
  const [selectedRecord, setSelectedRecord] = useState(null)
  const [activeTab, setActiveTab] = useState('overview')

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass': return '#52c41a'
      case 'warning': return '#faad14'
      case 'fail': return '#ff4d4f'
      default: return '#d9d9d9'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass': return <CheckCircleOutlined />
      case 'warning': return <ExclamationCircleOutlined />
      case 'fail': return <CloseCircleOutlined />
      default: return null
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pass': return '合格'
      case 'warning': return '警告'
      case 'fail': return '不合格'
      default: return '未知'
    }
  }

  const testColumns = [
    {
      title: '批次号',
      dataIndex: 'batchNumber',
      key: 'batchNumber',
      render: (text: string) => <Text code>{text}</Text>
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
    },
    {
      title: '检测日期',
      dataIndex: 'testDate',
      key: 'testDate',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: '检测类型',
      dataIndex: 'testType',
      key: 'testType',
      render: (type: string) => (
        <Tag color={type === '出厂检测' ? 'blue' : 'green'}>{type}</Tag>
      )
    },
    {
      title: '综合评分',
      dataIndex: 'overallScore',
      key: 'overallScore',
      render: (score: number) => (
        <Space>
          <Progress
            type="circle"
            size={40}
            percent={score}
            format={() => score}
            strokeColor={score >= 95 ? '#52c41a' : score >= 85 ? '#faad14' : '#ff4d4f'}
          />
        </Space>
      )
    },
    {
      title: '检测状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Badge
          status={status === 'pass' ? 'success' : status === 'warning' ? 'warning' : 'error'}
          text={getStatusText(status)}
        />
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (record: any) => (
        <Button 
          type="link" 
          onClick={() => setSelectedRecord(record)}
        >
          查看详情
        </Button>
      )
    }
  ]

  const testTypeStats = [
    { name: '微生物检测', icon: <ExperimentOutlined />, color: '#1890ff' },
    { name: '化学成分', icon: <BarChartOutlined />, color: '#52c41a' },
    { name: '物理指标', icon: <TrophyOutlined />, color: '#faad14' },
    { name: '感官评价', icon: <EyeOutlined />, color: '#722ed1' },
    { name: '重金属检测', icon: <SafetyOutlined />, color: '#f5222d' },
    { name: '添加剂检测', icon: <FilterOutlined />, color: '#13c2c2' }
  ]

  return (
    <div>
      {/* 页面头部 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <BarChartOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          质量检测结果
        </Title>
        <Text type="secondary">查看产品质量检测数据和分析报告</Text>
      </div>

      {/* 质量保证说明 */}
      <Alert
        message="检测标准说明"
        description="所有检测均严格按照国家标准GB 19302-2010《食品安全国家标准 发酵乳》执行，确保每一批产品都符合食品安全要求。检测包括微生物指标、理化指标、重金属、添加剂等多个维度。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab}
        items={[
          {
            key: 'overview',
            label: '检测概览',
            children: (
              <>
                {/* 统计卡片 */}
                <Row gutter={24} style={{ marginBottom: 24 }}>
                  <Col span={6}>
                    <Card>
                      <Statistic
                        title="本月检测"
                        value={filteredData.length}
                        prefix={<ExperimentOutlined />}
                        suffix="批次"
                        valueStyle={{ color: '#1890ff' }}
                      />
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card>
                      <Statistic
                        title="合格率"
                        value={100}
                        prefix={<CheckCircleOutlined />}
                        suffix="%"
                        valueStyle={{ color: '#52c41a' }}
                      />
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card>
                      <Statistic
                        title="平均评分"
                        value={filteredData.reduce((sum, item) => sum + item.overallScore, 0) / filteredData.length}
                        prefix={<TrophyOutlined />}
                        precision={1}
                        suffix="/100"
                        valueStyle={{ color: '#722ed1' }}
                      />
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card>
                      <Statistic
                        title="最新检测"
                        value={1}
                        prefix={<CalendarOutlined />}
                        suffix="天前"
                        valueStyle={{ color: '#fa8c16' }}
                      />
                    </Card>
                  </Col>
                </Row>

                {/* 检测项目统计 */}
                <Card title="检测项目覆盖" style={{ marginBottom: 24 }}>
                  <Row gutter={16}>
                    {testTypeStats.map((item, index) => (
                      <Col span={4} key={index}>
                        <div style={{
                          textAlign: 'center',
                          padding: '20px',
                          border: '1px solid #f0f0f0',
                          borderRadius: '8px',
                          background: '#fafafa'
                        }}>
                          <div style={{ fontSize: '24px', color: item.color, marginBottom: '8px' }}>
                            {item.icon}
                          </div>
                          <div style={{ fontSize: '14px', fontWeight: 'bold' }}>{item.name}</div>
                          <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                            覆盖率: 100%
                          </div>
                        </div>
                      </Col>
                    ))}
                  </Row>
                </Card>
              </>
            )
          },
          {
            key: 'records',
            label: '检测记录',
            children: (
              <>
                {/* 筛选控件 */}
                <Card style={{ marginBottom: 16 }}>
                  <Row gutter={16} align="middle">
                    <Col>
                      <Text strong>筛选条件：</Text>
                    </Col>
                    <Col>
                      <RangePicker 
                        placeholder={['开始日期', '结束日期']}
                        format="YYYY-MM-DD"
                      />
                    </Col>
                    <Col>
                      <Select placeholder="检测类型" style={{ width: 120 }}>
                        <Option value="">全部</Option>
                        <Option value="出厂检测">出厂检测</Option>
                        <Option value="抽检">抽检</Option>
                      </Select>
                    </Col>
                    <Col>
                      <Select placeholder="检测状态" style={{ width: 120 }}>
                        <Option value="">全部</Option>
                        <Option value="pass">合格</Option>
                        <Option value="warning">警告</Option>
                        <Option value="fail">不合格</Option>
                      </Select>
                    </Col>
                    <Col>
                      <Button type="primary">查询</Button>
                    </Col>
                  </Row>
                </Card>

                {/* 检测记录表格 */}
                <Card>
                  <Table
                    columns={testColumns}
                    dataSource={filteredData}
                    rowKey="id"
                    pagination={{
                      pageSize: 10,
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total, range) => `显示 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
                    }}
                  />
                </Card>
              </>
            )
          },
          {
            key: 'trends',
            label: '质量趋势',
            children: (
              <>
                <Row gutter={24}>
                  <Col span={12}>
                    <Card title="质量评分趋势" style={{ marginBottom: 24 }}>
                      <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <Text type="secondary">质量评分趋势图表（需要图表组件支持）</Text>
                      </div>
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card title="检测项目分布" style={{ marginBottom: 24 }}>
                      <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <Text type="secondary">检测项目分布图表（需要图表组件支持）</Text>
                      </div>
                    </Card>
                  </Col>
                </Row>

                {/* 最近检测时间线 */}
                <Card title="最近检测活动">
                  <Timeline
                    items={filteredData.slice(0, 5).map((item) => ({
                      key: item.id,
                      dot: getStatusIcon(item.status),
                      color: getStatusColor(item.status),
                      children: (
                        <div>
                          <div>
                            <Text strong>{item.productName}</Text>
                            <Text type="secondary" style={{ marginLeft: 8 }}>
                              批次: {item.batchNumber}
                            </Text>
                          </div>
                          <div style={{ marginTop: 4 }}>
                            <Text type="secondary">{item.testDate}</Text>
                            <Tag color={getStatusColor(item.status)} style={{ marginLeft: 8 }}>
                              评分: {item.overallScore}
                            </Tag>
                          </div>
                        </div>
                      )
                    }))}
                  />
                </Card>
              </>
            )
          }
        ]}
      />

      {/* 详情弹窗 */}
      {selectedRecord && (
        <Card
          title={`检测详情 - ${selectedRecord.productName}`}
          extra={
            <Button onClick={() => setSelectedRecord(null)}>
              关闭
            </Button>
          }
          style={{ 
            position: 'fixed',
            top: 20,
            right: 20,
            width: 500,
            maxHeight: '80vh',
            overflow: 'auto',
            zIndex: 1000,
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
          }}
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            {/* 基本信息 */}
            <div>
              <Text strong>批次号：</Text>
              <Text code>{selectedRecord.batchNumber}</Text>
            </div>
            <div>
              <Text strong>检测日期：</Text>
              <Text>{selectedRecord.testDate}</Text>
            </div>
            <div>
              <Text strong>综合评分：</Text>
              <Badge
                status={selectedRecord.status === 'pass' ? 'success' : 'warning'}
                text={`${selectedRecord.overallScore}分 (${getStatusText(selectedRecord.status)})`}
              />
            </div>

            <Divider />

            {/* 详细检测结果 */}
            <div>
              <Text strong>详细检测结果：</Text>
              <div style={{ marginTop: 12 }}>
                {Object.entries(selectedRecord.tests).map(([key, test]: [string, any]) => (
                  <div key={key} style={{ marginBottom: 12, padding: 12, background: '#fafafa', borderRadius: 6 }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 4 }}>
                      <Text strong>
                        {key === 'microorganisms' ? '微生物检测' :
                         key === 'chemical' ? '化学成分' :
                         key === 'physical' ? '物理指标' :
                         key === 'sensory' ? '感官评价' :
                         key === 'heavy_metals' ? '重金属检测' :
                         key === 'additives' ? '添加剂检测' : key}
                      </Text>
                      <Badge
                        status={test.status === 'pass' ? 'success' : 'warning'}
                        text={`${test.score}分`}
                      />
                    </div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {test.details}
                    </Text>
                  </div>
                ))}
              </div>
            </div>

            <Divider />

            {/* 检测人员信息 */}
            <div>
              <Text strong>检测人员：</Text>
              <Text>{selectedRecord.tester}</Text>
            </div>
            <div>
              <Text strong>审核人员：</Text>
              <Text>{selectedRecord.reviewer}</Text>
            </div>
          </Space>
        </Card>
      )}
    </div>
  )
}

export default QualityResults