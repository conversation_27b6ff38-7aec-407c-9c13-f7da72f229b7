import React, { useEffect, useState } from 'react'
import { useTypedSelector } from '../hooks/useTypedSelector'
import { usePermissions } from '../hooks/usePermissions'
import LoadingSpinner from '../components/LoadingSpinner'
import RoleBasedDashboard from '../components/RoleBasedDashboard'
import './Dashboard.css'

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState(true)
  const { user } = useTypedSelector((state) => state.auth)
  const { getUserRole } = usePermissions()

  useEffect(() => {
    // 模拟数据加载
    const timer = setTimeout(() => {
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  if (loading) {
    return <LoadingSpinner tip="加载仪表板数据..." />
  }

  // 使用基于角色的仪表板
  return (
    <div className="dashboard-container">
      <RoleBasedDashboard user={user} />
    </div>
  )
}

export default Dashboard
