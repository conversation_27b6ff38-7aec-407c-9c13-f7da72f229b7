import React, { useEffect, useState } from 'react'
import {
  Card,
  Typography,
  Button,
  Space,
  Row,
  Col,
  Tag,
  Table,
  Descriptions,
  Statistic,
  Upload,
  Image,
  message,
  Modal,
  Spin,
  Progress,
  Timeline,
  Select
} from 'antd'
import {
  ArrowLeftOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  UploadOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  Check<PERSON>ircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons'
import { useNavigate, useParams } from 'react-router-dom'
import { batchAPI } from '../services/api/batch'

const { Title, Text } = Typography
const { Option } = Select

interface MicroscopeImage {
  id: string
  imageUrl: string
  description?: string
  magnification?: number
  createdAt: string
}

interface SensoryAssessment {
  id: string
  taste: number
  texture: number
  aroma: number
  appearance: number
  overallScore: number
  notes?: string
  createdAt: string
}

interface Batch {
  id: string
  batchNumber: string
  status: string
  productionDate: string
  quantity?: number
  actualQuantity?: number
  notes?: string
  startTime?: string
  endTime?: string
  fermentationStartTime?: string
  fermentationEndTime?: string
  filtrationStartTime?: string
  filtrationEndTime?: string
  recipe: {
    id: string
    name: string
    version: number
  }
  user: {
    id: string
    name: string
    email: string
  }
  microscopeImages: MicroscopeImage[]
  sensoryAssessments: SensoryAssessment[]
  createdAt: string
  updatedAt: string
}

const BatchDetail: React.FC = () => {
  const navigate = useNavigate()
  const { id } = useParams<{ id: string }>()
  const [batch, setBatch] = useState<Batch | null>(null)
  const [loading, setLoading] = useState(true)
  const [uploading, setUploading] = useState(false)
  const [previewVisible, setPreviewVisible] = useState(false)
  const [previewImage, setPreviewImage] = useState<string>('')

  // 加载批次详情
  const loadBatchDetail = async () => {
    if (!id) return
    
    try {
      setLoading(true)
      const response = await batchAPI.getBatchById(id)
      if (response.data.success) {
        setBatch(response.data.data.batch)
      } else {
        message.error('加载批次详情失败')
      }
    } catch (error: any) {
      console.error('Error loading batch:', error)
      message.error(error.response?.data?.message || '加载批次详情失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadBatchDetail()
  }, [id])

  // 更新批次状态
  const handleStatusUpdate = async (newStatus: string) => {
    if (!batch) return
    
    try {
      const response = await batchAPI.updateBatchStatus(batch.id, newStatus)
      if (response.data.success) {
        setBatch({ ...batch, status: newStatus })
        message.success('状态更新成功')
      } else {
        message.error(response.data.message || '状态更新失败')
      }
    } catch (error: any) {
      message.error(error.response?.data?.message || '状态更新失败')
    }
  }

  // 删除批次
  const handleDelete = () => {
    if (!batch) return
    
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除批次"${batch.batchNumber}"吗？此操作不可恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await batchAPI.deleteBatch(batch.id)
          if (response.data.success) {
            message.success('批次删除成功')
            navigate('/batches')
          } else {
            message.error(response.data.message || '删除失败')
          }
        } catch (error: any) {
          message.error(error.response?.data?.message || '删除失败')
        }
      },
    })
  }

  // 图片上传
  const handleImageUpload = async (file: File) => {
    if (!batch) return false
    
    const formData = new FormData()
    formData.append('images', file)
    
    try {
      setUploading(true)
      const response = await batchAPI.uploadBatchImages(batch.id, formData)
      if (response.data.success) {
        message.success('图片上传成功')
        loadBatchDetail() // 重新加载数据
      } else {
        message.error(response.data.message || '上传失败')
      }
    } catch (error: any) {
      message.error(error.response?.data?.message || '上传失败')
    } finally {
      setUploading(false)
    }
    
    return false // 阻止默认上传行为
  }

  // 状态配置
  const statusConfig = {
    'PLANNING': { color: 'default', text: '计划中', icon: <EditOutlined /> },
    'IN_PROGRESS': { color: 'processing', text: '进行中', icon: <PlayCircleOutlined /> },
    'FERMENTING': { color: 'orange', text: '发酵中', icon: <PauseCircleOutlined /> },
    'FILTERING': { color: 'cyan', text: '过滤中', icon: <PauseCircleOutlined /> },
    'COMPLETED': { color: 'success', text: '已完成', icon: <CheckCircleOutlined /> },
    'FAILED': { color: 'error', text: '失败', icon: <CloseCircleOutlined /> }
  }

  // 状态进度计算
  const getStatusProgress = (status: string) => {
    const statusOrder = ['PLANNING', 'IN_PROGRESS', 'FERMENTING', 'FILTERING', 'COMPLETED']
    const currentIndex = statusOrder.indexOf(status)
    if (status === 'FAILED') return 0
    return currentIndex >= 0 ? ((currentIndex + 1) / statusOrder.length) * 100 : 0
  }

  // 图片表格列
  const imageColumns = [
    {
      title: '图片',
      dataIndex: 'imageUrl',
      key: 'imageUrl',
      width: 100,
      render: (imageUrl: string) => (
        <Image
          width={60}
          height={60}
          src={imageUrl}
          style={{ objectFit: 'cover', cursor: 'pointer' }}
          preview={{
            onVisibleChange: (visible) => setPreviewVisible(visible),
          }}
        />
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      render: (description: string) => description || '-',
    },
    {
      title: '放大倍数',
      dataIndex: 'magnification',
      key: 'magnification',
      width: 120,
      render: (magnification: number) => magnification ? `${magnification}x` : '-',
    },
    {
      title: '拍摄时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (date: string) => new Date(date).toLocaleString('zh-CN'),
    },
  ]

  // 感官评估表格列
  const assessmentColumns = [
    {
      title: '评估时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (date: string) => new Date(date).toLocaleString('zh-CN'),
    },
    {
      title: '口感',
      dataIndex: 'taste',
      key: 'taste',
      width: 80,
      render: (score: number) => `${score}/10`,
    },
    {
      title: '质地',
      dataIndex: 'texture',
      key: 'texture',
      width: 80,
      render: (score: number) => `${score}/10`,
    },
    {
      title: '香味',
      dataIndex: 'aroma',
      key: 'aroma',
      width: 80,
      render: (score: number) => `${score}/10`,
    },
    {
      title: '外观',
      dataIndex: 'appearance',
      key: 'appearance',
      width: 80,
      render: (score: number) => `${score}/10`,
    },
    {
      title: '总分',
      dataIndex: 'overallScore',
      key: 'overallScore',
      width: 80,
      render: (score: number) => (
        <Text strong style={{ color: score >= 8 ? '#52c41a' : score >= 6 ? '#faad14' : '#f5222d' }}>
          {score}/10
        </Text>
      ),
    },
    {
      title: '备注',
      dataIndex: 'notes',
      key: 'notes',
      render: (notes: string) => notes || '-',
    },
  ]

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large">
          <div style={{ padding: '50px' }}>加载批次详情...</div>
        </Spin>
      </div>
    )
  }

  if (!batch) {
    return (
      <div>
        <div style={{ marginBottom: 24 }}>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/batches')}
          >
            返回批次列表
          </Button>
        </div>
        <Card>
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Text type="secondary">批次不存在或已被删除</Text>
          </div>
        </Card>
      </div>
    )
  }

  const currentStatusConfig = statusConfig[batch.status] || statusConfig['PLANNING']

  return (
    <div>
      {/* 页面头部 */}
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={() => navigate('/batches')}
        >
          返回批次列表
        </Button>
        
        <Space>
          <Select
            value={batch.status}
            style={{ width: 120 }}
            onChange={handleStatusUpdate}
            disabled={batch.status === 'COMPLETED' || batch.status === 'FAILED'}
          >
            <Option value="PLANNING">计划中</Option>
            <Option value="IN_PROGRESS">进行中</Option>
            <Option value="FERMENTING">发酵中</Option>
            <Option value="FILTERING">过滤中</Option>
            <Option value="COMPLETED">已完成</Option>
            <Option value="FAILED">失败</Option>
          </Select>
          <Button 
            icon={<EditOutlined />}
            onClick={() => navigate(`/batches/${batch.id}/edit`)}
          >
            编辑批次
          </Button>
          <Button 
            danger
            icon={<DeleteOutlined />}
            onClick={handleDelete}
          >
            删除批次
          </Button>
        </Space>
      </div>

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={16}>
          {/* 基本信息 */}
          <Card title="基本信息" style={{ marginBottom: 24 }}>
            <div style={{ marginBottom: 16 }}>
              <Title level={2} style={{ margin: 0, display: 'inline-block', marginRight: 16 }}>
                {batch.batchNumber}
              </Title>
              <Tag 
                color={currentStatusConfig.color} 
                icon={currentStatusConfig.icon}
                style={{ fontSize: '14px', padding: '4px 12px' }}
              >
                {currentStatusConfig.text}
              </Tag>
            </div>
            
            {/* 进度条 */}
            <div style={{ marginBottom: 24 }}>
              <Text strong>生产进度</Text>
              <Progress 
                percent={getStatusProgress(batch.status)}
                status={batch.status === 'FAILED' ? 'exception' : 'active'}
                strokeColor={batch.status === 'FAILED' ? '#f5222d' : undefined}
              />
            </div>
            
            <Descriptions column={2}>
              <Descriptions.Item label="关联配方">
                <Button 
                  type="link" 
                  onClick={() => navigate(`/recipes/${batch.recipe.id}`)}
                  style={{ padding: 0 }}
                >
                  {batch.recipe.name} (v{batch.recipe.version})
                </Button>
              </Descriptions.Item>
              <Descriptions.Item label="负责人">
                {batch.user.name}
              </Descriptions.Item>
              <Descriptions.Item label="生产日期">
                {new Date(batch.productionDate).toLocaleDateString('zh-CN')}
              </Descriptions.Item>
              <Descriptions.Item label="计划数量">
                {batch.quantity ? `${batch.quantity}L` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="实际数量">
                {batch.actualQuantity ? `${batch.actualQuantity}L` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(batch.createdAt).toLocaleString('zh-CN')}
              </Descriptions.Item>
              <Descriptions.Item label="备注" span={2}>
                {batch.notes || '-'}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* 时间轴 */}
          <Card title="生产时间轴" style={{ marginBottom: 24 }}>
            <Timeline
              items={[
                {
                  color: 'blue',
                  children: (
                    <div>
                      <Text strong>批次创建</Text>
                      <br />
                      <Text type="secondary">
                        {new Date(batch.createdAt).toLocaleString('zh-CN')}
                      </Text>
                    </div>
                  )
                },
                ...(batch.startTime ? [{
                  color: 'green' as const,
                  children: (
                    <div>
                      <Text strong>开始生产</Text>
                      <br />
                      <Text type="secondary">
                        {new Date(batch.startTime).toLocaleString('zh-CN')}
                      </Text>
                    </div>
                  )
                }] : []),
                ...(batch.fermentationStartTime ? [{
                  color: 'orange' as const,
                  children: (
                    <div>
                      <Text strong>开始发酵</Text>
                      <br />
                      <Text type="secondary">
                        {new Date(batch.fermentationStartTime).toLocaleString('zh-CN')}
                      </Text>
                    </div>
                  )
                }] : []),
                ...(batch.fermentationEndTime ? [{
                  color: 'cyan' as const,
                  children: (
                    <div>
                      <Text strong>发酵完成</Text>
                      <br />
                      <Text type="secondary">
                        {new Date(batch.fermentationEndTime).toLocaleString('zh-CN')}
                      </Text>
                    </div>
                  )
                }] : []),
                ...(batch.filtrationStartTime ? [{
                  color: 'purple' as const,
                  children: (
                    <div>
                      <Text strong>开始过滤</Text>
                      <br />
                      <Text type="secondary">
                        {new Date(batch.filtrationStartTime).toLocaleString('zh-CN')}
                      </Text>
                    </div>
                  )
                }] : []),
                ...(batch.endTime ? [{
                  color: (batch.status === 'COMPLETED' ? 'green' : 'red') as const,
                  children: (
                    <div>
                      <Text strong>{batch.status === 'COMPLETED' ? '生产完成' : '生产终止'}</Text>
                      <br />
                      <Text type="secondary">
                        {new Date(batch.endTime).toLocaleString('zh-CN')}
                      </Text>
                    </div>
                  )
                }] : [])
              ]}
            />
          </Card>

          {/* 显微镜图片 */}
          <Card 
            title="显微镜图片" 
            extra={
              <Upload
                accept="image/*"
                beforeUpload={handleImageUpload}
                showUploadList={false}
                disabled={uploading}
              >
                <Button icon={<UploadOutlined />} loading={uploading}>
                  上传图片
                </Button>
              </Upload>
            }
            style={{ marginBottom: 24 }}
          >
            <Table
              columns={imageColumns}
              dataSource={batch.microscopeImages || []}
              pagination={{
                pageSize: 5,
                showSizeChanger: false,
                showQuickJumper: true,
                showTotal: (total, range) => 
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              }}
              rowKey="id"
              locale={{ emptyText: '暂无图片记录' }}
            />
          </Card>

          {/* 感官评估 */}
          <Card title="感官评估">
            <Table
              columns={assessmentColumns}
              dataSource={batch.sensoryAssessments || []}
              pagination={{
                pageSize: 5,
                showSizeChanger: false,
                showQuickJumper: true,
                showTotal: (total, range) => 
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              }}
              rowKey="id"
              locale={{ emptyText: '暂无评估记录' }}
            />
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          {/* 统计信息 */}
          <Card title="统计信息" style={{ marginBottom: 24 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="图片数量"
                  value={batch.microscopeImages?.length || 0}
                  suffix="张"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="评估次数"
                  value={batch.sensoryAssessments?.length || 0}
                  suffix="次"
                />
              </Col>
            </Row>
            
            {batch.sensoryAssessments && batch.sensoryAssessments.length > 0 && (
              <div style={{ marginTop: 24 }}>
                <Text strong>平均评分</Text>
                <Row gutter={8} style={{ marginTop: 8 }}>
                  <Col span={12}>
                    <Statistic
                      title="口感"
                      value={(batch.sensoryAssessments.reduce((sum, a) => sum + a.taste, 0) / batch.sensoryAssessments.length).toFixed(1)}
                      suffix="/10"
                      precision={1}
                      valueStyle={{ fontSize: '16px' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="质地"
                      value={(batch.sensoryAssessments.reduce((sum, a) => sum + a.texture, 0) / batch.sensoryAssessments.length).toFixed(1)}
                      suffix="/10"
                      precision={1}
                      valueStyle={{ fontSize: '16px' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="香味"
                      value={(batch.sensoryAssessments.reduce((sum, a) => sum + a.aroma, 0) / batch.sensoryAssessments.length).toFixed(1)}
                      suffix="/10"
                      precision={1}
                      valueStyle={{ fontSize: '16px' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="外观"
                      value={(batch.sensoryAssessments.reduce((sum, a) => sum + a.appearance, 0) / batch.sensoryAssessments.length).toFixed(1)}
                      suffix="/10"
                      precision={1}
                      valueStyle={{ fontSize: '16px' }}
                    />
                  </Col>
                </Row>
                <div style={{ textAlign: 'center', marginTop: 16 }}>
                  <Statistic
                    title="总体评分"
                    value={(batch.sensoryAssessments.reduce((sum, a) => sum + a.overallScore, 0) / batch.sensoryAssessments.length).toFixed(1)}
                    suffix="/10"
                    precision={1}
                    valueStyle={{ 
                      fontSize: '24px', 
                      color: (batch.sensoryAssessments.reduce((sum, a) => sum + a.overallScore, 0) / batch.sensoryAssessments.length) >= 8 ? '#52c41a' : 
                             (batch.sensoryAssessments.reduce((sum, a) => sum + a.overallScore, 0) / batch.sensoryAssessments.length) >= 6 ? '#faad14' : '#f5222d'
                    }}
                  />
                </div>
              </div>
            )}
          </Card>

          {/* 快速操作 */}
          <Card title="快速操作">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button 
                block 
                icon={<EyeOutlined />}
                onClick={() => navigate(`/recipes/${batch.recipe.id}`)}
              >
                查看配方详情
              </Button>
              <Button 
                block 
                icon={<EditOutlined />}
                onClick={() => navigate(`/batches/${batch.id}/edit`)}
              >
                编辑批次信息
              </Button>
              {batch.status !== 'COMPLETED' && batch.status !== 'FAILED' && (
                <Button 
                  block 
                  type="primary"
                  icon={<PlayCircleOutlined />}
                  onClick={() => {
                    const nextStatus = {
                      'PLANNING': 'IN_PROGRESS',
                      'IN_PROGRESS': 'FERMENTING',
                      'FERMENTING': 'FILTERING',
                      'FILTERING': 'COMPLETED'
                    }[batch.status]
                    if (nextStatus) {
                      handleStatusUpdate(nextStatus)
                    }
                  }}
                >
                  进入下一阶段
                </Button>
              )}
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default BatchDetail
