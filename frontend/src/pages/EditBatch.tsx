import React, { useState, useEffect } from 'react'
import {
  Card,
  Form,
  Input,
  InputNumber,
  Button,
  Space,
  Row,
  Col,
  Select,
  DatePicker,
  message,
  Spin,
  Rate,
  Divider,
  Upload,
  Image,
  Modal
} from 'antd'
import { 
  ArrowLeftOutlined,
  SaveOutlined,
  PlusOutlined,
  DeleteOutlined,
  EyeOutlined
} from '@ant-design/icons'
import { useNavigate, useParams } from 'react-router-dom'
import { batchAPI } from '../services/api/batch'
import { recipeAPI } from '../services/api/recipe'
import dayjs from 'dayjs'

const { TextArea } = Input
const { Option } = Select

interface Recipe {
  id: string
  name: string
  version: number
}

interface Batch {
  id: string
  batchNumber: string
  status: string
  productionDate: string
  quantity?: number
  actualQuantity?: number
  notes?: string
  recipeId: string
  recipe: Recipe
}

interface SensoryAssessment {
  taste: number
  texture: number
  aroma: number
  appearance: number
  overallScore: number
  notes?: string
}

interface ProductImage {
  id?: string
  url: string
  description?: string
  uploadTime?: string
}

const EditBatch: React.FC = () => {
  const [form] = Form.useForm()
  const [batch, setBatch] = useState<Batch | null>(null)
  const [recipes, setRecipes] = useState<Recipe[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [showSensoryForm, setShowSensoryForm] = useState(false)
  const [productImages, setProductImages] = useState<ProductImage[]>([])
  const [uploading, setUploading] = useState(false)
  const [previewVisible, setPreviewVisible] = useState(false)
  const [previewImage, setPreviewImage] = useState('')
  const [previewTitle, setPreviewTitle] = useState('')
  const navigate = useNavigate()
  const { id } = useParams<{ id: string }>()

  // 加载批次详情
  const loadBatch = async () => {
    if (!id) return
    
    try {
      setLoading(true)
      const response = await batchAPI.getBatchById(id)
      if (response.data.success) {
        const batchData = response.data.data.batch
        setBatch(batchData)
        
        // 设置表单初始值
        form.setFieldsValue({
          batchNumber: batchData.batchNumber,
          status: batchData.status,
          productionDate: dayjs(batchData.productionDate),
          quantity: batchData.quantity,
          actualQuantity: batchData.actualQuantity,
          notes: batchData.notes,
          recipeId: batchData.recipe.id
        })
      } else {
        message.error('加载批次失败')
        navigate('/batches')
      }
    } catch (error: any) {
      console.error('Error loading batch:', error)
      message.error(error.response?.data?.message || '加载批次失败')
      navigate('/batches')
    } finally {
      setLoading(false)
    }
  }

  // 加载配方列表
  const loadRecipes = async () => {
    try {
      const response = await recipeAPI.getRecipes({ isActive: true })
      if (response.data.success) {
        setRecipes(response.data.data.recipes)
      }
    } catch (error: any) {
      console.error('Error loading recipes:', error)
      message.error('加载配方列表失败')
    }
  }

  useEffect(() => {
    loadBatch()
    loadRecipes()
  }, [id])

  // 保存批次
  const handleSubmit = async (values: any) => {
    if (!batch) return

    setSaving(true)
    try {
      const updateData = {
        ...values,
        productionDate: values.productionDate.format('YYYY-MM-DD')
      }
      
      const response = await batchAPI.updateBatch(batch.id, updateData)
      if (response.data.success) {
        message.success('批次更新成功')
        navigate(`/batches/${batch.id}`)
      } else {
        message.error(response.data.message || '更新失败')
      }
    } catch (error: any) {
      console.error('更新批次失败:', error)
      message.error(error.response?.data?.message || '更新失败')
    } finally {
      setSaving(false)
    }
  }

  // 提交感官评估
  const handleSensorySubmit = async (values: SensoryAssessment) => {
    if (!batch) return

    try {
      const response = await batchAPI.addSensoryAssessment(batch.id, values)
      if (response.data.success) {
        message.success('感官评估添加成功')
        setShowSensoryForm(false)
        // 可以选择刷新页面或返回详情页
      } else {
        message.error(response.data.message || '添加失败')
      }
    } catch (error: any) {
      console.error('添加感官评估失败:', error)
      message.error(error.response?.data?.message || '添加失败')
    }
  }

  // 图片上传前的处理
  const beforeUpload = (file: File) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/webp'
    if (!isJpgOrPng) {
      message.error('只支持上传 JPG/PNG/WEBP 格式的图片!')
      return false
    }
    const isLt5M = file.size / 1024 / 1024 < 5
    if (!isLt5M) {
      message.error('图片大小不能超过 5MB!')
      return false
    }
    return true
  }

  // 处理图片上传
  const handleImageUpload = async (file: File) => {
    if (!batch) return false

    const formData = new FormData()
    formData.append('image', file)
    formData.append('description', '产品形态图片')

    try {
      setUploading(true)
      // 这里应该调用产品图片上传API，暂时模拟
      // const response = await batchAPI.uploadProductImage(batch.id, formData)
      
      // 模拟上传成功，添加到本地状态
      const newImage: ProductImage = {
        id: Date.now().toString(),
        url: URL.createObjectURL(file),
        description: '产品形态图片',
        uploadTime: new Date().toISOString()
      }
      setProductImages([...productImages, newImage])
      message.success('图片上传成功')
    } catch (error: any) {
      message.error('图片上传失败')
    } finally {
      setUploading(false)
    }
    
    return false // 阻止默认上传行为
  }

  // 删除图片
  const handleImageDelete = (imageId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这张图片吗？',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        setProductImages(productImages.filter(img => img.id !== imageId))
        message.success('图片删除成功')
      }
    })
  }

  // 预览图片
  const handlePreview = (image: ProductImage) => {
    setPreviewImage(image.url)
    setPreviewTitle(image.description || '产品形态图片')
    setPreviewVisible(true)
  }

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large">
          <div style={{ padding: '50px' }}>加载批次信息...</div>
        </Spin>
      </div>
    )
  }

  if (!batch) {
    return (
      <div>
        <div style={{ marginBottom: 24 }}>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/batches')}
          >
            返回批次列表
          </Button>
        </div>
        <Card>
          <div style={{ textAlign: 'center', padding: '50px' }}>
            批次不存在或已被删除
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div>
      {/* 页面头部 */}
      <div style={{ marginBottom: 24 }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate(`/batches/${batch.id}`)}
          >
            返回详情页
          </Button>
        </Space>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
      >
        <Row gutter={24}>
          <Col xs={24} lg={16}>
            {/* 基本信息 */}
            <Card title="基本信息" style={{ marginBottom: 24 }}>
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="batchNumber"
                    label="批次号"
                    rules={[{ required: true, message: '请输入批次号' }]}
                  >
                    <Input placeholder="请输入批次号" />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="status"
                    label="批次状态"
                    rules={[{ required: true, message: '请选择批次状态' }]}
                  >
                    <Select placeholder="请选择批次状态">
                      <Option value="PLANNING">计划中</Option>
                      <Option value="IN_PROGRESS">进行中</Option>
                      <Option value="FERMENTING">发酵中</Option>
                      <Option value="FILTERING">过滤中</Option>
                      <Option value="COMPLETED">已完成</Option>
                      <Option value="FAILED">失败</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="recipeId"
                    label="关联配方"
                    rules={[{ required: true, message: '请选择配方' }]}
                  >
                    <Select placeholder="请选择配方">
                      {recipes.map(recipe => (
                        <Option key={recipe.id} value={recipe.id}>
                          {recipe.name} (v{recipe.version})
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="productionDate"
                    label="生产日期"
                    rules={[{ required: true, message: '请选择生产日期' }]}
                  >
                    <DatePicker 
                      style={{ width: '100%' }} 
                      placeholder="请选择生产日期"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="quantity"
                    label="计划数量 (升)"
                  >
                    <InputNumber
                      min={0}
                      style={{ width: '100%' }}
                      placeholder="请输入计划生产数量"
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="actualQuantity"
                    label="实际数量 (升)"
                  >
                    <InputNumber
                      min={0}
                      style={{ width: '100%' }}
                      placeholder="请输入实际生产数量"
                    />
                  </Form.Item>
                </Col>
              </Row>
              
              <Form.Item
                name="notes"
                label="备注信息"
              >
                <TextArea 
                  rows={4} 
                  placeholder="请输入批次备注信息" 
                />
              </Form.Item>
            </Card>

            {/* 产品形态图片 */}
            <Card title="产品形态图片" style={{ marginBottom: 24 }}>
              <div style={{ marginBottom: 16 }}>
                <Upload
                  accept="image/*"
                  beforeUpload={beforeUpload}
                  customRequest={({ file }) => handleImageUpload(file as File)}
                  showUploadList={false}
                  disabled={uploading}
                >
                  <Button 
                    icon={<PlusOutlined />} 
                    loading={uploading}
                    type="dashed"
                    style={{ width: '100%', height: '40px' }}
                  >
                    上传产品形态图片
                  </Button>
                </Upload>
              </div>
              
              {productImages.length > 0 && (
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
                  {productImages.map((image) => (
                    <div 
                      key={image.id} 
                      style={{ 
                        position: 'relative',
                        border: '1px solid #d9d9d9',
                        borderRadius: '8px',
                        overflow: 'hidden',
                        width: '200px',
                        height: '200px'
                      }}
                    >
                      <Image
                        src={image.url}
                        alt={image.description}
                        width={200}
                        height={200}
                        style={{ objectFit: 'cover' }}
                        preview={false}
                      />
                      <div 
                        style={{
                          position: 'absolute',
                          top: 0,
                          right: 0,
                          left: 0,
                          bottom: 0,
                          backgroundColor: 'rgba(0, 0, 0, 0.5)',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          gap: '8px',
                          opacity: 0,
                          transition: 'opacity 0.3s',
                        }}
                        className="image-overlay"
                        onMouseEnter={(e) => { e.currentTarget.style.opacity = '1' }}
                        onMouseLeave={(e) => { e.currentTarget.style.opacity = '0' }}
                      >
                        <Button
                          type="primary"
                          icon={<EyeOutlined />}
                          size="small"
                          onClick={() => handlePreview(image)}
                        >
                          预览
                        </Button>
                        <Button
                          danger
                          icon={<DeleteOutlined />}
                          size="small"
                          onClick={() => handleImageDelete(image.id!)}
                        >
                          删除
                        </Button>
                      </div>
                      <div style={{
                        position: 'absolute',
                        bottom: 0,
                        left: 0,
                        right: 0,
                        background: 'linear-gradient(transparent, rgba(0,0,0,0.7))',
                        color: 'white',
                        padding: '8px',
                        fontSize: '12px'
                      }}>
                        {image.description}
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              {productImages.length === 0 && (
                <div style={{ 
                  textAlign: 'center', 
                  color: '#999', 
                  padding: '40px',
                  border: '1px dashed #d9d9d9',
                  borderRadius: '8px',
                  marginTop: '16px'
                }}>
                  暂无产品形态图片，点击上方按钮上传
                </div>
              )}
            </Card>

            {/* 感官评估 */}
            <Card 
              title="添加感官评估" 
              extra={
                <Button 
                  type="primary" 
                  onClick={() => setShowSensoryForm(!showSensoryForm)}
                >
                  {showSensoryForm ? '取消评估' : '添加评估'}
                </Button>
              }
              style={{ marginBottom: 24 }}
            >
              {showSensoryForm && (
                <Form
                  layout="vertical"
                  onFinish={handleSensorySubmit}
                  initialValues={{
                    taste: 7,
                    texture: 7,
                    aroma: 7,
                    appearance: 7,
                    overallScore: 7
                  }}
                >
                  <Row gutter={16}>
                    <Col xs={24} sm={12}>
                      <Form.Item
                        name="taste"
                        label="口感评分"
                        rules={[{ required: true, message: '请评分' }]}
                      >
                        <Rate count={10} allowHalf />
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={12}>
                      <Form.Item
                        name="texture"
                        label="质地评分"
                        rules={[{ required: true, message: '请评分' }]}
                      >
                        <Rate count={10} allowHalf />
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Row gutter={16}>
                    <Col xs={24} sm={12}>
                      <Form.Item
                        name="aroma"
                        label="香味评分"
                        rules={[{ required: true, message: '请评分' }]}
                      >
                        <Rate count={10} allowHalf />
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={12}>
                      <Form.Item
                        name="appearance"
                        label="外观评分"
                        rules={[{ required: true, message: '请评分' }]}
                      >
                        <Rate count={10} allowHalf />
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Form.Item
                    name="overallScore"
                    label="总体评分"
                    rules={[{ required: true, message: '请评分' }]}
                  >
                    <Rate count={10} allowHalf />
                  </Form.Item>
                  
                  <Form.Item
                    name="notes"
                    label="评估备注"
                  >
                    <TextArea 
                      rows={3} 
                      placeholder="请输入评估备注信息" 
                    />
                  </Form.Item>
                  
                  <Form.Item>
                    <Space>
                      <Button type="primary" htmlType="submit">
                        提交评估
                      </Button>
                      <Button onClick={() => setShowSensoryForm(false)}>
                        取消
                      </Button>
                    </Space>
                  </Form.Item>
                </Form>
              )}
              
              {!showSensoryForm && (
                <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
                  点击"添加评估"按钮来为此批次添加感官评估
                </div>
              )}
            </Card>
          </Col>

          <Col xs={24} lg={8}>
            {/* 操作面板 */}
            <Card title="保存更改" style={{ marginBottom: 24 }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={saving}
                  icon={<SaveOutlined />}
                  block
                  size="large"
                >
                  保存更改
                </Button>
                <Button
                  block
                  onClick={() => navigate(`/batches/${batch.id}`)}
                >
                  取消编辑
                </Button>
              </Space>
            </Card>

            {/* 状态说明 */}
            <Card title="状态说明">
              <div style={{ fontSize: '12px', lineHeight: '1.6' }}>
                <div><strong>计划中:</strong> 批次刚创建，准备开始生产</div>
                <Divider style={{ margin: '8px 0' }} />
                <div><strong>进行中:</strong> 开始生产，准备材料和设备</div>
                <Divider style={{ margin: '8px 0' }} />
                <div><strong>发酵中:</strong> 正在进行发酵过程</div>
                <Divider style={{ margin: '8px 0' }} />
                <div><strong>过滤中:</strong> 发酵完成，正在过滤</div>
                <Divider style={{ margin: '8px 0' }} />
                <div><strong>已完成:</strong> 生产完成，可以出货</div>
                <Divider style={{ margin: '8px 0' }} />
                <div><strong>失败:</strong> 生产过程中出现问题</div>
              </div>
            </Card>
          </Col>
        </Row>
      </Form>

      {/* 图片预览Modal */}
      <Modal
        open={previewVisible}
        title={previewTitle}
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        width="80%"
        style={{ maxWidth: '800px' }}
      >
        <Image
          alt="预览图片"
          style={{ width: '100%' }}
          src={previewImage}
        />
      </Modal>
    </div>
  )
}

export default EditBatch