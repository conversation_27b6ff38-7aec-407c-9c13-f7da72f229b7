import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  DatePicker,
  Select,
  Input,
  Typography,
  Row,
  Col,
  Statistic,
  Alert,
  Tooltip,
  Modal,
  message,
} from 'antd';
import {
  AuditOutlined,
  UserOutlined,
  LoginOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  DownloadOutlined,
  ReloadOutlined,
  FilterOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons';
import { useTypedSelector } from '../hooks/useTypedSelector';
import dayjs from 'dayjs';

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { Search } = Input;

interface AuditLog {
  id: string;
  timestamp: string;
  userId: string;
  userName: string;
  userRole: string;
  action: string;
  resource: string;
  details: string;
  ipAddress: string;
  userAgent: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  success: boolean;
  duration?: number;
  metadata?: any;
}

interface AuditFilters {
  dateRange?: [dayjs.Dayjs, dayjs.Dayjs];
  userId?: string;
  action?: string;
  resource?: string;
  severity?: string;
  success?: boolean;
  search?: string;
}

const AuditLog: React.FC = () => {
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState<AuditFilters>({});
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [statistics, setStatistics] = useState({
    total: 0,
    todayCount: 0,
    errorCount: 0,
    warningCount: 0,
    successRate: 0,
  });

  const { user: currentUser } = useTypedSelector(state => state.auth);

  // 模拟数据
  const mockLogs: AuditLog[] = [
    {
      id: '1',
      timestamp: '2025-08-01T10:30:00Z',
      userId: 'admin-001',
      userName: '系统管理员',
      userRole: 'ADMIN',
      action: 'CREATE_USER',
      resource: 'USER',
      details: '创建用户: <EMAIL>',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
      severity: 'info',
      success: true,
      duration: 245,
      metadata: { cafeId: '6c126f62-57af-43a6-849c-55e9e5c702a6', newUserId: 'user-001' }
    },
    {
      id: '2',
      timestamp: '2025-08-01T10:25:00Z',
      userId: 'manager-haikou',
      userName: '海口店管理员',
      userRole: 'USER',
      action: 'LOGIN',
      resource: 'AUTH',
      details: '用户登录成功',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
      severity: 'info',
      success: true,
      duration: 156,
    },
    {
      id: '3',
      timestamp: '2025-08-01T10:20:00Z',
      userId: 'unknown',
      userName: '未知用户',
      userRole: 'UNKNOWN',
      action: 'LOGIN_FAILED',
      resource: 'AUTH',
      details: '登录失败: 密码错误',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
      severity: 'warning',
      success: false,
      duration: 89,
      metadata: { reason: 'Invalid password', attempts: 3 }
    },
    {
      id: '4',
      timestamp: '2025-08-01T10:15:00Z',
      userId: 'admin-001',
      userName: '系统管理员',
      userRole: 'ADMIN',
      action: 'DELETE_USER',
      resource: 'USER',
      details: '删除用户: <EMAIL>',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
      severity: 'warning',
      success: true,
      duration: 178,
      metadata: { deletedUserId: 'user-old-001', reason: 'Account cleanup' }
    },
    {
      id: '5',
      timestamp: '2025-08-01T10:10:00Z',
      userId: 'system',
      userName: '系统',
      userRole: 'SYSTEM',
      action: 'BACKUP_FAILED',
      resource: 'DATABASE',
      details: '数据库备份失败: 磁盘空间不足',
      ipAddress: '127.0.0.1',
      userAgent: 'System/1.0',
      severity: 'error',
      success: false,
      duration: 5000,
      metadata: { error: 'Disk space insufficient', requiredSpace: '2GB', availableSpace: '500MB' }
    },
  ];

  useEffect(() => {
    loadAuditLogs();
  }, [filters]);

  const loadAuditLogs = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      setTimeout(() => {
        let filteredLogs = [...mockLogs];

        // 应用过滤器
        if (filters.dateRange) {
          const [start, end] = filters.dateRange;
          filteredLogs = filteredLogs.filter(log => {
            const logDate = dayjs(log.timestamp);
            return logDate.isAfter(start) && logDate.isBefore(end);
          });
        }

        if (filters.severity) {
          filteredLogs = filteredLogs.filter(log => log.severity === filters.severity);
        }

        if (filters.action) {
          filteredLogs = filteredLogs.filter(log => log.action === filters.action);
        }

        if (filters.success !== undefined) {
          filteredLogs = filteredLogs.filter(log => log.success === filters.success);
        }

        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          filteredLogs = filteredLogs.filter(log =>
            log.details.toLowerCase().includes(searchLower) ||
            log.userName.toLowerCase().includes(searchLower) ||
            log.action.toLowerCase().includes(searchLower)
          );
        }

        setLogs(filteredLogs);
        
        // 计算统计数据
        const stats = {
          total: filteredLogs.length,
          todayCount: filteredLogs.filter(log => dayjs(log.timestamp).format('YYYY-MM-DD') === dayjs().format('YYYY-MM-DD')).length,
          errorCount: filteredLogs.filter(log => log.severity === 'error' || log.severity === 'critical').length,
          warningCount: filteredLogs.filter(log => log.severity === 'warning').length,
          successRate: Math.round((filteredLogs.filter(log => log.success).length / filteredLogs.length) * 100) || 0,
        };
        setStatistics(stats);
        
        setLoading(false);
      }, 800);
    } catch (error) {
      message.error('加载审计日志失败');
      setLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'info': return 'blue';
      case 'warning': return 'orange';
      case 'error': return 'red';
      case 'critical': return 'red';
      default: return 'default';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'info': return <CheckCircleOutlined />;
      case 'warning': return <WarningOutlined />;
      case 'error': return <CloseCircleOutlined />;
      case 'critical': return <ExclamationCircleOutlined />;
      default: return <CheckCircleOutlined />;
    }
  };

  const getActionIcon = (action: string) => {
    if (action.includes('LOGIN')) return <LoginOutlined />;
    if (action.includes('CREATE')) return <EditOutlined />;
    if (action.includes('DELETE')) return <DeleteOutlined />;
    if (action.includes('UPDATE')) return <EditOutlined />;
    return <AuditOutlined />;
  };

  const handleFilterChange = (key: keyof AuditFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleResetFilters = () => {
    setFilters({});
  };

  const handleExportLogs = () => {
    message.info('日志导出功能开发中...');
  };

  const showLogDetail = (log: AuditLog) => {
    setSelectedLog(log);
    setDetailModalVisible(true);
  };

  const columns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 160,
      render: (timestamp: string) => (
        <div>
          <div>{dayjs(timestamp).format('MM-DD HH:mm:ss')}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {dayjs(timestamp).fromNow()}
          </div>
        </div>
      ),
    },
    {
      title: '用户',
      key: 'user',
      width: 150,
      render: (record: AuditLog) => (
        <Space>
          <UserOutlined style={{ color: '#666' }} />
          <div>
            <div style={{ fontWeight: 500 }}>{record.userName}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>{record.userRole}</div>
          </div>
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (record: AuditLog) => (
        <Space>
          {getActionIcon(record.action)}
          <span>{record.action}</span>
        </Space>
      ),
    },
    {
      title: '资源',
      dataIndex: 'resource',
      key: 'resource',
      width: 100,
      render: (resource: string) => <Tag>{resource}</Tag>,
    },
    {
      title: '详情',
      dataIndex: 'details',
      key: 'details',
      ellipsis: true,
    },
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'severity',
      width: 100,
      render: (severity: string) => (
        <Tag color={getSeverityColor(severity)} icon={getSeverityIcon(severity)}>
          {severity.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'success',
      key: 'success',
      width: 80,
      render: (success: boolean) => (
        <Tag color={success ? 'green' : 'red'}>
          {success ? '成功' : '失败'}
        </Tag>
      ),
    },
    {
      title: 'IP地址',
      dataIndex: 'ipAddress',
      key: 'ipAddress',
      width: 120,
    },
    {
      title: '操作',
      key: 'actions',
      width: 80,
      render: (_, record: AuditLog) => (
        <Button
          type="text"
          icon={<EyeOutlined />}
          onClick={() => showLogDetail(record)}
        >
          详情
        </Button>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>审计日志</Title>
        <div style={{ color: '#666', fontSize: '14px' }}>
          系统操作记录和安全审计
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总日志数"
              value={statistics.total}
              prefix={<AuditOutlined />}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日日志"
              value={statistics.todayCount}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="错误/警告"
              value={statistics.errorCount + statistics.warningCount}
              prefix={<WarningOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="成功率"
              value={statistics.successRate}
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
              loading={loading}
            />
          </Card>
        </Col>
      </Row>

      {/* 过滤器 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <RangePicker
              value={filters.dateRange}
              onChange={(dates) => handleFilterChange('dateRange', dates)}
              placeholder={['开始时间', '结束时间']}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={3}>
            <Select
              placeholder="严重程度"
              allowClear
              value={filters.severity}
              onChange={(value) => handleFilterChange('severity', value)}
              style={{ width: '100%' }}
            >
              <Option value="info">信息</Option>
              <Option value="warning">警告</Option>
              <Option value="error">错误</Option>
              <Option value="critical">严重</Option>
            </Select>
          </Col>
          <Col span={3}>
            <Select
              placeholder="操作类型"
              allowClear
              value={filters.action}
              onChange={(value) => handleFilterChange('action', value)}
              style={{ width: '100%' }}
            >
              <Option value="LOGIN">登录</Option>
              <Option value="CREATE_USER">创建用户</Option>
              <Option value="DELETE_USER">删除用户</Option>
              <Option value="UPDATE_USER">更新用户</Option>
              <Option value="BACKUP">备份</Option>
            </Select>
          </Col>
          <Col span={3}>
            <Select
              placeholder="状态"
              allowClear
              value={filters.success}
              onChange={(value) => handleFilterChange('success', value)}
              style={{ width: '100%' }}
            >
              <Option value={true}>成功</Option>
              <Option value={false}>失败</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Search
              placeholder="搜索日志..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              allowClear
            />
          </Col>
          <Col span={5}>
            <Space>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={loadAuditLogs}
                loading={loading}
              >
                刷新
              </Button>
              <Button 
                icon={<FilterOutlined />} 
                onClick={handleResetFilters}
              >
                重置
              </Button>
              <Button 
                icon={<DownloadOutlined />} 
                onClick={handleExportLogs}
              >
                导出
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 安全警告 */}
      {statistics.errorCount > 0 && (
        <Alert
          message="检测到安全事件"
          description={`发现 ${statistics.errorCount} 个错误事件，请及时检查处理`}
          type="error"
          showIcon
          style={{ marginBottom: '16px' }}
        />
      )}

      {/* 日志表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={logs}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            pageSize: 20,
            showSizeChanger: true,
          }}
          scroll={{ x: 1200 }}
          size="small"
        />
      </Card>

      {/* 详情模态框 */}
      <Modal
        title="日志详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedLog && (
          <div>
            <Row gutter={16} style={{ marginBottom: '16px' }}>
              <Col span={12}>
                <strong>时间:</strong> {dayjs(selectedLog.timestamp).format('YYYY-MM-DD HH:mm:ss')}
              </Col>
              <Col span={12}>
                <strong>用户:</strong> {selectedLog.userName} ({selectedLog.userRole})
              </Col>
            </Row>
            
            <Row gutter={16} style={{ marginBottom: '16px' }}>
              <Col span={12}>
                <strong>操作:</strong> {selectedLog.action}
              </Col>
              <Col span={12}>
                <strong>资源:</strong> {selectedLog.resource}
              </Col>
            </Row>
            
            <Row gutter={16} style={{ marginBottom: '16px' }}>
              <Col span={12}>
                <strong>IP地址:</strong> {selectedLog.ipAddress}
              </Col>
              <Col span={12}>
                <strong>持续时间:</strong> {selectedLog.duration}ms
              </Col>
            </Row>
            
            <Row gutter={16} style={{ marginBottom: '16px' }}>
              <Col span={24}>
                <strong>详情:</strong> {selectedLog.details}
              </Col>
            </Row>
            
            <Row gutter={16} style={{ marginBottom: '16px' }}>
              <Col span={24}>
                <strong>User Agent:</strong> {selectedLog.userAgent}
              </Col>
            </Row>
            
            {selectedLog.metadata && (
              <Row gutter={16}>
                <Col span={24}>
                  <strong>元数据:</strong>
                  <pre style={{ 
                    background: '#f5f5f5', 
                    padding: '8px', 
                    borderRadius: '4px',
                    fontSize: '12px',
                    marginTop: '8px'
                  }}>
                    {JSON.stringify(selectedLog.metadata, null, 2)}
                  </pre>
                </Col>
              </Row>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default AuditLog;