import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Statistic,
  Spin,
  Alert,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  TeamOutlined,
  CrownOutlined,
  EyeOutlined,
  FilterOutlined,
  ReloadOutlined,
  ShopOutlined,
} from '@ant-design/icons';
import { userAPI, UserQueryParams, CreateUserRequest, UpdateUserRequest, UserStatistics } from '../services/api/user';
import { useTypedSelector } from '../hooks/useTypedSelector';

const { Title } = Typography;
const { Option } = Select;

interface User {
  id: string;
  email: string;
  username: string;
  name: string;
  role: string;
  cafeId: string | null;
  cafeName: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface Cafe {
  id: string;
  name: string;
  description: string;
  address: string;
}

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [cafes, setCafes] = useState<Cafe[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [statistics, setStatistics] = useState<UserStatistics>({
    total: 0,
    admin: 0,
    user: 0,
    viewer: 0,
    active: 0
  });
  const [filters, setFilters] = useState<UserQueryParams>({});
  const [form] = Form.useForm();
  const { user: currentUser } = useTypedSelector(state => state.auth);

  useEffect(() => {
    loadUsers();
    loadCafes();
  }, [filters]);

  const loadUsers = async () => {
    setLoading(true);
    try {
      const response = await userAPI.getUsers(filters);
      if (response.data.success) {
        setUsers(response.data.data.users);
        setStatistics(response.data.data.statistics);
      }
    } catch (error: any) {
      console.error('加载用户列表失败:', error);
      message.error(error.response?.data?.message || '加载用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  const loadCafes = async () => {
    try {
      const response = await userAPI.getCafes();
      if (response.data.success) {
        setCafes(response.data.data.cafes);
      }
    } catch (error: any) {
      console.error('加载店面列表失败:', error);
    }
  };

  const handleCreateUser = () => {
    setEditingUser(null);
    form.resetFields();
    // 如果当前用户不是ADMIN，默认选择自己的店面
    if (currentUser?.role !== 'ADMIN' && currentUser?.cafeId) {
      form.setFieldsValue({ cafeId: currentUser.cafeId });
    }
    setModalVisible(true);
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    form.setFieldsValue({
      name: user.name,
      role: user.role,
      isActive: user.isActive
    });
    setModalVisible(true);
  };

  const handleDeleteUser = async (userId: string) => {
    try {
      await userAPI.deleteUser(userId);
      message.success('用户删除成功');
      loadUsers();
    } catch (error: any) {
      message.error(error.response?.data?.message || '删除用户失败');
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingUser) {
        // 更新用户
        const updateData: UpdateUserRequest = {
          name: values.name,
          role: values.role,
          isActive: values.isActive
        };
        await userAPI.updateUser(editingUser.id, updateData);
        message.success('用户更新成功');
      } else {
        // 创建用户
        const createData: CreateUserRequest = {
          email: values.email,
          username: values.username,
          name: values.name,
          password: values.password,
          role: values.role,
          cafeId: values.cafeId
        };
        await userAPI.createUser(createData);
        message.success('用户创建成功');
      }
      
      setModalVisible(false);
      loadUsers();
    } catch (error: any) {
      message.error(error.response?.data?.message || '操作失败');
    }
  };

  const handleFilterChange = (key: keyof UserQueryParams, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleResetFilters = () => {
    setFilters({});
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN': return '#ff4d4f';
      case 'MANAGER': return '#fa8c16';
      case 'USER': return '#1890ff';
      case 'VIEWER': return '#52c41a';
      default: return '#666';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'ADMIN': return <CrownOutlined />;
      case 'MANAGER': return <TeamOutlined />;
      case 'USER': return <UserOutlined />;
      case 'VIEWER': return <EyeOutlined />;
      default: return <UserOutlined />;
    }
  };

  const columns = [
    {
      title: '用户信息',
      key: 'user',
      render: (record: User) => (
        <Space>
          <div style={{ 
            width: 32, 
            height: 32, 
            borderRadius: '50%', 
            backgroundColor: getRoleColor(record.role),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white'
          }}>
            {getRoleIcon(record.role)}
          </div>
          <div>
            <div style={{ fontWeight: 500 }}>{record.name}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {record.username} ({record.email})
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role: string) => (
        <Tag color={getRoleColor(role)}>
          {role === 'ADMIN' ? '总管理员' : 
           role === 'MANAGER' ? '店面管理员' : 
           role === 'USER' ? '操作员' : 
           '顾客'}
        </Tag>
      ),
    },
    {
      title: '所属店面',
      key: 'cafe',
      render: (record: User) => (
        <Space>
          <ShopOutlined style={{ color: '#666' }} />
          {record.cafeName || '未分配'}
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '活跃' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleDateString('zh-CN'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: User) => {
        const canEdit = currentUser?.role === 'ADMIN' || 
                        (currentUser?.role === 'MANAGER' && record.cafeId === currentUser?.cafeId);
        const canDelete = canEdit && 
                         record.role !== 'ADMIN' && 
                         record.id !== currentUser?.id &&
                         (currentUser?.role === 'ADMIN' || record.role !== 'MANAGER');
        
        return (
          <Space>
            {canEdit && (
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => handleEditUser(record)}
              >
                编辑
              </Button>
            )}
            {canDelete && (
              <Popconfirm
                title="确定要删除这个用户吗？"
                onConfirm={() => handleDeleteUser(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                >
                  删除
                </Button>
              </Popconfirm>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>用户管理</Title>
        <div style={{ color: '#666', fontSize: '14px' }}>
          {currentUser?.role === 'ADMIN' ? '管理所有店面用户' : 
           currentUser?.role === 'MANAGER' ? `管理 ${currentUser?.cafeName || '当前店面'} 用户` :
           '查看用户信息'}
        </div>
        
        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: '24px', marginTop: '16px' }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总用户数"
                value={statistics.total}
                prefix={<TeamOutlined />}
                loading={loading}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="管理员"
                value={statistics.admin}
                prefix={<CrownOutlined />}
                valueStyle={{ color: '#ff4d4f' }}
                loading={loading}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="普通用户"
                value={statistics.user}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#1890ff' }}
                loading={loading}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="观察员"
                value={statistics.viewer}
                prefix={<EyeOutlined />}
                valueStyle={{ color: '#52c41a' }}
                loading={loading}
              />
            </Card>
          </Col>
        </Row>

        {/* 过滤器 */}
        <Card style={{ marginBottom: '16px' }}>
          <Row gutter={16} align="middle">
            <Col span={4}>
              <Select
                placeholder="按角色筛选"
                allowClear
                value={filters.role}
                onChange={(value) => handleFilterChange('role', value)}
                style={{ width: '100%' }}
              >
                <Option value="ADMIN">总管理员</Option>
                <Option value="MANAGER">店面管理员</Option>
                <Option value="USER">操作员</Option>
                <Option value="VIEWER">顾客</Option>
              </Select>
            </Col>
            {currentUser?.role === 'ADMIN' && (
              <Col span={4}>
                <Select
                  placeholder="按店面筛选"
                  allowClear
                  value={filters.cafeId}
                  onChange={(value) => handleFilterChange('cafeId', value)}
                  style={{ width: '100%' }}
                >
                  {cafes.map(cafe => (
                    <Option key={cafe.id} value={cafe.id}>
                      {cafe.name}
                    </Option>
                  ))}
                </Select>
              </Col>
            )}
            <Col span={4}>
              <Select
                placeholder="按状态筛选"
                allowClear
                value={filters.isActive}
                onChange={(value) => handleFilterChange('isActive', value)}
                style={{ width: '100%' }}
              >
                <Option value={true}>活跃</Option>
                <Option value={false}>禁用</Option>
              </Select>
            </Col>
            <Col span={4}>
              <Space>
                <Button 
                  icon={<ReloadOutlined />} 
                  onClick={loadUsers}
                  loading={loading}
                >
                  刷新
                </Button>
                <Button 
                  icon={<FilterOutlined />} 
                  onClick={handleResetFilters}
                >
                  重置
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>

        <Card
          title={
            <Space>
              <span>用户列表</span>
              {loading && <Spin size="small" />}
            </Space>
          }
          extra={
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateUser}
            >
              新建用户
            </Button>
          }
        >
          <Table
            columns={columns}
            dataSource={users}
            rowKey="id"
            loading={loading}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`,
              pageSize: 10,
              pageSizeOptions: ['10', '20', '50', '100'],
            }}
          />
        </Card>
      </div>

      {/* 用户编辑模态框 */}
      <Modal
        title={editingUser ? '编辑用户' : '新建用户'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        width={600}
        destroyOnHidden
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{ isActive: true }}
        >
          {!editingUser && (
            <>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' },
                ]}
              >
                <Input placeholder="请输入邮箱" />
              </Form.Item>

              <Form.Item
                name="username"
                label="用户名"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, message: '用户名至少3位' },
                  { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' },
                ]}
              >
                <Input placeholder="请输入用户名" />
              </Form.Item>

              <Form.Item
                name="password"
                label="密码"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码至少6位' },
                ]}
              >
                <Input.Password placeholder="请输入密码" />
              </Form.Item>

              <Form.Item
                name="cafeId"
                label="所属店面"
                rules={[{ required: true, message: '请选择店面' }]}
              >
                <Select 
                  placeholder="请选择店面"
                  disabled={currentUser?.role !== 'ADMIN'}
                >
                  {cafes.map(cafe => (
                    <Option key={cafe.id} value={cafe.id}>
                      {cafe.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </>
          )}

          <Form.Item
            name="name"
            label="姓名"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input placeholder="请输入姓名" />
          </Form.Item>

          <Form.Item
            name="role"
            label="角色"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Select 
              placeholder="请选择角色"
              disabled={currentUser?.role !== 'ADMIN' && editingUser?.role === 'ADMIN'}
            >
              <Option value="MANAGER">
                <Space>
                  <TeamOutlined style={{ color: '#fa8c16' }} />
                  店面管理员
                </Space>
              </Option>
              <Option value="USER">
                <Space>
                  <UserOutlined style={{ color: '#1890ff' }} />
                  操作员
                </Space>
              </Option>
              <Option value="VIEWER">
                <Space>
                  <EyeOutlined style={{ color: '#52c41a' }} />
                  顾客
                </Space>
              </Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="isActive"
            label="状态"
          >
            <Select>
              <Option value={true}>活跃</Option>
              <Option value={false}>禁用</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserManagement;
