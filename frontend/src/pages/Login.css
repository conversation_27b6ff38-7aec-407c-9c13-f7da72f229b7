/* =============================================================================
   Login 页面样式 - 仿MHIIS布局
   ============================================================================= */

/* 容器 */
.login-container {
  display: flex;
  min-height: 100vh;
  width: 100%;
  background: #f0f2f5;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.login-wrapper {
  display: flex;
  max-width: 1200px;
  width: 100%;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-height: 600px;
}

/* 左侧面板 */
.login-left-panel {
  flex: 1.2;
  background: linear-gradient(135deg, #F05654 0%, #FF6B69 50%, #A8ABB0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding: 50px 40px;
}

.login-left-panel::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.05) 0%, transparent 70%);
  animation: rotate 30s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.left-panel-content {
  position: relative;
  z-index: 1;
  color: white;
  max-width: 500px;
  text-align: center;
}

/* Logo设计 */
.system-logo {
  margin-bottom: 40px;
}

.logo-circle {
  width: 150px;
  height: 150px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  border: 2px solid rgba(255,255,255,0.2);
}

.logo-icon {
  font-size: 48px;
  position: absolute;
  left: 25px;
  top: 35px;
}

.logo-plus {
  font-size: 28px;
  color: #FFD700;
  font-weight: bold;
  position: absolute;
  left: 65px;
  top: 50px;
}

.logo-ai {
  font-size: 40px;
  position: absolute;
  right: 25px;
  top: 40px;
}

.logo-leaf {
  font-size: 32px;
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}

/* 系统名称 */
.system-name {
  margin-bottom: 20px;
}

.system-title {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 10px 0;
  letter-spacing: 4px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
}

.system-subtitle {
  font-size: 32px;
  font-weight: 500;
  margin: 0;
  letter-spacing: 2px;
  opacity: 0.95;
}

/* 标语 */
.system-tagline {
  font-size: 14px;
  letter-spacing: 2px;
  opacity: 0.8;
  margin-bottom: 20px;
  font-weight: 300;
}

.system-description {
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 40px;
  opacity: 0.9;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* 功能药丸 */
.feature-pills {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-bottom: 60px;
}

.feature-pill {
  background: rgba(255,255,255,0.15);
  backdrop-filter: blur(10px);
  padding: 12px 24px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  border: 1px solid rgba(255,255,255,0.2);
  transition: all 0.3s ease;
}

.feature-pill:hover {
  background: rgba(255,255,255,0.25);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.pill-icon {
  font-size: 20px;
}

/* 项目信息 */
.project-info {
  font-size: 14px;
  opacity: 0.7;
  line-height: 1.5;
  position: absolute;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  text-align: center;
}

/* 右侧面板 */
.login-right-panel {
  flex: 1;
  background: #2a3a4a;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 50px 40px;
}

.login-form-container {
  width: 100%;
  max-width: 360px;
}

/* 表单头部 */
.form-header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.form-header h2 {
  font-size: 24px;
  margin: 0 0 10px 0;
  font-weight: 400;
  opacity: 0.9;
}

.form-header h1 {
  font-size: 32px;
  margin: 0 0 10px 0;
  font-weight: 600;
  color: #F05654;
}

.form-header .subtitle {
  font-size: 14px;
  opacity: 0.7;
  margin: 0 0 15px 0;
  letter-spacing: 1px;
}

.welcome-text {
  font-size: 16px;
  opacity: 0.8;
  margin: 0;
  line-height: 1.5;
}

/* 表单样式 */
.login-form {
  margin-top: 30px;
}

.login-form .ant-form-item {
  margin-bottom: 24px;
}

.login-form .ant-form-item-label {
  padding-bottom: 4px;
}

.login-form .ant-form-item-label > label {
  color: #ffffff;
  font-size: 14px;
  font-weight: 400;
  opacity: 0.9;
}

.login-form .ant-input-affix-wrapper,
.login-form .ant-select-selector {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 8px;
  height: 44px;
  color: white !important;
  transition: all 0.3s ease;
}

.login-form .ant-input-affix-wrapper:hover,
.login-form .ant-select:hover .ant-select-selector {
  background: rgba(255, 255, 255, 0.15) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
}

.login-form .ant-input-affix-wrapper:focus,
.login-form .ant-input-affix-wrapper-focused,
.login-form .ant-select-focused .ant-select-selector {
  background: rgba(255, 255, 255, 0.15) !important;
  border-color: #F05654 !important;
  box-shadow: 0 0 0 2px rgba(240, 86, 84, 0.2) !important;
}

.login-form .ant-input,
.login-form .ant-input-password-icon {
  background: transparent !important;
  color: white !important;
}

.login-form .ant-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.login-form .ant-input-prefix {
  color: rgba(255, 255, 255, 0.6);
}

.login-form .ant-select-arrow {
  color: rgba(255, 255, 255, 0.6);
}

.login-form .ant-select-selection-placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.login-form .ant-select-selection-item {
  color: white;
}

/* 下拉菜单样式 */
.ant-select-dropdown {
  background: #2a3a4a !important;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.ant-select-item {
  color: rgba(255, 255, 255, 0.8) !important;
}

.ant-select-item:hover {
  background: rgba(240, 86, 84, 0.2) !important;
}

.ant-select-item-option-selected {
  background: rgba(240, 86, 84, 0.3) !important;
  color: white !important;
}

/* 额外说明文字 */
.login-form .ant-form-item-extra {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  margin-top: 4px;
}

/* 提交按钮 */
.submit-button {
  width: 100%;
  height: 48px;
  background: #F05654 !important;
  border: none !important;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  margin-top: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(240, 86, 84, 0.3);
}

.submit-button:hover {
  background: #FF6B69 !important;
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(240, 86, 84, 0.4);
}

.submit-button:active {
  transform: translateY(0);
}

/* 登录页脚 */
.login-footer {
  text-align: center;
  margin-top: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.link-button {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  transition: color 0.3s ease;
  text-decoration: none;
}

.link-button:hover {
  color: #F05654;
}

.link-button:focus {
  outline: 2px solid #F05654;
  outline-offset: 2px;
  border-radius: 4px;
}

.footer-divider {
  color: rgba(255, 255, 255, 0.3);
}

/* 版权信息 */
.copyright-info {
  text-align: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 1280px) {
  .login-container {
    padding: 20px;
  }
  
  .login-wrapper {
    max-width: 1000px;
  }
}

@media (max-width: 1024px) {
  .login-wrapper {
    max-width: 900px;
    min-height: 500px;
  }
  
  .login-left-panel {
    padding: 40px 30px;
  }
  
  .login-right-panel {
    padding: 40px 30px;
  }
}

@media (max-width: 900px) {
  .login-wrapper {
    flex-direction: column;
    max-width: 500px;
  }
  
  .login-left-panel {
    flex: none;
    min-height: 300px;
    padding: 30px 20px;
  }
  
  .login-right-panel {
    flex: none;
    padding: 30px 20px;
  }
  
  .left-panel-content {
    max-width: 400px;
  }
  
  .system-title {
    font-size: 36px !important;
  }
  
  .system-subtitle {
    font-size: 24px !important;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }
  
  .login-wrapper {
    border-radius: 15px;
    min-height: auto;
  }
  
  .login-left-panel {
    min-height: 250px;
    padding: 20px 15px;
  }
  
  .login-right-panel {
    padding: 20px 15px;
  }
  
  .login-form-container {
    max-width: 100%;
  }
  
  .form-header h1 {
    font-size: 28px;
  }
  
  .form-header h2 {
    font-size: 20px;
  }
  
  .login-form .ant-input-affix-wrapper,
  .login-form .ant-select-selector {
    height: 40px;
  }
  
  .submit-button {
    height: 44px;
  }
  
  .system-title {
    font-size: 28px !important;
  }
  
  .system-subtitle {
    font-size: 20px !important;
  }
  
  .feature-pills {
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .feature-pill {
    padding: 8px 16px;
    font-size: 14px;
  }
}

/* 加载状态 */
.submit-button.ant-btn-loading {
  opacity: 0.8;
}

/* 错误状态 */
.login-form .ant-form-item-has-error .ant-input-affix-wrapper,
.login-form .ant-form-item-has-error .ant-select-selector {
  border-color: #ff6b6b !important;
}

.login-form .ant-form-item-explain-error {
  color: #ff6b6b;
  font-size: 12px;
  margin-top: 4px;
}

/* 密码可见性按钮 */
.ant-input-password-icon {
  color: rgba(255, 255, 255, 0.6) !important;
}

.ant-input-password-icon:hover {
  color: rgba(255, 255, 255, 0.8) !important;
}