import React, { useState, useEffect, useRef } from 'react'
import { 
  <PERSON>, 
  Typo<PERSON>, 
  Button, 
  Space, 
  Upload, 
 
  Row, 
  Col,
  Collapse,
  Tag,
  Divider,
  Modal,
  Image,
  Progress,
  Spin,
  Alert,
  Input,
  List,
  Badge,
  App
} from 'antd'
import { 
  UploadOutlined, 
  RobotOutlined, 
  InfoCircleOutlined,
  ExperimentOutlined,
  ZoomInOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import localAIService from '../services/api/localAI'
import testAIAnalysis from '../utils/testAIAnalysis'
import { aiAnalysisAPI, AIAnalysisRecord } from '../services/api/aiAnalysisAPI'
import { uploadAPI } from '../services/api/uploadAPI'
import RealTimeAnalysisResult, { RealTimeAnalysisResultRef } from '../components/RealTimeAnalysisResult'
import AnalysisHistory from '../components/AnalysisHistory'
import { filterAnalysisResult } from '../utils/analysisResultFilter'
import { getAnalysisImageURL } from '../utils/imageUtils'

const { Title, Text, Paragraph } = Typography
const { TextArea } = Input

// 菌群数据
const bacteriaData = [
  {
    id: 'lactobacillus',
    name: '乳杆菌',
    englishName: 'Lactobacillus',
    image: getAnalysisImageURL('/images/svg/lactobacillus-bacteria.svg'),
    color: '#52c41a',
    description: '乳杆菌是酸奶发酵的主要菌种之一，能够将乳糖转化为乳酸，赋予酸奶独特的酸味。',
    characteristics: [
      '杆状细菌，长度2-10μm',
      '革兰氏阳性菌',
      '厌氧或兼性厌氧',
      '产生乳酸、醋酸等有机酸'
    ],
    functions: [
      '促进蛋白质消化',
      '增强免疫力',
      '维持肠道健康',
      '抑制有害菌生长'
    ]
  },
  {
    id: 'bifidobacterium',
    name: '双歧杆菌',
    englishName: 'Bifidobacterium',
    image: getAnalysisImageURL('/images/svg/bifidobacterium-bacteria.svg'),
    color: '#1890ff',
    description: '双歧杆菌是人体肠道中的有益菌群，在酸奶中起到调节肠道菌群平衡的重要作用。',
    characteristics: [
      'Y型或V型分叉形态',
      '革兰氏阳性菌',
      '严格厌氧菌',
      '耐酸性强'
    ],
    functions: [
      '改善肠道环境',
      '合成维生素B族',
      '降低胆固醇',
      '抗癌抗衰老'
    ]
  },
  {
    id: 'streptococcus',
    name: '链球菌',
    englishName: 'Streptococcus',
    image: getAnalysisImageURL('/images/svg/streptococcus-bacteria.svg'),
    color: '#fa8c16',
    description: '嗜热链球菌是酸奶发酵的经典菌种，与乳杆菌协同作用，快速发酵产酸。',
    characteristics: [
      '球形细菌，链状排列',
      '革兰氏阳性菌',
      '嗜热性（45°C最适）',
      '快速产酸能力强'
    ],
    functions: [
      '快速启动发酵',
      '产生风味物质',
      '改善酸奶质地',
      '与乳杆菌协同作用'
    ]
  }
]

// 本地分析结果接口（用于实时显示）
interface LocalAnalysisResult {
  id: string
  fileName: string
  imageUrl: string
  result: string
  status: 'analyzing' | 'completed' | 'error'
  timestamp: Date
  processingTime?: number
}

const Analysis: React.FC = () => {
  const { message } = App.useApp()
  const [previewVisible, setPreviewVisible] = useState(false)
  const [previewImage, setPreviewImage] = useState('')
  const [previewTitle, setPreviewTitle] = useState('')
  
  // AI分析相关状态
  const [localResults, setLocalResults] = useState<LocalAnalysisResult[]>([])
  const [savedAnalyses, setSavedAnalyses] = useState<AIAnalysisRecord[]>([])
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [aiServiceStatus, setAiServiceStatus] = useState<'checking' | 'online' | 'offline'>('checking')
  const [customPrompt, setCustomPrompt] = useState('')
  const [currentProgress, setCurrentProgress] = useState('')

  // 上传的图片状态
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string>('')
  const [analysisModalVisible, setAnalysisModalVisible] = useState(false)
  const [selectedResult, setSelectedResult] = useState<LocalAnalysisResult | AIAnalysisRecord | null>(null)
  const [loading, setLoading] = useState(false)

  // 实时分析状态
  const [isRealTimeAnalyzing, setIsRealTimeAnalyzing] = useState(false)
  const [currentAnalysisContent, setCurrentAnalysisContent] = useState('')
  const realTimeAnalysisRef = useRef<RealTimeAnalysisResultRef>(null)
  const analysisAbortController = useRef<AbortController | null>(null)

  // 系统提示词状态
  const [selectedSystemPrompt, setSelectedSystemPrompt] = useState<string>('standard')

  // 系统提示词选项
  const systemPromptOptions = [
    {
      key: 'standard',
      title: '标准质控分析',
      description: '适用于日常生产质控，全面评估菌群状态',
      prompt: `作为专业的食品微生物学家和酸奶质控专家，请对这张酸奶菌群显微镜图像进行全面的质量控制分析。

**分析要求**：
1. **菌群识别与计数**：
   - 识别主要菌群类型（乳杆菌、链球菌、双歧杆菌等）
   - 评估各菌群的相对数量和分布密度
   - 计算菌群比例是否符合酸奶标准

2. **形态学评估**：
   - 描述各菌群的形态特征（大小、形状、排列方式）
   - 评估细胞完整性和活性状态
   - 识别异常形态或退化细胞

3. **质量控制评价**：
   - 菌群活性等级（优秀/良好/一般/较差）
   - 污染风险评估（杂菌、霉菌、酵母菌检测）
   - 整体质量评分（1-10分）

4. **食品安全评估**：
   - 病原菌污染风险
   - 腐败菌检测
   - 出品建议（可出品/需处理/禁止出品）

5. **改进建议**：
   - 发酵工艺优化建议
   - 菌种配比调整建议
   - 生产环境改善建议

请使用专业术语，但保持描述清晰易懂，适合咖啡厅质控人员理解。`
    },
    {
      key: 'research',
      title: '研发创新分析',
      description: '适用于新品研发，深度分析菌群特性',
      prompt: `作为酸奶研发专家和微生物技术顾问，请对这张显微镜图像进行深度的研发导向分析。

**研发分析重点**：
1. **菌群生态分析**：
   - 主要菌种的生长特性和代谢活性
   - 菌群间的协同作用和竞争关系
   - 菌群平衡度对风味形成的影响

2. **发酵特性评估**：
   - 产酸能力评估（乳酸、醋酸产生）
   - 蛋白质分解活性
   - 风味化合物生成潜力

3. **创新潜力分析**：
   - 独特菌株识别和特性
   - 功能性成分产生能力
   - 益生菌活性评估

4. **配方优化建议**：
   - 菌种配比优化方案
   - 发酵条件调整建议
   - 营养强化添加建议

5. **市场差异化分析**：
   - 产品独特性评估
   - 健康功能卖点挖掘
   - 品牌故事素材提供

6. **技术改进方向**：
   - 发酵工艺创新点
   - 质量稳定性提升方案
   - 成本优化可能性

请从创新研发角度提供深度洞察，帮助开发具有市场竞争力的新产品。`
    },
    {
      key: 'safety',
      title: '食品安全检测',
      description: '专注食品安全，重点检测污染风险',
      prompt: `作为食品安全专家和微生物检验师，请对这张酸奶显微镜图像进行严格的食品安全评估。

**安全检测重点**：
1. **病原菌检测**：
   - 大肠杆菌群检测
   - 沙门氏菌风险评估
   - 金黄色葡萄球菌检测
   - 李斯特菌风险评估

2. **腐败菌识别**：
   - 假单胞菌属检测
   - 肠杆菌科细菌
   - 腐败性芽孢杆菌
   - 其他腐败指示菌

3. **霉菌酵母检测**：
   - 霉菌孢子和菌丝
   - 酵母菌细胞
   - 真菌污染程度评估

4. **卫生指标评估**：
   - 菌落总数估算
   - 大肠菌群指示
   - 卫生质量等级

5. **污染源分析**：
   - 原料污染可能性
   - 生产环境污染
   - 包装污染风险
   - 储存条件影响

6. **安全等级判定**：
   - 食品安全等级（A/B/C/D级）
   - 出品安全性评估
   - 召回风险评估
   - 紧急处理建议

7. **预防措施建议**：
   - HACCP关键控制点
   - 卫生操作规程改进
   - 监测频率建议
   - 培训重点内容

请严格按照食品安全标准进行评估，确保消费者健康安全。`
    }
  ]

  // 检查AI服务状态和加载历史记录
  useEffect(() => {
    checkAIServiceStatus()
    loadAnalysisHistory()

    // 在开发环境中暴露测试函数到全局
    if (process.env.NODE_ENV === 'development') {
      (window as any).testAIAnalysis = testAIAnalysis
      console.log('🧪 AI分析测试函数已暴露到全局: testAIAnalysis()')
    }
  }, [])

  // 清理预览URL
  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl)
      }
    }
  }, [previewUrl])

  const checkAIServiceStatus = async () => {
    setAiServiceStatus('checking')
    try {
      const response = await aiAnalysisAPI.checkHealth()
      setAiServiceStatus(response.data.data.status === 'online' ? 'online' : 'offline')
    } catch (error) {
      console.error('AI service health check failed:', error)
      // 即使健康检查失败，也设为离线状态而不是阻塞界面
      setAiServiceStatus('offline')
    }
  }

  // 加载分析历史记录
  const loadAnalysisHistory = async () => {
    setLoading(true)
    try {
      const response = await aiAnalysisAPI.getAnalyses({ limit: 50 })
      setSavedAnalyses(response.data.data.analyses || [])
    } catch (error: any) {
      console.error('Failed to load analysis history:', error)
      // 如果是500错误，可能是表不存在或权限问题，先设为空数组
      setSavedAnalyses([])
      // 只在非500错误情况下显示错误
      if (error?.response?.status !== 500) {
        message?.error('加载分析历史失败')
      }
    } finally {
      setLoading(false)
    }
  }

  // 处理文件上传（只上传，不分析）
  const handleUpload = async (info: any) => {
    const { file } = info

    if (file.status === 'error') {
      message?.error(`${file.name} 文件上传失败`)
      return
    }

    // 当beforeUpload返回false时，file.originFileObj就是原始文件
    // 或者直接使用file本身（如果它是File对象）
    const fileToUpload = file.originFileObj || file

    if (fileToUpload && fileToUpload instanceof File) {
      // 清理之前的预览URL
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl)
      }

      // 设置上传的文件和预览URL
      setUploadedFile(fileToUpload)
      const newPreviewUrl = URL.createObjectURL(fileToUpload)
      setPreviewUrl(newPreviewUrl)

      message?.success(`${fileToUpload.name} 上传成功，请点击"开始分析"按钮进行AI分析`)
    } else {
      console.error('无效的文件对象:', file)
      message?.error('文件格式不正确，请选择图片文件')
    }
  }

  // 开始分析按钮处理
  const handleStartAnalysis = async () => {
    if (!uploadedFile) {
      message?.error('请先上传图片')
      return
    }

    if (aiServiceStatus !== 'online') {
      message?.error('AI分析服务离线，请检查服务连接')
      return
    }

    await analyzeImageDirectly(uploadedFile)
  }

  // 直接调用 LM Studio 分析图像
  const analyzeImageDirectly = async (file: File) => {
    if (aiServiceStatus !== 'online') {
      message?.error('AI分析服务离线，请检查服务连接')
      return
    }

    if (isRealTimeAnalyzing) {
      message?.warning('正在分析中，请等待当前分析完成')
      return
    }

    setIsRealTimeAnalyzing(true)
    setCurrentAnalysisContent('')

    // 重置实时分析组件
    if (realTimeAnalysisRef.current) {
      realTimeAnalysisRef.current.resetAnalysis()
    }

    const startTime = Date.now()

    try {
      // 将文件转换为base64
      const base64 = await fileToBase64(file)

      // 创建中止控制器
      analysisAbortController.current = new AbortController()

      // 构建分析请求
      const selectedOption = systemPromptOptions.find(option => option.key === selectedSystemPrompt)
      const systemPrompt = selectedOption?.prompt || systemPromptOptions[0].prompt

      const finalPrompt = customPrompt || systemPrompt

      const request = {
        model: 'google/gemma-3-27b',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: finalPrompt
              },
              {
                type: 'image_url',
                image_url: {
                  url: base64.startsWith('data:') ? base64 : `data:image/jpeg;base64,${base64}`
                }
              }
            ]
          }
        ],
        stream: true,
        max_tokens: 2000,
        temperature: 0.7
      }

      // 调用 LM Studio API
      const response = await fetch('http://*************:1234/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
        signal: analysisAbortController.current.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      const decoder = new TextDecoder()
      let fullContent = ''

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          const trimmedLine = line.trim()
          if (trimmedLine.startsWith('data: ')) {
            const data = trimmedLine.slice(6).trim()

            if (data === '[DONE]') {
              // 分析完成
              const endTime = Date.now()
              const processingTime = endTime - startTime

              if (realTimeAnalysisRef.current) {
                realTimeAnalysisRef.current.appendContent('', 'complete')
                realTimeAnalysisRef.current.setStatus('completed')
              }

              // 保存分析结果到历史记录
              const newResult: LocalAnalysisResult = {
                id: Date.now().toString(),
                fileName: file.name,
                imageUrl: URL.createObjectURL(file),
                result: fullContent,
                status: 'completed',
                timestamp: new Date(),
                processingTime
              }

              setLocalResults(prev => [newResult, ...prev])

              message?.success(`分析完成！耗时 ${(processingTime / 1000).toFixed(1)} 秒`)
              return fullContent
            }

            if (data) {
              try {
                const parsed = JSON.parse(data)
                const content = parsed.choices[0]?.delta?.content
                if (content) {
                  fullContent += content
                  setCurrentAnalysisContent(fullContent)

                  // 更新实时分析组件
                  if (realTimeAnalysisRef.current) {
                    realTimeAnalysisRef.current.appendContent(content, 'content')
                  }
                }
              } catch (e) {
                console.warn('解析流式数据失败:', e)
              }
            }
          }
        }
      }
    } catch (error: any) {
      console.error('分析失败:', error)

      if (error.name === 'AbortError') {
        message?.info('分析已停止')
      } else {
        message?.error(`分析失败: ${error.message}`)
      }

      if (realTimeAnalysisRef.current) {
        realTimeAnalysisRef.current.setStatus('error')
      }
    } finally {
      setIsRealTimeAnalyzing(false)
      analysisAbortController.current = null
    }
  }

  // 停止分析
  const handleStopAnalysis = () => {
    if (analysisAbortController.current) {
      analysisAbortController.current.abort()
    }
    setIsRealTimeAnalyzing(false)
    message?.info('已停止分析')
  }

  // 分析图像
  const analyzeImage = async (file: File) => {
    if (aiServiceStatus !== 'online') {
      message?.error('AI分析服务离线，请检查服务连接')
      return
    }

    const resultId = Date.now().toString()
    let serverImageUrl = ''
    
    // 先创建临时的blob URL用于立即显示
    const tempImageUrl = URL.createObjectURL(file)
    
    // 添加新的分析任务
    const newResult: LocalAnalysisResult = {
      id: resultId,
      fileName: file.name,
      imageUrl: tempImageUrl, // 临时使用blob URL
      result: '',
      status: 'analyzing',
      timestamp: new Date()
    }
    
    setLocalResults(prev => [newResult, ...prev])
    setIsAnalyzing(true)
    setCurrentProgress('正在上传图片...')
    
    const startTime = Date.now()
    
    try {
      // 1. 先上传图片到服务器
      const uploadResponse = await uploadAPI.uploadAIImage(file)
      serverImageUrl = uploadResponse.data.imageUrl
      
      // 更新本地结果中的图片URL为服务器URL
      setLocalResults(prev => 
        prev.map(item => 
          item.id === resultId 
            ? { ...item, imageUrl: uploadAPI.getAIImageUrl(serverImageUrl) }
            : item
        )
      )
      
      // 清理临时blob URL
      URL.revokeObjectURL(tempImageUrl)
      
      setCurrentProgress('正在分析图片...')
      
      // 2. 将文件转换为base64用于AI分析
      const base64 = await fileToBase64(file)
      
      // 3. 调用本地AI服务进行分析
      const result = await localAIService.analyzeYogurtBacteria(
        base64,
        customPrompt || undefined,
        (progressContent) => {
          // 过滤分析结果，移除不相关内容
          const filteredContent = filterAnalysisResult(progressContent)
          setCurrentProgress(filteredContent)
          // 实时更新结果
          setLocalResults(prev =>
            prev.map(item =>
              item.id === resultId
                ? { ...item, result: filteredContent }
                : item
            )
          )
        }
      )
      
      const processingTime = Date.now() - startTime

      // 过滤最终结果
      const filteredResult = filterAnalysisResult(result)

      // 4. 更新最终结果
      setLocalResults(prev =>
        prev.map(item =>
          item.id === resultId
            ? {
                ...item,
                result: filteredResult,
                status: 'completed',
                processingTime
              }
            : item
        )
      )
      
      // 5. 保存到后端数据库（使用服务器图片URL）
      try {
        await aiAnalysisAPI.saveAnalysisRecord({
          fileName: file.name,
          imageUrl: uploadAPI.getAIImageUrl(serverImageUrl),
          analysisResult: filteredResult,
          processingTime,
          customPrompt: customPrompt || undefined,
          status: 'completed'
        })
        
        // 重新加载历史记录
        await loadAnalysisHistory()
      } catch (error) {
        console.error('Failed to save analysis record:', error)
        message?.warning('分析完成但保存记录失败')
      }
      
      message?.success(`${file.name} 分析完成！`)
      
    } catch (error) {
      console.error('Analysis failed:', error)
      
      // 清理临时blob URL
      URL.revokeObjectURL(tempImageUrl)
      
      setLocalResults(prev => 
        prev.map(item => 
          item.id === resultId 
            ? { ...item, status: 'error', result: '分析失败，请重试' }
            : item
        )
      )
      
      message?.error(`${file.name} 分析失败`)
    } finally {
      setIsAnalyzing(false)
      setCurrentProgress('')
    }
  }

  // 文件转base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        resolve(result)
      }
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  }

  // 查看分析结果
  const viewAnalysisResult = (result: LocalAnalysisResult | AIAnalysisRecord) => {
    setSelectedResult(result)
    setAnalysisModalVisible(true)
  }

  // 删除本地分析结果
  const deleteLocalResult = (id: string) => {
    setLocalResults(prev => prev.filter(item => item.id !== id))
    message?.success('本地分析结果已删除')
  }

  // 删除保存的分析记录
  const deleteSavedAnalysis = async (id: string) => {
    try {
      await aiAnalysisAPI.deleteAnalysis(id)
      setSavedAnalyses(prev => prev.filter(item => item.id !== id))
      message?.success('分析记录已删除')
    } catch (error) {
      console.error('Failed to delete analysis:', error)
      message?.error('删除分析记录失败')
    }
  }

  // 重新分析
  const reanalyzeImage = async (result: LocalAnalysisResult | AIAnalysisRecord) => {
    try {
      const imageUrl = 'imageUrl' in result ? result.imageUrl : ''
      if (!imageUrl) {
        message?.error('无法获取图片URL')
        return
      }

      const response = await fetch(imageUrl)
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status}`)
      }
      
      const blob = await response.blob()
      const fileName = 'fileName' in result ? result.fileName : '重新分析.jpg'
      const file = new File([blob], fileName, { type: blob.type || 'image/jpeg' })
      await analyzeImage(file)
    } catch (error) {
      console.error('Failed to reanalyze image:', error)
      message?.error('重新分析失败，无法获取原始图片')
    }
  }

  // 合并本地结果和保存的分析记录
  const allResults = [
    ...localResults.map(r => ({ ...r, source: 'local' as const })),
    ...savedAnalyses.map(r => ({ 
      ...r, 
      result: r.analysisResult, 
      timestamp: new Date(r.createdAt),
      source: 'saved' as const
    }))
  ].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())

  const handlePreview = (image: string, title: string) => {
    setPreviewImage(image)
    setPreviewTitle(title)
    setPreviewVisible(true)
  }

  return (
    <div>
      {/* 页面头部 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <ExperimentOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          AI 质量分析
        </Title>
        <Text type="secondary">酸奶菌群显微镜图像智能分析平台</Text>
      </div>

      {/* 酸奶菌群简介 */}
      <Card 
        title={
          <Space>
            <InfoCircleOutlined style={{ color: '#1890ff' }} />
            <span>酸奶菌群简介</span>
          </Space>
        }
        style={{ marginBottom: 24 }}
      >
        <Paragraph>
          酸奶是通过特定的乳酸菌发酵牛奶制成的乳制品。主要的发酵菌群包括乳杆菌、双歧杆菌和链球菌等。
          这些有益菌群不仅赋予酸奶独特的风味和质地，还为人体健康带来诸多益处。
        </Paragraph>

        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          {bacteriaData.map((bacteria) => (
            <Col xs={24} md={12} lg={8} key={bacteria.id}>
              <Card
                hoverable
                style={{ height: '100%' }}
                cover={
                  <div 
                    style={{ 
                      height: 200, 
                      background: '#f5f5f5',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      cursor: 'pointer'
                    }}
                    onClick={() => handlePreview(bacteria.image, bacteria.name)}
                  >
                    <img 
                      src={bacteria.image} 
                      alt={bacteria.name}
                      style={{ 
                        maxWidth: '80%', 
                        maxHeight: '80%',
                        objectFit: 'contain'
                      }}
                    />
                    <div style={{
                      position: 'absolute',
                      top: 8,
                      right: 8,
                      background: 'rgba(0,0,0,0.5)',
                      color: 'white',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      fontSize: '12px'
                    }}>
                      <ZoomInOutlined /> 点击放大
                    </div>
                  </div>
                }
                actions={[
                  <Button 
                    type="text" 
                    icon={<ZoomInOutlined />}
                    onClick={() => handlePreview(bacteria.image, bacteria.name)}
                  >
                    查看大图
                  </Button>
                ]}
              >
                <Card.Meta
                  title={
                    <Space>
                      <Tag color={bacteria.color}>{bacteria.name}</Tag>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {bacteria.englishName}
                      </Text>
                    </Space>
                  }
                  description={
                    <div>
                      <Paragraph ellipsis={{ rows: 2 }} style={{ marginBottom: 16 }}>
                        {bacteria.description}
                      </Paragraph>
                      
                      <Collapse 
                        size="small" 
                        ghost
                        items={[
                          {
                            key: 'characteristics',
                            label: '形态特征',
                            children: (
                              <ul style={{ margin: 0, paddingLeft: 16 }}>
                                {bacteria.characteristics.map((char, index) => (
                                  <li key={index} style={{ fontSize: '12px', marginBottom: 4 }}>
                                    {char}
                                  </li>
                                ))}
                              </ul>
                            )
                          },
                          {
                            key: 'functions',
                            label: '主要功能',
                            children: (
                              <ul style={{ margin: 0, paddingLeft: 16 }}>
                                {bacteria.functions.map((func, index) => (
                                  <li key={index} style={{ fontSize: '12px', marginBottom: 4 }}>
                                    {func}
                                  </li>
                                ))}
                              </ul>
                            )
                          }
                        ]}
                      />
                    </div>
                  }
                />
              </Card>
            </Col>
          ))}
        </Row>

        {/* 混合菌群和发酵过程 */}
        <Divider orientation="left" style={{ marginTop: 32 }}>
          <Space>
            <ExperimentOutlined />
            <span>发酵过程与混合菌群</span>
          </Space>
        </Divider>

        <Row gutter={24} style={{ marginTop: 24 }}>
          <Col xs={24} lg={12}>
            <Card
              title="混合酸奶菌群"
              hoverable
              cover={
                <div 
                  style={{ 
                    height: 250, 
                    background: '#f5f5f5',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    cursor: 'pointer'
                  }}
                  onClick={() => handlePreview(getAnalysisImageURL('/images/svg/yogurt-bacteria-mixed.svg'), '混合酸奶菌群')}
                >
                  <img 
                    src={getAnalysisImageURL('/images/svg/yogurt-bacteria-mixed.svg')} 
                    alt="混合酸奶菌群"
                    style={{ 
                      maxWidth: '90%', 
                      maxHeight: '90%',
                      objectFit: 'contain'
                    }}
                  />
                </div>
              }
            >
              <Paragraph>
                在酸奶发酵过程中，多种有益菌群协同作用，形成复杂的微生态系统。
                不同菌群之间相互协调，共同完成乳糖发酵、蛋白质分解等生化过程。
              </Paragraph>
            </Card>
          </Col>
          
          <Col xs={24} lg={12}>
            <Card
              title="酸奶发酵过程"
              hoverable
              cover={
                <div 
                  style={{ 
                    height: 250, 
                    background: '#f5f5f5',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    cursor: 'pointer'
                  }}
                  onClick={() => handlePreview(getAnalysisImageURL('/images/svg/yogurt-fermentation-process.svg'), '酸奶发酵过程')}
                >
                  <img 
                    src={getAnalysisImageURL('/images/svg/yogurt-fermentation-process.svg')} 
                    alt="酸奶发酵过程"
                    style={{ 
                      maxWidth: '90%', 
                      maxHeight: '90%',
                      objectFit: 'contain'
                    }}
                  />
                </div>
              }
            >
              <Paragraph>
                酸奶发酵是一个复杂的生化过程，包括菌种接种、温度控制、pH调节、
                发酵监控等多个环节。了解发酵过程有助于优化生产工艺。
              </Paragraph>
            </Card>
          </Col>
        </Row>
      </Card>

      {/* AI服务状态 */}
      <Card style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Space>
            <Badge 
              status={aiServiceStatus === 'online' ? 'success' : aiServiceStatus === 'offline' ? 'error' : 'processing'}
              text={
                aiServiceStatus === 'online' ? 'AI分析服务在线' :
                aiServiceStatus === 'offline' ? 'AI分析服务离线' : '检查服务状态中...'
              }
            />
            {aiServiceStatus === 'online' && (
              <Tag color="green">Gemma-3 模型就绪</Tag>
            )}
          </Space>
          <Button 
            icon={<ReloadOutlined />}
            onClick={checkAIServiceStatus}
            loading={aiServiceStatus === 'checking'}
          >
            刷新状态
          </Button>
        </div>
        
        {aiServiceStatus === 'offline' && (
          <Alert
            message="AI分析服务离线"
            description="请确保本地AI服务运行在 http://*************:1234"
            type="warning"
            style={{ marginTop: 16 }}
          />
        )}
      </Card>

      {/* AI分析功能区 */}
      <Card 
        title={
          <Space>
            <RobotOutlined style={{ color: '#52c41a' }} />
            <span>AI 智能分析</span>
            {isAnalyzing && <Spin size="small" />}
          </Space>
        }
        style={{ marginBottom: 24 }}
      >
        {/* 系统提示词选择 */}
        <Card title="分析模式选择" size="small" style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]}>
            {systemPromptOptions.map((option) => (
              <Col xs={24} sm={8} key={option.key}>
                <Card
                  size="small"
                  hoverable
                  className={selectedSystemPrompt === option.key ? 'selected-prompt' : ''}
                  onClick={() => setSelectedSystemPrompt(option.key)}
                  style={{
                    border: selectedSystemPrompt === option.key ? '2px solid #1890ff' : '1px solid #d9d9d9',
                    backgroundColor: selectedSystemPrompt === option.key ? '#f0f9ff' : '#ffffff',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease'
                  }}
                >
                  <Space direction="vertical" size={4} style={{ width: '100%' }}>
                    <Text strong style={{
                      color: selectedSystemPrompt === option.key ? '#1890ff' : '#262626',
                      fontSize: '14px'
                    }}>
                      {option.title}
                    </Text>
                    <Text type="secondary" style={{ fontSize: '12px', lineHeight: '1.4' }}>
                      {option.description}
                    </Text>
                    {selectedSystemPrompt === option.key && (
                      <div style={{ textAlign: 'center', marginTop: 8 }}>
                        <CheckCircleOutlined style={{ color: '#1890ff', fontSize: '16px' }} />
                      </div>
                    )}
                  </Space>
                </Card>
              </Col>
            ))}
          </Row>

          {/* 提示词预览 */}
          {selectedSystemPrompt && (
            <div style={{ marginTop: 16 }}>
              <Collapse 
                size="small"
                items={[
                  {
                    key: 'preview',
                    label: (
                      <Space>
                        <EyeOutlined />
                        <Text type="secondary">预览选中的分析模板</Text>
                      </Space>
                    ),
                    children: (
                      <div style={{
                        background: '#fafafa',
                        padding: '12px',
                        borderRadius: '6px',
                        fontSize: '12px',
                        lineHeight: '1.6',
                        maxHeight: '200px',
                        overflowY: 'auto',
                        whiteSpace: 'pre-wrap'
                      }}>
                        {systemPromptOptions.find(opt => opt.key === selectedSystemPrompt)?.prompt}
                      </div>
                    )
                  }
                ]}
              />
            </div>
          )}
        </Card>

        {/* 自定义提示词 */}
        <Card title="自定义分析提示词（可选）" size="small" style={{ marginBottom: 16 }}>
          <Alert
            message="提示"
            description="如果填写自定义提示词，将覆盖上方选择的系统模板。留空则使用选中的分析模式。"
            type="info"
            showIcon
            style={{ marginBottom: 12, fontSize: '12px' }}
          />
          <TextArea
            placeholder="可选：输入自定义分析要求，留空则使用上方选择的分析模式"
            value={customPrompt}
            onChange={(e) => setCustomPrompt(e.target.value)}
            rows={3}
            disabled={isAnalyzing}
          />
        </Card>
        
        <Row gutter={24}>
          <Col xs={24} lg={16}>
            <Upload.Dragger
              name="images"
              multiple={false}
              accept="image/*"
              onChange={handleUpload}
              disabled={isAnalyzing || aiServiceStatus !== 'online'}
              showUploadList={false}
              beforeUpload={() => false}
              style={{ padding: uploadedFile ? 20 : 40 }}
            >
              {uploadedFile && previewUrl ? (
                // 显示上传的图片预览
                <div style={{ textAlign: 'center' }}>
                  <img
                    src={previewUrl}
                    alt={uploadedFile.name}
                    style={{
                      maxWidth: '100%',
                      maxHeight: '200px',
                      objectFit: 'contain',
                      marginBottom: 8
                    }}
                  />
                  <p style={{ margin: 0, fontSize: '14px', color: '#666' }}>
                    {uploadedFile.name}
                  </p>
                  <p style={{ margin: '4px 0 0 0', fontSize: '12px', color: '#999' }}>
                    点击重新选择图片
                  </p>
                </div>
              ) : (
                // 显示上传提示
                <>
                  <p className="ant-upload-drag-icon">
                    <UploadOutlined style={{
                      fontSize: 48,
                      color: aiServiceStatus === 'online' ? '#1890ff' : '#d9d9d9'
                    }} />
                  </p>
                  <p className="ant-upload-text">
                    上传显微镜图像进行AI分析
                  </p>
                  <p className="ant-upload-hint">
                    支持 JPG、PNG、TIFF 格式，使用 Gemma-3 多模态模型分析
                  </p>
                </>
              )}
            </Upload.Dragger>

            {/* 开始分析按钮 */}
            {uploadedFile && (
              <div style={{ marginTop: 16, textAlign: 'center' }}>
                <Button
                  type="primary"
                  size="large"
                  icon={<RobotOutlined />}
                  onClick={handleStartAnalysis}
                  disabled={isRealTimeAnalyzing || aiServiceStatus !== 'online'}
                  loading={isRealTimeAnalyzing}
                  style={{ minWidth: 120 }}
                >
                  {isRealTimeAnalyzing ? '分析中...' : '开始分析'}
                </Button>
              </div>
            )}
            

          </Col>
          
          <Col xs={24} lg={8}>
            <Card title="分析功能" size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Tag color="blue">菌群识别</Tag>
                  <Text type="secondary">自动识别不同菌群类型</Text>
                </div>
                <div>
                  <Tag color="green">形态分析</Tag>
                  <Text type="secondary">分析菌群形态特征</Text>
                </div>
                <div>
                  <Tag color="orange">污染检测</Tag>
                  <Text type="secondary">检测有害菌群污染</Text>
                </div>
                <div>
                  <Tag color="purple">质量评估</Tag>
                  <Text type="secondary">综合质量评分</Text>
                </div>
                <div>
                  <Tag color="red">异常预警</Tag>
                  <Text type="secondary">异常情况实时预警</Text>
                </div>
              </Space>
            </Card>
          </Col>
        </Row>
      </Card>

      {/* 实时分析结果组件 */}
      <RealTimeAnalysisResult
        ref={realTimeAnalysisRef}
        isAnalyzing={isRealTimeAnalyzing}
        onStopAnalysis={handleStopAnalysis}
      />

      {/* 分析历史组件 */}
      <AnalysisHistory
        localResults={localResults}
        savedAnalyses={savedAnalyses}
        onViewResult={viewAnalysisResult}
        onReanalyze={(result) => {
          // 重新分析时使用新的直接分析方法
          if ('fileName' in result && result.fileName) {
            // 这里需要重新上传文件，暂时提示用户
            message?.info('请重新上传图片进行分析')
          }
        }}
        onDelete={(result) => {
          // 根据结果类型调用不同的删除方法
          if ('timestamp' in result) {
            // 本地结果
            deleteLocalResult(result.id)
          } else {
            // 保存的分析记录
            deleteSavedAnalysis(result.id)
          }
        }}
      />

      {/* 图片预览Modal */}
      <Modal
        open={previewVisible}
        title={previewTitle}
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        width="80%"
        style={{ maxWidth: '1000px' }}
      >
        <div style={{ textAlign: 'center' }}>
          <img
            alt={previewTitle}
            style={{ width: '100%', maxHeight: '70vh', objectFit: 'contain' }}
            src={previewImage}
          />
        </div>
      </Modal>

      {/* 分析结果Modal */}
      <Modal
        open={analysisModalVisible}
        title={
          <Space>
            <RobotOutlined style={{ color: '#52c41a' }} />
            <span>AI分析结果</span>
            {selectedResult?.fileName && <Text type="secondary">- {selectedResult.fileName}</Text>}
          </Space>
        }
        onCancel={() => setAnalysisModalVisible(false)}
        width="90%"
        style={{ maxWidth: '1200px' }}
        footer={[
          <Button key="close" onClick={() => setAnalysisModalVisible(false)}>
            关闭
          </Button>
        ]}
      >
        {selectedResult && (
          <Row gutter={24}>
            <Col xs={24} lg={8}>
              <Card title="原始图像" size="small">
                <img 
                  src={'imageUrl' in selectedResult ? selectedResult.imageUrl : ''}
                  alt={'fileName' in selectedResult ? selectedResult.fileName : '分析图像'}
                  style={{ width: '100%', borderRadius: 4 }}
                />
                <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                  <div>文件名: {'fileName' in selectedResult ? selectedResult.fileName : '未知'}</div>
                  <div>分析时间: {
                    'timestamp' in selectedResult 
                      ? selectedResult.timestamp.toLocaleString() 
                      : 'createdAt' in selectedResult 
                        ? new Date(selectedResult.createdAt).toLocaleString()
                        : '未知'
                  }</div>
                  {selectedResult.processingTime && (
                    <div>处理耗时: {(selectedResult.processingTime / 1000).toFixed(1)}秒</div>
                  )}
                  {'userName' in selectedResult && selectedResult.userName && (
                    <div>分析者: {selectedResult.userName}</div>
                  )}
                </div>
              </Card>
            </Col>
            <Col xs={24} lg={16}>
              <Card title="分析结果" size="small">
                <div style={{ 
                  maxHeight: '500px', 
                  overflowY: 'auto',
                  padding: 16,
                  background: '#fafafa',
                  borderRadius: 4,
                  whiteSpace: 'pre-wrap',
                  fontSize: '14px',
                  lineHeight: 1.6
                }}>
                  {(() => {
                    const rawResult = 'result' in selectedResult ? selectedResult.result : 'analysisResult' in selectedResult ? selectedResult.analysisResult : '暂无分析结果'
                    return filterAnalysisResult(rawResult)
                  })()}
                </div>
              </Card>
            </Col>
          </Row>
        )}
      </Modal>
    </div>
  )
}

export default Analysis
