import React, { useState, useEffect } from 'react'
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Space,
  Row,
  Col,
  Statistic,
  Table,
  Select,
  DatePicker,
  Form,
  Modal,
  Input,
  message,
  Tabs,
  Tag,
  Dropdown,
  Menu,
  Divider,
  Progress,
  Alert,
  Spin,
  Empty
} from 'antd'
import {
  FileTextOutlined,
  DownloadOutlined,
  PlusOutlined,
  EyeOutlined,
  DeleteOutlined,
  ShareAltOutlined,
  RiseOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  CalendarOutlined,
  FilterOutlined,
  ExportOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import * as echarts from 'echarts'

const { Title, Text, Paragraph } = Typography
const { Option } = Select
const { RangePicker } = DatePicker
const { TextArea } = Input

// 模拟数据类型
interface Report {
  id: string
  title: string
  type: 'quality_trend' | 'batch_comparison' | 'recipe_performance' | 'custom'
  status: 'generating' | 'completed' | 'failed'
  createdAt: string
  createdBy: string
  description?: string
  dataRange?: string
  exportFormats: string[]
}

interface QualityTrendData {
  date: string
  averageScore: number
  batchCount: number
  passRate: number
}

interface BatchComparisonData {
  batchId: string
  batchNumber: string
  recipe: string
  qualityScore: number
  productionDate: string
  status: string
}

interface RecipePerformanceData {
  recipeId: string
  recipeName: string
  totalBatches: number
  averageScore: number
  successRate: number
  avgCost: number
}

const Reports: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview')
  const [reports, setReports] = useState<Report[]>([])
  const [loading, setLoading] = useState(false)
  const [generateModalVisible, setGenerateModalVisible] = useState(false)
  const [reportDetailsVisible, setReportDetailsVisible] = useState(false)
  const [selectedReport, setSelectedReport] = useState<Report | null>(null)
  const [form] = Form.useForm()

  // 模拟统计数据
  const [statistics, setStatistics] = useState({
    totalReports: 45,
    monthlyReports: 12,
    qualityReports: 28,
    trendAnalysis: 17
  })

  // 模拟质量趋势数据
  const [qualityTrendData, setQualityTrendData] = useState<QualityTrendData[]>([
    { date: '2025-01-01', averageScore: 8.2, batchCount: 15, passRate: 93.3 },
    { date: '2025-01-08', averageScore: 8.5, batchCount: 18, passRate: 94.4 },
    { date: '2025-01-15', averageScore: 8.1, batchCount: 12, passRate: 91.7 },
    { date: '2025-01-22', averageScore: 8.7, batchCount: 20, passRate: 95.0 },
    { date: '2025-01-29', averageScore: 8.3, batchCount: 16, passRate: 93.8 }
  ])

  // 模拟批次对比数据
  const [batchComparisonData, setBatchComparisonData] = useState<BatchComparisonData[]>([
    { batchId: '1', batchNumber: 'YG-2025-001', recipe: '经典酸奶配方', qualityScore: 8.5, productionDate: '2025-01-28', status: 'COMPLETED' },
    { batchId: '2', batchNumber: 'YG-2025-002', recipe: '希腊酸奶配方', qualityScore: 8.2, productionDate: '2025-01-27', status: 'COMPLETED' },
    { batchId: '3', batchNumber: 'YG-2025-003', recipe: '水果酸奶配方', qualityScore: 8.7, productionDate: '2025-01-26', status: 'COMPLETED' },
    { batchId: '4', batchNumber: 'YG-2025-004', recipe: '经典酸奶配方', qualityScore: 8.1, productionDate: '2025-01-25', status: 'COMPLETED' },
    { batchId: '5', batchNumber: 'YG-2025-005', recipe: '低脂酸奶配方', qualityScore: 8.4, productionDate: '2025-01-24', status: 'COMPLETED' }
  ])

  // 模拟配方性能数据
  const [recipePerformanceData, setRecipePerformanceData] = useState<RecipePerformanceData[]>([
    { recipeId: '1', recipeName: '经典酸奶配方', totalBatches: 25, averageScore: 8.3, successRate: 96.0, avgCost: 4.8 },
    { recipeId: '2', recipeName: '希腊酸奶配方', totalBatches: 18, averageScore: 8.1, successRate: 94.4, avgCost: 6.2 },
    { recipeId: '3', recipeName: '水果酸奶配方', totalBatches: 22, averageScore: 8.6, successRate: 95.5, avgCost: 5.9 },
    { recipeId: '4', recipeName: '低脂酸奶配方', totalBatches: 15, averageScore: 8.0, successRate: 93.3, avgCost: 4.5 },
    { recipeId: '5', recipeName: '有机酸奶配方', totalBatches: 12, averageScore: 8.8, successRate: 100.0, avgCost: 8.1 }
  ])

  // 模拟报告历史数据
  useEffect(() => {
    const mockReports: Report[] = [
      {
        id: '1',
        title: '2025年1月质量趋势分析报告',
        type: 'quality_trend',
        status: 'completed',
        createdAt: '2025-01-30T10:00:00Z',
        createdBy: '张三',
        description: '分析了1月份所有批次的质量趋势变化',
        dataRange: '2025-01-01 至 2025-01-31',
        exportFormats: ['PDF', 'Excel', 'Word']
      },
      {
        id: '2',
        title: '经典酸奶vs希腊酸奶批次对比',
        type: 'batch_comparison',
        status: 'completed',
        createdAt: '2025-01-29T14:30:00Z',
        createdBy: '李四',
        description: '对比分析两种配方的生产质量差异',
        dataRange: '最近30个批次',
        exportFormats: ['PDF', 'Excel']
      },
      {
        id: '3',
        title: '配方性能综合评估报告',
        type: 'recipe_performance',
        status: 'completed',
        createdAt: '2025-01-28T09:15:00Z',
        createdBy: '王五',
        description: '评估所有配方的综合性能表现',
        dataRange: '2024-12-01 至 2025-01-31',
        exportFormats: ['PDF', 'PowerPoint']
      },
      {
        id: '4',
        title: '自定义质量分析报告',
        type: 'custom',
        status: 'generating',
        createdAt: '2025-01-30T16:45:00Z',
        createdBy: '赵六',
        description: '基于特定筛选条件的自定义分析',
        dataRange: '用户自定义',
        exportFormats: ['PDF']
      }
    ]
    setReports(mockReports)
  }, [])

  // 生成报告
  const handleGenerateReport = async (values: any) => {
    setLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const newReport: Report = {
        id: Date.now().toString(),
        title: values.title,
        type: values.type,  
        status: 'completed',
        createdAt: new Date().toISOString(),
        createdBy: '当前用户',
        description: values.description,
        dataRange: values.dateRange ? 
          `${values.dateRange[0].format('YYYY-MM-DD')} 至 ${values.dateRange[1].format('YYYY-MM-DD')}` : 
          '全部数据',
        exportFormats: ['PDF', 'Excel']
      }
      
      setReports([newReport, ...reports])
      setGenerateModalVisible(false)
      form.resetFields()
      message.success('报告生成成功')
    } catch (error) {
      message.error('报告生成失败')
    } finally {
      setLoading(false)
    }
  }

  // 导出报告
  const handleExportReport = (report: Report, format: string) => {
    message.success(`正在导出${format}格式的报告: ${report.title}`)
  }

  // 删除报告
  const handleDeleteReport = (reportId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个报告吗？此操作不可恢复。',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        setReports(reports.filter(r => r.id !== reportId))
        message.success('报告删除成功')
      }
    })
  }

  // 报告列表表格列
  const reportColumns = [
    {
      title: '报告标题',
      dataIndex: 'title',
      key: 'title',
      render: (title: string, record: Report) => (
        <div>
          <Text strong>{title}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.description}
          </Text>
        </div>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: string) => {
        const typeConfig = {
          quality_trend: { color: 'blue', text: '质量趋势' },
          batch_comparison: { color: 'green', text: '批次对比' },
          recipe_performance: { color: 'orange', text: '配方性能' },
          custom: { color: 'purple', text: '自定义' }
        }[type] || { color: 'default', text: '未知' }
        
        return <Tag color={typeConfig.color}>{typeConfig.text}</Tag>
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const statusConfig = {
          generating: { color: 'processing', text: '生成中' },
          completed: { color: 'success', text: '已完成' },
          failed: { color: 'error', text: '失败' }
        }[status] || { color: 'default', text: '未知' }
        
        return <Tag color={statusConfig.color}>{statusConfig.text}</Tag>
      }
    },
    {
      title: '数据范围',
      dataIndex: 'dataRange',
      key: 'dataRange',
      width: 150
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm')
    },
    {
      title: '创建人',
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 100
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_: any, record: Report) => (
        <Space>
          <Button 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => {
              setSelectedReport(record)
              setReportDetailsVisible(true)
            }}
          >
            查看
          </Button>
          
          <Dropdown
            menu={{
              items: record.exportFormats.map(format => ({
                key: format,
                label: format,
                onClick: () => handleExportReport(record, format)
              }))
            }}
          >
            <Button size="small" icon={<DownloadOutlined />}>
              导出
            </Button>
          </Dropdown>
          
          <Button 
            size="small" 
            icon={<ShareAltOutlined />}
            onClick={() => message.info('分享功能开发中')}
          >
            分享
          </Button>
          
          <Button 
            size="small" 
            danger 
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteReport(record.id)}
          >
            删除
          </Button>
        </Space>
      )
    }
  ]

  // 批次对比表格列
  const batchColumns = [
    {
      title: '批次号',
      dataIndex: 'batchNumber',
      key: 'batchNumber'
    },
    {
      title: '配方',
      dataIndex: 'recipe',
      key: 'recipe'
    },
    {
      title: '质量评分',
      dataIndex: 'qualityScore',
      key: 'qualityScore',
      render: (score: number) => (
        <Text style={{ color: score >= 8.5 ? '#52c41a' : score >= 8.0 ? '#faad14' : '#f5222d' }}>
          {score}/10
        </Text>
      )
    },
    {
      title: '生产日期',
      dataIndex: 'productionDate',
      key: 'productionDate'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'COMPLETED' ? 'success' : 'processing'}>
          {status === 'COMPLETED' ? '已完成' : '进行中'}
        </Tag>
      )
    }
  ]

  // 配方性能表格列
  const recipeColumns = [
    {
      title: '配方名称',
      dataIndex: 'recipeName',
      key: 'recipeName'
    },
    {
      title: '总批次数',
      dataIndex: 'totalBatches',
      key: 'totalBatches'
    },
    {
      title: '平均评分',
      dataIndex: 'averageScore',
      key: 'averageScore',
      render: (score: number) => (
        <Text style={{ color: score >= 8.5 ? '#52c41a' : score >= 8.0 ? '#faad14' : '#f5222d' }}>
          {score}/10
        </Text>
      )
    },
    {
      title: '成功率',
      dataIndex: 'successRate',
      key: 'successRate',
      render: (rate: number) => (
        <div>
          <Progress 
            percent={rate} 
            size="small" 
            status={rate >= 95 ? 'success' : rate >= 90 ? 'normal' : 'exception'}
          />
        </div>
      )
    },
    {
      title: '平均成本',
      dataIndex: 'avgCost',
      key: 'avgCost',
      render: (cost: number) => `¥${cost.toFixed(2)}`
    }
  ]

  return (
    <div>
      {/* 页面头部 */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <div>
          <Title level={2}>
            <FileTextOutlined style={{ marginRight: 8, color: '#1890ff' }} />
            报告中心
          </Title>
          <Text type="secondary">生成和管理质量分析报告</Text>
        </div>
        <Space>
          <Button icon={<FilterOutlined />}>筛选报告</Button>
          <Button icon={<ExportOutlined />}>批量导出</Button>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => setGenerateModalVisible(true)}
          >
            生成报告
          </Button>
        </Space>
      </div>

      {/* 统计概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="总报告数"
              value={statistics.totalReports}
              prefix={<FileTextOutlined style={{ color: '#F05654' }} />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="本月报告"
              value={statistics.monthlyReports}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="质量报告"
              value={statistics.qualityReports}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="趋势分析"
              value={statistics.trendAnalysis}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Card>
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          items={[
            {
              key: 'overview',
              label: (
                <span>
                  <FileTextOutlined />
                  报告概览
                </span>
              ),
              children: (
                <Table
                  columns={reportColumns}
                  dataSource={reports}
                  rowKey="id"
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) => 
                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                  }}
                  locale={{ emptyText: '暂无报告记录' }}
                />
              )
            },
            {
              key: 'quality_trend',
              label: (
                <span>
                  <RiseOutlined />
                  质量趋势
                </span>
              ),
              children: (
                <div>
                  <Alert
                    message="质量趋势分析"
                    description="显示指定时间范围内的质量趋势变化，包括平均评分、批次数量和合格率等关键指标。"
                    type="info"
                    showIcon
                    style={{ marginBottom: 16 }}
                  />
                  
                  <Row gutter={[16, 16]}>
                    <Col xs={24} lg={16}>
                      <Card title="质量趋势图表" extra={<Button icon={<LineChartOutlined />}>查看详细图表</Button>}>
                        <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#f5f5f5' }}>
                          <Text type="secondary">质量趋势图表区域 (集成ECharts)</Text>
                        </div>
                      </Card>
                    </Col>
                    <Col xs={24} lg={8}>
                      <Card title="关键指标">
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <Statistic
                            title="本周平均评分"
                            value={8.3}
                            suffix="/10"
                            precision={1}
                            valueStyle={{ color: '#52c41a' }}
                          />
                          <Divider />
                          <Statistic
                            title="本周批次数"
                            value={16}
                            suffix="个"
                          />
                          <Divider />
                          <Statistic
                            title="合格率"
                            value={93.8}
                            suffix="%"
                            precision={1}
                            valueStyle={{ color: '#1890ff' }}
                          />
                        </Space>
                      </Card>
                    </Col>
                  </Row>
                  
                  <Card title="趋势数据详情" style={{ marginTop: 16 }}>
                    <Table
                      columns={[
                        { title: '日期', dataIndex: 'date', key: 'date' },
                        { 
                          title: '平均评分', 
                          dataIndex: 'averageScore', 
                          key: 'averageScore',
                          render: (score: number) => `${score}/10`
                        },
                        { title: '批次数量', dataIndex: 'batchCount', key: 'batchCount' },
                        { 
                          title: '合格率', 
                          dataIndex: 'passRate', 
                          key: 'passRate',
                          render: (rate: number) => `${rate}%`
                        }
                      ]}
                      dataSource={qualityTrendData}
                      rowKey="date"
                      pagination={false}
                      size="small"
                    />
                  </Card>
                </div>
              )
            },
            {
              key: 'batch_comparison',
              label: (
                <span>
                  <BarChartOutlined />
                  批次对比
                </span>
              ),
              children: (
                <div>
                  <Alert
                    message="批次对比分析"
                    description="对比不同批次的质量表现，识别最佳实践和改进机会。"
                    type="info"
                    showIcon
                    style={{ marginBottom: 16 }}
                  />
                  
                  <Card title="批次对比数据" extra={
                    <Space>
                      <Select defaultValue="all" style={{ width: 120 }}>
                        <Option value="all">全部配方</Option>
                        <Option value="classic">经典酸奶</Option>
                        <Option value="greek">希腊酸奶</Option>
                        <Option value="fruit">水果酸奶</Option>
                      </Select>
                      <Button icon={<BarChartOutlined />}>生成对比图表</Button>
                    </Space>
                  }>
                    <Table
                      columns={batchColumns}
                      dataSource={batchComparisonData}
                      rowKey="batchId"
                      pagination={{ pageSize: 8 }}
                      size="middle"
                    />
                  </Card>
                </div>
              )
            },
            {
              key: 'recipe_performance',
              label: (
                <span>
                  <PieChartOutlined />
                  配方性能
                </span>
              ),
              children: (
                <div>
                  <Alert
                    message="配方性能统计"
                    description="分析各配方的综合性能表现，包括质量评分、成功率和成本效益等维度。"
                    type="info"
                    showIcon
                    style={{ marginBottom: 16 }}
                  />
                  
                  <Row gutter={[16, 16]}>
                    <Col xs={24} lg={16}>
                      <Card title="配方性能对比">
                        <Table
                          columns={recipeColumns}
                          dataSource={recipePerformanceData}
                          rowKey="recipeId"
                          pagination={false}
                          size="middle"
                        />
                      </Card>
                    </Col>
                    <Col xs={24} lg={8}>
                      <Card title="性能排行">
                        <div style={{ marginBottom: 16 }}>
                          <Text strong>质量评分TOP3</Text>
                          <div style={{ marginTop: 8 }}>
                            {recipePerformanceData
                              .sort((a, b) => b.averageScore - a.averageScore)
                              .slice(0, 3)
                              .map((recipe, index) => (
                                <div key={recipe.recipeId} style={{ marginBottom: 8 }}>
                                  <Text>{index + 1}. {recipe.recipeName}</Text>
                                  <br />
                                  <Text type="secondary">{recipe.averageScore}/10</Text>
                                </div>
                              ))}
                          </div>
                        </div>
                        
                        <Divider />
                        
                        <div>
                          <Text strong>成功率TOP3</Text>
                          <div style={{ marginTop: 8 }}>
                            {recipePerformanceData
                              .sort((a, b) => b.successRate - a.successRate)
                              .slice(0, 3)
                              .map((recipe, index) => (
                                <div key={recipe.recipeId} style={{ marginBottom: 8 }}>
                                  <Text>{index + 1}. {recipe.recipeName}</Text>
                                  <br />
                                  <Text type="secondary">{recipe.successRate}%</Text>
                                </div>
                              ))}
                          </div>
                        </div>
                      </Card>
                    </Col>
                  </Row>
                </div>
              )
            }
          ]}
        />
      </Card>

      {/* 生成报告弹窗 */}
      <Modal
        title="生成新报告"
        open={generateModalVisible}
        onCancel={() => setGenerateModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleGenerateReport}
        >
          <Form.Item
            name="title"
            label="报告标题"
            rules={[{ required: true, message: '请输入报告标题' }]}
          >
            <Input placeholder="请输入报告标题" />
          </Form.Item>
          
          <Form.Item
            name="type"
            label="报告类型"
            rules={[{ required: true, message: '请选择报告类型' }]}
          >
            <Select placeholder="请选择报告类型">
              <Option value="quality_trend">质量趋势分析</Option>
              <Option value="batch_comparison">批次对比分析</Option>
              <Option value="recipe_performance">配方性能统计</Option>
              <Option value="custom">自定义报告</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="dateRange"
            label="数据时间范围"
          >
            <RangePicker style={{ width: '100%' }} />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="报告描述"
          >
            <TextArea rows={3} placeholder="请输入报告描述" />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                {loading ? '生成中...' : '生成报告'}
              </Button>
              <Button onClick={() => setGenerateModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 报告详情弹窗 */}
      <Modal
        title={selectedReport?.title}
        open={reportDetailsVisible}
        onCancel={() => setReportDetailsVisible(false)}
        footer={[
          <Button key="close" onClick={() => setReportDetailsVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedReport && (
          <div>
            <Paragraph>
              <Text strong>报告类型：</Text>
              <Tag color="blue">
                {selectedReport.type === 'quality_trend' ? '质量趋势' :
                 selectedReport.type === 'batch_comparison' ? '批次对比' :
                 selectedReport.type === 'recipe_performance' ? '配方性能' : '自定义'}
              </Tag>
            </Paragraph>
            
            <Paragraph>
              <Text strong>数据范围：</Text>{selectedReport.dataRange}
            </Paragraph>
            
            <Paragraph>
              <Text strong>创建时间：</Text>{dayjs(selectedReport.createdAt).format('YYYY-MM-DD HH:mm:ss')}
            </Paragraph>
            
            <Paragraph>
              <Text strong>创建人：</Text>{selectedReport.createdBy}
            </Paragraph>
            
            <Paragraph>
              <Text strong>报告描述：</Text><br />
              {selectedReport.description}
            </Paragraph>
            
            <Paragraph>
              <Text strong>支持导出格式：</Text>
              <Space>
                {selectedReport.exportFormats.map(format => (
                  <Tag key={format}>{format}</Tag>
                ))}
              </Space>
            </Paragraph>
            
            <div style={{ 
              height: 200, 
              backgroundColor: '#f5f5f5', 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              marginTop: 16,
              borderRadius: 4
            }}>
              <Text type="secondary">报告预览区域</Text>
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default Reports
