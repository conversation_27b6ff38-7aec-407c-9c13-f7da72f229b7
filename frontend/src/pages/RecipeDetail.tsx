import React, { useEffect, useState } from 'react'
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  Space,
  Row,
  Col,
  Tag,
  Table,
  Descriptions,
  Statistic,
  Divider,
  message,
  Modal,
  Spin
} from 'antd'
import {
  ArrowLeftOutlined,
  EditOutlined,
  CopyOutlined,
  DeleteOutlined,
  Check<PERSON><PERSON>cleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'
import { useNavigate, useParams } from 'react-router-dom'
import { recipeAPI } from '../services/api/recipe'

const { Title, Text } = Typography

interface Recipe {
  id: string
  name: string
  description?: string
  version: number
  isActive: boolean
  ingredients: any[]
  process: any[]
  fermentationTemperature?: number
  fermentationDuration?: number
  filtrationDuration?: number
  createdAt: string
  updatedAt: string
  user?: {
    id: string
    name: string
    email: string
  }
  batches?: any[]
}

const RecipeDetail: React.FC = () => {
  const navigate = useNavigate()
  const { id } = useParams<{ id: string }>()
  const [recipe, setRecipe] = useState<Recipe | null>(null)
  const [loading, setLoading] = useState(true)

  // 加载配方详情
  const loadRecipeDetail = async () => {
    if (!id) return
    
    try {
      setLoading(true)
      const response = await recipeAPI.getRecipeById(id)
      if (response.data.success) {
        setRecipe(response.data.data.recipe)
      } else {
        message.error('加载配方详情失败')
      }
    } catch (error: any) {
      console.error('Error loading recipe:', error)
      message.error(error.response?.data?.message || '加载配方详情失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadRecipeDetail()
  }, [id])

  // 删除配方
  const handleDelete = () => {
    if (!recipe) return
    
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除配方"${recipe.name}"吗？此操作不可恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await recipeAPI.deleteRecipe(recipe.id)
          if (response.data.success) {
            message.success('配方删除成功')
            navigate('/recipes')
          } else {
            message.error(response.data.message || '删除失败')
          }
        } catch (error: any) {
          message.error(error.response?.data?.message || '删除失败')
        }
      },
    })
  }

  // 复制配方
  const handleDuplicate = async () => {
    if (!recipe) return
    
    try {
      const response = await recipeAPI.duplicateRecipe(recipe.id)
      if (response.data.success) {
        message.success(`配方"${recipe.name}"复制成功`)
        navigate('/recipes')
      } else {
        message.error(response.data.message || '复制失败')
      }
    } catch (error: any) {
      message.error(error.response?.data?.message || '复制失败')
    }
  }

  // 配料表格列
  const ingredientColumns = [
    {
      title: '配料名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '用量',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number, record: any) => `${amount} ${record.unit}`,
    },
  ]

  // 工艺流程表格列
  const processColumns = [
    {
      title: '步骤',
      dataIndex: 'step',
      key: 'step',
      width: 80,
      render: (step: number) => (
        <Tag color="blue">{step}</Tag>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '时长',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
      render: (duration: number) => duration ? `${duration}分钟` : '-',
    },
    {
      title: '温度',
      dataIndex: 'temperature',
      key: 'temperature',
      width: 100,
      render: (temperature: number) => temperature ? `${temperature}°C` : '-',
    },
  ]

  // 批次历史表格列
  const batchColumns = [
    {
      title: '批次号',
      dataIndex: 'batchNumber',
      key: 'batchNumber',
      render: (batchNumber: string, record: any) => (
        <Button 
          type="link" 
          onClick={() => navigate(`/batches/${record.id}`)}
          style={{ padding: 0 }}
        >
          {batchNumber}
        </Button>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const colors: Record<string, string> = {
          'PLANNING': 'default',
          'IN_PROGRESS': 'processing',
          'FERMENTING': 'orange',
          'FILTERING': 'cyan',
          'COMPLETED': 'success',
          'FAILED': 'error'
        }
        const texts: Record<string, string> = {
          'PLANNING': '计划中',
          'IN_PROGRESS': '进行中',
          'FERMENTING': '发酵中',
          'FILTERING': '过滤中',
          'COMPLETED': '已完成',
          'FAILED': '失败'
        }
        return <Tag color={colors[status]}>{texts[status] || status}</Tag>
      },
    },
    {
      title: '生产日期',
      dataIndex: 'productionDate',
      key: 'productionDate',
      render: (date: string) => new Date(date).toLocaleDateString('zh-CN'),
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity: number) => quantity ? `${quantity}L` : '-',
    },
  ]

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large">
          <div style={{ padding: '50px' }}>加载配方详情...</div>
        </Spin>
      </div>
    )
  }

  if (!recipe) {
    return (
      <div>
        <div style={{ marginBottom: 24 }}>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/recipes')}
          >
            返回配方列表
          </Button>
        </div>
        <Card>
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Text type="secondary">配方不存在或已被删除</Text>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div>
      {/* 页面头部 */}
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={() => navigate('/recipes')}
        >
          返回配方列表
        </Button>
        
        <Space>
          <Button 
            icon={<EditOutlined />}
            onClick={() => navigate(`/recipes/${recipe.id}/edit`)}
          >
            编辑配方
          </Button>
          <Button 
            icon={<CopyOutlined />}
            onClick={handleDuplicate}
          >
            复制配方
          </Button>
          <Button 
            danger
            icon={<DeleteOutlined />}
            onClick={handleDelete}
          >
            删除配方
          </Button>
        </Space>
      </div>

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={16}>
          {/* 基本信息 */}
          <Card title="基本信息" style={{ marginBottom: 24 }}>
            <div style={{ marginBottom: 16 }}>
              <Title level={2} style={{ margin: 0, display: 'inline-block', marginRight: 16 }}>
                {recipe.name}
              </Title>
              <Tag color={recipe.isActive ? 'success' : 'default'}>
                {recipe.isActive ? (
                  <><CheckCircleOutlined /> 启用</>
                ) : (
                  <><ClockCircleOutlined /> 停用</>
                )}
              </Tag>
              <Tag color="blue">v{recipe.version}</Tag>
            </div>
            
            <Descriptions column={2}>
              <Descriptions.Item label="配方描述" span={2}>
                {recipe.description || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="创建者">
                {recipe.user?.name || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(recipe.createdAt).toLocaleString('zh-CN')}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {new Date(recipe.updatedAt).toLocaleString('zh-CN')}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* 配料信息 */}
          <Card title="配料信息" style={{ marginBottom: 24 }}>
            <Table
              columns={ingredientColumns}
              dataSource={(recipe.ingredients || []).map((item, index) => ({ ...item, key: `ingredient-${index}` }))}
              pagination={false}
              rowKey="key"
              locale={{ emptyText: '暂无配料信息' }}
            />
          </Card>

          {/* 工艺流程 */}
          <Card title="工艺流程" style={{ marginBottom: 24 }}>
            <Table
              columns={processColumns}
              dataSource={(recipe.process || []).map((item, index) => ({ ...item, key: `process-${index}` }))}
              pagination={false}
              rowKey="key"
              locale={{ emptyText: '暂无工艺流程' }}
            />
          </Card>

          {/* 批次历史 */}
          <Card title="批次历史">
            <Table
              columns={batchColumns}
              dataSource={recipe.batches || []}
              pagination={{
                pageSize: 10,
                showSizeChanger: false,
                showQuickJumper: true,
                showTotal: (total, range) => 
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              }}
              rowKey="id"
              locale={{ emptyText: '暂无批次记录' }}
            />
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          {/* 统计信息 */}
          <Card title="统计信息" style={{ marginBottom: 24 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="使用次数"
                  value={recipe.batches?.length || 0}
                  suffix="次"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="成功批次"
                  value={recipe.batches?.filter(b => b.status === 'COMPLETED').length || 0}
                  suffix="次"
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
            </Row>
          </Card>

          {/* 发酵参数 */}
          <Card title="发酵参数">
            <Descriptions column={1} bordered>
              <Descriptions.Item label="发酵温度">
                {recipe.fermentationTemperature ? `${recipe.fermentationTemperature}°C` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="发酵时长">
                {recipe.fermentationDuration ? `${recipe.fermentationDuration}小时` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="过滤时长">
                {recipe.filtrationDuration ? `${recipe.filtrationDuration}小时` : '-'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default RecipeDetail
