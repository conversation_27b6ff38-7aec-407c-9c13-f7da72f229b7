import React, { useEffect, useState } from 'react'
import {
  Card,
  Table,
  Button,
  Input,
  Space,
  Tag,
  Dropdown,
  Modal,
  Typography,
  Row,
  Col,
  Statistic,
  Select,
  message
} from 'antd'
import {
  PlusOutlined,
  MoreOutlined,
  EditOutlined,
  CopyOutlined,
  DeleteOutlined,
  EyeOutlined,
  ExperimentOutlined,
  ExportOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { recipeAPI } from '../services/api/recipe'
import LoadingSpinner from '../components/LoadingSpinner'
import './Recipes.css'

const { Title, Text } = Typography
const { Search } = Input
const { Option } = Select

interface Recipe {
  id: string
  name: string
  description?: string
  version: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  userId: string
  ingredients: any[]
  process: any[]
  fermentationTemperature?: number
  fermentationDuration?: number
  filtrationDuration?: number
  user?: {
    id: string
    name: string
    email: string
  }
  _count?: {
    batches: number
  }
}

const Recipes: React.FC = () => {
  const [loading, setLoading] = useState(true)
  const [recipes, setRecipes] = useState<Recipe[]>([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })
  const [searchText, setSearchText] = useState('')
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all')
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])

  const navigate = useNavigate()

  // 加载配方数据
  const loadRecipes = async (params?: any) => {
    try {
      setLoading(true)
      const queryParams = {
        page: params?.current || pagination.current,
        limit: params?.pageSize || pagination.pageSize,
        search: searchText || undefined,
        isActive: filterStatus === 'all' ? undefined : filterStatus === 'active'
      }
      
      const response = await recipeAPI.getRecipes(queryParams)
      if (response.data.success) {
        setRecipes(response.data.data.recipes)
        setPagination({
          current: response.data.data.pagination.page,
          pageSize: response.data.data.pagination.limit,
          total: response.data.data.pagination.total
        })
      }
    } catch (error) {
      message.error('加载配方列表失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadRecipes()
  }, [searchText, filterStatus])

  // 处理删除配方
  const handleDelete = (id: string, name: string) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除配方"${name}"吗？此操作不可恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await recipeAPI.deleteRecipe(id)
          if (response.data.success) {
            message.success('配方删除成功')
            loadRecipes() // 重新加载列表
          } else {
            message.error(response.data.message || '删除失败')
          }
        } catch (error: any) {
          message.error(error.response?.data?.message || '删除失败')
        }
      },
    })
  }

  // 处理复制配方
  const handleDuplicate = async (id: string, name: string) => {
    try {
      const response = await recipeAPI.duplicateRecipe(id)
      if (response.data.success) {
        message.success(`配方"${name}"复制成功`)
        loadRecipes() // 重新加载列表
      } else {
        message.error(response.data.message || '复制失败')
      }
    } catch (error: any) {
      message.error(error.response?.data?.message || '复制失败')
    }
  }

  // 操作菜单
  const getActionMenu = (record: any) => ({
    items: [
      {
        key: 'view',
        icon: <EyeOutlined />,
        label: '查看详情',
        onClick: () => navigate(`/recipes/${record.id}`),
      },
      {
        key: 'edit',
        icon: <EditOutlined />,
        label: '编辑配方',
        onClick: () => navigate(`/recipes/${record.id}/edit`),
      },
      {
        key: 'duplicate',
        icon: <CopyOutlined />,
        label: '复制配方',
        onClick: () => handleDuplicate(record.id, record.name),
      },
      {
        type: 'divider' as const,
      },
      {
        key: 'delete',
        icon: <DeleteOutlined />,
        label: '删除配方',
        danger: true,
        onClick: () => handleDelete(record.id, record.name),
      },
    ],
  })

  // 格式化时间显示
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).replace(/\//g, '-')
  }

  // 表格列配置
  const columns = [
    {
      title: '配方名称',
      dataIndex: 'name',
      key: 'name',
      width: 220,
      render: (text: string, record: any) => (
        <div>
          <Button 
            type="link" 
            onClick={() => navigate(`/recipes/${record.id}`)}
            style={{ padding: 0, color: '#F05654', fontWeight: 500 }}
          >
            {text}
          </Button>
          {record.description && (
            <div>
              <Text type="secondary" style={{ fontSize: 12 }}>
                {record.description}
              </Text>
            </div>
          )}
        </div>
      ),
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 80,
      render: (version: number) => (
        <Tag color="blue">v{version}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 100,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'success' : 'default'}>
          {isActive ? '启用' : '停用'}
        </Tag>
      ),
    },
    {
      title: '发酵温度',
      dataIndex: 'fermentationTemperature',
      key: 'fermentationTemperature',
      width: 120,
      render: (temp: number) => temp ? `${temp}°C` : '-',
    },
    {
      title: '发酵时长',
      dataIndex: 'fermentationDuration',
      key: 'fermentationDuration',
      width: 120,
      render: (duration: number) => duration ? `${duration}小时` : '-',
    },
    {
      title: '使用次数',
      dataIndex: '_count',
      key: 'batchCount',
      width: 100,
      render: (count: any) => (
        <Text strong style={{ color: '#F05654' }}>{count?.batches || 0}</Text>
      ),
    },
    {
      title: '创建者',
      dataIndex: 'user',
      key: 'user',
      width: 140,
      render: (user: any) => (
        <div style={{ whiteSpace: 'nowrap' }}>
          {user?.name || '-'}
        </div>
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 180,
      render: (dateString: string) => (
        <div style={{ whiteSpace: 'nowrap', fontSize: '12px' }}>
          {formatDateTime(dateString)}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      fixed: 'right' as const,
      render: (_: any, record: any) => (
        <Dropdown menu={getActionMenu(record)} trigger={['click']}>
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ]

  // 统计数据
  const stats = {
    total: pagination.total,
    active: recipes.filter(r => r.isActive).length,
    totalBatches: recipes.reduce((sum, r) => sum + (r._count?.batches || 0), 0)
  }

  if (loading) {
    return <LoadingSpinner tip="加载配方数据..." />
  }

  return (
    <div className="recipes-container">
      {/* 页面头部 */}
      <div className="recipes-header">
        <div>
          <Title level={2} className="page-title">配方管理</Title>
          <Text className="page-subtitle">管理和维护酸奶制作配方</Text>
        </div>
        <Space>
          <Button icon={<ReloadOutlined />} onClick={() => loadRecipes()}>刷新</Button>
          <Button icon={<ExportOutlined />}>导出</Button>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => navigate('/recipes/new')}
          >
            新建配方
          </Button>
        </Space>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="stats-row">
        <Col xs={12} sm={6}>
          <Card className="stat-card" variant="outlined">
            <Statistic
              title="配方总数"
              value={stats.total}
              prefix={<ExperimentOutlined style={{ color: '#F05654' }} />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card className="stat-card" variant="outlined">
            <Statistic
              title="启用配方"
              value={stats.active}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card className="stat-card" variant="outlined">
            <Statistic
              title="当前页配方"
              value={recipes.length}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card className="stat-card" variant="outlined">
            <Statistic
              title="总批次数"
              value={stats.totalBatches}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card className="filter-card" variant="outlined">
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜索配方名称或描述"
              allowClear
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Select
              value={filterStatus}
              onChange={setFilterStatus}
              style={{ width: '100%' }}
              placeholder="筛选状态"
            >
              <Option value="all">全部状态</Option>
              <Option value="active">启用</Option>
              <Option value="inactive">停用</Option>
            </Select>
          </Col>
          <Col xs={24} md={10}>
            <div className="filter-info">
              <Text type="secondary">
                共找到 {pagination.total} 个配方
              </Text>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 配方表格 */}
      <Card className="table-card" variant="outlined">
        <Table
          columns={columns}
          dataSource={recipes}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              loadRecipes({ current: page, pageSize })
            }
          }}
          scroll={{ x: 1400 }}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
        />
      </Card>
    </div>
  )
}

export default Recipes
