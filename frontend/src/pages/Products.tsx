import React, { useState, useEffect } from 'react'
import { apiClient } from '../services/api'
import { useTypedSelector } from '../hooks/useTypedSelector'
import { getProductImageURL, handleImageError } from '../utils/imageUtils'
import {
  Card,
  Row,
  Col,
  Typography,
  Tag,
  Space,
  Button,
  Divider,
  List,
  Avatar,
  Progress,
  Timeline,
  Statistic,
  Alert,
  Badge,
  Modal,
  Image,
  Descriptions,
  message
} from 'antd'
import {
  ShopOutlined,
  SafetyOutlined,
  TrophyOutlined,
  CalendarOutlined,
  EnvironmentOutlined,
  PhoneOutlined,
  StarOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined,
  HeartOutlined,
  FireOutlined,
  ThunderboltOutlined
} from '@ant-design/icons'

const { Title, Text, Paragraph } = Typography


const Products: React.FC = () => {
  const [products, setProducts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedProduct, setSelectedProduct] = useState<any>(null)
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  const { user } = useTypedSelector((state) => state.auth)

  // 加载产品数据
  useEffect(() => {
    const loadProducts = async () => {
      try {
        setLoading(true)
        const response = await apiClient.get('/api/products?status=active')
        if (response.data.success) {
          // 处理API返回的产品数据格式
          const apiProducts = response.data.data.products.map((product: any) => ({
            ...product,
            // 适配前端字段名
            qualityScore: product.quality_score || 95, // 默认质量分
            nutritionPer100g: product.nutrition_per_100g || {},
            expiryDays: product.shelf_life_days || 21,
            productionDate: product.created_at ? new Date(product.created_at).toISOString().split('T')[0] : '2025-01-30',
            batchNumber: `YG${product.created_at ? new Date(product.created_at).toISOString().replace(/[-:]/g, '').slice(0, 8) : '20250130'}${product.id.slice(0, 3).toUpperCase()}`,
            // 处理图片URL
            image: product.images && product.images[0] ? getProductImageURL(product.images[0].url) : getProductImageURL('/images/products/default-yogurt.jpg'),
            // 格式化价格
            price: `¥${product.price}`
          }))
          setProducts(apiProducts)
        } else {
          setProducts([])
          message.info('暂无产品数据')
        }
      } catch (error) {
        console.error('Failed to load products:', error)
        setProducts([])
        message.error('加载产品数据失败')
      } finally {
        setLoading(false)
      }
    }

    loadProducts()
  }, [])

  const getQualityColor = (score: number) => {
    if (score >= 95) return '#52c41a'
    if (score >= 90) return '#faad14'
    return '#ff4d4f'
  }

  const getQualityStatus = (score: number) => {
    if (score >= 95) return { text: '优秀', status: 'success' }
    if (score >= 90) return { text: '良好', status: 'warning' }
    return { text: '合格', status: 'error' }
  }

  const showProductDetail = (product: any) => {
    setSelectedProduct(product)
    setDetailModalVisible(true)
  }

  return (
    <div>
      {/* 页面头部 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <ShopOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          产品信息
        </Title>
        <Text type="secondary">查看酸奶产品详细信息和质量认证</Text>
      </div>

      {/* 质量保证承诺 */}
      <Alert
        message="质量承诺"
        description="我们承诺每一款产品都经过严格的质量检测，确保为消费者提供安全、健康、美味的酸奶产品。所有产品均符合国家食品安全标准，并获得相关质量认证。"
        type="info"
        showIcon
        icon={<SafetyOutlined />}
        style={{ marginBottom: 24 }}
      />

      {/* 产品统计 */}
      <Row gutter={24} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="在售产品"
              value={products.length}
              prefix={<ShopOutlined />}
              suffix="款"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均质量分"
              value={products.length > 0 ? products.reduce((sum, p) => sum + (p.qualityScore || 0), 0) / products.length : 0}
              prefix={<TrophyOutlined />}
              suffix="/100"
              precision={1}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="认证数量"
              value={[...new Set(products.flatMap(p => p.certifications || []))].length}
              prefix={<SafetyOutlined />}
              suffix="项"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="合格率"
              value={100}
              prefix={<CheckCircleOutlined />}
              suffix="%"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 产品列表 */}
      <Row gutter={[24, 24]}>
        {products.map((product) => (
          <Col xs={24} lg={8} key={product.id}>
            <Card
              hoverable
              cover={
                <div style={{ 
                  height: 200, 
                  background: '#f5f5f5',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  position: 'relative'
                }}>
                  {/* 质量评分徽章 */}
                  <div style={{
                    position: 'absolute',
                    top: 12,
                    right: 12,
                    background: getQualityColor(product.qualityScore || 0),
                    color: 'white',
                    padding: '4px 8px',
                    borderRadius: '12px',
                    fontSize: '12px',
                    fontWeight: 'bold'
                  }}>
                    质量分: {product.qualityScore || 0}
                  </div>
                  
                  {/* 产品图片 */}
                  <img
                    src={product.image}
                    alt={product.name}
                    style={{
                      width: 120,
                      height: 160,
                      objectFit: 'cover',
                      borderRadius: '8px'
                    }}
                    onError={(e) => {
                      // 图片加载失败时显示占位符
                      const target = e.currentTarget;
                      target.style.display = 'none';
                      const placeholder = target.nextElementSibling as HTMLElement;
                      if (placeholder) {
                        placeholder.style.display = 'flex';
                      }
                    }}
                  />
                  <div style={{
                    width: 120,
                    height: 160,
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    borderRadius: '8px',
                    display: 'none',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '16px'
                  }}>
                    🥛
                  </div>
                </div>
              }
              actions={[
                <Button 
                  type="primary" 
                  size="small"
                  onClick={() => showProductDetail(product)}
                >
                  查看详情
                </Button>
              ]}
            >
              <Card.Meta
                title={
                  <Space direction="vertical" size={0}>
                    <Text strong style={{ fontSize: '16px' }}>{product.name}</Text>
                    <Space>
                      <Tag color="blue">{product.type}</Tag>
                      <Text strong style={{ color: '#1890ff' }}>{product.price}</Text>
                      <Text type="secondary">({product.volume})</Text>
                    </Space>
                  </Space>
                }
                description={
                  <div>
                    {/* 质量状态 */}
                    <div style={{ marginBottom: 12 }}>
                      <Space>
                        <Progress 
                          percent={product.qualityScore || 0} 
                          size="small" 
                          strokeColor={getQualityColor(product.qualityScore || 0)}
                          format={() => null}
                          style={{ width: 80 }}
                        />
                        <Badge 
                          status={getQualityStatus(product.qualityScore || 0).status as any} 
                          text={getQualityStatus(product.qualityScore || 0).text}
                        />
                      </Space>
                    </div>

                    {/* 认证标签 */}
                    <div style={{ marginBottom: 12 }}>
                      <Space wrap>
                        {(product.certifications || []).slice(0, 2).map((cert: any) => (
                          <Tag key={cert} color="green">
                            <SafetyOutlined /> {cert}
                          </Tag>
                        ))}
                        {(product.certifications || []).length > 2 && (
                          <Tag>+{(product.certifications || []).length - 2}</Tag>
                        )}
                      </Space>
                    </div>

                    {/* 主要功效 */}
                    <div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        <HeartOutlined /> {(product.benefits || []).slice(0, 2).join('、')}
                        {(product.benefits || []).length > 2 && '...'}
                      </Text>
                    </div>
                  </div>
                }
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* 产品详情弹窗 */}
      <Modal
        title={selectedProduct?.name}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedProduct && (
          <div>
            <Row gutter={24}>
              <Col span={8}>
                {/* 产品图片 */}
                <div style={{ marginBottom: 16, position: 'relative' }}>
                  <img
                    src={selectedProduct.image}
                    alt={selectedProduct.name}
                    style={{
                      width: '100%',
                      height: 250,
                      objectFit: 'cover',
                      borderRadius: '8px'
                    }}
                    onError={(e) => {
                      // 图片加载失败时显示占位符
                      const target = e.currentTarget;
                      target.style.display = 'none';
                      const placeholder = target.nextElementSibling as HTMLElement;
                      if (placeholder) {
                        placeholder.style.display = 'flex';
                      }
                    }}
                  />
                  <div style={{
                    width: '100%',
                    height: 250,
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    borderRadius: '8px',
                    display: 'none',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '48px'
                  }}>
                    🥛
                  </div>
                </div>
                
                {/* 质量评分 */}
                <Card size="small">
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: getQualityColor(selectedProduct.qualityScore || 0) }}>
                      {selectedProduct.qualityScore || 0}
                    </div>
                    <div style={{ fontSize: '12px', color: '#666' }}>质量评分</div>
                    <Progress
                      percent={selectedProduct.qualityScore || 0}
                      strokeColor={getQualityColor(selectedProduct.qualityScore || 0)}
                      showInfo={false}
                      size="small"
                      style={{ marginTop: 8 }}
                    />
                  </div>
                </Card>
              </Col>
              
              <Col span={16}>
                {/* 基本信息 */}
                <Descriptions title="产品信息" bordered size="small" column={2}>
                  <Descriptions.Item label="产品名称" span={2}>{selectedProduct.name}</Descriptions.Item>
                  <Descriptions.Item label="产品类型">{selectedProduct.type}</Descriptions.Item>
                  <Descriptions.Item label="规格">{selectedProduct.volume}</Descriptions.Item>
                  <Descriptions.Item label="价格">{selectedProduct.price}</Descriptions.Item>
                  <Descriptions.Item label="保质期">{selectedProduct.expiryDays}天</Descriptions.Item>
                  <Descriptions.Item label="生产日期">{selectedProduct.productionDate}</Descriptions.Item>
                  <Descriptions.Item label="批次号">{selectedProduct.batchNumber}</Descriptions.Item>
                </Descriptions>

                {/* 营养成分 */}
                <Divider orientation="left">营养成分（每100g）</Divider>
                <Row gutter={16}>
                  {Object.entries(selectedProduct.nutritionPer100g || {}).map(([key, value]) => (
                    <Col span={8} key={key}>
                      <Card size="small" styles={{ body: { padding: '8px' } }}>
                        <div style={{ textAlign: 'center' }}>
                          <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                            {typeof value === 'object' && value !== null 
                              ? ((value as any).name || (value as any).percentage || JSON.stringify(value))
                              : String(value || '')}
                          </div>
                          <div style={{ fontSize: '12px', color: '#666' }}>
                            {key === 'energy' || key === 'calories' ? '能量' :
                             key === 'protein' ? '蛋白质' :
                             key === 'fat' ? '脂肪' :
                             key === 'carbohydrate' || key === 'carbs' ? '碳水化合物' :
                             key === 'calcium' ? '钙' :
                             key === 'probiotics' ? '益生菌' :
                             key === 'anthocyanins' ? '花青素' : 
                             key.charAt(0).toUpperCase() + key.slice(1)}
                          </div>
                        </div>
                      </Card>
                    </Col>
                  ))}
                </Row>
              </Col>
            </Row>

            <Divider />

            <Row gutter={24}>
              <Col span={12}>
                {/* 配料表 */}
                <Card title="配料表" size="small">
                  <Space wrap>
                    {(selectedProduct.ingredients || []).map((ingredient: any, index: number) => (
                      <Tag key={index} color="blue">
                        {typeof ingredient === 'object' && ingredient !== null
                          ? ((ingredient as any).name || JSON.stringify(ingredient))
                          : String(ingredient || '')}
                      </Tag>
                    ))}
                  </Space>
                </Card>
              </Col>
              
              <Col span={12}>
                {/* 主要功效 */}
                <Card title="主要功效" size="small">
                  <List
                    size="small"
                    dataSource={selectedProduct.benefits || []}
                    renderItem={(item) => (
                      <List.Item style={{ padding: '4px 0' }}>
                        <Space>
                          <CheckCircleOutlined style={{ color: '#52c41a' }} />
                          <Text>
                            {typeof item === 'object' && item !== null
                              ? ((item as any).name || JSON.stringify(item))
                              : String(item || '')}
                          </Text>
                        </Space>
                      </List.Item>
                    )}
                  />
                </Card>
              </Col>
            </Row>

            <Divider />

            {/* 质量认证 */}
            <Card title="质量认证" size="small">
              <Row gutter={16}>
                {(selectedProduct.certifications || []).map((cert: any, index: number) => (
                  <Col span={6} key={index}>
                    <div style={{
                      textAlign: 'center',
                      padding: '16px',
                      border: '1px solid #d9d9d9',
                      borderRadius: '8px',
                      background: '#fafafa'
                    }}>
                      <SafetyOutlined style={{ fontSize: '24px', color: '#52c41a', marginBottom: '8px' }} />
                      <div style={{ fontSize: '12px', fontWeight: 'bold' }}>
                        {typeof cert === 'object' && cert !== null
                          ? ((cert as any).name || JSON.stringify(cert))
                          : String(cert || '')}
                      </div>
                    </div>
                  </Col>
                ))}
              </Row>
            </Card>

            {/* 联系信息 */}
            <Alert
              message="如有疑问，请联系我们"
              description={
                <Space split={<Divider type="vertical" />}>
                  <span><PhoneOutlined /> 13976388728</span>
                  <span><EnvironmentOutlined /> 海南省海口市</span>
                </Space>
              }
              type="info"
              style={{ marginTop: 16 }}
            />
          </div>
        )}
      </Modal>
    </div>
  )
}

export default Products