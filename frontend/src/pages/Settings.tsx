import React, { useState } from 'react'
import { 
  Card, 
  Typography, 
  Tabs, 
  Form, 
  Input, 
  Switch, 
  Button, 
  Space, 
  Divider, 
  Select,
  TimePicker,
  Checkbox,
  InputNumber,
  message,
  Alert,
  Row,
  Col
} from 'antd'
import { 
  SettingOutlined, 
  BellOutlined, 
  SecurityScanOutlined,
  MailOutlined,
  MobileOutlined,
  NotificationOutlined,
  LockOutlined,
  SafetyOutlined,
  KeyOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { TabPane } = Tabs
const { Option } = Select

const Settings: React.FC = () => {
  const [generalForm] = Form.useForm()
  const [notificationForm] = Form.useForm()
  const [securityForm] = Form.useForm()
  const [loading, setLoading] = useState(false)

  const handleSave = async (values: any, type: string) => {
    setLoading(true)
    try {
      console.log(`保存${type}设置:`, values)
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      message.success(`${type}设置保存成功`)
    } catch (error) {
      message.error(`保存${type}设置失败`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>系统设置</Title>
        <Text type="secondary">管理系统配置和个人偏好</Text>
      </div>
      
      <Card>
        <Tabs defaultActiveKey="general" type="card">
          <TabPane 
            tab={
              <span>
                <SettingOutlined />
                常规设置
              </span>
            } 
            key="general"
          >
            <Form
              form={generalForm}
              layout="vertical"
              onFinish={(values) => handleSave(values, '常规')}
              style={{ maxWidth: 600 }}
            >
              <Form.Item
                label="系统名称"
                name="systemName"
                initialValue="Yogurt AI QC"
              >
                <Input />
              </Form.Item>
              
              <Form.Item
                label="默认语言"
                name="language"
                initialValue="zh-CN"
              >
                <Select>
                  <Option value="zh-CN">简体中文</Option>
                  <Option value="en-US">English</Option>
                  <Option value="zh-TW">繁體中文</Option>
                </Select>
              </Form.Item>
              
              <Form.Item
                label="时区"
                name="timezone"
                initialValue="Asia/Shanghai"
              >
                <Select>
                  <Option value="Asia/Shanghai">中国标准时间 (UTC+8)</Option>
                  <Option value="Asia/Tokyo">日本标准时间 (UTC+9)</Option>
                  <Option value="America/New_York">美东时间 (UTC-5)</Option>
                  <Option value="Europe/London">英国时间 (UTC+0)</Option>
                </Select>
              </Form.Item>
              
              <Divider />
              
              <Form.Item
                label="启用AI分析"
                name="enableAI"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>
              
              <Form.Item
                label="自动备份"
                name="autoBackup"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>
              
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" loading={loading}>
                    保存设置
                  </Button>
                  <Button onClick={() => generalForm.resetFields()}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <BellOutlined />
                通知设置
              </span>
            } 
            key="notifications"
          >
            <Form
              form={notificationForm}
              layout="vertical"
              onFinish={(values) => handleSave(values, '通知')}
              style={{ maxWidth: 800 }}
            >
              <Alert
                message="通知设置"
                description="配置各种系统通知和营销推送设置，帮助您更好地与用户沟通"
                type="info"
                showIcon
                style={{ marginBottom: 24 }}
              />

              <Title level={4}>
                <NotificationOutlined /> 系统通知设置
              </Title>
              
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    label="邮件通知"
                    name="emailNotifications"
                    valuePropName="checked"
                    initialValue={true}
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="短信通知"
                    name="smsNotifications"
                    valuePropName="checked"
                    initialValue={false}
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="通知发送时间段"
                name="notificationTime"
                initialValue={[dayjs('09:00', 'HH:mm'), dayjs('21:00', 'HH:mm')]}
              >
                <TimePicker.RangePicker format="HH:mm" />
              </Form.Item>

              <Divider />

              <Title level={4}>
                <MailOutlined /> 产品营销通知
              </Title>
              
              <Form.Item
                label="产品推广通知"
                name="productPromotions"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="新产品上架通知"
                name="newProductNotifications"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="优惠活动通知"
                name="promotionNotifications"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="质量检测结果通知"
                name="qualityResultNotifications"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>

              <Divider />

              <Title level={4}>
                <MobileOutlined /> 推送频率设置
              </Title>

              <Form.Item
                label="每日最大推送次数"
                name="maxDailyNotifications"
                initialValue={5}
              >
                <InputNumber min={1} max={20} />
              </Form.Item>

              <Form.Item
                label="推送内容类型"
                name="notificationTypes"
                initialValue={['product', 'promotion', 'quality']}
              >
                <Checkbox.Group>
                  <Space direction="vertical">
                    <Checkbox value="product">产品信息推送</Checkbox>
                    <Checkbox value="promotion">优惠活动推送</Checkbox>
                    <Checkbox value="quality">质量报告推送</Checkbox>
                    <Checkbox value="system">系统维护通知</Checkbox>
                    <Checkbox value="security">安全提醒通知</Checkbox>
                  </Space>
                </Checkbox.Group>
              </Form.Item>

              <Form.Item
                label="推送模板"
                name="notificationTemplate"
                initialValue="default"
              >
                <Select>
                  <Option value="default">默认模板</Option>
                  <Option value="simple">简洁模板</Option>
                  <Option value="detailed">详细模板</Option>
                  <Option value="marketing">营销模板</Option>
                </Select>
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" loading={loading}>
                    保存通知设置
                  </Button>
                  <Button onClick={() => notificationForm.resetFields()}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <SecurityScanOutlined />
                安全设置
              </span>
            } 
            key="security"
          >
            <Form
              form={securityForm}
              layout="vertical"
              onFinish={(values) => handleSave(values, '安全')}
              style={{ maxWidth: 800 }}
            >
              <Alert
                message="安全设置"
                description="配置系统安全策略，保护用户数据和系统安全"
                type="warning"
                showIcon
                style={{ marginBottom: 24 }}
              />

              <Title level={4}>
                <LockOutlined /> 密码安全策略
              </Title>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    label="密码最小长度"
                    name="minPasswordLength"
                    initialValue={8}
                  >
                    <InputNumber min={6} max={20} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="密码过期天数"
                    name="passwordExpiryDays"
                    initialValue={90}
                  >
                    <InputNumber min={30} max={365} />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="密码复杂度要求"
                name="passwordRequirements"
                initialValue={['uppercase', 'lowercase', 'numbers']}
              >
                <Checkbox.Group>
                  <Space direction="vertical">
                    <Checkbox value="uppercase">包含大写字母</Checkbox>
                    <Checkbox value="lowercase">包含小写字母</Checkbox>
                    <Checkbox value="numbers">包含数字</Checkbox>
                    <Checkbox value="symbols">包含特殊符号</Checkbox>
                  </Space>
                </Checkbox.Group>
              </Form.Item>

              <Divider />

              <Title level={4}>
                <SafetyOutlined /> 登录安全设置
              </Title>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    label="启用双因素认证"
                    name="enableTwoFactor"
                    valuePropName="checked"
                    initialValue={false}
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="强制HTTPS访问"
                    name="forceHttps"
                    valuePropName="checked"
                    initialValue={true}
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    label="登录失败锁定次数"
                    name="maxLoginAttempts"
                    initialValue={5}
                  >
                    <InputNumber min={3} max={10} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="账户锁定时间(分钟)"
                    name="lockoutDuration"
                    initialValue={30}
                  >
                    <InputNumber min={5} max={120} />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="会话超时时间(小时)"
                name="sessionTimeout"
                initialValue={8}
              >
                <InputNumber min={1} max={24} />
              </Form.Item>

              <Divider />

              <Title level={4}>
                <KeyOutlined /> 数据安全设置
              </Title>

              <Form.Item
                label="启用数据加密"
                name="enableDataEncryption"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="启用访问日志记录"
                name="enableAccessLogging"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="敏感操作审计"
                name="enableAuditLogging"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="IP访问白名单"
                name="ipWhitelist"
                help="每行一个IP地址或IP段，留空表示不限制"
              >
                <Input.TextArea rows={4} placeholder="***********/24&#10;********&#10;***********/24" />
              </Form.Item>

              <Form.Item
                label="定期安全扫描"
                name="enableSecurityScan"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" loading={loading}>
                    保存安全设置
                  </Button>
                  <Button onClick={() => securityForm.resetFields()}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  )
}

export default Settings
