import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Upload,
  message,
  Typography,
  Row,
  Col,
  Statistic,
  Alert,
  Progress,
  DatePicker,
  Tooltip,
  Divider,
} from 'antd';
import {
  DatabaseOutlined,
  UploadOutlined,
  DownloadOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
  ClearOutlined,
  HistoryOutlined,
  FileTextOutlined,
  UserOutlined,
  ShopOutlined,
  ScheduleOutlined,
  WarningOutlined,
  ReloadOutlined,
  CloudUploadOutlined,
  CloudDownloadOutlined,
} from '@ant-design/icons';
import { useTypedSelector } from '../hooks/useTypedSelector';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;

interface BackupRecord {
  id: string;
  name: string;
  type: 'manual' | 'scheduled' | 'auto';
  status: 'completed' | 'running' | 'failed';
  size: string;
  createdAt: string;
  description?: string;
  tables?: string[];
  progress?: number;
}

interface ImportExportRecord {
  id: string;
  type: 'import' | 'export';
  format: 'csv' | 'json' | 'xlsx';
  status: 'completed' | 'running' | 'failed';
  fileName: string;
  recordCount: number;
  createdAt: string;
  errorMessage?: string;
}

interface SystemStatistics {
  databaseSize: string;
  totalTables: number;
  totalRecords: number;
  lastBackup: string;
  diskUsage: number;
  performanceScore: number;
}

const DataManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [backupRecords, setBackupRecords] = useState<BackupRecord[]>([]);
  const [importExportRecords, setImportExportRecords] = useState<ImportExportRecord[]>([]);
  const [systemStats, setSystemStats] = useState<SystemStatistics>({
    databaseSize: '0 MB',
    totalTables: 0,
    totalRecords: 0,
    lastBackup: '',
    diskUsage: 0,
    performanceScore: 0,
  });
  const [backupModalVisible, setBackupModalVisible] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [cleanupModalVisible, setCleanupModalVisible] = useState(false);
  const [form] = Form.useForm();
  const { user: currentUser } = useTypedSelector(state => state.auth);

  // 模拟数据
  const mockBackupRecords: BackupRecord[] = [
    {
      id: '1',
      name: '每日自动备份_2025-08-01',
      type: 'auto',
      status: 'completed',
      size: '42.5 MB',
      createdAt: '2025-08-01T02:00:00Z',
      description: '系统自动备份',
      tables: ['users', 'cafes', 'batches', 'recipes'],
    },
    {
      id: '2',
      name: '手动备份_升级前',
      type: 'manual',
      status: 'completed',
      size: '38.2 MB',
      createdAt: '2025-07-31T16:30:00Z',
      description: '系统升级前的安全备份',
      tables: ['users', 'cafes', 'batches', 'recipes', 'audit_logs'],
    },
    {
      id: '3',
      name: '周度备份_第31周',
      type: 'scheduled',
      status: 'running',
      size: '',
      createdAt: '2025-08-01T12:00:00Z',
      description: '每周定时备份',
      progress: 65,
    },
  ];

  const mockImportExportRecords: ImportExportRecord[] = [
    {
      id: '1',
      type: 'export',
      format: 'xlsx',
      status: 'completed',
      fileName: '用户数据导出_2025-08-01.xlsx',
      recordCount: 125,
      createdAt: '2025-08-01T10:15:00Z',
    },
    {
      id: '2',
      type: 'import',
      format: 'csv',
      status: 'completed',
      fileName: '新店面数据.csv',
      recordCount: 15,
      createdAt: '2025-07-31T14:20:00Z',
    },
    {
      id: '3',
      type: 'export',
      format: 'json',
      status: 'failed',
      fileName: '批次数据导出.json',
      recordCount: 0,
      createdAt: '2025-07-31T09:45:00Z',
      errorMessage: '数据格式验证失败',
    },
  ];

  const mockSystemStats: SystemStatistics = {
    databaseSize: '156.8 MB',
    totalTables: 12,
    totalRecords: 2847,
    lastBackup: '2025-08-01T02:00:00Z',
    diskUsage: 68,
    performanceScore: 92,
  };

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      setTimeout(() => {
        setBackupRecords(mockBackupRecords);
        setImportExportRecords(mockImportExportRecords);
        setSystemStats(mockSystemStats);
        setLoading(false);
      }, 800);
    } catch (error) {
      message.error('加载数据失败');
      setLoading(false);
    }
  };

  const handleManualBackup = async (values: any) => {
    try {
      message.loading('正在创建备份...', 0);
      // 模拟备份过程
      setTimeout(() => {
        message.destroy();
        message.success('备份创建成功');
        setBackupModalVisible(false);
        loadData();
      }, 3000);
    } catch (error) {
      message.error('备份失败');
    }
  };

  const handleImport = async (values: any) => {
    try {
      message.loading('正在导入数据...', 0);
      setTimeout(() => {
        message.destroy();
        message.success('数据导入成功');
        setImportModalVisible(false);
        loadData();
      }, 2000);
    } catch (error) {
      message.error('导入失败');
    }
  };

  const handleExport = async (values: any) => {
    try {
      message.loading('正在导出数据...', 0);
      setTimeout(() => {
        message.destroy();
        message.success('数据导出成功，请检查下载文件');
        setExportModalVisible(false);
        loadData();
      }, 2000);
    } catch (error) {
      message.error('导出失败');
    }
  };

  const handleDataCleanup = async (values: any) => {
    try {
      message.loading('正在清理数据...', 0);
      setTimeout(() => {
        message.destroy();
        message.success(`清理完成，共清理了 ${Math.floor(Math.random() * 100)} 条过期数据`);
        setCleanupModalVisible(false);
        loadData();
      }, 2500);
    } catch (error) {
      message.error('数据清理失败');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'green';
      case 'running': return 'blue';
      case 'failed': return 'red';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircleOutlined />;
      case 'running': return <SyncOutlined spin />;
      case 'failed': return <ExclamationCircleOutlined />;
      default: return <CheckCircleOutlined />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'manual': return 'orange';
      case 'scheduled': return 'blue';
      case 'auto': return 'green';
      default: return 'default';
    }
  };

  const backupColumns = [
    {
      title: '备份名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: BackupRecord) => (
        <div>
          <div style={{ fontWeight: 500 }}>{name}</div>
          {record.description && (
            <div style={{ fontSize: '12px', color: '#666' }}>{record.description}</div>
          )}
        </div>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: string) => (
        <Tag color={getTypeColor(type)}>
          {type === 'manual' ? '手动' : type === 'scheduled' ? '定时' : '自动'}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string, record: BackupRecord) => (
        <div>
          <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
            {status === 'completed' ? '完成' : status === 'running' ? '进行中' : '失败'}
          </Tag>
          {status === 'running' && record.progress && (
            <Progress percent={record.progress} size="small" style={{ marginTop: 4 }} />
          )}
        </div>
      ),
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (date: string) => dayjs(date).format('MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record: BackupRecord) => (
        <Space>
          {record.status === 'completed' && (
            <Tooltip title="下载备份">
              <Button
                type="text"
                icon={<DownloadOutlined />}
                onClick={() => message.info('下载功能开发中...')}
              />
            </Tooltip>
          )}
          <Tooltip title="删除备份">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => message.info('删除功能开发中...')}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const importExportColumns = [
    {
      title: '操作',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      render: (type: string) => (
        <Tag color={type === 'import' ? 'blue' : 'green'}>
          {type === 'import' ? '导入' : '导出'}
        </Tag>
      ),
    },
    {
      title: '文件名',
      dataIndex: 'fileName',
      key: 'fileName',
    },
    {
      title: '格式',
      dataIndex: 'format',
      key: 'format',
      width: 80,
      render: (format: string) => <Tag>{format.toUpperCase()}</Tag>,
    },
    {
      title: '记录数',
      dataIndex: 'recordCount',
      key: 'recordCount',
      width: 100,
      render: (count: number) => count.toLocaleString(),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string, record: ImportExportRecord) => (
        <div>
          <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
            {status === 'completed' ? '完成' : status === 'running' ? '进行中' : '失败'}
          </Tag>
          {status === 'failed' && record.errorMessage && (
            <Tooltip title={record.errorMessage}>
              <WarningOutlined style={{ color: '#ff4d4f', marginLeft: 4 }} />
            </Tooltip>
          )}
        </div>
      ),
    },
    {
      title: '时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (date: string) => dayjs(date).format('MM-DD HH:mm'),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>数据管理</Title>
        <div style={{ color: '#666', fontSize: '14px' }}>
          数据备份、导入导出、系统维护和性能监控
        </div>
      </div>

      {/* 系统统计 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="数据库大小"
              value={systemStats.databaseSize}
              prefix={<DatabaseOutlined />}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总记录数"
              value={systemStats.totalRecords}
              prefix={<FileTextOutlined />}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="磁盘使用率"
              value={systemStats.diskUsage}
              suffix="%"
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: systemStats.diskUsage > 80 ? '#ff4d4f' : '#52c41a' }}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="性能评分"
              value={systemStats.performanceScore}
              suffix="/100"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: systemStats.performanceScore > 90 ? '#52c41a' : '#faad14' }}
              loading={loading}
            />
          </Card>
        </Col>
      </Row>

      {/* 系统状态警告 */}
      {systemStats.diskUsage > 80 && (
        <Alert
          message="磁盘空间警告"
          description={`磁盘使用率已达到 ${systemStats.diskUsage}%，建议清理过期数据或扩展存储空间`}
          type="warning"
          showIcon
          style={{ marginBottom: '16px' }}
        />
      )}

      {systemStats.lastBackup && dayjs().diff(dayjs(systemStats.lastBackup), 'hours') > 48 && (
        <Alert
          message="备份提醒"
          description={`距离上次备份已超过48小时，建议立即创建备份`}
          type="error"
          showIcon
          style={{ marginBottom: '16px' }}
        />
      )}

      {/* 快速操作 */}
      <Card title="快速操作" style={{ marginBottom: '16px' }}>
        <Space wrap>
          <Button
            type="primary"
            icon={<DatabaseOutlined />}
            onClick={() => setBackupModalVisible(true)}
          >
            创建备份
          </Button>
          <Button
            icon={<CloudUploadOutlined />}
            onClick={() => setImportModalVisible(true)}
          >
            导入数据
          </Button>
          <Button
            icon={<CloudDownloadOutlined />}
            onClick={() => setExportModalVisible(true)}
          >
            导出数据
          </Button>
          <Button
            icon={<ClearOutlined />}
            onClick={() => setCleanupModalVisible(true)}
          >
            数据清理
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadData}
            loading={loading}
          >
            刷新状态
          </Button>
        </Space>
      </Card>

      {/* 备份记录 */}
      <Card
        title={
          <Space>
            <HistoryOutlined />
            备份记录
          </Space>
        }
        style={{ marginBottom: '16px' }}
      >
        <Table
          columns={backupColumns}
          dataSource={backupRecords}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            pageSize: 10,
          }}
          size="small"
        />
      </Card>

      {/* 导入导出记录 */}
      <Card
        title={
          <Space>
            <SyncOutlined />
            导入导出记录
          </Space>
        }
      >
        <Table
          columns={importExportColumns}
          dataSource={importExportRecords}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            pageSize: 10,
          }}
          size="small"
        />
      </Card>

      {/* 创建备份模态框 */}
      <Modal
        title="创建备份"
        open={backupModalVisible}
        onOk={() => form.submit()}
        onCancel={() => setBackupModalVisible(false)}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleManualBackup}
        >
          <Form.Item
            name="name"
            label="备份名称"
            rules={[{ required: true, message: '请输入备份名称' }]}
          >
            <Input placeholder="例如：系统升级前备份" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="备份描述"
          >
            <TextArea rows={3} placeholder="请输入备份描述" />
          </Form.Item>
          
          <Form.Item
            name="tables"
            label="备份范围"
            rules={[{ required: true, message: '请选择备份范围' }]}
          >
            <Select mode="multiple" placeholder="选择要备份的数据表">
              <Option value="users">用户数据</Option>
              <Option value="cafes">店面数据</Option>
              <Option value="batches">批次数据</Option>
              <Option value="recipes">配方数据</Option>
              <Option value="audit_logs">审计日志</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 导入数据模态框 */}
      <Modal
        title="导入数据"
        open={importModalVisible}
        onOk={() => form.submit()}
        onCancel={() => setImportModalVisible(false)}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleImport}
        >
          <Form.Item
            name="dataType"
            label="数据类型"
            rules={[{ required: true, message: '请选择数据类型' }]}
          >
            <Select placeholder="选择要导入的数据类型">
              <Option value="users">用户数据</Option>
              <Option value="cafes">店面数据</Option>
              <Option value="batches">批次数据</Option>
              <Option value="recipes">配方数据</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="file"
            label="选择文件"
            rules={[{ required: true, message: '请选择要导入的文件' }]}
          >
            <Upload
              beforeUpload={() => false}
              accept=".csv,.xlsx,.json"
              maxCount={1}
            >
              <Button icon={<UploadOutlined />}>选择文件</Button>
            </Upload>
          </Form.Item>
          
          <Alert
            message="导入说明"
            description="支持 CSV、Excel、JSON 格式文件，请确保数据格式正确"
            type="info"
            showIcon
          />
        </Form>
      </Modal>

      {/* 导出数据模态框 */}
      <Modal
        title="导出数据"
        open={exportModalVisible}
        onOk={() => form.submit()}
        onCancel={() => setExportModalVisible(false)}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleExport}
        >
          <Form.Item
            name="dataType"
            label="数据类型"
            rules={[{ required: true, message: '请选择数据类型' }]}
          >
            <Select placeholder="选择要导出的数据类型">
              <Option value="users">用户数据</Option>
              <Option value="cafes">店面数据</Option>
              <Option value="batches">批次数据</Option>
              <Option value="recipes">配方数据</Option>
              <Option value="audit_logs">审计日志</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="format"
            label="导出格式"
            rules={[{ required: true, message: '请选择导出格式' }]}
          >
            <Select placeholder="选择导出格式">
              <Option value="xlsx">Excel (.xlsx)</Option>
              <Option value="csv">CSV (.csv)</Option>
              <Option value="json">JSON (.json)</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="dateRange"
            label="时间范围"
          >
            <RangePicker placeholder={['开始时间', '结束时间']} />
          </Form.Item>
          
          {currentUser?.role === 'ADMIN' && (
            <Form.Item
              name="cafeId"
              label="店面筛选"
            >
              <Select placeholder="选择店面（留空表示所有店面）" allowClear>
                <Option value="cafe1">海口电视台店</Option>
                <Option value="cafe2">三亚店</Option>
                <Option value="cafe3">琼海店</Option>
              </Select>
            </Form.Item>
          )}
        </Form>
      </Modal>

      {/* 数据清理模态框 */}
      <Modal
        title="数据清理"
        open={cleanupModalVisible}
        onOk={() => form.submit()}
        onCancel={() => setCleanupModalVisible(false)}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleDataCleanup}
        >
          <Alert
            message="清理警告"
            description="数据清理操作不可逆，请谨慎操作"
            type="warning"
            showIcon
            style={{ marginBottom: '16px' }}
          />
          
          <Form.Item
            name="cleanupType"
            label="清理类型"
            rules={[{ required: true, message: '请选择清理类型' }]}
          >
            <Select placeholder="选择要清理的数据类型">
              <Option value="expired_logs">过期日志（30天前）</Option>
              <Option value="temp_files">临时文件</Option>
              <Option value="duplicate_records">重复记录检测</Option>
              <Option value="inactive_users">非活跃用户数据</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="confirm"
            label="确认清理"
            rules={[{ required: true, message: '请输入确认文本' }]}
          >
            <Input placeholder="请输入 'CONFIRM' 确认清理操作" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default DataManagement;