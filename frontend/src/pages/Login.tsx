import React, { useState, useEffect } from 'react'
import { Form, Input, Button, message, Select } from 'antd'
import { UserOutlined, LockOutlined } from '@ant-design/icons'
import { useNavigate, useLocation } from 'react-router-dom'
import { useDispatch } from 'react-redux'
import { login } from '../store/slices/authSlice'
import LoadingSpinner from '../components/LoadingSpinner'
import { useTypedSelector } from '../hooks/useTypedSelector'
import { apiClient } from '../services/api'
import './Login.css'

const { Option } = Select

interface LoginFormData {
  identifier: string
  password: string
  cafeId: string
}



interface Cafe {
  id: string
  name: string
  description: string
  address: string
}

const Login: React.FC = () => {
  const [form] = Form.useForm()
  const [cafes, setCafes] = useState<Cafe[]>([])
  const [loadingCafes, setLoadingCafes] = useState(false)
  const [attemptedAdminLogin, setAttemptedAdminLogin] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()
  const dispatch = useDispatch()
  const { isLoading } = useTypedSelector(state => state.auth)

  useEffect(() => {
    fetchCafes()
  }, [])

  const fetchCafes = async () => {
    setLoadingCafes(true)
    try {
      const response = await apiClient.get('/api/auth/cafes')
      if (response.data?.success && response.data?.data?.cafes) {
        setCafes(response.data.data.cafes)
      } else {
        message.error('无法获取咖啡厅列表，请联系管理员')
        setCafes([])
      }
    } catch (error) {
      console.error('获取咖啡厅列表失败:', error)
      message.error('网络连接失败，请检查后端服务是否启动')
      setCafes([])
    } finally {
      setLoadingCafes(false)
    }
  }

  // 重置登录状态（当用户修改用户名或密码时）
  const resetLoginState = () => {
    setAttemptedAdminLogin(false)
  }

  const handleSubmit = async (values: LoginFormData) => {
    try {
      // 如果没有提供店面，且没有尝试过管理员登录，先尝试总管理员登录
      if (!values.cafeId && !attemptedAdminLogin) {
        setAttemptedAdminLogin(true)
        
        try {
          const adminLoginData = {
            identifier: values.identifier,
            password: values.password
          }
          await dispatch(login(adminLoginData) as any)
          const from = location.state?.from?.pathname || '/dashboard'
          navigate(from)
          return
        } catch (adminError: any) {
          // 如果总管理员登录失败，提示用户选择店面
          if (adminError.response?.status === 400 && 
              (adminError.response?.data?.message === '请选择店面' || 
               adminError.response?.data?.error?.message === '请选择店面')) {
            message.warning('请选择您所属的店面')
            return
          }
          // 其他错误（如密码错误）直接抛出
          throw adminError
        }
      }

      // 正常登录流程（必须选择店面）
      if (!values.cafeId) {
        message.error('请选择店面')
        return
      }

      const loginData = {
        identifier: values.identifier,
        password: values.password,
        cafeId: values.cafeId
      }
      await dispatch(login(loginData) as any)
      const from = location.state?.from?.pathname || '/dashboard'
      navigate(from)
    } catch (error) {
      console.error('登录失败:', error)
    }
  }

  if (isLoading) {
    return <LoadingSpinner />
  }

  return (
    <div className="login-container">
      <div className="login-wrapper">
        <div className="login-left-panel">
        <div className="left-panel-content">
          <div className="system-logo">
            <div className="logo-circle">
              <span className="logo-icon">🥛</span>
              <span className="logo-plus">+</span>
              <span className="logo-ai">🧠</span>
              <div className="logo-leaf">🌿</div>
            </div>
          </div>
          
          <div className="system-name">
            <h1 className="system-title">酸奶质控</h1>
            <h2 className="system-subtitle">AI智能分析系统</h2>
          </div>
          
          <div className="system-tagline">
            YOGURT QUALITY CONTROL INTELLIGENT INSIGHT SYSTEM
          </div>
          
          <div className="system-description">
            基于人工智能的酸奶品质与活菌成分综合分析平台
          </div>
          
          <div className="feature-pills">
            <div className="feature-pill">
              <span className="pill-icon">🔬</span>
              <span>智能分析</span>
            </div>
            <div className="feature-pill">
              <span className="pill-icon">📊</span>
              <span>数据安全</span>
            </div>
            <div className="feature-pill">
              <span className="pill-icon">⚡</span>
              <span>专业服务</span>
            </div>
          </div>  
        </div>
      </div>
      
      <div className="login-right-panel">
        <div className="login-form-container">
          <div className="form-header">
            <h2>欢迎！</h2>
            <h1>酸奶质控AI系统</h1>
            <p className="subtitle">Yogurt Quality Control AI System</p>
            <p className="welcome-text">登录酸奶质控AI系统开始您的智能分析之旅</p>
          </div>



          <Form
             form={form}
             name="loginForm"
             onFinish={handleSubmit}
             onValuesChange={(changedValues) => {
               if (changedValues.identifier !== undefined || changedValues.password !== undefined) {
                 resetLoginState()
               }
             }}
             layout="vertical"
             size="large"
             autoComplete="off"
             className="login-form"
           >
             <Form.Item
               name="identifier"
               label="用户名或邮箱"
               rules={[
                 { required: true, message: '请输入用户名或邮箱' }
               ]}
             >
               <Input
                 prefix={<UserOutlined />}
                 placeholder="请输入用户名或邮箱"
                 autoComplete="username"
               />
             </Form.Item>

             <Form.Item
               name="cafeId"
               label="选择店面"
               rules={[{ required: false, message: '请选择店面' }]}
               extra="总管理员可不选择店面直接登录"
             >
               <Select
                 placeholder={cafes.length === 0 && !loadingCafes ? "暂无可选店面" : "请选择店面"}
                 loading={loadingCafes}
                 showSearch
                 optionFilterProp="children"
                 disabled={cafes.length === 0 && !loadingCafes}
                 allowClear
               >
                 {cafes.map(cafe => (
                   <Option key={cafe.id} value={cafe.id}>
                     {cafe.name}
                   </Option>
                 ))}
               </Select>
             </Form.Item>

             <Form.Item
               name="password"
               label="密码"
               rules={[
                 { required: true, message: '请输入密码' },
                 { min: 6, message: '密码至少6位' }
               ]}
             >
               <Input.Password
                 prefix={<LockOutlined />}
                 placeholder="请输入密码"
                 autoComplete="current-password"
               />
             </Form.Item>

            <Form.Item>
               <Button
                 type="primary"
                 htmlType="submit"
                 loading={isLoading}
                 block
                 size="large"
                 className="submit-button"
               >
                 登录
               </Button>
             </Form.Item>
           </Form>

           <div className="login-footer">
             <button type="button" className="forgot-password link-button" onClick={() => message.info('请联系管理员重置密码')}>
               忘记密码？
             </button>
             <span className="footer-divider">|</span>
             <button type="button" className="register-link link-button" onClick={() => message.info('请联系管理员创建账号')}>
               立即注册
             </button>
           </div>

           <div className="copyright-info">
             © 2025 海南长小养智能科技有限责任公司. 保留所有权利.
           </div>
        </div>
      </div>
      </div>
    </div>
  )
}

export default Login
