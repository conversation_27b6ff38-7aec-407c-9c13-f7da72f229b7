import React, { useEffect, useState } from 'react'
import {
  Card,
  Table,
  Button,
  Input,
  Space,
  Tag,
  Dropdown,
  Modal,
  Typography,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Upload,
  Progress,
  Tooltip,
  message
} from 'antd'
import {
  PlusOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  UploadOutlined,
  ReloadOutlined,
  ExportOutlined,
  CameraOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { batchAPI } from '../services/api/batch'
import LoadingSpinner from '../components/LoadingSpinner'
import type { UploadProps } from 'antd'

const { Title, Text } = Typography
const { Search } = Input
const { Option } = Select
const { RangePicker } = DatePicker

interface Batch {
  id: string
  batchNumber: string
  recipeId: string
  userId: string
  status: 'PLANNING' | 'IN_PROGRESS' | 'FERMENTING' | 'FILTERING' | 'COMPLETED' | 'FAILED'
  productionDate: string
  quantity?: number
  actualFermentationDuration?: number
  actualTemperature?: number
  notes?: string
  createdAt: string
  updatedAt: string
  recipe?: {
    id: string
    name: string
    version: number
  }
  user?: {
    id: string
    name: string
    email: string
  }
  microscopeImages?: Array<{
    id: string
    filename: string
    originalName: string
    createdAt: string
  }>
  _count?: {
    sensoryAssessments: number
    aiAnalyses: number
  }
}

const Batches: React.FC = () => {
  const [loading, setLoading] = useState(true)
  const [batches, setBatches] = useState<Batch[]>([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })
  const [searchText, setSearchText] = useState('')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [uploadModalVisible, setUploadModalVisible] = useState(false)
  const [currentBatchId, setCurrentBatchId] = useState<string>('')

  const navigate = useNavigate()

  // 加载批次数据
  const loadBatches = async (params?: any) => {
    try {
      setLoading(true)
      const queryParams = {
        page: params?.current || pagination.current,
        limit: params?.pageSize || pagination.pageSize,
        search: searchText || undefined,
        status: filterStatus === 'all' ? undefined : filterStatus
      }
      
      const response = await batchAPI.getBatches(queryParams)
      if (response.data.success) {
        setBatches(response.data.data.batches)
        setPagination({
          current: response.data.data.pagination.page,
          pageSize: response.data.data.pagination.limit,
          total: response.data.data.pagination.total
        })
      }
    } catch (error) {
      message.error('加载批次列表失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadBatches()
  }, [searchText, filterStatus])

  // 处理删除批次
  const handleDelete = (id: string, batchNumber: string) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除批次"${batchNumber}"吗？此操作不可恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await batchAPI.deleteBatch(id)
          if (response.data.success) {
            message.success('批次删除成功')
            loadBatches() // 重新加载列表
          } else {
            message.error(response.data.message || '删除失败')
          }
        } catch (error: any) {
          message.error(error.response?.data?.message || '删除失败')
        }
      },
    })
  }

  // 处理状态更新
  const handleStatusUpdate = async (id: string, status: string) => {
    try {
      const response = await batchAPI.updateBatchStatus(id, status as any)
      if (response.data.success) {
        message.success('状态更新成功')
        loadBatches() // 重新加载列表
      } else {
        message.error(response.data.message || '状态更新失败')
      }
    } catch (error: any) {
      message.error(error.response?.data?.message || '状态更新失败')
    }
  }

  // 状态标签颜色
  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'PLANNING': 'default',
      'IN_PROGRESS': 'processing',
      'FERMENTING': 'orange',
      'FILTERING': 'cyan',
      'COMPLETED': 'success',
      'FAILED': 'error'
    }
    return colors[status] || 'default'
  }

  // 状态中文名
  const getStatusText = (status: string) => {
    const texts: Record<string, string> = {
      'PLANNING': '计划中',
      'IN_PROGRESS': '进行中',
      'FERMENTING': '发酵中',
      'FILTERING': '过滤中',
      'COMPLETED': '已完成',
      'FAILED': '失败'
    }
    return texts[status] || status
  }

  // 获取状态进度
  const getStatusProgress = (status: string) => {
    const progress: Record<string, number> = {
      'PLANNING': 10,
      'IN_PROGRESS': 30,
      'FERMENTING': 60,
      'FILTERING': 80,
      'COMPLETED': 100,
      'FAILED': 0
    }
    return progress[status] || 0
  }

  // 操作菜单
  const getActionMenu = (record: Batch) => ({
    items: [
      {
        key: 'view',
        icon: <EyeOutlined />,
        label: '查看详情',
        onClick: () => navigate(`/batches/${record.id}`),
      },
      {
        key: 'edit',
        icon: <EditOutlined />,
        label: '编辑批次',
        onClick: () => navigate(`/batches/${record.id}/edit`),
      },
      {
        key: 'upload',
        icon: <CameraOutlined />,
        label: '上传图片',
        onClick: () => {
          setCurrentBatchId(record.id)
          setUploadModalVisible(true)
        },
      },
      {
        type: 'divider' as const,
      },
      {
        key: 'status',
        label: '更新状态',
        children: [
          {
            key: 'IN_PROGRESS',
            label: '开始生产',
            onClick: () => handleStatusUpdate(record.id, 'IN_PROGRESS'),
          },
          {
            key: 'FERMENTING',
            label: '开始发酵',
            onClick: () => handleStatusUpdate(record.id, 'FERMENTING'),
          },
          {
            key: 'FILTERING',
            label: '开始过滤',
            onClick: () => handleStatusUpdate(record.id, 'FILTERING'),
          },
          {
            key: 'COMPLETED',
            label: '标记完成',
            onClick: () => handleStatusUpdate(record.id, 'COMPLETED'),
          },
        ],
      },
      {
        type: 'divider' as const,
      },
      {
        key: 'delete',
        icon: <DeleteOutlined />,
        label: '删除批次',
        danger: true,
        onClick: () => handleDelete(record.id, record.batchNumber),
      },
    ],
  })

  // 图片上传配置
  const uploadProps: UploadProps = {
    name: 'images',
    action: `/api/upload/batch-images/${currentBatchId}`,
    headers: {
      authorization: `Bearer ${localStorage.getItem('token')}`,
    },
    multiple: true,
    accept: 'image/*',
    onChange(info) {
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 上传成功`)
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`)
      }
    },
  }

  // 格式化时间显示
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).replace(/\//g, '-')
  }

  // 表格列配置
  const columns = [
    {
      title: '批次号',
      dataIndex: 'batchNumber',
      key: 'batchNumber',
      width: 140,
      render: (text: string, record: Batch) => (
        <div style={{ whiteSpace: 'nowrap' }}>
          <Button 
            type="link" 
            onClick={() => navigate(`/batches/${record.id}`)}
            style={{ padding: 0, color: '#F05654', fontWeight: 500 }}
          >
            {text}
          </Button>
        </div>
      ),
    },
    {
      title: '配方',
      dataIndex: 'recipe',
      key: 'recipe',
      width: 180,
      render: (recipe: any) => (
        <div>
          <div style={{ marginBottom: '2px' }}>
            <Text strong style={{ fontSize: '13px' }}>{recipe?.name || '-'}</Text>
          </div>
          {recipe?.version && (
            <Tag color="blue" size="small">v{recipe.version}</Tag>
          )}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 130,
      render: (status: string) => (
        <div>
          <div style={{ marginBottom: '4px' }}>
            <Tag color={getStatusColor(status)} style={{ fontSize: '11px' }}>
              {getStatusText(status)}
            </Tag>
          </div>
          <Progress 
            percent={getStatusProgress(status)} 
            size="small" 
            showInfo={false}
            strokeColor={status === 'FAILED' ? '#ff4d4f' : undefined}
          />
        </div>
      ),
    },
    {
      title: '生产日期',
      dataIndex: 'productionDate',
      key: 'productionDate',
      width: 110,
      render: (date: string) => (
        <div style={{ whiteSpace: 'nowrap', fontSize: '12px' }}>
          {new Date(date).toLocaleDateString('zh-CN').replace(/\//g, '/')}
        </div>
      ),
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
      render: (quantity: number) => (
        <div style={{ whiteSpace: 'nowrap' }}>
          {quantity ? `${quantity}L` : '-'}
        </div>
      ),
    },
    {
      title: '图片数',
      dataIndex: 'microscopeImages',
      key: 'images',
      width: 80,
      render: (images: any[]) => (
        <Tooltip title="点击查看图片">
          <Button 
            type="text" 
            size="small"
            icon={<CameraOutlined />}
            onClick={() => {
              // 这里可以打开图片预览
            }}
          >
            {images?.length || 0}
          </Button>
        </Tooltip>
      ),
    },
    {
      title: '评估',
      dataIndex: '_count',
      key: 'assessments',
      width: 70,
      render: (count: any) => (
        <div style={{ textAlign: 'center' }}>
          <Text style={{ fontSize: 12 }}>{count?.sensoryAssessments || 0}</Text>
        </div>
      ),
    },
    {
      title: '创建者',
      dataIndex: 'user',
      key: 'user',
      width: 120,
      render: (user: any) => (
        <div style={{ whiteSpace: 'nowrap' }}>
          {user?.name || '-'}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      fixed: 'right' as const,
      render: (_: any, record: Batch) => (
        <Dropdown menu={getActionMenu(record)} trigger={['click']}>
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ]

  // 统计数据
  const stats = {
    total: pagination.total,
    completed: batches.filter(b => b.status === 'COMPLETED').length,
    inProgress: batches.filter(b => ['IN_PROGRESS', 'FERMENTING', 'FILTERING'].includes(b.status)).length,
    failed: batches.filter(b => b.status === 'FAILED').length
  }

  if (loading && batches.length === 0) {
    return <LoadingSpinner tip="加载批次数据..." />
  }

  return (
    <div className="batches-container">
      {/* 页面头部 */}
      <div className="batches-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <div>
          <Title level={2}>批次管理</Title>
          <Text type="secondary">管理和跟踪酸奶生产批次</Text>
        </div>
        <Space>
          <Button icon={<ReloadOutlined />} onClick={() => loadBatches()}>刷新</Button>
          <Button icon={<ExportOutlined />}>导出</Button>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => navigate('/batches/new')}
          >
            新建批次
          </Button>
        </Space>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={12} sm={6}>
          <Card variant="outlined">
            <Statistic
              title="批次总数"
              value={stats.total}
              prefix={<CheckCircleOutlined style={{ color: '#F05654' }} />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card variant="outlined">
            <Statistic
              title="已完成"
              value={stats.completed}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card variant="outlined">
            <Statistic
              title="进行中"
              value={stats.inProgress}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card variant="outlined">
            <Statistic
              title="失败"
              value={stats.failed}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card variant="outlined" style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜索批次号或配方名称"
              allowClear
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Select
              value={filterStatus}
              onChange={setFilterStatus}
              style={{ width: '100%' }}
              placeholder="筛选状态"
            >
              <Option value="all">全部状态</Option>
              <Option value="PLANNING">计划中</Option>
              <Option value="IN_PROGRESS">进行中</Option>
              <Option value="FERMENTING">发酵中</Option>
              <Option value="FILTERING">过滤中</Option>
              <Option value="COMPLETED">已完成</Option>
              <Option value="FAILED">失败</Option>
            </Select>
          </Col>
          <Col xs={24} md={10}>
            <div>
              <Text type="secondary">
                共找到 {pagination.total} 个批次
              </Text>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 批次表格 */}
      <Card variant="outlined">
        <Table
          columns={columns}
          dataSource={batches}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              loadBatches({ current: page, pageSize })
            }
          }}
          scroll={{ x: 1400 }}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
        />
      </Card>

      {/* 图片上传模态框 */}
      <Modal
        title="上传批次图片"
        open={uploadModalVisible}
        onCancel={() => setUploadModalVisible(false)}
        footer={null}
        width={600}
      >
        <div style={{ margin: '20px 0' }}>
          <Text type="secondary">支持上传多张图片，包括发酵过程和成品图片</Text>
        </div>
        <Upload.Dragger {...uploadProps}>
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持单次或批量上传。严格禁止上传公司数据或其他敏感文件。
          </p>
        </Upload.Dragger>
      </Modal>
    </div>
  )
}

export default Batches
