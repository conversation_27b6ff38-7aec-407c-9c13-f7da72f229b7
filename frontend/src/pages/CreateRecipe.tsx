import React, { useState } from 'react'
import {
  Card,
  Form,
  Input,
  InputNumber,
  Button,
  Space,
  Row,
  Col,
  Table,
  Select,
  Switch,
  Upload,
  Image,
  Modal,
  Tooltip
} from 'antd'
import { 
  ArrowLeftOutlined, 
  PlusOutlined, 
  DeleteOutlined, 
  SaveOutlined,
  PictureOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { useDispatch } from 'react-redux'
import { createRecipe } from '../store/slices/recipeSlice'
import useMessage from '../hooks/useMessage'
import type { Ingredient, CreateRecipeForm } from '../types'

// const { Title } = Typography
const { TextArea } = Input
const { Option } = Select

const CreateRecipe: React.FC = () => {
  const [form] = Form.useForm()
  const [ingredients, setIngredients] = useState<Ingredient[]>([])
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [previewVisible, setPreviewVisible] = useState(false)
  const [previewImage, setPreviewImage] = useState('')
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const message = useMessage()

  // 添加配料
  const addIngredient = () => {
    const newIngredient: Ingredient = {
      name: '',
      amount: 0,
      unit: 'ml',
      price: 0,
      totalCost: 0
    }
    setIngredients([...ingredients, newIngredient])
  }

  // 删除配料
  const removeIngredient = (index: number) => {
    const newIngredients = ingredients.filter((_, i) => i !== index)
    setIngredients(newIngredients)
  }

  // 更新配料
  const updateIngredient = (index: number, field: keyof Ingredient, value: any) => {
    const newIngredients = [...ingredients]
    newIngredients[index] = { ...newIngredients[index], [field]: value }
    
    // 如果更新了数量或单价，自动计算总成本
    if (field === 'amount' || field === 'price') {
      const amount = field === 'amount' ? value : newIngredients[index].amount
      const price = field === 'price' ? value : newIngredients[index].price || 0
      newIngredients[index].totalCost = amount * price
    }
    
    setIngredients(newIngredients)
  }

  // 处理配料图片上传
  const handleIngredientImageUpload = async (file: File, index: number) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/webp'
    if (!isJpgOrPng) {
      message.error('只支持上传 JPG/PNG/WEBP 格式的图片!')
      return false
    }
    const isLt2M = file.size / 1024 / 1024 < 2
    if (!isLt2M) {
      message.error('图片大小不能超过 2MB!')
      return false
    }

    try {
      setUploading(true)
      // 暂时使用本地URL模拟
      const imageUrl = URL.createObjectURL(file)
      updateIngredient(index, 'packageImage', imageUrl)
      message.success('图片上传成功')
    } catch (error) {
      message.error('图片上传失败')
    } finally {
      setUploading(false)
    }
    
    return false
  }

  // 删除配料图片
  const handleDeleteIngredientImage = (index: number) => {
    updateIngredient(index, 'packageImage', undefined)
    message.success('图片已删除')
  }

  // 预览图片
  const handlePreview = (imageUrl: string) => {
    setPreviewImage(imageUrl)
    setPreviewVisible(true)
  }

  // 配料表格列
  const ingredientColumns = [
    {
      title: '配料名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (value: string, _: Ingredient, index: number) => (
        <Input
          value={value}
          onChange={(e) => updateIngredient(index, 'name', e.target.value)}
          placeholder="请输入配料名称"
        />
      ),
    },
    {
      title: '用量',
      dataIndex: 'amount',
      key: 'amount',
      width: 100,
      render: (value: number, _: Ingredient, index: number) => (
        <InputNumber
          value={value}
          onChange={(val) => updateIngredient(index, 'amount', val || 0)}
          min={0}
          style={{ width: '100%' }}
        />
      ),
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      width: 100,
      render: (value: string, _: Ingredient, index: number) => (
        <Select
          value={value}
          onChange={(val) => updateIngredient(index, 'unit', val)}
          style={{ width: '100%' }}
        >
          <Option value="ml">毫升</Option>
          <Option value="l">升</Option>
          <Option value="g">克</Option>
          <Option value="kg">千克</Option>
          <Option value="包">包</Option>
          <Option value="个">个</Option>
        </Select>
      ),
    },
    {
      title: '单价(元)',
      dataIndex: 'price',
      key: 'price',
      width: 120,
      render: (value: number, _: Ingredient, index: number) => (
        <InputNumber
          value={value}
          onChange={(val) => updateIngredient(index, 'price', val || 0)}
          min={0}
          precision={2}
          style={{ width: '100%' }}
          placeholder="0.00"
        />
      ),
    },
    {
      title: '总成本(元)',
      dataIndex: 'totalCost',
      key: 'totalCost',
      width: 120,
      render: (value: number) => (
        <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
          ¥{(value || 0).toFixed(2)}
        </span>
      ),
    },
    {
      title: '包装图片',
      dataIndex: 'packageImage',
      key: 'packageImage',
      width: 120,
      render: (value: string, _: Ingredient, index: number) => (
        <div style={{ textAlign: 'center' }}>
          {value ? (
            <Space>
              <Tooltip title="点击预览">
                <Image
                  src={value}
                  width={40}
                  height={40}
                  style={{ cursor: 'pointer', objectFit: 'cover' }}
                  onClick={() => handlePreview(value)}
                  preview={false}
                />
              </Tooltip>
              <Button
                type="text"
                danger
                size="small"
                icon={<DeleteOutlined />}
                onClick={() => handleDeleteIngredientImage(index)}
              />
            </Space>
          ) : (
            <Upload
              accept="image/*"
              showUploadList={false}
              beforeUpload={(file) => handleIngredientImageUpload(file, index)}
              disabled={uploading}
            >
              <Button 
                size="small" 
                icon={<PictureOutlined />}
                loading={uploading}
              >
                上传
              </Button>
            </Upload>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_: any, __: Ingredient, index: number) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => removeIngredient(index)}
        />
      ),
    },
  ]

  // 提交表单
  const handleSubmit = async (values: any) => {
    if (ingredients.length === 0) {
      message.error('请至少添加一种配料')
      return
    }

    // 验证配料信息
    const invalidIngredients = ingredients.filter(ing => !ing.name || ing.amount <= 0)
    if (invalidIngredients.length > 0) {
      message.error('请完善配料信息')
      return
    }

    setLoading(true)
    try {
      const recipeData: CreateRecipeForm = {
        ...values,
        ingredients
      }
      
      await dispatch(createRecipe(recipeData) as any)
      message.success('配方创建成功')
      navigate('/recipes')
    } catch (error) {
      console.error('创建配方失败:', error)
      message.error(error instanceof Error ? error.message : '创建失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div>
      {/* 页面头部 */}
      <div style={{ marginBottom: 24 }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/recipes')}
          >
            返回配方列表
          </Button>
        </Space>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          fermentationTemperature: 42,
          fermentationDuration: 8,
          isActive: true
        }}
      >
        <Row gutter={24}>
          <Col xs={24} lg={16}>
            {/* 基本信息 */}
            <Card title="基本信息" style={{ marginBottom: 24 }}>
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="name"
                    label="配方名称"
                    rules={[{ required: true, message: '请输入配方名称' }]}
                  >
                    <Input placeholder="请输入配方名称" />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="isActive"
                    label="启用状态"
                    valuePropName="checked"
                  >
                    <Switch checkedChildren="启用" unCheckedChildren="停用" />
                  </Form.Item>
                </Col>
              </Row>
              
              <Form.Item
                name="description"
                label="配方描述"
              >
                <TextArea 
                  rows={3} 
                  placeholder="请输入配方描述" 
                />
              </Form.Item>
            </Card>

            {/* 配料信息 */}
            <Card 
              title="配料信息" 
              extra={
                <Space>
                  <span style={{ marginRight: 16 }}>
                    总成本: <span style={{ color: '#ff4d4f', fontSize: '18px', fontWeight: 'bold' }}>
                      ¥{ingredients.reduce((sum, item) => sum + (item.totalCost || 0), 0).toFixed(2)}
                    </span>
                  </span>
                  <Button 
                    type="primary" 
                    icon={<PlusOutlined />}
                    onClick={addIngredient}
                  >
                    添加配料
                  </Button>
                </Space>
              }
              style={{ marginBottom: 24 }}
            >
              <Table
                columns={ingredientColumns}
                dataSource={ingredients.map((item, index) => ({ ...item, key: `ingredient-${index}` }))}
                pagination={false}
                rowKey="key"
                locale={{ emptyText: '暂无配料，请点击"添加配料"按钮添加' }}
                scroll={{ x: 800 }}
              />
            </Card>

            {/* 制作说明 */}
            <Card title="制作说明" style={{ marginBottom: 24 }}>
              <Form.Item
                name="instructions"
                label="制作步骤"
              >
                <TextArea 
                  rows={6} 
                  placeholder="请详细描述制作步骤和注意事项" 
                />
              </Form.Item>
              
              <Form.Item
                name="notes"
                label="备注信息"
              >
                <TextArea 
                  rows={3} 
                  placeholder="其他需要说明的信息" 
                />
              </Form.Item>
            </Card>
          </Col>

          <Col xs={24} lg={8}>
            {/* 发酵参数 */}
            <Card title="发酵参数" style={{ marginBottom: 24 }}>
              <Form.Item
                name="fermentationTemperature"
                label="发酵温度 (°C)"
                rules={[{ required: true, message: '请输入发酵温度' }]}
              >
                <InputNumber
                  min={30}
                  max={50}
                  style={{ width: '100%' }}
                  placeholder="建议 40-45°C"
                />
              </Form.Item>
              
              <Form.Item
                name="fermentationDuration"
                label="发酵时长 (小时)"
                rules={[{ required: true, message: '请输入发酵时长' }]}
              >
                <InputNumber
                  min={1}
                  max={24}
                  style={{ width: '100%' }}
                  placeholder="建议 6-12小时"
                />
              </Form.Item>
            </Card>

            {/* 操作按钮 */}
            <Card>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={<SaveOutlined />}
                  block
                  size="large"
                >
                  保存配方
                </Button>
                <Button
                  block
                  onClick={() => form.resetFields()}
                >
                  重置表单
                </Button>
              </Space>
            </Card>
          </Col>
        </Row>
      </Form>

      {/* 图片预览Modal */}
      <Modal
        open={previewVisible}
        title="包装图片预览"
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        width="60%"
        style={{ maxWidth: '600px' }}
      >
        <Image
          alt="包装图片"
          style={{ width: '100%' }}
          src={previewImage}
        />
      </Modal>
    </div>
  )
}

export default CreateRecipe
