import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Typography,
  Tag,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  message,
  Divider,
  Row,
  Col,
  Popconfirm,
  Badge,
  Switch,
  Upload,
  Image
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ShopOutlined,
  TagOutlined,
  DollarOutlined,
  InfoCircleOutlined,
  UploadOutlined,
  PictureOutlined
} from '@ant-design/icons'
import { apiClient } from '../services/api'
import { useTypedSelector } from '../hooks/useTypedSelector'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select

interface Product {
  id: string
  name: string
  type: string
  description: string
  price: number
  volume: string
  status: 'active' | 'inactive' | 'discontinued'
  ingredients?: string[]
  nutrition_per_100g?: Record<string, string>
  benefits?: string[]
  certifications?: string[]
  shelf_life_days: number
  images?: Array<{
    id?: string
    filename?: string
    originalName?: string
    imageUrl?: string
    // 兼容数据库现有格式
    url?: string
    alt?: string
  }>
  creator_name: string
  cafe_name: string
  created_at: string
  updated_at: string
}

const ProductManagement: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)
  const [form] = Form.useForm()
  const { user } = useTypedSelector((state) => state.auth)
  const [uploadLoading, setUploadLoading] = useState(false)
  const [uploadedImages, setUploadedImages] = useState<Array<{
    id?: string
    filename?: string
    originalName?: string
    imageUrl?: string
    // 兼容数据库现有格式
    url?: string
    alt?: string
  }>>([])
  const [previewVisible, setPreviewVisible] = useState(false)
  const [previewImage, setPreviewImage] = useState('')

  // 产品类型选项
  const productTypes = [
    '原味系列',
    '果味系列',
    '椰子系列',
    '热带水果系列',
    '浓稠系列',
    '低脂系列',
    '有机系列',
    '特色系列'
  ]

  // 预定义选项
  const commonIngredients = [
    '生牛乳', '乳酸菌', '白砂糖', '蓝莓果粒', '草莓果粒', 
    '芒果果粒', '椰子片', '燕麦', '蜂蜜', '香草精'
  ]

  const commonBenefits = [
    '补充益生菌', '促进消化', '增强免疫力', '抗氧化', 
    '护眼明目', '美容养颜', '补充蛋白质', '强化骨骼'
  ]

  const commonCertifications = [
    '有机认证', 'ISO9001', '绿色食品', 'HACCP', 
    'FDA认证', '无添加剂', '非转基因'
  ]

  // 加载产品列表
  const loadProducts = async () => {
    try {
      setLoading(true)
      const response = await apiClient.get('/api/products')
      if (response.data.success) {
        setProducts(response.data.data.products)
      }
    } catch (error) {
      console.error('Failed to load products:', error)
      message.error('加载产品列表失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadProducts()
  }, [])

  // 打开添加产品弹窗
  const handleAdd = () => {
    setEditingProduct(null)
    form.resetFields()
    form.setFieldsValue({
      status: 'active',
      shelfLifeDays: 21,
      ingredients: [],
      benefits: [],
      certifications: []
    })
    setModalVisible(true)
  }

  // 打开编辑产品弹窗
  const handleEdit = (product: Product) => {
    setEditingProduct(product)

    // 过滤掉空值和无效值
    const filterEmptyValues = (arr: string[] | undefined): string[] => {
      if (!arr) return []
      return arr.filter(item => {
        // 确保item是字符串类型且不为空
        return item && typeof item === 'string' && item.trim() !== ''
      })
    }

    form.setFieldsValue({
      ...product,
      shelfLifeDays: product.shelf_life_days,
      nutritionPer100g: product.nutrition_per_100g || {},
      ingredients: filterEmptyValues(product.ingredients),
      benefits: filterEmptyValues(product.benefits),
      certifications: filterEmptyValues(product.certifications)
    })
    // 加载现有图片
    if (product.images && product.images.length > 0) {
      setUploadedImages(product.images)
    } else {
      setUploadedImages([])
    }
    setModalVisible(true)
  }

  // 保存产品
  const handleSave = async (values: any) => {
    try {
      const productData = {
        ...values,
        shelfLifeDays: values.shelfLifeDays,
        nutritionPer100g: values.nutritionPer100g || {},
        ingredients: values.ingredients || [],
        benefits: values.benefits || [],
        certifications: values.certifications || [],
        images: uploadedImages
      }

      if (editingProduct) {
        // 更新产品
        await apiClient.put(`/api/products/${editingProduct.id}`, productData)
        message.success('产品更新成功')
      } else {
        // 创建产品
        await apiClient.post('/api/products', productData)
        message.success('产品创建成功')
      }

      setModalVisible(false)
      resetFormAndImages()
      loadProducts()
    } catch (error: any) {
      console.error('Failed to save product:', error)
      message.error(error.response?.data?.message || '保存产品失败')
    }
  }

  // 删除产品
  const handleDelete = async (productId: string) => {
    try {
      await apiClient.delete(`/api/products/${productId}`)
      message.success('产品删除成功')
      loadProducts()
    } catch (error: any) {
      console.error('Failed to delete product:', error)
      message.error(error.response?.data?.message || '删除产品失败')
    }
  }

  // 切换产品状态
  const handleStatusToggle = async (product: Product) => {
    try {
      const newStatus = product.status === 'active' ? 'inactive' : 'active'
      await apiClient.put(`/api/products/${product.id}`, { status: newStatus })
      message.success(`产品已${newStatus === 'active' ? '激活' : '停用'}`)
      loadProducts()
    } catch (error: any) {
      console.error('Failed to toggle product status:', error)
      message.error('状态切换失败')
    }
  }

  // 图片上传处理函数
  const handleImageUpload = async (file: File) => {
    setUploadLoading(true)
    try {
      const formData = new FormData()
      formData.append('images', file)

      const token = localStorage.getItem('token')
      const baseURL = import.meta.env.DEV ? '' : (import.meta.env.VITE_API_URL || 'http://localhost:3010')
      const response = await fetch(`${baseURL}/api/upload/product-images`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      })

      const result = await response.json()
      
      if (result.success) {
        const newImages = result.data.images
        setUploadedImages(prev => [...prev, ...newImages])
        message.success('图片上传成功')
        return true
      } else {
        message.error('图片上传失败：' + result.message)
        return false
      }
    } catch (error) {
      console.error('Image upload error:', error)
      message.error('图片上传失败')
      return false
    } finally {
      setUploadLoading(false)
    }
  }

  // 图片预览
  const handlePreview = (imageUrl: string) => {
    setPreviewImage(imageUrl)
    setPreviewVisible(true)
  }

  // 删除图片
  const handleImageRemove = (imageId: string) => {
    setUploadedImages(prev => prev.filter(img => img.id !== imageId))
  }

  // 重置表单和图片
  const resetFormAndImages = () => {
    form.resetFields()
    setUploadedImages([])
    setEditingProduct(null)
  }

  // 关闭弹窗
  const handleCancel = () => {
    setModalVisible(false)
    resetFormAndImages()
  }

  const columns = [
    {
      title: '产品名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Product) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Tag color="blue">{record.type}</Tag>
        </div>
      )
    },
    {
      title: '产品图片',
      key: 'images',
      width: 120,
      render: (record: Product) => (
        <div>
          {record.images && record.images.length > 0 ? (
            <div style={{ display: 'flex', gap: '4px', flexWrap: 'wrap' }}>
              {record.images.slice(0, 2).map((img, index) => {
                const imageUrl = img.imageUrl || img.url || '';
                const imageKey = img.id || img.alt || `img-${index}`;
                return (
                  <div key={`${record.id}-${imageKey}-${index}`} style={{ position: 'relative' }}>
                    <Image
                      width={40}
                      height={40}
                      style={{ objectFit: 'cover', borderRadius: '4px' }}
                      src={imageUrl.startsWith('http') ? imageUrl : `${import.meta.env.DEV ? '' : (import.meta.env.VITE_API_URL || 'http://localhost:3010')}${imageUrl}`}
                      preview={{
                        src: imageUrl.startsWith('http') ? imageUrl : `${import.meta.env.DEV ? '' : (import.meta.env.VITE_API_URL || 'http://localhost:3010')}${imageUrl}`
                      }}
                      fallback="data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='40' height='40' fill='%23f0f8ff' stroke='%23e0e0e0' stroke-dasharray='2,2' rx='4'/%3E%3Ctext x='20' y='26' text-anchor='middle' font-size='18'%3E🥛%3C/text%3E%3C/svg%3E"
                      onError={() => {
                        console.log('Image failed to load:', imageUrl);
                      }}
                    />
                  </div>
                );
              })}
              {record.images.length > 2 && (
                <div style={{ 
                  width: 40, 
                  height: 40, 
                  backgroundColor: '#f0f0f0', 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  borderRadius: '4px',
                  fontSize: '12px',
                  color: '#666'
                }}>
                  +{record.images.length - 2}
                </div>
              )}
            </div>
          ) : (
            <div style={{ 
              width: 40, 
              height: 40, 
              backgroundColor: '#f0f8ff', 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              borderRadius: '4px',
              border: '1px dashed #e0e0e0'
            }}>
              <div style={{
                fontSize: '18px',
                color: '#87ceeb'
              }}>🥛</div>
            </div>
          )}
        </div>
      )
    },
    {
      title: '价格/规格',
      key: 'price',
      render: (record: Product) => (
        <div>
          <Text strong style={{ color: '#1890ff' }}>¥{record.price}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>{record.volume}</Text>
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: Product) => {
        const statusConfig = {
          active: { color: 'success', text: '在售' },
          inactive: { color: 'warning', text: '停售' },
          discontinued: { color: 'error', text: '下架' }
        }
        const config = statusConfig[status as keyof typeof statusConfig]
        return (
          <Space>
            <Badge status={config.color as any} text={config.text} />
            {status !== 'discontinued' && (
              <Switch
                size="small"
                checked={status === 'active'}
                onChange={() => handleStatusToggle(record)}
              />
            )}
          </Space>
        )
      }
    },
    {
      title: '保质期',
      dataIndex: 'shelf_life_days',
      key: 'shelf_life_days',
      render: (days: number) => `${days}天`
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleDateString()
    },
    {
      title: '操作',
      key: 'action',
      render: (record: Product) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            disabled={record.status === 'discontinued'}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个产品吗？"
            description="删除后产品将被标记为下架状态"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              disabled={record.status === 'discontinued'}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <div>
      {/* 页面头部 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <ShopOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          产品管理
        </Title>
        <Text type="secondary">管理您的酸奶产品信息</Text>
      </div>

      {/* 操作按钮 */}
      <Card style={{ marginBottom: 24 }}>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            添加产品
          </Button>
          <Button onClick={loadProducts}>刷新</Button>
        </Space>
      </Card>

      {/* 产品列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={products}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `显示 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 添加/编辑产品弹窗 */}
      <Modal
        title={editingProduct ? '编辑产品' : '添加产品'}
        open={modalVisible}
        onCancel={handleCancel}
        onOk={() => form.submit()}
        width={800}
        okText="保存"
        cancelText="取消"
      >
        <Form
          key={`product-form-${editingProduct?.id || 'new'}`}
          form={form}
          layout="vertical"
          onFinish={handleSave}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="产品名称"
                rules={[
                  { required: true, message: '请输入产品名称' },
                  { min: 2, max: 100, message: '产品名称长度为2-100字符' }
                ]}
              >
                <Input placeholder="请输入产品名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="产品类型"
                rules={[{ required: true, message: '请选择产品类型' }]}
              >
                <Select placeholder="请选择产品类型">
                  {productTypes.map((type, index) => (
                    <Option key={`product-type-${index}-${type}`} value={type}>{type}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="price"
                label="价格（元）"
                rules={[
                  { required: true, message: '请输入价格' },
                  { type: 'number', min: 0, message: '价格不能为负数' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="0.00"
                  precision={2}
                  min={0}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="volume"
                label="规格"
                rules={[{ required: true, message: '请输入产品规格' }]}
              >
                <Input placeholder="如：200ml" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="shelfLifeDays"
                label="保质期（天）"
                rules={[
                  { required: true, message: '请输入保质期' },
                  { type: 'number', min: 1, max: 365, message: '保质期为1-365天' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="21"
                  min={1}
                  max={365}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="产品描述"
            rules={[{ max: 500, message: '描述不能超过500字符' }]}
          >
            <TextArea
              rows={3}
              placeholder="请输入产品描述"
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="ingredients"
                label="配料表"
              >
                <Select
                  key={`ingredients-${editingProduct?.id || 'new'}`}
                  mode="tags"
                  placeholder="请输入配料，按回车添加"
                  style={{ width: '100%' }}
                  tokenSeparators={[',']}
                  optionFilterProp="children"
                  options={commonIngredients.map((ingredient, index) => ({
                    key: `ingredient-${index}-${ingredient}`,
                    value: ingredient,
                    label: ingredient
                  }))}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="benefits"
                label="主要功效"
              >
                <Select
                  key={`benefits-${editingProduct?.id || 'new'}`}
                  mode="tags"
                  placeholder="请输入功效，按回车添加"
                  style={{ width: '100%' }}
                  tokenSeparators={[',']}
                  optionFilterProp="children"
                  options={commonBenefits.map((benefit, index) => ({
                    key: `benefit-${index}-${benefit}`,
                    value: benefit,
                    label: benefit
                  }))}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="certifications"
                label="质量认证"
              >
                <Select
                  key={`certifications-${editingProduct?.id || 'new'}`}
                  mode="tags"
                  placeholder="请输入认证，按回车添加"
                  style={{ width: '100%' }}
                  tokenSeparators={[',']}
                  optionFilterProp="children"
                  options={commonCertifications.map((cert, index) => ({
                    key: `cert-${index}-${cert}`,
                    value: cert,
                    label: cert
                  }))}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="产品状态"
                rules={[{ required: true, message: '请选择产品状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Option key="status-active" value="active">在售</Option>
                  <Option key="status-inactive" value="inactive">停售</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Divider orientation="left">营养成分（每100g）</Divider>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name={['nutritionPer100g', 'energy']} label="能量">
                <Input placeholder="如：315kJ" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name={['nutritionPer100g', 'protein']} label="蛋白质">
                <Input placeholder="如：3.2g" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name={['nutritionPer100g', 'fat']} label="脂肪">
                <Input placeholder="如：3.1g" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name={['nutritionPer100g', 'carbohydrate']} label="碳水化合物">
                <Input placeholder="如：4.8g" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name={['nutritionPer100g', 'calcium']} label="钙">
                <Input placeholder="如：104mg" />
              </Form.Item>
            </Col>
          </Row>

          <Divider orientation="left">产品图片</Divider>
          <Form.Item
            label="产品图片"
            help="支持上传JPG、PNG等图片格式，最多5张图片，单张图片不超过10MB"
          >
            <Upload
              listType="picture-card"
              fileList={uploadedImages.map((img, index) => {
                // 处理不同的图片数据格式
                const imageUrl = img.imageUrl || (img as any).url || '';
                const fullImageUrl = imageUrl ? (imageUrl.startsWith('http') ? imageUrl : `${import.meta.env.DEV ? '' : (import.meta.env.VITE_API_URL || 'http://localhost:3010')}${imageUrl}`) : '';
                return {
                  uid: img.id || `upload-${index}`,
                  name: img.originalName || (img as any).alt || `image-${index}`,
                  status: 'done' as const,
                  url: fullImageUrl,
                  thumbUrl: fullImageUrl
                };
              })}
              beforeUpload={(file) => {
                handleImageUpload(file)
                return false
              }}
              onRemove={(file) => {
                handleImageRemove(file.uid)
              }}
              onPreview={(file) => {
                if (file.url) {
                  handlePreview(file.url)
                }
              }}
              multiple
              accept="image/*"
              maxCount={5}
            >
              {uploadedImages.length >= 5 ? null : (
                <div>
                  <PlusOutlined />
                  <div style={{ marginTop: 8 }}>上传图片</div>
                </div>
              )}
            </Upload>
          </Form.Item>
        </Form>
      </Modal>

      {/* 图片预览Modal */}
      <Modal
        open={previewVisible}
        title="图片预览"
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        centered
      >
        <Image
          alt="预览图片"
          style={{ width: '100%' }}
          src={previewImage}
        />
      </Modal>
    </div>
  )
}

export default ProductManagement