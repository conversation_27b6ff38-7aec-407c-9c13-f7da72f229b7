import { useEffect, useCallback, useRef } from 'react';
import { addPassiveEventListener, removePassiveEventListener, throttle } from '../utils/eventUtils';

/**
 * 使用被动滚动事件监听器的Hook
 * 解决浏览器性能警告
 */

interface UsePassiveScrollOptions {
  /**
   * 滚动事件处理函数
   */
  onScroll?: (event: Event) => void;
  /**
   * 节流延迟时间（毫秒）
   */
  throttleMs?: number;
  /**
   * 目标元素，默认为window
   */
  target?: Element | Window | null;
  /**
   * 是否启用
   */
  enabled?: boolean;
}

export const usePassiveScroll = ({
  onScroll,
  throttleMs = 16, // 约60fps
  target = null,
  enabled = true,
}: UsePassiveScrollOptions = {}) => {
  const handlerRef = useRef<EventListener>();

  // 创建节流的滚动处理函数
  const throttledHandler = useCallback(
    throttle((event: Event) => {
      onScroll?.(event);
    }, throttleMs),
    [onScroll, throttleMs]
  );

  useEffect(() => {
    if (!enabled || !onScroll) return;

    const element = target || window;
    handlerRef.current = throttledHandler;

    // 使用被动事件监听器
    addPassiveEventListener(element, 'scroll', throttledHandler, {
      passive: true,
      capture: false,
    });

    return () => {
      if (handlerRef.current) {
        removePassiveEventListener(element, 'scroll', handlerRef.current);
      }
    };
  }, [throttledHandler, target, enabled, onScroll]);

  return {
    /**
     * 手动触发滚动检查
     */
    checkScroll: useCallback(() => {
      if (handlerRef.current) {
        const event = new Event('scroll');
        handlerRef.current(event);
      }
    }, []),
  };
};

/**
 * 滚动到顶部的Hook
 */
export const useScrollToTop = () => {
  const scrollToTop = useCallback((smooth: boolean = true) => {
    if (typeof window !== 'undefined') {
      window.scrollTo({
        top: 0,
        behavior: smooth ? 'smooth' : 'auto',
      });
    }
  }, []);

  return { scrollToTop };
};

/**
 * 检测滚动方向的Hook
 */
export const useScrollDirection = () => {
  const lastScrollY = useRef(0);
  const scrollDirection = useRef<'up' | 'down' | null>(null);

  const { checkScroll } = usePassiveScroll({
    onScroll: () => {
      const currentScrollY = window.scrollY;
      
      if (currentScrollY > lastScrollY.current) {
        scrollDirection.current = 'down';
      } else if (currentScrollY < lastScrollY.current) {
        scrollDirection.current = 'up';
      }
      
      lastScrollY.current = currentScrollY;
    },
  });

  return {
    scrollDirection: scrollDirection.current,
    checkScroll,
  };
};
