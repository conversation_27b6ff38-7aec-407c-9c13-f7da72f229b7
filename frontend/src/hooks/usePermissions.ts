import { useMemo } from 'react';
import { useTypedSelector } from './useTypedSelector';
import { Permission, UserRole, RolePermissions } from '../types/permissions';

/**
 * 权限检查Hook
 * 提供基于角色的权限验证功能
 */
export const usePermissions = () => {
  const { user } = useTypedSelector((state) => state.auth);

  // 获取用户权限列表
  const userPermissions = useMemo(() => {
    if (!user?.role) {
      return [];
    }

    const role = user.role.toUpperCase() as UserRole;
    const permissions = RolePermissions[role];

    if (!permissions) {
      console.warn(`未找到角色 ${role} 的权限配置`);
      return [];
    }

    return permissions;
  }, [user?.role]);

  // 检查是否有特定权限
  const hasPermission = (permission: Permission): boolean => {
    return userPermissions.includes(permission);
  };

  // 检查是否有任一权限
  const hasAnyPermission = (permissions: Permission[]): boolean => {
    return permissions.some(permission => hasPermission(permission));
  };

  // 检查是否有所有权限
  const hasAllPermissions = (permissions: Permission[]): boolean => {
    return permissions.every(permission => hasPermission(permission));
  };

  // 检查是否是特定角色
  const hasRole = (role: UserRole): boolean => {
    if (!user?.role) return false;
    return user.role.toUpperCase() === role;
  };

  // 检查是否有任一角色
  const hasAnyRole = (roles: UserRole[]): boolean => {
    return roles.some(role => hasRole(role));
  };

  // 检查是否是管理员
  const isAdmin = (): boolean => {
    return hasRole(UserRole.ADMIN);
  };

  // 检查是否是普通用户
  const isUser = (): boolean => {
    return hasRole(UserRole.USER);
  };

  // 检查是否是观察员
  const isViewer = (): boolean => {
    return hasRole(UserRole.VIEWER);
  };

  // 检查是否是消费者
  const isConsumer = (): boolean => {
    return hasRole(UserRole.VIEWER);
  };

  // 获取用户角色
  const getUserRole = (): UserRole | null => {
    if (!user?.role) return null;
    return user.role.toUpperCase() as UserRole;
  };

  // 检查是否可以访问路由
  const canAccessRoute = (requiredPermissions?: Permission[], requiredRoles?: UserRole[]): boolean => {
    // 如果没有要求，则允许访问
    if (!requiredPermissions && !requiredRoles) {
      return true;
    }

    // 检查角色要求
    if (requiredRoles && requiredRoles.length > 0) {
      if (!hasAnyRole(requiredRoles)) return false;
    }

    // 检查权限要求
    if (requiredPermissions && requiredPermissions.length > 0) {
      if (!hasAnyPermission(requiredPermissions)) return false;
    }

    return true;
  };

  // 过滤用户可访问的菜单项
  const filterAccessibleMenuItems = <T extends { permissions?: Permission[]; roles?: UserRole[] }>(
    items: T[]
  ): T[] => {
    return items.filter(item => 
      canAccessRoute(item.permissions, item.roles)
    );
  };

  return {
    // 权限检查
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    userPermissions,

    // 角色检查
    hasRole,
    hasAnyRole,
    isAdmin,
    isUser,
    isViewer,
    isConsumer,
    getUserRole,

    // 路由和菜单
    canAccessRoute,
    filterAccessibleMenuItems,

    // 用户信息
    currentUser: user,
  };
};

/**
 * 权限守卫Hook
 * 用于组件级别的权限控制
 */
export const usePermissionGuard = (
  requiredPermissions?: Permission[],
  requiredRoles?: UserRole[]
) => {
  const { canAccessRoute } = usePermissions();
  
  const hasAccess = canAccessRoute(requiredPermissions, requiredRoles);
  
  return {
    hasAccess,
    canRender: hasAccess,
  };
};

/**
 * 角色守卫Hook
 * 专门用于角色检查
 */
export const useRoleGuard = (...roles: UserRole[]) => {
  const { hasAnyRole } = usePermissions();
  
  const hasAccess = hasAnyRole(roles);
  
  return {
    hasAccess,
    canRender: hasAccess,
  };
};
