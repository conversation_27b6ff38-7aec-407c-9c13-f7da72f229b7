/**
 * 仪表板数据服务
 * 提供仪表板所需的统计数据和图表数据
 */

export interface UserActivityData {
  date: string;
  loginCount: number;
  activeUsers: number;
}

export interface RoleDistributionData {
  role: string;
  count: number;
  percentage: number;
}

export interface SystemUsageData {
  module: string;
  usage: number;
  trend: 'up' | 'down' | 'stable';
}

export interface BatchProcessingData {
  date: string;
  batchCount: number;
  completedCount: number;
  pendingCount: number;
}

export interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalBatches: number;
  completedBatches: number;
  systemHealth: 'excellent' | 'good' | 'warning' | 'error';
  avgProcessingTime: number;
}

/**
 * 获取仪表板统计数据
 */
export const getDashboardStats = async (): Promise<DashboardStats> => {
  try {
    const token = localStorage.getItem('token');
    const response = await fetch('/api/dashboard/stats', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('获取统计数据失败');
    }

    const data = await response.json();
    return data.data.stats;
  } catch (error) {
    console.error('获取统计数据失败，使用模拟数据:', error);
    
    // 失败时返回模拟数据 - 生成更真实的酸奶质控系统数据
    const currentHour = new Date().getHours();
    const isBusinessHours = currentHour >= 8 && currentHour <= 18;
    
    return {
      totalUsers: 234,
      activeUsers: isBusinessHours ? 127 : 34, // 工作时间活跃用户更多
      totalBatches: 2156,
      completedBatches: 2089,
      systemHealth: 'excellent',
      avgProcessingTime: 1.8, // 酸奶质检处理时间相对较快
    };
  }
};

/**
 * 获取用户活跃度数据（最近30天）
 */
export const getUserActivityData = async (): Promise<UserActivityData[]> => {
  try {
    const token = localStorage.getItem('token');
    const response = await fetch('/api/dashboard/activity-trend?days=30', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('获取活跃度数据失败');
    }

    const result = await response.json();
    return result.data.trend.map((item: any) => ({
      date: item.date,
      loginCount: item.actions,
      activeUsers: item.activeUsers,
    }));
  } catch (error) {
    console.error('获取活跃度数据失败，使用模拟数据:', error);
    
    // 失败时返回模拟数据
    const data: UserActivityData[] = [];
    const today = new Date();
    
    for (let i = 29; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      
      // 基于日期生成有规律的模拟数据
      const dayOfWeek = date.getDay();
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
      
      // 工作日活跃度更高，周末稍低
      const baseLogin = isWeekend ? 25 : 45;
      const baseActive = isWeekend ? 15 : 28;
      
      // 添加一些随机波动和趋势
      const trendFactor = 1 + (i / 50); // 最近的日期活跃度更高
      const randomFactor = 0.7 + Math.random() * 0.6; // 随机变化因子
      
      data.push({
        date: date.toISOString().split('T')[0],
        loginCount: Math.floor(baseLogin * trendFactor * randomFactor),
        activeUsers: Math.floor(baseActive * trendFactor * randomFactor),
      });
    }
    
    return data;
  }
};

/**
 * 获取角色分布数据
 */
export const getRoleDistributionData = async (): Promise<RoleDistributionData[]> => {
  await new Promise(resolve => setTimeout(resolve, 200));

  return [
    { role: '管理员', count: 12, percentage: 5.1 },
    { role: '质检员', count: 145, percentage: 62.0 },
    { role: '消费者', count: 77, percentage: 32.9 },
  ];
};

/**
 * 获取系统使用情况数据
 */
export const getSystemUsageData = async (): Promise<SystemUsageData[]> => {
  await new Promise(resolve => setTimeout(resolve, 400));
  
  return [
    { module: '产品管理', usage: 342, trend: 'up' },
    { module: '批次管理', usage: 289, trend: 'up' },
    { module: 'AI分析', usage: 267, trend: 'up' },
    { module: '配方管理', usage: 198, trend: 'stable' },
    { module: '报告中心', usage: 156, trend: 'up' },
    { module: '用户管理', usage: 89, trend: 'stable' },
    { module: '系统设置', usage: 34, trend: 'down' },
    { module: '仪表盘', usage: 445, trend: 'up' },
  ];
};

/**
 * 获取批次处理趋势数据（最近14天）
 */
export const getBatchProcessingData = async (): Promise<BatchProcessingData[]> => {
  await new Promise(resolve => setTimeout(resolve, 350));
  
  const data: BatchProcessingData[] = [];
  const today = new Date();
  
  for (let i = 13; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    
    // 基于日期生成更真实的批次数据
    const dayOfWeek = date.getDay();
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
    
    // 工作日批次处理量更高
    const baseBatchCount = isWeekend ? 8 : 18;
    
    // 添加季节性和随机波动
    const seasonalFactor = 0.8 + 0.4 * Math.sin((i / 7) * Math.PI); // 周期性波动
    const randomFactor = 0.7 + Math.random() * 0.6;
    
    const batchCount = Math.floor(baseBatchCount * seasonalFactor * randomFactor);
    
    // 完成率在85%-98%之间，最近几天完成率更高
    const completionRate = 0.85 + 0.13 * Math.random() + (i < 3 ? 0.05 : 0);
    const completedCount = Math.floor(batchCount * completionRate);
    const pendingCount = batchCount - completedCount;
    
    data.push({
      date: date.toISOString().split('T')[0],
      batchCount,
      completedCount,
      pendingCount: Math.max(0, pendingCount), // 确保不为负数
    });
  }
  
  return data;
};

/**
 * 刷新所有仪表板数据
 */
export const refreshDashboardData = async () => {
  const [stats, userActivity, roleDistribution, systemUsage, batchProcessing] = await Promise.all([
    getDashboardStats(),
    getUserActivityData(),
    getRoleDistributionData(),
    getSystemUsageData(),
    getBatchProcessingData(),
  ]);
  
  return {
    stats,
    userActivity,
    roleDistribution,
    systemUsage,
    batchProcessing,
  };
};

/**
 * 店面管理员仪表板数据接口
 */
export interface ManagerDashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalBatches: number;
  completedBatches: number;
  pendingApprovals: number;
  qualityScore: number;
  todayProduction: number;
  monthlyTarget: number;
  targetProgress: number;
}

/**
 * 获取店面管理员仪表板统计数据
 */
export const getManagerDashboardStats = async (cafeId: string): Promise<ManagerDashboardStats> => {
  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`/api/dashboard/manager-stats?cafeId=${cafeId}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('获取店面统计数据失败');
    }

    const data = await response.json();
    return data.data.stats;
  } catch (error) {
    console.error('获取店面统计数据失败，使用模拟数据:', error);
    
    // 失败时返回模拟数据 - 店面级别的酸奶质控数据
    const currentHour = new Date().getHours();
    const currentDay = new Date().getDate();
    const isBusinessHours = currentHour >= 8 && currentHour <= 18;
    
    // 根据时间动态调整数据
    const todayBaseProduction = isBusinessHours ? 
      Math.floor(120 + (currentHour - 8) * 15) : // 营业时间内逐渐增加
      180; // 非营业时间显示全天产量
    
    return {
      totalUsers: 15,
      activeUsers: isBusinessHours ? 12 : 3,
      totalBatches: 67,
      completedBatches: 59,
      pendingApprovals: 4,
      qualityScore: 9.4, // 质量评分较高
      todayProduction: todayBaseProduction,
      monthlyTarget: 1500,
      targetProgress: Math.min(95, Math.floor((currentDay / 31) * 100) + 15), // 基于月份进度
    };
  }
};

/**
 * 获取店面用户活跃度数据
 */
export const getManagerUserActivityData = async (cafeId: string): Promise<UserActivityData[]> => {
  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`/api/dashboard/manager-activity?cafeId=${cafeId}&days=7`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('获取店面活跃度数据失败');
    }

    const result = await response.json();
    return result.data.trend.map((item: any) => ({
      date: item.date,
      loginCount: item.actions,
      activeUsers: item.activeUsers,
    }));
  } catch (error) {
    console.error('获取店面活跃度数据失败，使用模拟数据:', error);
    
    // 失败时返回模拟数据
    const data: UserActivityData[] = [];
    const today = new Date();
    
    // 使用 cafeId 生成不同店面的差异化数据
    const cafeIdHash = cafeId ? cafeId.charCodeAt(0) % 10 : 0;
    const storeSizeFactor = 0.7 + (cafeIdHash / 10); // 不同店面规模略有差异
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      
      // 店面管理员数据规模较小但更集中
      const dayOfWeek = date.getDay();
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
      
      // 工作日活跃度更高，店面规模较小
      const baseLogin = isWeekend ? 4 : 8;
      const baseActive = isWeekend ? 3 : 6;
      
      // 添加适度的随机波动和店面差异
      const randomFactor = 0.8 + Math.random() * 0.4;
      
      data.push({
        date: `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`,
        loginCount: Math.max(1, Math.floor(baseLogin * randomFactor * storeSizeFactor)),
        activeUsers: Math.max(1, Math.floor(baseActive * randomFactor * storeSizeFactor)),
      });
    }
    
    return data;
  }
};

/**
 * 获取店面角色分布数据
 */
export const getManagerRoleDistributionData = async (cafeId: string): Promise<RoleDistributionData[]> => {
  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`/api/dashboard/manager-roles?cafeId=${cafeId}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('获取店面角色分布失败');
    }

    const result = await response.json();
    return result.data.distribution;
  } catch (error) {
    console.error('获取店面角色分布失败，使用模拟数据:', error);
    
    // 失败时返回模拟数据
    return [
      { role: '质检员', count: 8, percentage: 53.3 },
      { role: '观察员', count: 5, percentage: 33.3 },
      { role: '管理员', count: 2, percentage: 13.4 },
    ];
  }
};

/**
 * 获取店面批次处理状态数据
 */
export const getManagerBatchProcessingData = async (cafeId: string): Promise<any[]> => {
  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`/api/dashboard/manager-batches?cafeId=${cafeId}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('获取店面批次数据失败');
    }

    const result = await response.json();
    return result.data.batches;
  } catch (error) {
    console.error('获取店面批次数据失败，使用模拟数据:', error);
    
    // 根据时间和店面ID生成动态的模拟数据
    const currentHour = new Date().getHours();
    const isWorkTime = currentHour >= 8 && currentHour <= 18;
    
    const completedBase = isWorkTime ? 42 : 38;
    const inProgressBase = isWorkTime ? 6 : 4;
    const pendingBase = isWorkTime ? 5 : 3;
    
    // 使用 cafeId 作为种子，确保相同店面数据相对稳定
    const cafeIdHash = cafeId ? cafeId.length % 5 : 0;
    
    const result = [
      { status: '已完成', count: completedBase + cafeIdHash, color: '#52c41a' },
      { status: '进行中', count: inProgressBase + Math.floor(cafeIdHash / 2), color: '#1890ff' },
      { status: '待审批', count: pendingBase + (cafeIdHash % 3), color: '#faad14' },
    ];
    
    console.log('getManagerBatchProcessingData 返回数据:', {
      cafeId,
      isWorkTime,
      cafeIdHash,
      result
    });
    
    return result;
  }
};

/**
 * 刷新店面管理员仪表板数据
 */
export const refreshManagerDashboardData = async (cafeId: string) => {
  console.log('refreshManagerDashboardData 开始加载, cafeId:', cafeId);
  
  const [stats, userActivity, roleDistribution, batchProcessing] = await Promise.all([
    getManagerDashboardStats(cafeId),
    getManagerUserActivityData(cafeId),
    getManagerRoleDistributionData(cafeId),
    getManagerBatchProcessingData(cafeId),
  ]);
  
  console.log('refreshManagerDashboardData 加载完成:', {
    statsLoaded: !!stats,
    userActivityCount: userActivity.length,
    roleDistributionCount: roleDistribution.length,
    batchProcessingCount: batchProcessing.length,
    batchProcessingData: batchProcessing
  });
  
  return {
    stats,
    userActivity,
    roleDistribution,
    batchProcessing,
  };
};

/**
 * 获取图表主题色彩配置
 */
export const getChartTheme = () => {
  return {
    primary: '#F05654',      // 银红色主色调
    secondary: '#FF6B69',    // 悬停色
    success: '#52c41a',      // 成功色
    warning: '#faad14',      // 警告色
    error: '#ff4d4f',        // 错误色
    info: '#A8ABB0',         // 信息色
    background: '#fafafa',   // 背景色
    text: '#262626',         // 文本色
    border: '#f0f0f0',       // 边框色

    // 角色分布专用颜色
    roleColors: [
      '#F05654',  // 管理员 - 银红色
      '#1890ff',  // 质检员 - 蓝色
      '#52c41a',  // 消费者 - 绿色
    ],

    // 系统使用情况专用颜色
    moduleColors: [
      '#F05654',  // 配方管理 - 银红色
      '#FF6B69',  // 批次管理 - 浅红色
      '#1890ff',  // AI分析 - 蓝色
      '#52c41a',  // 报告中心 - 绿色
      '#faad14',  // 用户管理 - 橙色
      '#722ed1',  // 系统设置 - 紫色
    ],

    // 通用渐变色
    gradient: [
      '#F05654',
      '#FF6B69',
      '#FF8A88',
      '#FFA9A7',
      '#FFC8C6',
    ],
  };
};
