import { apiClient, ApiResponse } from './index'

// AI分析记录接口
export interface AIAnalysisRecord {
  id: string
  userId: string
  cafeId: string
  fileName: string
  imageUrl: string
  analysisResult: string
  processingTime?: number
  customPrompt?: string
  status: 'analyzing' | 'completed' | 'error'
  createdAt: string
  updatedAt: string
  userName?: string
  userEmail?: string
  cafeName?: string
}

// AI分析统计接口
export interface AIAnalysisStats {
  totalAnalyses: number
  completedAnalyses: number
  failedAnalyses: number
  processingAnalyses: number
  avgProcessingTime: number
  recentAnalyses: number
}

// AI分析查询参数
export interface AIAnalysisQueryParams {
  page?: number
  limit?: number
  status?: 'analyzing' | 'completed' | 'error'
}

// AI分析列表响应
export interface AIAnalysisListResponse {
  analyses: AIAnalysisRecord[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// AI分析API
export const aiAnalysisAPI = {
  // 检查本地AI服务健康状态
  checkHealth: () => {
    return apiClient.get<ApiResponse<{
      status: 'online' | 'offline'
      serviceUrl: string
      serviceResponse?: any
      error?: string
    }>>('/api/ai-analysis/health')
  },

  // 获取可用AI模型
  getModels: () => {
    return apiClient.get<ApiResponse<{
      models: Array<{
        id: string
        object: string
        created: number
        owned_by: string
      }>
    }>>('/api/ai-analysis/models')
  },

  // 获取AI分析历史记录
  getAnalyses: (params?: AIAnalysisQueryParams) => {
    return apiClient.get<ApiResponse<AIAnalysisListResponse>>('/api/ai-analysis', { params })
  },

  // 获取分析记录详情
  getAnalysisById: (id: string) => {
    return apiClient.get<ApiResponse<{ analysis: AIAnalysisRecord }>>(`/api/ai-analysis/${id}`)
  },

  // 保存AI分析记录
  saveAnalysisRecord: (data: {
    fileName: string
    imageUrl: string
    analysisResult: string
    processingTime?: number
    customPrompt?: string
    status?: 'analyzing' | 'completed' | 'error'
  }) => {
    return apiClient.post<ApiResponse<{ analysis: AIAnalysisRecord }>>('/api/ai-analysis', data)
  },

  // 删除分析记录
  deleteAnalysis: (id: string) => {
    return apiClient.delete<ApiResponse<{}>>(`/api/ai-analysis/${id}`)
  },

  // 获取分析统计
  getAnalysisStats: () => {
    return apiClient.get<ApiResponse<AIAnalysisStats>>('/api/ai-analysis/stats')
  }
}