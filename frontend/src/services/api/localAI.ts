import axios, { AxiosInstance } from 'axios'

// 本地多模态模型配置
const LOCAL_AI_URL = 'http://192.168.1.225:1234'

// 创建本地AI客户端
const localAIClient: AxiosInstance = axios.create({
  baseURL: LOCAL_AI_URL,
  timeout: 60000, // 60秒超时，AI分析可能较慢
  headers: {
    'Content-Type': 'application/json',
  },
})

// 添加请求重试拦截器
localAIClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const config = error.config

    // 如果是429错误且没有重试过，则等待后重试
    if (error.response?.status === 429 && !config._retry) {
      config._retry = true
      console.log('遇到429错误，等待5秒后重试...')
      await new Promise(resolve => setTimeout(resolve, 5000))
      return localAIClient(config)
    }

    return Promise.reject(error)
  }
)

// AI分析请求接口
export interface LocalAIAnalysisRequest {
  messages: Array<{
    role: 'user' | 'assistant' | 'system'
    content: Array<{
      type: 'text' | 'image_url'
      text?: string
      image_url?: {
        url: string // base64编码的图片或图片URL
      }
    }>
  }>
  model: string
  stream?: boolean
  max_tokens?: number
  temperature?: number
}

// AI分析响应接口
export interface LocalAIAnalysisResponse {
  choices: Array<{
    message: {
      role: 'assistant'
      content: string
    }
    finish_reason: string
  }>
  usage?: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

// 流式响应接口
export interface LocalAIStreamChunk {
  choices: Array<{
    delta: {
      content?: string
    }
    finish_reason?: string
  }>
}

// 本地AI服务类
export const localAIService = {
  // 检查模型健康状态
  checkHealth: async (): Promise<boolean> => {
    try {
      // 使用 /v1/models 端点来检查服务状态，因为 /health 端点不存在
      const response = await localAIClient.get('/v1/models')
      return response.status === 200 && response.data?.data?.length > 0
    } catch (error) {
      console.error('Local AI service health check failed:', error)
      return false
    }
  },

  // 获取可用模型列表
  getModels: async (): Promise<string[]> => {
    try {
      const response = await localAIClient.get('/v1/models')
      return response.data.data?.map((model: any) => model.id) || []
    } catch (error) {
      console.error('Failed to get models:', error)
      return []
    }
  },

  // 分析酸奶菌群图像
  analyzeYogurtBacteria: async (
    imageBase64: string,
    prompt?: string,
    onProgress?: (content: string) => void
  ): Promise<string> => {
    const defaultPrompt = `你是一个专业的微生物学家和酸奶质量控制专家。请仔细分析这张酸奶菌群的显微镜图像，并提供详细的分析报告。

请按以下格式进行分析：

1. **菌群识别**：
   - 识别图像中的主要菌群类型（乳杆菌、双歧杆菌、链球菌等）
   - 评估各类菌群的数量和分布情况

2. **形态特征**：
   - 描述观察到的细菌形态（杆状、球状、链状等）
   - 评估细菌大小、排列方式

3. **质量评估**：
   - 评估菌群活性和健康状况
   - 检测是否存在污染菌或异常菌群
   - 给出质量评分（1-10分）

4. **发酵效果预测**：
   - 基于菌群状况预测发酵效果
   - 评估酸奶最终品质

5. **建议和改进**：
   - 提供质量改进建议
   - 如有问题，提出解决方案

请使用专业术语，但保持描述清晰易懂。`

    const request: LocalAIAnalysisRequest = {
      model: 'google/gemma-3-27b',
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: prompt || defaultPrompt
            },
            {
              type: 'image_url',
              image_url: {
                url: imageBase64.startsWith('data:') 
                  ? imageBase64 
                  : `data:image/jpeg;base64,${imageBase64}`
              }
            }
          ]
        }
      ],
      stream: !!onProgress,
      max_tokens: 2000,
      temperature: 0.7
    }

    if (onProgress) {
      // 使用原生 fetch API 处理流式响应
      return new Promise(async (resolve, reject) => {
        let fullContent = ''

        try {
          const response = await fetch(`${LOCAL_AI_URL}/v1/chat/completions`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(request)
          })

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }

          const reader = response.body?.getReader()
          if (!reader) {
            throw new Error('无法获取响应流')
          }

          const decoder = new TextDecoder()

          while (true) {
            const { done, value } = await reader.read()
            if (done) {
              resolve(fullContent)
              break
            }

            const chunk = decoder.decode(value, { stream: true })
            const lines = chunk.split('\n')

            for (const line of lines) {
              const trimmedLine = line.trim()
              if (trimmedLine.startsWith('data: ')) {
                const data = trimmedLine.slice(6).trim()

                if (data === '[DONE]') {
                  resolve(fullContent)
                  return
                }

                if (data) {
                  try {
                    const parsed: LocalAIStreamChunk = JSON.parse(data)
                    const content = parsed.choices[0]?.delta?.content
                    if (content) {
                      fullContent += content
                      onProgress(fullContent)
                    }
                  } catch (e) {
                    console.warn('解析流式数据失败:', e, '数据:', data)
                  }
                }
              }
            }
          }
        } catch (error) {
          console.error('流式请求失败:', error)
          reject(error)
        }
      })
    } else {
      // 非流式响应
      const response = await localAIClient.post<LocalAIAnalysisResponse>(
        '/v1/chat/completions',
        { ...request, stream: false }
      )
      
      return response.data.choices[0]?.message?.content || ''
    }
  },

  // 自定义分析（可以传入自定义提示词）
  customAnalysis: async (
    imageBase64: string,
    customPrompt: string,
    onProgress?: (content: string) => void
  ): Promise<string> => {
    return localAIService.analyzeYogurtBacteria(imageBase64, customPrompt, onProgress)
  },

  // 批量分析
  batchAnalysis: async (
    images: string[],
    onProgress?: (imageIndex: number, content: string, isComplete: boolean) => void
  ): Promise<string[]> => {
    const results: string[] = []
    
    for (let i = 0; i < images.length; i++) {
      const result = await localAIService.analyzeYogurtBacteria(
        images[i],
        undefined,
        onProgress ? (content) => onProgress(i, content, false) : undefined
      )
      results.push(result)
      
      if (onProgress) {
        onProgress(i, result, true)
      }
    }
    
    return results
  }
}

export default localAIService