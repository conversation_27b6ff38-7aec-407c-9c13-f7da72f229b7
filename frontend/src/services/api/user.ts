import { apiClient, ApiResponse } from './index'
import { User } from '../../types'

// 用户查询参数
export interface UserQueryParams {
  role?: 'ADMIN' | 'USER' | 'VIEWER'
  cafeId?: string
  isActive?: boolean
}

// 用户创建请求
export interface CreateUserRequest {
  email: string
  username: string
  name?: string
  password: string
  role: 'USER' | 'VIEWER'
  cafeId: string
}

// 用户更新请求
export interface UpdateUserRequest {
  name?: string
  role?: 'USER' | 'VIEWER'
  isActive?: boolean
}

// 用户统计数据
export interface UserStatistics {
  total: number
  admin: number
  user: number
  viewer: number
  active: number
}

// 用户API类
export const userAPI = {
  // 获取用户列表
  getUsers: (params?: UserQueryParams) => {
    return apiClient.get<ApiResponse<{
      users: Array<{
        id: string
        email: string
        username: string
        name: string
        role: string
        cafeId: string | null
        cafeName: string | null
        isActive: boolean
        createdAt: string
        updatedAt: string
      }>
      total: number
      statistics: UserStatistics
    }>>('/api/users', { params })
  },

  // 创建用户
  createUser: (userData: CreateUserRequest) => {
    return apiClient.post<ApiResponse<{ user: any }>>('/api/users', userData)
  },

  // 更新用户
  updateUser: (id: string, userData: UpdateUserRequest) => {
    return apiClient.put<ApiResponse<{ user: any }>>(`/api/users/${id}`, userData)
  },

  // 删除用户
  deleteUser: (id: string) => {
    return apiClient.delete<ApiResponse<{}>>(`/api/users/${id}`)
  },

  // 获取店面列表
  getCafes: () => {
    return apiClient.get<ApiResponse<{
      cafes: Array<{
        id: string
        name: string
        description: string
        address: string
      }>
    }>>('/api/auth/cafes')
  }
}
