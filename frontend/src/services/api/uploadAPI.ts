import axios from 'axios'

const API_BASE_URL = '/api'

export interface UploadImageResponse {
  success: boolean
  data: {
    filename: string
    originalName: string
    imageUrl: string
    fileSize: number
    mimeType: string
  }
  message: string
}

class UploadAPI {
  /**
   * 上传AI分析图片
   */
  async uploadAIImage(file: File): Promise<UploadImageResponse> {
    const formData = new FormData()
    formData.append('image', file)

    const response = await axios.post(
      `${API_BASE_URL}/upload/ai-analysis-image`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 60000, // 60秒超时
      }
    )

    return response.data
  }

  /**
   * 获取AI分析图片URL（完整URL）
   */
  getAIImageUrl(imageUrl: string): string {
    // 如果已经是完整URL，直接返回
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl
    }
    
    // 如果是相对路径，添加基础URL
    if (imageUrl.startsWith('/api/')) {
      return `${window.location.origin}${imageUrl}`
    }
    
    // 如果是文件名，构建完整路径
    return `${window.location.origin}/api/upload/ai-images/${imageUrl}`
  }
}

export const uploadAPI = new UploadAPI()
export default uploadAPI