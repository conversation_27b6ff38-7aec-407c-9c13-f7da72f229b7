import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { showMessage } from '../../utils/messageHandler'

// API基础配置
// 在开发环境中使用 Vite 代理（空字符串），生产环境使用完整 URL
const API_BASE_URL = import.meta.env.DEV
  ? '' // 开发环境使用 Vite 代理，相对路径
  : ((import.meta as any).env?.VITE_API_URL || 'http://localhost:3010')

const AI_SERVICE_URL = import.meta.env.DEV
  ? '' // 开发环境使用 Vite 代理
  : ((import.meta as any).env?.VITE_AI_SERVICE_URL || 'http://localhost:6800')

// 创建axios实例
const createApiInstance = (baseURL: string): AxiosInstance => {
  const instance = axios.create({
    baseURL,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  })

  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      // 添加认证token
      const token = localStorage.getItem('token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    },
    (error) => {
      return Promise.reject(error instanceof Error ? error : new Error(String(error)))
    }
  )

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      return response
    },
    (error) => {
      // 统一错误处理
      if (error.response) {
        const { status, data } = error.response
        
        switch (status) {
          case 401:
            // 未授权，只有在非登录页面时才跳转
            if (!window.location.pathname.includes('/login')) {
              localStorage.removeItem('token')
              window.location.href = '/login'
              showMessage.error('登录已过期，请重新登录')
            } else {
              // 在登录页面时，显示登录失败消息
              showMessage.error(data.message || '登录失败，请检查用户名和密码')
            }
            break
          case 403:
            showMessage.error('权限不足')
            break
          case 404:
            showMessage.error('请求的资源不存在')
            break
          case 422:
            showMessage.error(data.message || '请求参数错误')
            break
          case 500:
            showMessage.error('服务器内部错误')
            break
          default:
            showMessage.error(data.message || '请求失败')
        }
      } else if (error.request) {
        showMessage.error('网络连接失败，请检查网络设置')
      } else {
        showMessage.error('请求配置错误')
      }

      return Promise.reject(error instanceof Error ? error : new Error(String(error)))
    }
  )

  return instance
}

// 创建API实例
export const apiClient = createApiInstance(API_BASE_URL)
export const aiClient = createApiInstance(AI_SERVICE_URL)

// 通用API响应类型 - 从types/index.ts导入
export type { ApiResponse } from '../../types'

// 通用API错误类型
export interface ApiError {
  message: string
  code?: string
  details?: any
}

// 文件上传配置
export const uploadConfig: AxiosRequestConfig = {
  headers: {
    'Content-Type': 'multipart/form-data',
  },
  timeout: 60000, // 文件上传超时时间更长
}

// 导出所有API模块
export * from './auth'
export * from './recipe'
export * from './batch'
export * from './analysis'
export * from './report'
export * from './user'
export * from './localAI'
export * from './aiAnalysisAPI'
