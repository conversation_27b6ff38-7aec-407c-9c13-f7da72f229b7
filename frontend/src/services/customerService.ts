import { apiClient } from './api';

export interface ProbioticHealthIndex {
  index: number;
  level: string;
  color: string;
  advice: string;
  breakdown: {
    frequency: number;
    diversity: number;
    continuity: number;
    probioticPreference: number;
    dietMatch: number;
  };
}

export interface CustomerStats {
  totalOrders: number;
  totalSpent: number;
  loyaltyPoints: number;
  favoriteProduct: string;
  avgOrderValue: number;
  recentOrders: Array<{ date: string; amount: number; items: string }>;
  monthlyPurchases: Array<{ month: string; count: number; amount: number }>;
  probioticHealthIndex: ProbioticHealthIndex;
}

export interface CustomerPreferences {
  favoriteProducts: string[];
  tastePreferences: {
    acidity?: string;
    sweetness?: string;
    texture?: string;
  };
  dietaryRestrictions: string[];
  preferredSize: string;
  preferredTimeSlots: string[];
  notes: string;
}

export interface CustomerFeedback {
  id: string;
  rating: number;
  feedbackType: string;
  title: string;
  content: string;
  response?: string;
  status: string;
  createdAt: string;
  productName?: string;
}

export interface LoyaltyPoint {
  pointsChange: number;
  pointsBalance: number;
  transactionType: string;
  description: string;
  createdAt: string;
}

export interface CreateFeedbackRequest {
  productId?: string;
  rating?: number;
  feedbackType?: string;
  title: string;
  content: string;
}

// 获取顾客统计数据
export const getCustomerStats = async (): Promise<CustomerStats> => {
  try {
    const response = await apiClient.get('/api/customers/stats');
    return response.data;
  } catch (error) {
    console.error('获取顾客统计数据失败:', error);
    throw error;
  }
};

// 获取顾客偏好
export const getCustomerPreferences = async (): Promise<CustomerPreferences> => {
  try {
    const response = await apiClient.get('/api/customers/preferences');
    return response.data;
  } catch (error) {
    console.error('获取顾客偏好失败:', error);
    throw error;
  }
};

// 获取顾客反馈记录
export const getCustomerFeedback = async (): Promise<CustomerFeedback[]> => {
  try {
    const response = await apiClient.get('/api/customers/feedback');
    return response.data;
  } catch (error) {
    console.error('获取顾客反馈失败:', error);
    throw error;
  }
};

// 获取积分记录
export const getLoyaltyPoints = async (): Promise<LoyaltyPoint[]> => {
  try {
    const response = await apiClient.get('/api/customers/points');
    return response.data;
  } catch (error) {
    console.error('获取积分记录失败:', error);
    throw error;
  }
};

// 创建反馈
export const createFeedback = async (feedback: CreateFeedbackRequest): Promise<{ success: boolean; id: string; message: string }> => {
  try {
    const response = await apiClient.post('/api/customers/feedback', feedback);
    return response.data;
  } catch (error) {
    console.error('创建反馈失败:', error);
    throw error;
  }
};