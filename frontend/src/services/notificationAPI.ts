import { apiClient } from './api'

const API_BASE_URL = '/api/notifications'

export interface Notification {
  id: string
  type: 'BATCH_COMPLETED' | 'BATCH_FAILED' | 'QUALITY_ALERT' | 'RECIPE_UPDATED' | 'SYSTEM_MAINTENANCE' | 'USER_MENTION' | 'REPORT_READY'
  title: string
  content: string
  isRead: boolean
  data?: string
  createdAt: string
  updatedAt: string
}

export interface NotificationResponse {
  success: boolean
  data?: {
    notifications: Notification[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
    unreadCount: number
  }
  message?: string
}

export interface UnreadCountResponse {
  success: boolean
  data?: {
    unreadCount: number
  }
  message?: string
}

// 获取通知列表
export const getNotifications = async (params?: {
  page?: number
  limit?: number
  unreadOnly?: boolean
}): Promise<NotificationResponse> => {
  const response = await apiClient.get(API_BASE_URL, { params })
  return response.data
}

// 获取未读通知数量
export const getUnreadCount = async (): Promise<UnreadCountResponse> => {
  const response = await apiClient.get(`${API_BASE_URL}/unread-count`)
  return response.data
}

// 标记通知为已读
export const markAsRead = async (notificationId: string): Promise<{ success: boolean; message?: string }> => {
  const response = await apiClient.patch(`${API_BASE_URL}/${notificationId}/read`)
  return response.data
}

// 批量标记通知为已读
export const markBatchAsRead = async (notificationIds: string[]): Promise<{ success: boolean; message?: string }> => {
  const response = await apiClient.patch(`${API_BASE_URL}/batch/read`, { notificationIds })
  return response.data
}

// 标记所有通知为已读
export const markAllAsRead = async (): Promise<{ success: boolean; message?: string }> => {
  const response = await apiClient.patch(`${API_BASE_URL}/all/read`)
  return response.data
}

// 删除通知
export const deleteNotification = async (notificationId: string): Promise<{ success: boolean; message?: string }> => {
  const response = await apiClient.delete(`${API_BASE_URL}/${notificationId}`)
  return response.data
}

export default {
  getNotifications,
  getUnreadCount,
  markAsRead,
  markBatchAsRead,
  markAllAsRead,
  deleteNotification
}