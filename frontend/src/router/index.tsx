import React, { Suspense } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { ErrorBoundary } from 'react-error-boundary'

// 组件导入
import Layout from '../components/Layout'
import ProtectedRoute from '../components/ProtectedRoute'
import ErrorFallback from '../components/ErrorFallback'
import LoadingSpinner from '../components/LoadingSpinner'

// 页面组件懒加载
const Dashboard = React.lazy(() => import('../pages/Dashboard'))
const Login = React.lazy(() => import('../pages/Login'))
const Recipes = React.lazy(() => import('../pages/Recipes'))
const RecipeDetail = React.lazy(() => import('../pages/RecipeDetail'))
const CreateRecipe = React.lazy(() => import('../pages/CreateRecipe'))
const EditRecipe = React.lazy(() => import('../pages/EditRecipe'))
const Batches = React.lazy(() => import('../pages/Batches'))
const BatchDetail = React.lazy(() => import('../pages/BatchDetail'))
const CreateBatch = React.lazy(() => import('../pages/CreateBatch'))
const EditBatch = React.lazy(() => import('../pages/EditBatch'))
const Analysis = React.lazy(() => import('../pages/Analysis'))
const Reports = React.lazy(() => import('../pages/Reports'))
const Settings = React.lazy(() => import('../pages/Settings'))
const Profile = React.lazy(() => import('../pages/Profile'))
const NotFound = React.lazy(() => import('../pages/NotFound'))

// 权限相关页面
const UserManagement = React.lazy(() => import('../pages/UserManagement'))
const AuditLog = React.lazy(() => import('../pages/AuditLog'))
const DataManagement = React.lazy(() => import('../pages/DataManagement'))

// 消费者页面
const Products = React.lazy(() => import('../pages/Products'))
const QualityResults = React.lazy(() => import('../pages/QualityResults'))

// 质检员页面
const ProductManagement = React.lazy(() => import('../pages/ProductManagement'))

// 路由配置
const AppRouter: React.FC = () => {
  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <Suspense fallback={<LoadingSpinner tip="加载页面中..." />}>
        <Routes>
          {/* 登录页面 */}
          <Route path="/login" element={<Login />} />
          
          {/* 受保护的路由 */}
          <Route
            path="/*"
            element={
              <ProtectedRoute>
                <Layout>
                  <Routes>
                    {/* 仪表板 */}
                    <Route path="/" element={<Dashboard />} />
                    <Route path="/dashboard" element={<Navigate to="/" replace />} />
                    
                    {/* 配方管理 */}
                    <Route path="/recipes" element={<Recipes />} />
                    <Route path="/recipes/new" element={<CreateRecipe />} />
                    <Route path="/recipes/:id" element={<RecipeDetail />} />
                    <Route path="/recipes/:id/edit" element={<EditRecipe />} />
                    
                    {/* 批次管理 */}
                    <Route path="/batches" element={<Batches />} />
                    <Route path="/batches/new" element={<CreateBatch />} />
                    <Route path="/batches/:id" element={<BatchDetail />} />
                    <Route path="/batches/:id/edit" element={<EditBatch />} />
                    
                    {/* AI分析 */}
                    <Route path="/analysis" element={<Analysis />} />
                    <Route path="/analysis/:id" element={<Analysis />} />
                    
                    {/* 报告中心 */}
                    <Route path="/reports" element={<Reports />} />
                    <Route path="/reports/:id" element={<Reports />} />

                    {/* 用户管理 - 仅管理员 */}
                    <Route path="/users" element={<UserManagement />} />

                    {/* 消费者页面 - 产品信息和质量结果 */}
                    <Route path="/products" element={<Products />} />
                    <Route path="/quality-results" element={<QualityResults />} />

                    {/* 店面管理员和操作员页面 - 产品管理 */}
                    <Route 
                      path="/product-management" 
                      element={
                        <ProtectedRoute requiredRoles={['MANAGER', 'USER']}>
                          <ProductManagement />
                        </ProtectedRoute>
                      } 
                    />

                    {/* 审计日志 - 仅管理员 */}
                    <Route 
                      path="/audit" 
                      element={
                        <ProtectedRoute requiredRoles={['ADMIN']}>
                          <AuditLog />
                        </ProtectedRoute>
                      } 
                    />

                    {/* 数据管理 - 仅管理员 */}
                    <Route 
                      path="/data" 
                      element={
                        <ProtectedRoute requiredRoles={['ADMIN']}>
                          <DataManagement />
                        </ProtectedRoute>
                      } 
                    />

                    {/* 系统设置 */}
                    <Route
                      path="/settings"
                      element={
                        <ProtectedRoute requiredRoles={['ADMIN']}>
                          <Settings />
                        </ProtectedRoute>
                      }
                    />
                    
                    {/* 个人资料 */}
                    <Route path="/profile" element={<Profile />} />
                    
                    {/* 404页面 */}
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </Layout>
              </ProtectedRoute>
            }
          />
        </Routes>
      </Suspense>
    </ErrorBoundary>
  )
}

export default AppRouter
