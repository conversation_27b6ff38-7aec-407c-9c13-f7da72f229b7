/* =============================================================================
   Yogurt AI QC - 全局样式
   主色调：银红色 #F05654
   辅助色：浅灰色 #A8ABB0
   ============================================================================= */

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #262626;
  background-color: #fafafa;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100%;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #A8ABB0;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #F05654;
}

/* 主题色相关的CSS变量 */
:root {
  --primary-color: #F05654;
  --primary-hover: #FF6B69;
  --primary-active: #E04442;
  --secondary-color: #A8ABB0;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --text-color: #262626;
  --text-secondary: #A8ABB0;
  --border-color: #A8ABB0;
  --background-color: #fafafa;
  --card-background: #ffffff;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* 通用工具类 */
.text-primary {
  color: var(--primary-color) !important;
}

.text-secondary {
  color: var(--secondary-color) !important;
}

.bg-primary {
  background-color: var(--primary-color) !important;
}

.bg-secondary {
  background-color: var(--secondary-color) !important;
}

.border-primary {
  border-color: var(--primary-color) !important;
}

.shadow-light {
  box-shadow: var(--shadow-light) !important;
}

.shadow-medium {
  box-shadow: var(--shadow-medium) !important;
}

.shadow-heavy {
  box-shadow: var(--shadow-heavy) !important;
}

/* 布局相关 */
.full-height {
  height: 100vh;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

/* 响应式断点 */
@media (max-width: 576px) {
  .mobile-hidden {
    display: none !important;
  }
}

@media (max-width: 768px) {
  .tablet-hidden {
    display: none !important;
  }
}

@media (max-width: 992px) {
  .desktop-hidden {
    display: none !important;
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 自定义Ant Design组件样式 */
.ant-layout {
  background: var(--background-color) !important;
}

.ant-layout-sider {
  background: var(--card-background) !important;
  box-shadow: var(--shadow-light);
}

.ant-layout-header {
  background: var(--card-background) !important;
}

/* 仅移除仪表板Card组件的边框和阴影 */
.dashboard-container .ant-card {
  border: none !important;
  box-shadow: none !important;
}

.ant-menu-item-selected {
  background-color: rgba(240, 86, 84, 0.1) !important;
  color: var(--primary-color) !important;
}

.ant-menu-item:hover {
  color: var(--primary-color) !important;
}

.ant-btn-primary {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.ant-btn-primary:hover {
  background-color: var(--primary-hover) !important;
  border-color: var(--primary-hover) !important;
}

.ant-btn-primary:active {
  background-color: var(--primary-active) !important;
  border-color: var(--primary-active) !important;
}

/* 卡片样式增强 */
.custom-card {
  border-radius: 12px;
  box-shadow: var(--shadow-light);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.custom-card:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-secondary);
}

.empty-state .ant-empty-description {
  color: var(--text-secondary);
}

/* 状态标签 */
.status-tag {
  border-radius: 12px;
  font-weight: 500;
  font-size: 12px;
  padding: 2px 8px;
}

.status-success {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-warning {
  background-color: #fffbe6;
  color: #faad14;
  border: 1px solid #ffe58f;
}

.status-error {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.status-processing {
  background-color: rgba(240, 86, 84, 0.1);
  color: var(--primary-color);
  border: 1px solid rgba(240, 86, 84, 0.3);
}

/* 表格样式增强 */
.custom-table .ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.custom-table .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  color: var(--text-color);
}

.custom-table .ant-table-tbody > tr:hover > td {
  background-color: rgba(240, 86, 84, 0.05) !important;
}

/* 表单样式增强 */
.custom-form .ant-form-item-label > label {
  font-weight: 500;
  color: var(--text-color);
}

.custom-form .ant-input:focus,
.custom-form .ant-input-focused {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(240, 86, 84, 0.2);
}

/* 图表容器 */
.chart-container {
  background: var(--card-background);
  border-radius: 12px;
  padding: 24px;
  box-shadow: var(--shadow-light);
  border: 1px solid #f0f0f0;
}

/* 页面标题 */
.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 24px;
}

.page-subtitle {
  font-size: 16px;
  color: var(--text-secondary);
  margin-bottom: 16px;
}
