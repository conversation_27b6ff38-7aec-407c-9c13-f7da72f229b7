/**
 * 权限和角色定义
 * 基于角色的访问控制 (RBAC) 系统
 */

// 用户角色枚举
export enum UserRole {
  ADMIN = 'ADMIN',
  MANAGER = 'MANAGER', // 店面管理员
  USER = 'USER',
  VIEWER = 'VIEWER', // 观察员/消费者
}

// 权限枚举
export enum Permission {
  // 用户管理权限
  USER_CREATE = 'user:create',
  USER_READ = 'user:read',
  USER_UPDATE = 'user:update',
  USER_DELETE = 'user:delete',
  USER_MANAGE_ROLES = 'user:manage_roles',

  // 配方管理权限
  RECIPE_CREATE = 'recipe:create',
  RECIPE_READ = 'recipe:read',
  RECIPE_UPDATE = 'recipe:update',
  RECIPE_DELETE = 'recipe:delete',
  RECIPE_PUBLISH = 'recipe:publish',

  // 批次管理权限
  BATCH_CREATE = 'batch:create',
  BATCH_READ = 'batch:read',
  BATCH_UPDATE = 'batch:update',
  BATCH_DELETE = 'batch:delete',
  BATCH_APPROVE = 'batch:approve',

  // AI分析权限
  AI_ANALYZE = 'ai:analyze',
  AI_RESULTS_READ = 'ai:results_read',
  AI_RESULTS_EXPORT = 'ai:results_export',

  // 质量管理权限
  QUALITY_ASSESS = 'quality:assess',
  QUALITY_READ = 'quality:read',
  QUALITY_APPROVE = 'quality:approve',
  QUALITY_EXPORT = 'quality:export',

  // 报告权限
  REPORT_CREATE = 'report:create',
  REPORT_READ = 'report:read',
  REPORT_EXPORT = 'report:export',
  REPORT_SHARE = 'report:share',

  // 系统管理权限
  SYSTEM_SETTINGS = 'system:settings',
  SYSTEM_LOGS = 'system:logs',
  SYSTEM_BACKUP = 'system:backup',
  SYSTEM_MAINTENANCE = 'system:maintenance',

  // 数据管理权限
  DATA_IMPORT = 'data:import',
  DATA_EXPORT = 'data:export',
  DATA_BULK_OPERATIONS = 'data:bulk_operations',

  // 审计权限
  AUDIT_READ = 'audit:read',
  AUDIT_EXPORT = 'audit:export',

  // 消费者查看权限
  CONSUMER_PRODUCT_INFO = 'consumer:product_info',
  CONSUMER_QUALITY_RESULTS = 'consumer:quality_results',
}

// 角色权限映射
export const RolePermissions: Record<UserRole, Permission[]> = {
  [UserRole.ADMIN]: [
    // 管理员拥有所有权限
    ...Object.values(Permission)
  ],

  [UserRole.MANAGER]: [
    // 店面管理员权限（可以管理店面用户和所有业务操作）
    Permission.USER_CREATE,
    Permission.USER_READ,
    Permission.USER_UPDATE,
    Permission.USER_DELETE,
    Permission.RECIPE_CREATE,
    Permission.RECIPE_READ,
    Permission.RECIPE_UPDATE,
    Permission.RECIPE_DELETE,
    Permission.RECIPE_PUBLISH,
    Permission.BATCH_CREATE,
    Permission.BATCH_READ,
    Permission.BATCH_UPDATE,
    Permission.BATCH_DELETE,
    Permission.BATCH_APPROVE,
    Permission.AI_ANALYZE,
    Permission.AI_RESULTS_READ,
    Permission.AI_RESULTS_EXPORT,
    Permission.QUALITY_ASSESS,
    Permission.QUALITY_READ,
    Permission.QUALITY_APPROVE,
    Permission.QUALITY_EXPORT,
    Permission.REPORT_CREATE,
    Permission.REPORT_READ,
    Permission.REPORT_EXPORT,
    Permission.REPORT_SHARE,
    Permission.DATA_EXPORT,
  ],

  [UserRole.USER]: [
    // 质检员权限
    Permission.RECIPE_READ,
    Permission.BATCH_CREATE,
    Permission.BATCH_READ,
    Permission.BATCH_UPDATE,
    Permission.AI_ANALYZE,
    Permission.AI_RESULTS_READ,
    Permission.QUALITY_ASSESS,
    Permission.QUALITY_READ,
    Permission.REPORT_CREATE,
    Permission.REPORT_READ,
    Permission.REPORT_EXPORT,
  ],

  [UserRole.VIEWER]: [
    // 观察员/消费者权限（面向酸奶消费者的只读权限）
    Permission.CONSUMER_PRODUCT_INFO,
    Permission.CONSUMER_QUALITY_RESULTS,
    Permission.QUALITY_READ,
    Permission.REPORT_READ,
  ],
};

// 角色显示名称
export const RoleDisplayNames: Record<UserRole, string> = {
  [UserRole.ADMIN]: '系统管理员',
  [UserRole.MANAGER]: '店面管理员',
  [UserRole.USER]: '操作员',
  [UserRole.VIEWER]: '顾客',
};

// 角色描述
export const RoleDescriptions: Record<UserRole, string> = {
  [UserRole.ADMIN]: '拥有系统所有权限，可以管理用户、系统设置和所有数据',
  [UserRole.MANAGER]: '店面管理员，可以管理店面用户、审批业务流程、查看店面所有数据',
  [UserRole.USER]: '操作员，可以进行酸奶质检操作、查看检测结果、创建质量报告',
  [UserRole.VIEWER]: '顾客角色，可以查看产品质量信息和检测结果，了解产品安全性',
};

// 角色颜色主题
export const RoleColors: Record<UserRole, string> = {
  [UserRole.ADMIN]: '#ff4d4f', // 红色
  [UserRole.MANAGER]: '#fa8c16', // 橙色
  [UserRole.USER]: '#1890ff', // 蓝色
  [UserRole.VIEWER]: '#52c41a', // 绿色
};

// 角色图标
export const RoleIcons: Record<UserRole, string> = {
  [UserRole.ADMIN]: 'crown',
  [UserRole.MANAGER]: 'team',
  [UserRole.USER]: 'experiment',
  [UserRole.VIEWER]: 'eye',
};

// 权限分组
export const PermissionGroups = {
  user: {
    name: '用户管理',
    permissions: [
      Permission.USER_CREATE,
      Permission.USER_READ,
      Permission.USER_UPDATE,
      Permission.USER_DELETE,
      Permission.USER_MANAGE_ROLES,
    ],
  },
  recipe: {
    name: '配方管理',
    permissions: [
      Permission.RECIPE_CREATE,
      Permission.RECIPE_READ,
      Permission.RECIPE_UPDATE,
      Permission.RECIPE_DELETE,
      Permission.RECIPE_PUBLISH,
    ],
  },
  batch: {
    name: '批次管理',
    permissions: [
      Permission.BATCH_CREATE,
      Permission.BATCH_READ,
      Permission.BATCH_UPDATE,
      Permission.BATCH_DELETE,
      Permission.BATCH_APPROVE,
    ],
  },
  ai: {
    name: 'AI分析',
    permissions: [
      Permission.AI_ANALYZE,
      Permission.AI_RESULTS_READ,
      Permission.AI_RESULTS_EXPORT,
    ],
  },
  quality: {
    name: '质量管理',
    permissions: [
      Permission.QUALITY_ASSESS,
      Permission.QUALITY_READ,
      Permission.QUALITY_APPROVE,
      Permission.QUALITY_EXPORT,
    ],
  },
  report: {
    name: '报告管理',
    permissions: [
      Permission.REPORT_CREATE,
      Permission.REPORT_READ,
      Permission.REPORT_EXPORT,
      Permission.REPORT_SHARE,
    ],
  },
  system: {
    name: '系统管理',
    permissions: [
      Permission.SYSTEM_SETTINGS,
      Permission.SYSTEM_LOGS,
      Permission.SYSTEM_BACKUP,
      Permission.SYSTEM_MAINTENANCE,
    ],
  },
  data: {
    name: '数据管理',
    permissions: [
      Permission.DATA_IMPORT,
      Permission.DATA_EXPORT,
      Permission.DATA_BULK_OPERATIONS,
    ],
  },
  audit: {
    name: '审计管理',
    permissions: [
      Permission.AUDIT_READ,
      Permission.AUDIT_EXPORT,
    ],
  },
  consumer: {
    name: '消费者查看',
    permissions: [
      Permission.CONSUMER_PRODUCT_INFO,
      Permission.CONSUMER_QUALITY_RESULTS,
    ],
  },
};
