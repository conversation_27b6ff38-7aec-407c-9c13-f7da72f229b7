import React, { useEffect } from 'react'
import { useDispatch } from 'react-redux'
import { App as AntApp } from 'antd'

import { checkAuthStatus, clearError, initializeAuth } from './store/slices/authSlice'
import { setGlobalMessage } from './utils/messageHandler'
import { hasValidToken, clearAuthStorage } from './utils/authUtils'
import AppRouter from './router'

const App: React.FC = () => {
  const dispatch = useDispatch()

  useEffect(() => {
    // 检查认证状态
    if (hasValidToken()) {
      // 设置超时机制，防止认证检查一直pending
      const timeoutId = setTimeout(() => {
        console.warn('认证检查超时，清理状态')
        clearAuthStorage()
        dispatch(clearError())
      }, 5000) // 5秒超时

      dispatch(checkAuthStatus() as any)
        .finally(() => {
          clearTimeout(timeoutId)
        })
    } else {
      // 如果没有有效token，清理并初始化认证状态
      clearAuthStorage()
      dispatch(initializeAuth())
    }
  }, [dispatch])

  return (
    <AntApp>
      <AppContent />
    </AntApp>
  )
}

const AppContent: React.FC = () => {
  const { message } = AntApp.useApp()

  useEffect(() => {
    // 设置全局消息实例
    setGlobalMessage(message)
  }, [message])

  return <AppRouter />
}

export default App
