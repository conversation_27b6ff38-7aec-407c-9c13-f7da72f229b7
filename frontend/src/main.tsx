import React from 'react'
import ReactDOM from 'react-dom/client'
import { Provider } from 'react-redux'
import { PersistGate } from 'redux-persist/integration/react'
import { BrowserRouter } from 'react-router-dom'
import { ConfigProvider } from 'antd'

import { ErrorBoundary } from 'react-error-boundary'
import zhCN from 'antd/locale/zh_CN'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

import App from './App'
import { store, persistor } from './store'
import ErrorFallback from './components/ErrorFallback'
import LoadingSpinner from './components/LoadingSpinner'
import './utils/antdConfig' // 导入 Antd 配置，处理兼容性警告
import { initPerformanceConfig, performanceMonitor } from './utils/performanceConfig'
import { optimizeResourceLoading } from './utils/simplePreloader'
import { productionOptimizer } from './utils/productionOptimizer'
import './index.css'

// 初始化性能优化配置
initPerformanceConfig()

// 优化资源加载
optimizeResourceLoading()

// 初始化生产环境优化
productionOptimizer.initialize()

// 启动性能监控
performanceMonitor.monitorPageLoad()

// 优化预加载警告 - 简化版本
const optimizePreloading = () => {
  // 在页面加载完成后清理未使用的预加载
  window.addEventListener('load', () => {
    setTimeout(() => {
      const preloadLinks = document.querySelectorAll('link[rel="preload"]');
      preloadLinks.forEach(link => {
        const href = (link as HTMLLinkElement).href;

        // 检查资源是否实际被使用
        let isUsed = false;

        if (href.includes('index.css')) {
          // CSS通过Vite自动加载，不需要预加载
          isUsed = false;
        } else if (href.includes('antd-icons')) {
          // 检查是否有图标元素
          isUsed = document.querySelector('.anticon') !== null;
        }

        if (!isUsed) {
          console.debug(`移除未使用的预加载: ${href}`);
          link.remove();
        }
      });
    }, 1000); // 1秒后检查
  });
};

optimizePreloading();

// 设置 dayjs 中文语言
dayjs.locale('zh-cn')

// Ant Design 主题配置 - 银红色主题
const theme = {
  token: {
    colorPrimary: '#F05654', // 银红色主色调
    colorPrimaryHover: '#FF6B69', // 悬停时的颜色
    colorPrimaryActive: '#E04442', // 激活时的颜色
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#ff4d4f',
    colorInfo: '#A8ABB0', // 浅灰色辅助色
    colorText: '#262626',
    colorTextSecondary: '#A8ABB0',
    colorBorder: '#A8ABB0',
    colorBorderSecondary: '#f0f0f0',
    borderRadius: 8,
    fontSize: 14,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
  },
  components: {
    Button: {
      borderRadius: 8,
      fontWeight: 500,
    },
    Card: {
      borderRadius: 12,
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
    },
    Input: {
      borderRadius: 8,
    },
    Select: {
      borderRadius: 8,
    },
    Menu: {
      itemSelectedBg: 'rgba(240, 86, 84, 0.1)',
      itemSelectedColor: '#F05654',
    },
    Layout: {
      siderBg: '#ffffff',
      headerBg: '#ffffff',
    },
    Table: {
      borderRadius: 8,
    },
  },
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={(error, errorInfo) => {
        console.error('Application Error:', error, errorInfo)
        // 这里可以添加错误上报逻辑
      }}
    >
      <Provider store={store}>
        <PersistGate loading={<LoadingSpinner />} persistor={persistor}>
          <BrowserRouter>
            <ConfigProvider locale={zhCN} theme={theme}>
              <App />
            </ConfigProvider>
          </BrowserRouter>
        </PersistGate>
      </Provider>
    </ErrorBoundary>
  </React.StrictMode>,
)
