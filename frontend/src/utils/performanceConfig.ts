/**
 * 性能优化配置
 * 解决浏览器性能警告和优化用户体验
 */

/**
 * 初始化性能优化配置
 */
export const initPerformanceConfig = async () => {
  if (typeof window === 'undefined') return;

  // 1. 优化控制台输出
  optimizeConsoleOutput();

  // 2. 优化事件监听器
  optimizeEventListeners();

  // 3. 优化滚动性能
  optimizeScrollPerformance();

  // 4. 预加载关键资源（异步）
  preloadCriticalResources().catch(error => {
    console.warn('预加载资源时出错:', error);
  });
};

/**
 * 优化控制台输出
 */
const optimizeConsoleOutput = () => {
  if (process.env.NODE_ENV === 'production') {
    // 生产环境禁用控制台输出
    console.log = () => {};
    console.warn = () => {};
    console.info = () => {};
  } else {
    // 开发环境过滤无用警告
    const originalWarn = console.warn;
    console.warn = (...args) => {
      const message = args[0];
      if (typeof message === 'string') {
        // 过滤Vite HMR连接信息
        if (message.includes('[vite]') && message.includes('connecting')) {
          return;
        }
        // 过滤被动事件监听器警告
        if (message.includes('Added non-passive event listener')) {
          return;
        }
        // 过滤违规警告
        if (message.includes('[Violation]')) {
          return;
        }
      }
      originalWarn.apply(console, args);
    };
  }
};

/**
 * 优化事件监听器
 */
const optimizeEventListeners = () => {
  // 检测浏览器支持
  let supportsPassive = false;
  try {
    const opts = Object.defineProperty({}, 'passive', {
      get() {
        supportsPassive = true;
        return false;
      },
    });
    window.addEventListener('testPassive', () => {}, opts);
    window.removeEventListener('testPassive', () => {}, opts);
  } catch (e) {
    // 浏览器不支持被动事件监听器，使用默认行为
    console.debug('浏览器不支持被动事件监听器:', e);
  }

  if (!supportsPassive) return;

  // 重写 addEventListener 以自动添加 passive 选项
  const originalAddEventListener = EventTarget.prototype.addEventListener;
  EventTarget.prototype.addEventListener = function(
    type: string,
    listener: EventListenerOrEventListenerObject,
    options?: boolean | AddEventListenerOptions
  ) {
    // 为特定事件类型自动添加 passive 选项
    const passiveEvents = ['scroll', 'wheel', 'touchstart', 'touchmove', 'mousewheel'];
    
    if (passiveEvents.includes(type)) {
      if (typeof options === 'boolean') {
        options = { capture: options, passive: true };
      } else if (!options) {
        options = { passive: true };
      } else if (options.passive === undefined) {
        options = { ...options, passive: true };
      }
    }

    return originalAddEventListener.call(this, type, listener, options);
  };
};

/**
 * 优化滚动性能
 */
const optimizeScrollPerformance = () => {
  // 添加 CSS 优化
  const style = document.createElement('style');
  style.textContent = `
    /* 优化滚动性能 */
    * {
      -webkit-overflow-scrolling: touch;
    }
    
    /* 优化动画性能 */
    .ant-motion-collapse,
    .ant-motion-collapse-active {
      will-change: height;
    }
    
    .ant-drawer,
    .ant-modal {
      will-change: transform, opacity;
    }
    
    /* 优化表格滚动 */
    .ant-table-tbody {
      will-change: scroll-position;
    }
    
    /* 减少重绘 */
    .ant-layout-sider {
      will-change: width;
    }
  `;
  document.head.appendChild(style);
};

/**
 * 预加载关键资源
 * 使用智能预加载器，避免预加载不存在的资源
 */
const preloadCriticalResources = async () => {
  try {
    // 动态导入资源预加载器
    const { resourcePreloader } = await import('./resourcePreloader');
    await resourcePreloader.initializePreloading();
  } catch (error) {
    console.warn('资源预加载失败:', error);

    // 降级方案：只做基本的预连接
    const apiPreconnect = document.createElement('link');
    apiPreconnect.rel = 'preconnect';
    apiPreconnect.href = 'http://localhost:3010';
    document.head.appendChild(apiPreconnect);
  }
};

/**
 * 性能监控
 */
export const performanceMonitor = {
  /**
   * 监控页面加载性能
   */
  monitorPageLoad: () => {
    if (typeof window === 'undefined' || !window.performance) return;

    window.addEventListener('load', () => {
      setTimeout(() => {
        // 使用现代的 Performance API
        if (window.performance.getEntriesByType) {
          const navigation = window.performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
          if (navigation) {
            const loadTime = navigation.loadEventEnd - navigation.fetchStart;

            if (process.env.NODE_ENV === 'development') {
              console.log(`页面加载时间: ${Math.round(loadTime)}ms`);
              console.log(`DNS查询: ${Math.round(navigation.domainLookupEnd - navigation.domainLookupStart)}ms`);
              console.log(`TCP连接: ${Math.round(navigation.connectEnd - navigation.connectStart)}ms`);
              console.log(`首字节时间: ${Math.round(navigation.responseStart - navigation.fetchStart)}ms`);
            }

            // 发送到分析服务的示例
            if (typeof window !== 'undefined' && (window as any).gtag) {
              (window as any).gtag('event', 'page_load_time', {
                value: Math.round(loadTime),
                custom_parameter: 'performance_monitoring'
              });
            }
          }
        }
      }, 0);
    });
  },

  /**
   * 监控组件渲染性能
   */
  measureRender: (componentName: string, fn: () => void) => {
    if (process.env.NODE_ENV === 'development') {
      const start = performance.now();
      fn();
      const end = performance.now();
      console.log(`${componentName} 渲染时间: ${(end - start).toFixed(2)}ms`);
    } else {
      fn();
    }
  },
};

/**
 * 内存优化
 */
export const memoryOptimizer = {
  /**
   * 清理定时器
   */
  clearTimers: (timers: (NodeJS.Timeout | number)[]) => {
    timers.forEach(timer => {
      // 统一处理，因为clearTimeout和clearInterval在现代浏览器中可以互换使用
      clearTimeout(timer as any);
      clearInterval(timer as any);
    });
  },

  /**
   * 清理事件监听器
   */
  clearEventListeners: (
    element: Element | Window | Document,
    events: Array<{ type: string; handler: EventListener }>
  ) => {
    events.forEach(({ type, handler }) => {
      element.removeEventListener(type, handler);
    });
  },
};
