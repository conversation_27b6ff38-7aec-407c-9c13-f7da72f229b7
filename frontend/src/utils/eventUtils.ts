/**
 * 事件处理工具函数
 * 解决浏览器被动事件监听器警告
 */

/**
 * 检测浏览器是否支持被动事件监听器
 */
let supportsPassive = false;
try {
  const opts = Object.defineProperty({}, 'passive', {
    get() {
      supportsPassive = true;
      return false;
    },
  });
  window.addEventListener('testPassive', () => {}, opts);
  window.removeEventListener('testPassive', () => {}, opts);
} catch (e) {
  // 浏览器不支持被动事件监听器
}

/**
 * 获取事件监听器选项
 * @param passive 是否使用被动模式
 * @param capture 是否在捕获阶段处理
 * @returns 事件监听器选项
 */
export const getEventListenerOptions = (
  passive: boolean = true,
  capture: boolean = false
): boolean | AddEventListenerOptions => {
  if (supportsPassive) {
    return {
      passive,
      capture,
    };
  }
  return capture;
};

/**
 * 安全地添加事件监听器
 * @param element 目标元素
 * @param event 事件名称
 * @param handler 事件处理函数
 * @param options 选项
 */
export const addPassiveEventListener = (
  element: Element | Window | Document,
  event: string,
  handler: EventListener,
  options: { passive?: boolean; capture?: boolean } = {}
): void => {
  const { passive = true, capture = false } = options;
  const listenerOptions = getEventListenerOptions(passive, capture);
  
  element.addEventListener(event, handler, listenerOptions);
};

/**
 * 安全地移除事件监听器
 * @param element 目标元素
 * @param event 事件名称
 * @param handler 事件处理函数
 * @param options 选项
 */
export const removePassiveEventListener = (
  element: Element | Window | Document,
  event: string,
  handler: EventListener,
  options: { capture?: boolean } = {}
): void => {
  const { capture = false } = options;
  const listenerOptions = getEventListenerOptions(false, capture);
  
  element.removeEventListener(event, handler, listenerOptions);
};

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @returns 防抖后的函数
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(null, args), wait);
  };
};

/**
 * 节流函数
 * @param func 要节流的函数
 * @param limit 限制时间（毫秒）
 * @returns 节流后的函数
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func.apply(null, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};
