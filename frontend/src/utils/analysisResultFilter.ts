/**
 * 分析结果过滤工具
 * 用于过滤多模态大模型输出中不相关的部分，只保留菌群分析结果
 */

/**
 * 过滤分析结果，移除情绪分析和老年医学影像分析部分
 * @param result 原始分析结果
 * @returns 过滤后的分析结果
 */
export const filterAnalysisResult = (result: string): string => {
  if (!result || typeof result !== 'string') {
    return result || ''
  }

  let filteredResult = result

  // 移除情绪分析相关部分（考虑各种可能的标题变体）
  // 匹配以 ## 开头的情绪分析部分，直到下一个 ## 或文档结束
  filteredResult = filteredResult.replace(/##?\s*情绪分析[^#]*?(?=##|$)/gis, '')
  filteredResult = filteredResult.replace(/##?\s*.*?情绪.*?分析[^#]*?(?=##|$)/gis, '')

  // 移除 **第一部分：情绪分析** 格式的内容
  filteredResult = filteredResult.replace(/\*\*第一部分：情绪分析.*?\*\*[^*]*?(?=\*\*第二部分|\*\*.*?部分|$)/gis, '')

  // 移除从开头到第二部分之间包含情绪分析的内容
  filteredResult = filteredResult.replace(/^.*?情绪分析.*?(?=\*\*第二部分|\*\*.*?菌群|\*\*.*?分析|##.*?菌群|##.*?分析|$)/gis, '')

  // 移除老年医学影像分析相关部分
  filteredResult = filteredResult.replace(/##?\s*老年医学影像分析[^#]*?(?=##|$)/gis, '')
  filteredResult = filteredResult.replace(/##?\s*.*?老年.*?医学.*?影像[^#]*?(?=##|$)/gis, '')
  filteredResult = filteredResult.replace(/##?\s*.*?医学.*?影像.*?分析[^#]*?(?=##|$)/gis, '')

  // 移除人脸相关分析
  filteredResult = filteredResult.replace(/##?\s*.*?人脸.*?分析[^#]*?(?=##|$)/gis, '')
  filteredResult = filteredResult.replace(/##?\s*.*?面部.*?分析[^#]*?(?=##|$)/gis, '')

  // 移除包含特定关键词的段落
  filteredResult = filteredResult.replace(/.*?很抱歉.*?没有看到任何人物面部.*?(?=##|\*\*|$)/gis, '')
  filteredResult = filteredResult.replace(/.*?请您提供老年病患者的医学影像.*?(?=##|\*\*|$)/gis, '')
  filteredResult = filteredResult.replace(/.*?这张照片中没有人物.*?(?=##|\*\*|$)/gis, '')
  filteredResult = filteredResult.replace(/.*?无法进行情绪识别.*?(?=##|\*\*|$)/gis, '')
  
  // 清理连续的分隔线
  filteredResult = filteredResult.replace(/---+/g, '')
  
  // 清理多余的空行（保留最多两个连续换行）
  filteredResult = filteredResult.replace(/\n{3,}/g, '\n\n')
  
  // 清理开头和结尾的空白
  filteredResult = filteredResult.trim()

  // 如果过滤后内容为空或太短，返回提示信息
  if (!filteredResult || filteredResult.length < 50) {
    return '暂无相关的菌群分析结果，请检查图像内容或重新分析。'
  }

  // 清理多余的标题（如"第二部分：酸奶菌群分析"）
  filteredResult = filteredResult.replace(/\*\*第二部分：.*?\*\*\s*/gi, '')
  filteredResult = filteredResult.replace(/\*\*.*?部分：.*?菌群.*?\*\*\s*/gi, '')

  // 添加专业的开场白
  const professionalIntro = '作为一个酸奶菌群的分析专家，我很乐意为您服务。\n\n'

  // 如果内容不是以专业开场白开始，则添加
  if (!filteredResult.startsWith('作为一个酸奶菌群的分析专家')) {
    filteredResult = professionalIntro + filteredResult
  }

  return filteredResult
}

/**
 * 检查分析结果是否包含不相关内容
 * @param result 分析结果
 * @returns 是否包含不相关内容
 */
export const hasIrrelevantContent = (result: string): boolean => {
  if (!result || typeof result !== 'string') {
    return false
  }

  const irrelevantPatterns = [
    /情绪分析/i,
    /老年医学影像分析/i,
    /人脸.*?分析/i,
    /面部.*?分析/i,
    /很抱歉.*?没有看到任何人物面部/i,
    /请您提供老年病患者的医学影像/i,
    /第一部分：情绪分析/i,
    /这张照片中没有人物/i,
    /无法进行情绪识别/i
  ]

  return irrelevantPatterns.some(pattern => pattern.test(result))
}

/**
 * 获取过滤统计信息
 * @param originalResult 原始结果
 * @param filteredResult 过滤后结果
 * @returns 过滤统计信息
 */
export const getFilterStats = (originalResult: string, filteredResult: string) => {
  const originalLength = originalResult?.length || 0
  const filteredLength = filteredResult?.length || 0
  const removedLength = originalLength - filteredLength
  const removedPercentage = originalLength > 0 ? Math.round((removedLength / originalLength) * 100) : 0

  return {
    originalLength,
    filteredLength,
    removedLength,
    removedPercentage,
    hasIrrelevantContent: hasIrrelevantContent(originalResult)
  }
}

/**
 * 带日志的过滤函数（用于调试）
 * @param result 原始分析结果
 * @param enableLogging 是否启用日志
 * @returns 过滤后的分析结果
 */
export const filterAnalysisResultWithLogging = (result: string, enableLogging = false): string => {
  const filteredResult = filterAnalysisResult(result)
  
  if (enableLogging && hasIrrelevantContent(result)) {
    const stats = getFilterStats(result, filteredResult)
    console.log('🔍 分析结果过滤统计:', stats)
    console.log('📝 原始结果长度:', stats.originalLength)
    console.log('✂️ 过滤后长度:', stats.filteredLength)
    console.log('🗑️ 移除内容比例:', `${stats.removedPercentage}%`)
  }
  
  return filteredResult
}
