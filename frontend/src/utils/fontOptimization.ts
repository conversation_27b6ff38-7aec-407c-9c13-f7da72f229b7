/**
 * 字体优化配置
 * 解决字体预加载警告，优化字体加载性能
 */

/**
 * 优化Ant Design图标字体加载
 */
export const optimizeAntdIconFont = (): void => {
  // 添加字体显示优化
  const style = document.createElement('style');
  style.textContent = `
    /* 优化Ant Design图标字体加载 */
    @font-face {
      font-family: 'anticon';
      font-display: swap; /* 使用字体交换策略 */
    }
    
    /* 确保图标在字体加载前有合理的回退 */
    .anticon {
      font-display: swap;
      /* 字体加载前显示空白而不是不可见文本 */
      text-rendering: optimizeSpeed;
    }
    
    /* 减少字体加载时的布局偏移 */
    .anticon::before {
      display: inline-block;
      width: 1em;
      height: 1em;
    }
  `;
  
  document.head.appendChild(style);
};

/**
 * 智能字体预加载
 * 只在确实需要时预加载字体
 */
export const smartFontPreload = (): void => {
  let hasPreloaded = false;

  // 检查页面是否有Ant Design图标
  const checkForIcons = async () => {
    if (hasPreloaded) return;

    const hasIcons = document.querySelector('.anticon') !== null;

    if (hasIcons) {
      hasPreloaded = true;
      await preloadAntdIconFont();
    }
  };

  // 延迟检查，避免在初始渲染时执行
  setTimeout(() => {
    checkForIcons();
  }, 500);

  // 在DOM变化时检查（但限制频率）
  let checkTimeout: NodeJS.Timeout;
  const observer = new MutationObserver(() => {
    if (hasPreloaded) {
      observer.disconnect();
      return;
    }

    clearTimeout(checkTimeout);
    checkTimeout = setTimeout(() => {
      checkForIcons();
    }, 100);
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['class'],
  });

  // 3秒后停止观察
  setTimeout(() => {
    observer.disconnect();
  }, 3000);
};

/**
 * 预加载Ant Design图标字体
 */
const preloadAntdIconFont = async (): Promise<void> => {
  // 检查是否已经预加载
  const existingPreload = document.querySelector('link[href*="antd-icons"]');
  if (existingPreload) {
    return;
  }

  // 尝试不同的字体路径，但只预加载第一个找到的
  const fontPaths = [
    '/fonts/antd-icons.woff2',
    '/assets/fonts/antd-icons.woff2',
    '/node_modules/@ant-design/icons/lib/fonts/antd-icons.woff2',
  ];

  // 顺序检查，只预加载第一个存在的字体文件
  for (const path of fontPaths) {
    try {
      const response = await fetch(path, { method: 'HEAD' });
      if (response.ok) {
        createFontPreload(path);
        return; // 找到一个就停止
      }
    } catch {
      // 继续尝试下一个路径
    }
  }
};

/**
 * 创建字体预加载链接
 */
const createFontPreload = (href: string): void => {
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = href;
  link.as = 'font';
  link.type = 'font/woff2';
  link.crossOrigin = 'anonymous';

  // 添加加载完成处理
  link.onload = () => {
    console.debug(`字体预加载完成: ${href}`);
  };

  link.onerror = () => {
    console.debug(`字体预加载失败: ${href}`);
    // 移除失败的预加载链接
    link.remove();
  };

  document.head.appendChild(link);
};

/**
 * 清理未使用的字体预加载
 */
export const cleanupUnusedFontPreloads = (): void => {
  window.addEventListener('load', () => {
    setTimeout(() => {
      const fontPreloads = document.querySelectorAll('link[rel="preload"][as="font"]');
      
      fontPreloads.forEach(link => {
        const href = (link as HTMLLinkElement).href;
        
        // 检查字体是否实际被使用
        if (href.includes('antd-icons')) {
          const hasIcons = document.querySelector('.anticon') !== null;
          
          if (!hasIcons) {
            console.debug(`移除未使用的字体预加载: ${href}`);
            link.remove();
          }
        }
      });
    }, 3000); // 3秒后检查
  });
};

/**
 * 初始化字体优化
 */
export const initFontOptimization = (): void => {
  // 优化字体显示
  optimizeAntdIconFont();
  
  // 智能预加载
  smartFontPreload();
  
  // 清理未使用的预加载
  cleanupUnusedFontPreloads();
};

/**
 * 字体加载性能监控
 */
export const monitorFontPerformance = (): void => {
  if (!window.performance?.getEntriesByType) {
    return;
  }

  window.addEventListener('load', () => {
    setTimeout(() => {
      const fontEntries = window.performance.getEntriesByType('resource')
        .filter(entry => entry.name.includes('font'));

      if (fontEntries.length > 0) {
        console.debug('字体加载性能:', fontEntries.map(entry => ({
          name: entry.name,
          duration: entry.duration,
          size: (entry as any).transferSize,
        })));
      }
    }, 1000);
  });
};
