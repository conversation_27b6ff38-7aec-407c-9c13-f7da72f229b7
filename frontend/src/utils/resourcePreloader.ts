/**
 * 智能资源预加载器
 * 只预加载实际需要的资源，避免无用的预加载警告
 */

interface PreloadResource {
  href: string;
  as: string;
  type?: string;
  crossOrigin?: string;
  media?: string;
}

/**
 * 检查资源是否存在
 */
const checkResourceExists = async (url: string): Promise<boolean> => {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch {
    return false;
  }
};

/**
 * 安全地预加载资源
 */
const safePreload = async (resource: PreloadResource): Promise<void> => {
  // 检查资源是否存在
  const exists = await checkResourceExists(resource.href);
  if (!exists) {
    console.debug(`跳过预加载不存在的资源: ${resource.href}`);
    return;
  }

  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = resource.href;
  link.as = resource.as;
  
  if (resource.type) link.type = resource.type;
  if (resource.crossOrigin) link.crossOrigin = resource.crossOrigin;
  if (resource.media) link.media = resource.media;

  // 添加错误处理
  link.onerror = () => {
    console.warn(`预加载资源失败: ${resource.href}`);
    document.head.removeChild(link);
  };

  document.head.appendChild(link);
  console.debug(`成功预加载资源: ${resource.href}`);
};

/**
 * 预连接到域名
 */
const preconnectToDomain = (href: string, crossOrigin: boolean = false): void => {
  const link = document.createElement('link');
  link.rel = 'preconnect';
  link.href = href;
  if (crossOrigin) link.crossOrigin = 'anonymous';
  document.head.appendChild(link);
};

/**
 * DNS预解析
 */
const dnsPrefetch = (href: string): void => {
  const link = document.createElement('link');
  link.rel = 'dns-prefetch';
  link.href = href;
  document.head.appendChild(link);
};

/**
 * 智能资源预加载管理器
 */
export class ResourcePreloader {
  private readonly preloadedResources = new Set<string>();

  /**
   * 预加载关键CSS
   */
  async preloadCriticalCSS(): Promise<void> {
    // 禁用CSS预加载，Vite会自动处理CSS加载
    // 避免"preloaded but not used"警告
    console.debug('CSS预加载已禁用，由Vite自动处理');
  }

  /**
   * 预加载关键JavaScript
   */
  async preloadCriticalJS(): Promise<void> {
    // JavaScript预加载已禁用，由Vite自动处理
    console.debug('JavaScript预加载已禁用，由Vite自动处理');
  }

  /**
   * 预连接到API服务器
   */
  preconnectToAPI(): void {
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3010';
    preconnectToDomain(apiUrl);
    dnsPrefetch(apiUrl);
  }

  /**
   * 预连接到CDN
   */
  preconnectToCDN(): void {
    const cdnDomains = [
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com',
      'https://cdn.jsdelivr.net',
    ];

    cdnDomains.forEach(domain => {
      preconnectToDomain(domain, true);
      dnsPrefetch(domain);
    });
  }

  /**
   * 预加载图片资源
   */
  async preloadImages(images: string[]): Promise<void> {
    for (const image of images) {
      if (!this.preloadedResources.has(image)) {
        await safePreload({
          href: image,
          as: 'image',
        });
        this.preloadedResources.add(image);
      }
    }
  }

  /**
   * 预加载字体（已禁用）
   */
  async preloadFonts(): Promise<void> {
    // 完全禁用字体预加载，避免"preloaded but not used"警告
    // 字体将由CSS按需加载
    console.debug('字体预加载已禁用，避免未使用资源警告');
  }

  /**
   * 初始化所有预加载
   */
  async initializePreloading(): Promise<void> {
    try {
      // 立即预连接，不需要等待
      this.preconnectToAPI();
      this.preconnectToCDN();

      // 并行预加载关键资源
      await Promise.allSettled([
        this.preloadCriticalCSS(),
        this.preloadCriticalJS(),
        this.preloadFonts(),
      ]);

      console.debug('资源预加载初始化完成');
    } catch (error) {
      console.warn('资源预加载初始化失败:', error);
    }
  }

  /**
   * 清理预加载的资源
   */
  cleanup(): void {
    this.preloadedResources.clear();
  }
}

// 创建全局实例
export const resourcePreloader = new ResourcePreloader();

// 自动初始化
if (typeof window !== 'undefined') {
  // 等待DOM加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      resourcePreloader.initializePreloading();
    });
  } else {
    resourcePreloader.initializePreloading();
  }
}
