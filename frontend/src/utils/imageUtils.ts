/**
 * 图片URL工具函数
 * 用于处理不同环境下的图片路径
 */

// 获取基础URL
const getBaseURL = (): string => {
  if (import.meta.env.DEV) {
    // 开发环境使用Vite代理
    return ''
  } else {
    // 生产环境使用配置的API URL
    return import.meta.env.VITE_API_URL || 'http://localhost:3010'
  }
}

/**
 * 获取完整的图片URL
 * @param imagePath 相对图片路径（如：'/images/yogurt-classic.jpg'）
 * @returns 完整的图片URL
 */
export const getImageURL = (imagePath: string): string => {
  if (!imagePath) return ''
  
  // 如果已经是完整URL，直接返回
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath
  }
  
  // 拼接基础URL和图片路径
  const baseURL = getBaseURL()
  return `${baseURL}${imagePath}`
}

/**
 * 获取产品图片URL
 * @param productImage 产品图片路径
 * @returns 完整的产品图片URL
 */
export const getProductImageURL = (productImage: string): string => {
  return getImageURL(productImage)
}

/**
 * 获取分析图片URL
 * @param analysisImage 分析图片路径
 * @returns 完整的分析图片URL
 */
export const getAnalysisImageURL = (analysisImage: string): string => {
  if (!analysisImage) return ''
  
  // 如果已经是完整URL，直接返回
  if (analysisImage.startsWith('http://') || analysisImage.startsWith('https://')) {
    return analysisImage
  }
  
  // 直接返回路径，让 Vite 配置处理代理逻辑
  return analysisImage
}

/**
 * 处理图片加载错误的回调
 * @param imagePath 原始图片路径
 * @param fallbackPath 备用图片路径
 * @returns 处理错误的函数
 */
export const handleImageError = (imagePath: string, fallbackPath?: string) => {
  return (event: React.SyntheticEvent<HTMLImageElement, Event>) => {
    console.warn(`图片加载失败: ${imagePath}`)
    if (fallbackPath && event.currentTarget.src !== getImageURL(fallbackPath)) {
      event.currentTarget.src = getImageURL(fallbackPath)
    }
  }
}