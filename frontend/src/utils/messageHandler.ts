/**
 * 全局消息处理器
 * 用于在无法使用 useMessage hook 的地方（如 API 拦截器）显示消息
 */

let globalMessageInstance: any = null

export const setGlobalMessage = (messageInstance: any) => {
  globalMessageInstance = messageInstance
}

export const showMessage = {
  success: (content: string) => {
    if (globalMessageInstance) {
      globalMessageInstance.success(content)
    } else {
      console.log('Success:', content)
    }
  },
  error: (content: string) => {
    if (globalMessageInstance) {
      globalMessageInstance.error(content)
    } else {
      console.error('Error:', content)
    }
  },
  warning: (content: string) => {
    if (globalMessageInstance) {
      globalMessageInstance.warning(content)
    } else {
      console.warn('Warning:', content)
    }
  },
  info: (content: string) => {
    if (globalMessageInstance) {
      globalMessageInstance.info(content)
    } else {
      console.info('Info:', content)
    }
  }
}
