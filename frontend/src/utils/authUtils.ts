/**
 * 认证相关工具函数
 */

/**
 * 清理所有认证相关的localStorage数据
 */
export const clearAuthStorage = (): void => {
  try {
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('user')
    localStorage.removeItem('authExpiry')
    console.log('已清理认证相关的localStorage数据')
  } catch (error) {
    console.warn('清理localStorage时出错:', error)
  }
}

/**
 * 检查token是否存在且有效（基本检查）
 */
export const hasValidToken = (): boolean => {
  try {
    const token = localStorage.getItem('token')
    if (!token) {
      return false
    }
    
    // 基本的token格式检查
    const parts = token.split('.')
    if (parts.length !== 3) {
      console.warn('Token格式无效')
      clearAuthStorage()
      return false
    }
    
    return true
  } catch (error) {
    console.warn('检查token时出错:', error)
    clearAuthStorage()
    return false
  }
}

/**
 * 安全地获取token
 */
export const getToken = (): string | null => {
  try {
    return localStorage.getItem('token')
  } catch (error) {
    console.warn('获取token时出错:', error)
    return null
  }
}

/**
 * 安全地设置token
 */
export const setToken = (token: string): void => {
  try {
    localStorage.setItem('token', token)
  } catch (error) {
    console.warn('设置token时出错:', error)
  }
}
