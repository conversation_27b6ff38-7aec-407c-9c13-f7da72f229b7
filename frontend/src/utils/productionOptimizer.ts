/**
 * 生产环境优化器
 * 根据环境自动启用不同的优化策略
 */

export class ProductionOptimizer {
  private static instance: ProductionOptimizer;
  
  static getInstance(): ProductionOptimizer {
    if (!this.instance) {
      this.instance = new ProductionOptimizer();
    }
    return this.instance;
  }

  /**
   * 初始化生产环境优化
   */
  initialize(): void {
    if (import.meta.env.PROD) {
      this.enableProductionOptimizations();
    }
  }

  /**
   * 启用生产环境优化
   */
  private enableProductionOptimizations(): void {
    // 1. 启用关键资源预加载
    this.preloadCriticalResources();
    
    // 2. 启用服务工作线程（PWA）
    this.registerServiceWorker();
    
    // 3. 启用资源提示
    this.addResourceHints();
    
    // 4. 启用性能监控
    this.enablePerformanceMonitoring();
  }

  /**
   * 预加载关键资源
   */
  private preloadCriticalResources(): void {
    // 预加载关键图片
    const criticalImages = [
      '/logo.png',
      '/hero-image.jpg'
    ];
    
    criticalImages.forEach(image => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = image;
      document.head.appendChild(link);
    });
  }

  /**
   * 注册服务工作线程
   */
  private registerServiceWorker(): void {
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js').catch(() => {
          console.log('Service Worker registration failed');
        });
      });
    }
  }

  /**
   * 添加资源提示
   */
  private addResourceHints(): void {
    // DNS预解析外部域名
    const externalDomains = [
      'https://api.yourdomain.com',
      'https://cdn.yourdomain.com'
    ];
    
    externalDomains.forEach(domain => {
      const link = document.createElement('link');
      link.rel = 'dns-prefetch';
      link.href = domain;
      document.head.appendChild(link);
    });
  }

  /**
   * 启用性能监控
   */
  private enablePerformanceMonitoring(): void {
    // 监控长任务
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          // 发送到分析服务
          console.log('Long task detected:', entry);
        }
      });
      
      observer.observe({ entryTypes: ['longtask'] });
    }

    // 监控最大内容绘制时间
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          console.log('LCP:', entry.startTime);
        }
      });
      
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
    }
  }

  /**
   * 预加载下一页
   */
  prefetchNextPage(url: string): void {
    if (import.meta.env.PROD) {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = url;
      document.head.appendChild(link);
    }
  }

  /**
   * 延迟加载非关键资源
   */
  lazyLoadResource(url: string, type: 'script' | 'style'): Promise<void> {
    return new Promise((resolve, reject) => {
      if (type === 'script') {
        const script = document.createElement('script');
        script.src = url;
        script.async = true;
        script.onload = () => resolve();
        script.onerror = reject;
        document.body.appendChild(script);
      } else {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = url;
        link.onload = () => resolve();
        link.onerror = reject;
        document.head.appendChild(link);
      }
    });
  }
}

// 导出单例实例
export const productionOptimizer = ProductionOptimizer.getInstance();