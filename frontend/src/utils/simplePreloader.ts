/**
 * 简化的预加载管理器
 * 专门解决"preloaded but not used"警告
 */

let preloadCleanupInitialized = false;

/**
 * 初始化预加载清理
 */
export const initPreloadCleanup = (): void => {
  if (preloadCleanupInitialized) return;
  preloadCleanupInitialized = true;

  // 页面加载完成后清理未使用的预加载
  window.addEventListener('load', () => {
    setTimeout(cleanupUnusedPreloads, 1500);
  });
};

/**
 * 清理未使用的预加载资源
 */
const cleanupUnusedPreloads = (): void => {
  const preloadLinks = document.querySelectorAll('link[rel="preload"]');
  
  preloadLinks.forEach(link => {
    const href = (link as HTMLLinkElement).href;
    const as = (link as HTMLLinkElement).getAttribute('as');
    
    let shouldRemove = false;
    
    // 检查CSS预加载
    if (as === 'style' || href.includes('.css')) {
      // CSS由Vite自动处理，不需要预加载
      shouldRemove = true;
    }
    
    // 检查字体预加载
    if (as === 'font' || href.includes('font')) {
      // 检查是否有使用字体的元素
      const hasIconElements = document.querySelector('.anticon, [class*="icon"]') !== null;
      const hasCustomFonts = Array.from(document.styleSheets).some(sheet => {
        try {
          return Array.from(sheet.cssRules).some(rule => 
            rule.cssText.includes('font-family') && rule.cssText.includes('anticon')
          );
        } catch {
          return false;
        }
      });
      
      if (!hasIconElements && !hasCustomFonts) {
        shouldRemove = true;
      }
    }
    
    if (shouldRemove) {
      console.debug(`清理未使用的预加载资源: ${href}`);
      link.remove();
    }
  });
};

/**
 * 智能字体预加载（仅在需要时）
 */
export const smartPreloadFont = (fontUrl: string): void => {
  // 检查是否已经预加载
  const existing = document.querySelector(`link[href="${fontUrl}"]`);
  if (existing) return;

  // 检查是否有图标元素
  const hasIcons = document.querySelector('.anticon') !== null;
  if (!hasIcons) return;

  // 创建预加载链接
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = fontUrl;
  link.as = 'font';
  link.type = 'font/woff2';
  link.crossOrigin = 'anonymous';

  // 设置超时清理
  setTimeout(() => {
    const hasIconsNow = document.querySelector('.anticon') !== null;
    if (!hasIconsNow && link.parentNode) {
      console.debug(`超时清理字体预加载: ${fontUrl}`);
      link.remove();
    }
  }, 3000);

  document.head.appendChild(link);
};

/**
 * 禁用所有自动预加载
 */
export const disableAutoPreload = (): void => {
  // 移除所有现有的预加载链接
  const preloadLinks = document.querySelectorAll('link[rel="preload"]');
  preloadLinks.forEach(link => {
    const href = (link as HTMLLinkElement).href;
    console.debug(`移除预加载链接: ${href}`);
    link.remove();
  });

  // 移除可能的modulepreload链接
  const modulePreloads = document.querySelectorAll('link[rel="modulepreload"]');
  modulePreloads.forEach(link => {
    const href = (link as HTMLLinkElement).href;
    if (href.includes('font') || href.includes('.css')) {
      console.debug(`移除模块预加载: ${href}`);
      link.remove();
    }
  });
};

/**
 * 优化资源加载策略
 */
export const optimizeResourceLoading = (): void => {
  // 1. 立即禁用所有预加载
  disableAutoPreload();

  // 2. 初始化清理机制
  initPreloadCleanup();

  // 3. 优化字体加载策略
  const style = document.createElement('style');
  style.textContent = `
    /* 优化字体加载 - 使用系统字体作为回退 */
    .anticon {
      font-display: swap;
      font-family: 'anticon', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    }

    /* 确保图标在字体加载前不显示空白 */
    .anticon::before {
      content: '';
      display: inline-block;
      width: 1em;
      height: 1em;
    }
  `;
  document.head.appendChild(style);

  // 4. 完全禁用Vite的预加载
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as Element;
          if (element.tagName === 'LINK' && element.getAttribute('rel') === 'preload') {
            const href = element.getAttribute('href');
            if (href && (href.includes('font') || href.includes('.css'))) {
              console.debug(`阻止预加载: ${href}`);
              element.remove();
            }
          }
        }
      });
    });
  });

  observer.observe(document.head, { childList: true });

  // 5秒后停止观察
  setTimeout(() => observer.disconnect(), 5000);

  console.debug('资源加载优化已启用 - 预加载已完全禁用');
};
