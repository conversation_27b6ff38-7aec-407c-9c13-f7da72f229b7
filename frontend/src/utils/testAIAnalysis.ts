// AI分析功能测试工具
// 在浏览器控制台运行: (window as any).testAIAnalysis()

import { aiAnalysisAPI } from '../services/api/aiAnalysisAPI'
import localAIService from '../services/api/localAI'

// 创建一个测试图片的base64数据（1x1像素的透明PNG）
const TEST_IMAGE_BASE64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAG8BqO0BQAAAABJRU5ErkJggg=='

export const testAIAnalysis = async () => {
  console.group('🧪 AI分析功能测试')
  
  try {
    // 1. 测试AI服务健康检查
    console.log('1️⃣ 测试AI服务健康检查...')
    const healthResponse = await aiAnalysisAPI.checkHealth()
    console.log('健康检查结果:', healthResponse.data)
    
    // 2. 测试获取模型列表
    console.log('2️⃣ 测试获取AI模型列表...')
    try {
      const modelsResponse = await aiAnalysisAPI.getModels()
      console.log('可用模型:', modelsResponse.data)
    } catch (error) {
      console.warn('获取模型列表失败 (预期，如果AI服务离线):', error)
    }
    
    // 3. 测试本地AI服务连接
    console.log('3️⃣ 测试本地AI服务连接...')
    const aiHealthy = await localAIService.checkHealth()
    console.log('本地AI服务状态:', aiHealthy ? '在线 ✅' : '离线 ❌')
    
    // 4. 测试获取分析历史
    console.log('4️⃣ 测试获取分析历史...')
    const historyResponse = await aiAnalysisAPI.getAnalyses({ limit: 10 })
    console.log('分析历史:', historyResponse.data)
    
    // 5. 测试保存分析记录
    console.log('5️⃣ 测试保存分析记录...')
    const saveResponse = await aiAnalysisAPI.saveAnalysisRecord({
      fileName: 'test_image.png',
      imageUrl: TEST_IMAGE_BASE64,
      analysisResult: '这是一个测试分析结果：\n\n1. **菌群识别**: 检测到测试图像\n2. **质量评估**: 测试通过 ✅\n3. **建议**: 继续测试其他功能',
      processingTime: 1500,
      customPrompt: '这是一个测试提示词',
      status: 'completed'
    })
    console.log('保存结果:', saveResponse.data)
    
    // 6. 测试分析统计
    console.log('6️⃣ 测试分析统计...')
    const statsResponse = await aiAnalysisAPI.getAnalysisStats()
    console.log('分析统计:', statsResponse.data)
    
    console.log('✅ AI分析功能测试完成！')
    
    // 如果创建了测试记录，清理它
    if (saveResponse.data?.data?.analysis) {
      console.log('🧹 清理测试数据...')
      try {
        await aiAnalysisAPI.deleteAnalysis(saveResponse.data.data.analysis.id)
        console.log('✅ 测试数据清理完成')
      } catch (error) {
        console.warn('⚠️ 清理测试数据失败:', error)
      }
    }
    
    return {
      success: true,
      message: 'AI分析功能测试通过',
      results: {
        healthCheck: healthResponse.data,
        history: historyResponse.data,
        stats: statsResponse.data
      }
    }
    
  } catch (error) {
    console.error('❌ AI分析功能测试失败:', error)
    return {
      success: false,
      message: 'AI分析功能测试失败',
      error: error
    }
  } finally {
    console.groupEnd()
  }
}

// 在全局window对象上暴露测试函数
if (typeof window !== 'undefined') {
  (window as any).testAIAnalysis = testAIAnalysis
}

export default testAIAnalysis