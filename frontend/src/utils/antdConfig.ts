/**
 * Antd 配置文件
 * 用于处理 React 版本兼容性警告和其他配置
 */

// 抑制 React 版本兼容性警告和性能警告
if (typeof window !== 'undefined') {
  const originalConsoleWarn = console.warn
  console.warn = (...args) => {
    // 过滤掉 Antd React 版本兼容性警告
    if (
      args[0] &&
      typeof args[0] === 'string' &&
      (args[0].includes('antd v5 support React is 16 ~ 18') ||
       args[0].includes('Added non-passive event listener') ||
       args[0].includes('[Violation]'))
    ) {
      return
    }
    originalConsoleWarn.apply(console, args)
  }

  // 优化 Ant Design 的事件监听器
  const originalAddEventListener = EventTarget.prototype.addEventListener
  EventTarget.prototype.addEventListener = function(
    type: string,
    listener: EventListenerOrEventListenerObject,
    options?: boolean | AddEventListenerOptions
  ) {
    // 为滚动相关事件自动添加 passive 选项
    if (
      (type === 'scroll' || type === 'wheel' || type === 'touchstart' || type === 'touchmove') &&
      typeof options !== 'boolean' &&
      (!options || options.passive === undefined)
    ) {
      const newOptions = { ...(options || {}), passive: true }
      return originalAddEventListener.call(this, type, listener, newOptions)
    }
    return originalAddEventListener.call(this, type, listener, options)
  }
}

export const antdConfig = {
  // 可以在这里添加其他 Antd 配置
  theme: {
    token: {
      colorPrimary: '#F05654',
    },
  },
}
