import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  But<PERSON>,
  Dropdown,
  List,
  Typography,
  Space,
  Spin,
  Empty,
  Divider,
  Tag,
  Tooltip
} from 'antd'
import {
  BellOutlined,
  CheckOutlined,
  DeleteOutlined,
  SettingOutlined,
  CloseOutlined,
  ExperimentOutlined,
  DatabaseOutlined,
  WarningOutlined,
  FileTextOutlined,
  ToolOutlined,
  UserOutlined,
  EyeOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

import { 
  getNotifications, 
  getUnreadCount, 
  markAsRead, 
  markAllAsRead, 
  deleteNotification,
  type Notification 
} from '../services/notificationAPI'
import { useMessage } from '../hooks/useMessage'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

const { Text, Title } = Typography

interface NotificationDropdownProps {
  className?: string
}

const NotificationDropdown: React.FC<NotificationDropdownProps> = ({ className }) => {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [loading, setLoading] = useState(false)
  const [dropdownOpen, setDropdownOpen] = useState(false)
  const [showUnreadOnly, setShowUnreadOnly] = useState(false)
  const message = useMessage()

  // 获取通知图标
  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'BATCH_COMPLETED':
        return <DatabaseOutlined style={{ color: '#52c41a' }} />
      case 'BATCH_FAILED':
        return <DatabaseOutlined style={{ color: '#f5222d' }} />
      case 'QUALITY_ALERT':
        return <WarningOutlined style={{ color: '#faad14' }} />
      case 'RECIPE_UPDATED':
        return <ExperimentOutlined style={{ color: '#1890ff' }} />
      case 'SYSTEM_MAINTENANCE':
        return <ToolOutlined style={{ color: '#722ed1' }} />
      case 'USER_MENTION':
        return <UserOutlined style={{ color: '#13c2c2' }} />
      case 'REPORT_READY':
        return <FileTextOutlined style={{ color: '#52c41a' }} />
      default:
        return <BellOutlined style={{ color: '#666' }} />
    }
  }

  // 获取通知类型标签
  const getNotificationTypeTag = (type: Notification['type']) => {
    const typeConfig = {
      BATCH_COMPLETED: { color: 'success', text: '批次完成' },
      BATCH_FAILED: { color: 'error', text: '批次失败' },
      QUALITY_ALERT: { color: 'warning', text: '质量预警' },
      RECIPE_UPDATED: { color: 'processing', text: '配方更新' },
      SYSTEM_MAINTENANCE: { color: 'purple', text: '系统维护' },
      USER_MENTION: { color: 'cyan', text: '用户提及' },
      REPORT_READY: { color: 'success', text: '报告就绪' }
    }

    const config = typeConfig[type] || { color: 'default', text: '未知' }
    return <Tag color={config.color} size="small">{config.text}</Tag>
  }

  // 加载通知数据
  const loadNotifications = async (silent = false) => {
    try {
      if (!silent) setLoading(true)
      
      const [notificationsResponse, unreadResponse] = await Promise.all([
        getNotifications({ 
          limit: 20, 
          unreadOnly: showUnreadOnly 
        }),
        getUnreadCount()
      ])

      if (notificationsResponse.success && notificationsResponse.data) {
        setNotifications(notificationsResponse.data.notifications)
      }

      if (unreadResponse.success && unreadResponse.data) {
        setUnreadCount(unreadResponse.data.unreadCount)
      }
    } catch (error) {
      // 静默模式下不显示错误消息（用于后台轮询）
      if (!silent) {
        console.error('加载通知失败:', error)
        if (error?.response?.status === 401) {
          message.error('请重新登录')
        } else {
          message.error('加载通知失败，请检查网络连接')
        }
      }
    } finally {
      if (!silent) setLoading(false)
    }
  }

  // 标记为已读
  const handleMarkAsRead = async (notificationId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    try {
      const response = await markAsRead(notificationId)
      if (response.success) {
        setNotifications(prev => 
          prev.map(n => 
            n.id === notificationId ? { ...n, isRead: true } : n
          )
        )
        setUnreadCount(prev => Math.max(0, prev - 1))
        message.success('已标记为已读')
      }
    } catch (error) {
      console.error('标记已读失败:', error)
      message.error('标记已读失败')
    }
  }

  // 删除通知
  const handleDelete = async (notificationId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    try {
      const response = await deleteNotification(notificationId)
      if (response.success) {
        const deletedNotification = notifications.find(n => n.id === notificationId)
        setNotifications(prev => prev.filter(n => n.id !== notificationId))
        
        if (deletedNotification && !deletedNotification.isRead) {
          setUnreadCount(prev => Math.max(0, prev - 1))
        }
        
        message.success('通知删除成功')
      }
    } catch (error) {
      console.error('删除通知失败:', error)
      message.error('删除通知失败')
    }
  }

  // 标记所有为已读
  const handleMarkAllAsRead = async () => {
    try {
      const response = await markAllAsRead()
      if (response.success) {
        setNotifications(prev => prev.map(n => ({ ...n, isRead: true })))
        setUnreadCount(0)
        message.success('所有通知已标记为已读')
      }
    } catch (error) {
      console.error('标记所有已读失败:', error)
      message.error('标记所有已读失败')
    }
  }

  // 初始化加载和定时刷新
  useEffect(() => {
    loadNotifications()
    
    // 设置定时刷新（每30秒，静默模式）
    const interval = setInterval(() => loadNotifications(true), 30000)
    
    return () => clearInterval(interval)
  }, [showUnreadOnly])

  // 当下拉框打开时重新加载
  useEffect(() => {
    if (dropdownOpen) {
      loadNotifications()
    }
  }, [dropdownOpen])

  const dropdownContent = (
    <div style={{ width: 380, maxHeight: 500, overflow: 'hidden' }}>
      {/* 头部 */}
      <div style={{ 
        padding: '12px 16px', 
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Title level={5} style={{ margin: 0 }}>
          <BellOutlined style={{ marginRight: 8 }} />
          通知中心
        </Title>
        <Space>
          <Button 
            type="link" 
            size="small"
            onClick={() => setShowUnreadOnly(!showUnreadOnly)}
          >
            {showUnreadOnly ? '显示全部' : '仅未读'}
          </Button>
          {unreadCount > 0 && (
            <Button 
              type="link" 
              size="small"
              onClick={handleMarkAllAsRead}
            >
              全部已读
            </Button>
          )}
        </Space>
      </div>

      {/* 通知列表 */}
      <div style={{ maxHeight: 400, overflowY: 'auto' }}>
        <Spin spinning={loading}>
          {notifications.length === 0 ? (
            <Empty 
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={showUnreadOnly ? '暂无未读通知' : '暂无通知'}
              style={{ padding: '40px 20px' }}
            />
          ) : (
            <List
              dataSource={notifications}
              renderItem={(notification) => (
                <List.Item
                  style={{ 
                    padding: '12px 16px',
                    borderBottom: '1px solid #f5f5f5',
                    backgroundColor: notification.isRead ? '#fff' : '#f6ffed',
                    cursor: 'pointer'
                  }}
                  actions={[
                    <Space key="actions">
                      {!notification.isRead && (
                        <Tooltip title="标记为已读">
                          <Button
                            type="text"
                            size="small"
                            icon={<CheckOutlined />}
                            onClick={(e) => handleMarkAsRead(notification.id, e)}
                          />
                        </Tooltip>
                      )}
                      <Tooltip title="删除">
                        <Button
                          type="text"
                          size="small"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={(e) => handleDelete(notification.id, e)}
                        />
                      </Tooltip>
                    </Space>
                  ]}
                >
                  <List.Item.Meta
                    avatar={getNotificationIcon(notification.type)}
                    title={
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Text strong={!notification.isRead}>
                          {notification.title}
                        </Text>
                        {getNotificationTypeTag(notification.type)}
                      </div>
                    }
                    description={
                      <div>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {notification.content}
                        </Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: '11px' }}>
                          {dayjs(notification.createdAt).fromNow()}
                        </Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          )}
        </Spin>
      </div>

      {/* 底部 */}
      {notifications.length > 0 && (
        <div style={{ 
          padding: '8px 16px', 
          borderTop: '1px solid #f0f0f0',
          textAlign: 'center'
        }}>
          <Button type="link" size="small" icon={<EyeOutlined />}>
            查看全部通知
          </Button>
        </div>
      )}
    </div>
  )

  return (
    <Dropdown
      menu={{ items: [] }} // 使用空菜单，因为我们使用自定义 popupRender
      trigger={['click']}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
      popupRender={() => dropdownContent}
      placement="bottomRight"
      arrow
    >
      <Badge count={unreadCount} size="small">
        <Button 
          type="text" 
          icon={<BellOutlined />} 
          className={className}
        />
      </Badge>
    </Dropdown>
  )
}

export default NotificationDropdown