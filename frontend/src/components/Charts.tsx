import React from 'react';
import { Card, Spin } from 'antd';
import ReactECharts from 'echarts-for-react';
import {
  UserActivityData,
  RoleDistributionData,
  SystemUsageData,
  BatchProcessingData,
  getChartTheme,
} from '../services/dashboardService';

interface ChartProps {
  loading?: boolean;
  height?: number;
}

interface UserActivityChartProps extends ChartProps {
  data: UserActivityData[];
}

interface RoleDistributionChartProps extends ChartProps {
  data: RoleDistributionData[];
}

interface SystemUsageChartProps extends ChartProps {
  data: SystemUsageData[];
}

interface BatchProcessingChartProps extends ChartProps {
  data: BatchProcessingData[];
}

const theme = getChartTheme();

/**
 * 用户活跃度趋势图
 */
export const UserActivityChart: React.FC<UserActivityChartProps> = ({ 
  data, 
  loading = false, 
  height = 300 
}) => {
  const option = {
    title: {
      text: '用户活跃度趋势',
      left: 'left',
      textStyle: {
        color: theme.text,
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: theme.border,
      borderWidth: 1,
      textStyle: { color: theme.text },
    },
    legend: {
      data: ['登录次数', '活跃用户'],
      right: 'right',
      textStyle: { color: theme.text },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.map(item => new Date(item.date).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })),
      axisLine: { lineStyle: { color: theme.border } },
      axisTick: { lineStyle: { color: theme.border } },
      axisLabel: { color: theme.text },
    },
    yAxis: {
      type: 'value',
      axisLine: { lineStyle: { color: theme.border } },
      axisTick: { lineStyle: { color: theme.border } },
      axisLabel: { color: theme.text },
      splitLine: { lineStyle: { color: theme.border, type: 'dashed' } },
    },
    series: [
      {
        name: '登录次数',
        type: 'line',
        data: data.map(item => item.loginCount),
        smooth: true,
        lineStyle: { color: theme.primary, width: 3 },
        itemStyle: { color: theme.primary },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: theme.primary + '40'
            }, {
              offset: 1, color: theme.primary + '10'
            }]
          }
        }
      },
      {
        name: '活跃用户',
        type: 'line',
        data: data.map(item => item.activeUsers),
        smooth: true,
        lineStyle: { color: theme.success, width: 3 },
        itemStyle: { color: theme.success },
      }
    ]
  };

  return (
    <Card >
      <Spin spinning={loading}>
        <ReactECharts option={option} style={{ height: `${height}px` }} />
      </Spin>
    </Card>
  );
};

/**
 * 角色分布饼图
 */
export const RoleDistributionChart: React.FC<RoleDistributionChartProps> = ({ 
  data, 
  loading = false, 
  height = 300 
}) => {
  const option = {
    title: {
      text: '用户角色分布',
      left: 'left',
      textStyle: {
        color: theme.text,
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: theme.border,
      borderWidth: 1,
      textStyle: { color: theme.text },
    },
    legend: {
      orient: 'vertical',
      right: 'right',
      textStyle: { color: theme.text },
    },
    series: [
      {
        name: '角色分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        data: data.map((item, index) => ({
          value: item.count,
          name: item.role,
          itemStyle: { color: theme.roleColors[index % theme.roleColors.length] }
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          formatter: '{b}\n{c}人 ({d}%)',
          color: theme.text,
        }
      }
    ]
  };

  return (
    <Card >
      <Spin spinning={loading}>
        <ReactECharts option={option} style={{ height: `${height}px` }} />
      </Spin>
    </Card>
  );
};

/**
 * 系统使用情况柱状图
 */
export const SystemUsageChart: React.FC<SystemUsageChartProps> = ({ 
  data, 
  loading = false, 
  height = 300 
}) => {
  const option = {
    title: {
      text: '系统模块使用情况',
      left: 'left',
      textStyle: {
        color: theme.text,
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: theme.border,
      borderWidth: 1,
      textStyle: { color: theme.text },
      formatter: (params: any) => {
        const item = params[0];
        const trend = data[item.dataIndex]?.trend;
        const trendText = trend === 'up' ? '↗️ 上升' : trend === 'down' ? '↘️ 下降' : '➡️ 稳定';
        return `${item.name}<br/>使用次数: ${item.value}<br/>趋势: ${trendText}`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.module),
      axisLine: { lineStyle: { color: theme.border } },
      axisTick: { lineStyle: { color: theme.border } },
      axisLabel: { 
        color: theme.text,
        rotate: 45,
        interval: 0,
      },
    },
    yAxis: {
      type: 'value',
      axisLine: { lineStyle: { color: theme.border } },
      axisTick: { lineStyle: { color: theme.border } },
      axisLabel: { color: theme.text },
      splitLine: { lineStyle: { color: theme.border, type: 'dashed' } },
    },
    series: [
      {
        name: '使用次数',
        type: 'bar',
        data: data.map((item, index) => ({
          value: item.usage,
          itemStyle: { 
            color: theme.moduleColors[index % theme.moduleColors.length],
            borderRadius: [4, 4, 0, 0],
          }
        })),
        barWidth: '60%',
      }
    ]
  };

  return (
    <Card >
      <Spin spinning={loading}>
        <ReactECharts option={option} style={{ height: `${height}px` }} />
      </Spin>
    </Card>
  );
};

/**
 * 批次处理趋势图
 */
export const BatchProcessingChart: React.FC<BatchProcessingChartProps> = ({ 
  data, 
  loading = false, 
  height = 300 
}) => {
  const option = {
    title: {
      text: '批次处理趋势',
      left: 'left',
      textStyle: {
        color: theme.text,
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: theme.border,
      borderWidth: 1,
      textStyle: { color: theme.text },
    },
    legend: {
      data: ['总批次', '已完成', '进行中'],
      right: 'right',
      textStyle: { color: theme.text },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: data.map(item => new Date(item.date).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })),
      axisLine: { lineStyle: { color: theme.border } },
      axisTick: { lineStyle: { color: theme.border } },
      axisLabel: { color: theme.text },
    },
    yAxis: {
      type: 'value',
      axisLine: { lineStyle: { color: theme.border } },
      axisTick: { lineStyle: { color: theme.border } },
      axisLabel: { color: theme.text },
      splitLine: { lineStyle: { color: theme.border, type: 'dashed' } },
    },
    series: [
      {
        name: '总批次',
        type: 'bar',
        stack: 'total',
        data: data.map(item => item.batchCount),
        itemStyle: { color: theme.info },
      },
      {
        name: '已完成',
        type: 'bar',
        stack: 'status',
        data: data.map(item => item.completedCount),
        itemStyle: { color: theme.success },
      },
      {
        name: '进行中',
        type: 'bar',
        stack: 'status',
        data: data.map(item => item.pendingCount),
        itemStyle: { color: theme.warning },
      }
    ]
  };

  return (
    <Card >
      <Spin spinning={loading}>
        <ReactECharts option={option} style={{ height: `${height}px` }} />
      </Spin>
    </Card>
  );
};

/**
 * 店面对比图表
 */
interface CafeComparisonData {
  cafeName: string;
  userCount: number;
  batchCount: number;
  completionRate: number;
}

interface CafeComparisonChartProps extends ChartProps {
  data: CafeComparisonData[];
}

export const CafeComparisonChart: React.FC<CafeComparisonChartProps> = ({ 
  data, 
  loading = false, 
  height = 400 
}) => {
  const option = {
    title: {
      text: '店面运营对比',
      left: 'left',
      textStyle: {
        color: theme.text,
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: theme.border,
      borderWidth: 1,
      textStyle: { color: theme.text },
    },
    legend: {
      data: ['用户数', '批次数', '完成率(%)'],
      right: 'right',
      textStyle: { color: theme.text },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.cafeName),
      axisLine: { lineStyle: { color: theme.border } },
      axisTick: { lineStyle: { color: theme.border } },
      axisLabel: { color: theme.text },
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        position: 'left',
        axisLine: { lineStyle: { color: theme.border } },
        axisTick: { lineStyle: { color: theme.border } },
        axisLabel: { color: theme.text },
        splitLine: { lineStyle: { color: theme.border, type: 'dashed' } },
      },
      {
        type: 'value',
        name: '完成率(%)',
        position: 'right',
        max: 100,
        axisLine: { lineStyle: { color: theme.border } },
        axisTick: { lineStyle: { color: theme.border } },
        axisLabel: { color: theme.text, formatter: '{value}%' },
      }
    ],
    series: [
      {
        name: '用户数',
        type: 'bar',
        data: data.map(item => item.userCount),
        itemStyle: { color: theme.primary },
        barGap: '20%',
      },
      {
        name: '批次数',
        type: 'bar',
        data: data.map(item => item.batchCount),
        itemStyle: { color: theme.secondary },
      },
      {
        name: '完成率(%)',
        type: 'line',
        yAxisIndex: 1,
        data: data.map(item => item.completionRate),
        lineStyle: { color: theme.success, width: 3 },
        itemStyle: { color: theme.success },
        symbol: 'circle',
        symbolSize: 8,
      }
    ]
  };

  return (
    <Card >
      <Spin spinning={loading}>
        <ReactECharts option={option} style={{ height: `${height}px` }} />
      </Spin>
    </Card>
  );
};

export type { CafeComparisonData };