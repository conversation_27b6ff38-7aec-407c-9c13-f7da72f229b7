import React from 'react';
import { Result, Button } from 'antd';
import { useNavigate } from 'react-router-dom';
import { usePermissions } from '../hooks/usePermissions';
import { Permission, UserRole } from '../types/permissions';

interface PermissionGuardProps {
  children: React.ReactNode;
  permissions?: Permission[];
  roles?: UserRole[];
  fallback?: React.ReactNode;
  showFallback?: boolean;
}

/**
 * 权限守卫组件
 * 根据用户权限决定是否渲染子组件
 */
export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permissions,
  roles,
  fallback,
  showFallback = true,
}) => {
  const { canAccessRoute } = usePermissions();
  const navigate = useNavigate();

  const hasAccess = canAccessRoute(permissions, roles);

  if (hasAccess) {
    return <>{children}</>;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  if (!showFallback) {
    return null;
  }

  return (
    <Result
      status="403"
      title="权限不足"
      subTitle="抱歉，您没有访问此内容的权限。"
      extra={
        <Button type="primary" onClick={() => navigate('/')}>
          返回首页
        </Button>
      }
    />
  );
};

interface RoleGuardProps {
  children: React.ReactNode;
  roles: UserRole[];
  fallback?: React.ReactNode;
  showFallback?: boolean;
}

/**
 * 角色守卫组件
 * 根据用户角色决定是否渲染子组件
 */
export const RoleGuard: React.FC<RoleGuardProps> = ({
  children,
  roles,
  fallback,
  showFallback = true,
}) => {
  return (
    <PermissionGuard
      roles={roles}
      fallback={fallback}
      showFallback={showFallback}
    >
      {children}
    </PermissionGuard>
  );
};

interface AdminGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showFallback?: boolean;
}

/**
 * 管理员守卫组件
 * 只有管理员可以访问
 */
export const AdminGuard: React.FC<AdminGuardProps> = ({
  children,
  fallback,
  showFallback = true,
}) => {
  return (
    <RoleGuard
      roles={[UserRole.ADMIN]}
      fallback={fallback}
      showFallback={showFallback}
    >
      {children}
    </RoleGuard>
  );
};

interface ConditionalRenderProps {
  condition: boolean;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * 条件渲染组件
 * 通用的条件渲染工具
 */
export const ConditionalRender: React.FC<ConditionalRenderProps> = ({
  condition,
  children,
  fallback = null,
}) => {
  return condition ? <>{children}</> : <>{fallback}</>;
};

// 便捷的权限检查组件
interface PermissionCheckProps {
  permission: Permission;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export const PermissionCheck: React.FC<PermissionCheckProps> = ({
  permission,
  children,
  fallback,
}) => {
  return (
    <PermissionGuard
      permissions={[permission]}
      fallback={fallback}
      showFallback={false}
    >
      {children}
    </PermissionGuard>
  );
};

// 多权限检查组件（需要所有权限）
interface AllPermissionsCheckProps {
  permissions: Permission[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export const AllPermissionsCheck: React.FC<AllPermissionsCheckProps> = ({
  permissions,
  children,
  fallback,
}) => {
  const { hasAllPermissions } = usePermissions();
  const hasAccess = hasAllPermissions(permissions);

  return (
    <ConditionalRender condition={hasAccess} fallback={fallback}>
      {children}
    </ConditionalRender>
  );
};

// 任一权限检查组件（有任一权限即可）
interface AnyPermissionCheckProps {
  permissions: Permission[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export const AnyPermissionCheck: React.FC<AnyPermissionCheckProps> = ({
  permissions,
  children,
  fallback,
}) => {
  const { hasAnyPermission } = usePermissions();
  const hasAccess = hasAnyPermission(permissions);

  return (
    <ConditionalRender condition={hasAccess} fallback={fallback}>
      {children}
    </ConditionalRender>
  );
};

export default PermissionGuard;
