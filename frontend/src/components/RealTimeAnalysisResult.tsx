import React, { useState, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Card, Typography, Button, Progress, Alert, Space, Divider } from 'antd';
import { RobotOutlined, StopOutlined, CopyOutlined, DownloadOutlined } from '@ant-design/icons';
import { filterAnalysisResult } from '../utils/analysisResultFilter';
import './RealTimeAnalysisResult.css';

const { Title, Text, Paragraph } = Typography;

interface RealTimeAnalysisResultProps {
  isAnalyzing: boolean;
  onStopAnalysis?: () => void;
  className?: string;
}

interface AnalysisChunk {
  content: string;
  timestamp: number;
  type: 'content' | 'progress' | 'complete' | 'error';
}

export interface RealTimeAnalysisResultRef {
  appendContent: (content: string, type?: AnalysisChunk['type']) => void;
  resetAnalysis: () => void;
  setStatus: (status: 'idle' | 'analyzing' | 'completed' | 'error') => void;
}

const RealTimeAnalysisResult = forwardRef<RealTimeAnalysisResultRef, RealTimeAnalysisResultProps>(({
  isAnalyzing,
  onStopAnalysis,
  className
}, ref) => {
  const [analysisContent, setAnalysisContent] = useState<string>('');
  const [chunks, setChunks] = useState<AnalysisChunk[]>([]);
  const [progress, setProgress] = useState<number>(0);
  const [status, setStatus] = useState<'idle' | 'analyzing' | 'completed' | 'error'>('idle');
  const [startTime, setStartTime] = useState<number | null>(null);
  const [elapsedTime, setElapsedTime] = useState<number>(0);
  
  const contentRef = useRef<HTMLDivElement>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // 计算分析时间
  useEffect(() => {
    if (isAnalyzing && !startTime) {
      setStartTime(Date.now());
      setStatus('analyzing');
    }

    if (isAnalyzing) {
      intervalRef.current = setInterval(() => {
        if (startTime) {
          setElapsedTime(Date.now() - startTime);
        }
      }, 100);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isAnalyzing, startTime]);

  // 自动滚动到底部
  useEffect(() => {
    if (contentRef.current) {
      contentRef.current.scrollTop = contentRef.current.scrollHeight;
    }
  }, [analysisContent]);

  // 重置状态
  const resetAnalysis = () => {
    setAnalysisContent('');
    setChunks([]);
    setProgress(0);
    setStatus('idle');
    setStartTime(null);
    setElapsedTime(0);
  };

  // 添加内容
  const appendContent = (content: string, type: AnalysisChunk['type'] = 'content') => {
    const chunk: AnalysisChunk = {
      content,
      timestamp: Date.now(),
      type
    };
    
    setChunks(prev => [...prev, chunk]);
    
    if (type === 'content') {
      setAnalysisContent(prev => prev + content);
      // 估算进度（基于内容长度）
      const estimatedProgress = Math.min(95, (analysisContent.length + content.length) / 20);
      setProgress(estimatedProgress);
    } else if (type === 'complete') {
      setProgress(100);
      setStatus('completed');
    } else if (type === 'error') {
      setStatus('error');
    }
  };

  // 复制内容
  const copyContent = () => {
    const filteredContent = filterAnalysisResult(analysisContent);
    navigator.clipboard.writeText(filteredContent);
  };

  // 下载内容
  const downloadContent = () => {
    const filteredContent = filterAnalysisResult(analysisContent);
    const blob = new Blob([filteredContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `酸奶分析报告_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 格式化时间
  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    appendContent,
    resetAnalysis,
    setStatus: (newStatus: typeof status) => setStatus(newStatus)
  }));

  if (!isAnalyzing && status === 'idle') {
    return null;
  }

  return (
    <Card 
      className={`real-time-analysis-result ${className || ''}`}
      title={
        <Space>
          <RobotOutlined style={{ color: '#1890ff' }} />
          <span>实时分析结果</span>
          {isAnalyzing && (
            <Text type="secondary">
              {formatTime(elapsedTime)}
            </Text>
          )}
        </Space>
      }
      extra={
        <Space>
          {isAnalyzing && (
            <Button 
              type="text" 
              icon={<StopOutlined />} 
              onClick={onStopAnalysis}
              danger
            >
              停止分析
            </Button>
          )}
          {!isAnalyzing && analysisContent && (
            <>
              <Button 
                type="text" 
                icon={<CopyOutlined />} 
                onClick={copyContent}
              >
                复制
              </Button>
              <Button 
                type="text" 
                icon={<DownloadOutlined />} 
                onClick={downloadContent}
              >
                下载
              </Button>
            </>
          )}
        </Space>
      }
      size="small"
    >
      {/* 进度条 */}
      {isAnalyzing && (
        <div style={{ marginBottom: 16 }}>
          <Progress 
            percent={progress} 
            status={status === 'error' ? 'exception' : 'active'}
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
            showInfo={false}
          />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            正在分析中... {progress.toFixed(1)}%
          </Text>
        </div>
      )}

      {/* 状态提示 */}
      {status === 'error' && (
        <Alert
          message="分析出现错误"
          description="请检查网络连接和 LM Studio 服务状态"
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {status === 'completed' && (
        <Alert
          message="分析完成"
          description={`分析耗时: ${formatTime(elapsedTime)}`}
          type="success"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 分析内容 */}
      <div 
        ref={contentRef}
        className="analysis-content"
        style={{
          maxHeight: '400px',
          overflowY: 'auto',
          padding: '12px',
          backgroundColor: '#fafafa',
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
          fontSize: '13px',
          lineHeight: '1.6',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word'
        }}
      >
        {analysisContent ? filterAnalysisResult(analysisContent) : (
          <Text type="secondary" style={{ fontStyle: 'italic' }}>
            {isAnalyzing ? '等待分析结果...' : '暂无分析内容'}
          </Text>
        )}
        
        {/* 光标效果 */}
        {isAnalyzing && (
          <span 
            className="typing-cursor"
            style={{
              display: 'inline-block',
              width: '2px',
              height: '1em',
              backgroundColor: '#1890ff',
              marginLeft: '2px',
              animation: 'blink 1s infinite'
            }}
          />
        )}
      </div>

      {/* 统计信息 */}
      {analysisContent && (
        <div style={{ marginTop: 12, padding: '8px 0', borderTop: '1px solid #f0f0f0' }}>
          <Space split={<Divider type="vertical" />}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              字符数: {analysisContent.length}
            </Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              分析块数: {chunks.filter(c => c.type === 'content').length}
            </Text>
            {status === 'completed' && (
              <Text type="secondary" style={{ fontSize: '12px' }}>
                完成时间: {new Date().toLocaleTimeString()}
              </Text>
            )}
          </Space>
        </div>
      )}
    </Card>
  );
});

RealTimeAnalysisResult.displayName = 'RealTimeAnalysisResult';

export default RealTimeAnalysisResult;
