import React from 'react';
import { Tag, Tooltip, Avatar } from 'antd';
import { 
  CrownOutlined, 
  UserOutlined, 
  EyeOutlined, 
  TeamOutlined 
} from '@ant-design/icons';
import { UserRole, RoleDisplayNames, RoleDescriptions, RoleColors } from '../types/permissions';

interface UserRoleBadgeProps {
  role: string;
  size?: 'small' | 'default' | 'large';
  showIcon?: boolean;
  showTooltip?: boolean;
  style?: React.CSSProperties;
}

/**
 * 用户角色徽章组件
 * 显示用户角色的可视化标识
 */
export const UserRoleBadge: React.FC<UserRoleBadgeProps> = ({
  role,
  size = 'default',
  showIcon = true,
  showTooltip = true,
  style,
}) => {
  const userRole = role.toUpperCase() as UserRole;
  
  // 如果角色不存在，返回默认显示
  if (!Object.values(UserRole).includes(userRole)) {
    return (
      <Tag color="default" style={style}>
        {role}
      </Tag>
    );
  }

  const displayName = RoleDisplayNames[userRole];
  const description = RoleDescriptions[userRole];
  const color = RoleColors[userRole];

  // 获取角色图标
  const getIcon = () => {
    switch (userRole) {
      case UserRole.ADMIN:
        return <CrownOutlined />;
      case UserRole.MANAGER:
        return <TeamOutlined />;
      case UserRole.USER:
        return <UserOutlined />;
      case UserRole.VIEWER:
        return <EyeOutlined />;
      default:
        return <UserOutlined />;
    }
  };

  const badge = (
    <Tag 
      color={color} 
      icon={showIcon ? getIcon() : undefined}
      style={style}
    >
      {displayName}
    </Tag>
  );

  if (showTooltip) {
    return (
      <Tooltip title={description} placement="top">
        {badge}
      </Tooltip>
    );
  }

  return badge;
};

interface UserRoleAvatarProps {
  role: string;
  size?: number | 'large' | 'small' | 'default';
  showTooltip?: boolean;
}

/**
 * 用户角色头像组件
 * 使用不同颜色和图标表示角色
 */
export const UserRoleAvatar: React.FC<UserRoleAvatarProps> = ({
  role,
  size = 'default',
  showTooltip = true,
}) => {
  const userRole = role.toUpperCase() as UserRole;
  
  if (!Object.values(UserRole).includes(userRole)) {
    return <Avatar size={size} icon={<UserOutlined />} />;
  }

  const displayName = RoleDisplayNames[userRole];
  const description = RoleDescriptions[userRole];
  const color = RoleColors[userRole];

  const getIcon = () => {
    switch (userRole) {
      case UserRole.ADMIN:
        return <CrownOutlined />;
      case UserRole.MANAGER:
        return <TeamOutlined />;
      case UserRole.USER:
        return <UserOutlined />;
      case UserRole.VIEWER:
        return <EyeOutlined />;
      default:
        return <UserOutlined />;
    }
  };

  const avatar = (
    <Avatar 
      size={size} 
      style={{ backgroundColor: color }}
      icon={getIcon()}
    />
  );

  if (showTooltip) {
    return (
      <Tooltip title={`${displayName}: ${description}`} placement="top">
        {avatar}
      </Tooltip>
    );
  }

  return avatar;
};

interface RoleSelectOption {
  value: UserRole;
  label: string;
  description: string;
  color: string;
  disabled?: boolean;
}

/**
 * 获取角色选择选项
 * 用于角色选择器组件
 */
export const getRoleSelectOptions = (
  excludeRoles: UserRole[] = [],
  includeDescriptions: boolean = true
): RoleSelectOption[] => {
  return Object.values(UserRole)
    .filter(role => !excludeRoles.includes(role))
    .map(role => ({
      value: role,
      label: RoleDisplayNames[role],
      description: RoleDescriptions[role],
      color: RoleColors[role],
    }));
};

interface UserRoleCardProps {
  role: string;
  title?: string;
  description?: string;
  extra?: React.ReactNode;
  onClick?: () => void;
  style?: React.CSSProperties;
}

/**
 * 用户角色卡片组件
 * 用于角色选择或展示
 */
export const UserRoleCard: React.FC<UserRoleCardProps> = ({
  role,
  title,
  description,
  extra,
  onClick,
  style,
}) => {
  const userRole = role.toUpperCase() as UserRole;
  
  if (!Object.values(UserRole).includes(userRole)) {
    return null;
  }

  const displayName = title || RoleDisplayNames[userRole];
  const desc = description || RoleDescriptions[userRole];
  const color = RoleColors[userRole];

  return (
    <div
      style={{
        border: `2px solid ${color}`,
        borderRadius: 8,
        padding: 16,
        cursor: onClick ? 'pointer' : 'default',
        transition: 'all 0.3s ease',
        ...style,
      }}
      onClick={onClick}
      onMouseEnter={(e) => {
        if (onClick) {
          e.currentTarget.style.boxShadow = `0 4px 12px ${color}40`;
        }
      }}
      onMouseLeave={(e) => {
        if (onClick) {
          e.currentTarget.style.boxShadow = 'none';
        }
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
        <UserRoleAvatar role={role} showTooltip={false} />
        <span style={{ marginLeft: 12, fontSize: 16, fontWeight: 'bold' }}>
          {displayName}
        </span>
        {extra && <div style={{ marginLeft: 'auto' }}>{extra}</div>}
      </div>
      <p style={{ margin: 0, color: '#666', fontSize: 14 }}>
        {desc}
      </p>
    </div>
  );
};

export default UserRoleBadge;
