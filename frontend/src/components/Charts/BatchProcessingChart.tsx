import React from 'react';
import { Card, Spin, Empty } from 'antd';
import ReactECharts from 'echarts-for-react';
import { BatchProcessingData, getChartTheme } from '../../services/dashboardService';

interface BatchProcessingChartProps {
  data: BatchProcessingData[];
  loading?: boolean;
  height?: number;
}

/**
 * 批次处理趋势图
 */
const BatchProcessingChart: React.FC<BatchProcessingChartProps> = ({
  data,
  loading = false,
  height = 300,
}) => {
  const theme = getChartTheme();

  if (loading) {
    return (
      <Card title="批次处理趋势" style={{ height: height + 100 }}>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height }}>
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card title="批次处理趋势" style={{ height: height + 100 }}>
        <Empty description="暂无数据" />
      </Card>
    );
  }

  const option = {
    title: {
      text: '最近14天批次处理情况',
      textStyle: {
        color: theme.text,
        fontSize: 14,
        fontWeight: 'normal',
      },
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: theme.primary,
        },
      },
      formatter: (params: any) => {
        const date = params[0].axisValue;
        let tooltip = `<div style="font-weight: bold; margin-bottom: 4px;">${date}</div>`;
        params.forEach((param: any) => {
          tooltip += `<div style="display: flex; align-items: center; margin: 2px 0;">
            <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
            ${param.seriesName}: <strong>${param.value}</strong>
          </div>`;
        });
        return tooltip;
      },
    },
    legend: {
      data: ['总批次', '已完成', '待处理'],
      top: 30,
      textStyle: {
        color: theme.text,
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.map(item => {
        const date = new Date(item.date);
        return `${date.getMonth() + 1}/${date.getDate()}`;
      }),
      axisLine: {
        lineStyle: {
          color: theme.border,
        },
      },
      axisLabel: {
        color: theme.text,
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: theme.border,
        },
      },
      axisLabel: {
        color: theme.text,
      },
      splitLine: {
        lineStyle: {
          color: theme.border,
        },
      },
    },
    series: [
      {
        name: '总批次',
        type: 'line',
        smooth: true,
        lineStyle: {
          color: theme.info,
          width: 2,
        },
        itemStyle: {
          color: theme.info,
        },
        data: data.map(item => item.batchCount),
      },
      {
        name: '已完成',
        type: 'line',
        smooth: true,
        lineStyle: {
          color: theme.success,
          width: 2,
        },
        itemStyle: {
          color: theme.success,
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: theme.success + '40' },
              { offset: 1, color: theme.success + '10' },
            ],
          },
        },
        data: data.map(item => item.completedCount),
      },
      {
        name: '待处理',
        type: 'line',
        smooth: true,
        lineStyle: {
          color: theme.warning,
          width: 2,
        },
        itemStyle: {
          color: theme.warning,
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: theme.warning + '40' },
              { offset: 1, color: theme.warning + '10' },
            ],
          },
        },
        data: data.map(item => item.pendingCount),
      },
    ],
  };

  return (
    <Card title="批次处理趋势" style={{ height: height + 100 }}>
      <ReactECharts
        option={option}
        style={{ height, width: '100%' }}
        opts={{ renderer: 'canvas' }}
      />
    </Card>
  );
};

export default BatchProcessingChart;
