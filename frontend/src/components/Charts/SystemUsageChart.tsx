import React from 'react';
import { Card, Spin, Empty } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined, MinusOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import { SystemUsageData, getChartTheme } from '../../services/dashboardService';

interface SystemUsageChartProps {
  data: SystemUsageData[];
  loading?: boolean;
  height?: number;
}

/**
 * 系统使用情况柱状图
 */
const SystemUsageChart: React.FC<SystemUsageChartProps> = ({
  data,
  loading = false,
  height = 300,
}) => {
  const theme = getChartTheme();

  if (loading) {
    return (
      <Card title="系统使用情况" style={{ height: height + 100 }}>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height }}>
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card title="系统使用情况" style={{ height: height + 100 }}>
        <Empty description="暂无数据" />
      </Card>
    );
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <ArrowUpOutlined style={{ color: theme.success }} />;
      case 'down':
        return <ArrowDownOutlined style={{ color: theme.error }} />;
      default:
        return <MinusOutlined style={{ color: theme.info }} />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up':
        return theme.success;
      case 'down':
        return theme.error;
      default:
        return theme.info;
    }
  };

  const option = {
    title: {
      text: '各功能模块使用频次',
      textStyle: {
        color: theme.text,
        fontSize: 14,
        fontWeight: 'normal',
      },
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: (params: any) => {
        const param = params[0];
        const dataItem = data[param.dataIndex];

        let trendText = '稳定';
        if (dataItem.trend === 'up') {
          trendText = '上升';
        } else if (dataItem.trend === 'down') {
          trendText = '下降';
        }

        return `<div style="font-weight: bold; margin-bottom: 4px;">${param.name}</div>
                <div>使用次数: <strong>${param.value}</strong></div>
                <div>趋势: <strong style="color: ${getTrendColor(dataItem.trend)}">${trendText}</strong></div>`;
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.module),
      axisLine: {
        lineStyle: {
          color: theme.border,
        },
      },
      axisLabel: {
        color: theme.text,
        rotate: 45,
        interval: 0,
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: theme.border,
        },
      },
      axisLabel: {
        color: theme.text,
      },
      splitLine: {
        lineStyle: {
          color: theme.border,
        },
      },
    },
    series: [
      {
        name: '使用次数',
        type: 'bar',
        data: data.map((item, index) => {
          const baseColor = theme.moduleColors[index % theme.moduleColors.length];
          const lighterColor = baseColor + '80'; // 添加透明度

          return {
            value: item.usage,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: baseColor },
                  { offset: 1, color: lighterColor },
                ],
              },
              borderRadius: [4, 4, 0, 0],
            },
            emphasis: {
              itemStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    { offset: 0, color: lighterColor },
                    { offset: 1, color: baseColor },
                  ],
                },
              },
            },
          };
        }),
        barWidth: '60%',
      },
    ],
  };

  return (
    <Card
      title="系统使用情况"
      style={{ height: height + 100 }}
      extra={
        <div style={{ fontSize: '12px', color: theme.text, display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span>{getTrendIcon('up')} 上升</span>
          <span>{getTrendIcon('down')} 下降</span>
          <span>{getTrendIcon('stable')} 稳定</span>
        </div>
      }
    >
      <ReactECharts
        option={option}
        style={{ height, width: '100%' }}
        opts={{ renderer: 'canvas' }}
      />
    </Card>
  );
};

export default SystemUsageChart;
