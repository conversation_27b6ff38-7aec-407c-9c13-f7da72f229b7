import React from 'react';
import { Card, Spin, Empty } from 'antd';
import ReactECharts from 'echarts-for-react';
import { UserActivityData, getChartTheme } from '../../services/dashboardService';

interface UserActivityChartProps {
  data: UserActivityData[];
  loading?: boolean;
  height?: number;
}

/**
 * 用户活跃度趋势图
 */
const UserActivityChart: React.FC<UserActivityChartProps> = ({
  data,
  loading = false,
  height = 300,
}) => {
  const theme = getChartTheme();

  if (loading) {
    return (
      <Card title="用户活跃度趋势" style={{ height: height + 100 }}>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height }}>
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card title="用户活跃度趋势" style={{ height: height + 100 }}>
        <Empty description="暂无数据" />
      </Card>
    );
  }

  const option = {
    title: {
      text: data.length <= 7 ? '最近7天用户活跃度' : '最近30天用户活跃度',
      textStyle: {
        color: theme.text,
        fontSize: 14,
        fontWeight: 'normal',
      },
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: theme.primary,
        },
      },
      formatter: (params: any) => {
        const date = params[0].axisValue;
        let tooltip = `<div style="font-weight: bold; margin-bottom: 4px;">${date}</div>`;
        params.forEach((param: any) => {
          tooltip += `<div style="display: flex; align-items: center; margin: 2px 0;">
            <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
            ${param.seriesName}: <strong>${param.value}</strong>
          </div>`;
        });
        return tooltip;
      },
    },
    legend: {
      data: ['登录次数', '活跃用户'],
      top: 30,
      textStyle: {
        color: theme.text,
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.map(item => {
        const date = new Date(item.date);
        return `${date.getMonth() + 1}/${date.getDate()}`;
      }),
      axisLine: {
        lineStyle: {
          color: theme.border,
        },
      },
      axisLabel: {
        color: theme.text,
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: theme.border,
        },
      },
      axisLabel: {
        color: theme.text,
      },
      splitLine: {
        lineStyle: {
          color: theme.border,
        },
      },
    },
    series: [
      {
        name: '登录次数',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          color: theme.primary,
          width: 2,
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: theme.primary + '40' },
              { offset: 1, color: theme.primary + '10' },
            ],
          },
        },
        data: data.map(item => item.loginCount),
      },
      {
        name: '活跃用户',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          color: theme.secondary,
          width: 2,
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: theme.secondary + '40' },
              { offset: 1, color: theme.secondary + '10' },
            ],
          },
        },
        data: data.map(item => item.activeUsers),
      },
    ],
  };

  return (
    <Card title="用户活跃度趋势" style={{ height: height + 100 }}>
      <ReactECharts
        option={option}
        style={{ height, width: '100%' }}
        opts={{ renderer: 'canvas' }}
      />
    </Card>
  );
};

export default UserActivityChart;
