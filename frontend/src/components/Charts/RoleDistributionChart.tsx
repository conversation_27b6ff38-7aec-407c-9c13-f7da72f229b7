import React from 'react';
import { Card, Spin, Empty } from 'antd';
import ReactECharts from 'echarts-for-react';
import { RoleDistributionData, getChartTheme } from '../../services/dashboardService';

interface RoleDistributionChartProps {
  data: RoleDistributionData[];
  loading?: boolean;
  height?: number;
}

/**
 * 角色分布饼图
 */
const RoleDistributionChart: React.FC<RoleDistributionChartProps> = ({
  data,
  loading = false,
  height = 300,
}) => {
  const theme = getChartTheme();

  if (loading) {
    return (
      <Card title="角色分布" style={{ height: height + 100 }}>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height }}>
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card title="角色分布" style={{ height: height + 100 }}>
        <Empty description="暂无数据" />
      </Card>
    );
  }

  const option = {
    title: {
      text: '用户角色分布',
      textStyle: {
        color: theme.text,
        fontSize: 14,
        fontWeight: 'normal',
      },
      left: 'center',
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `<div style="font-weight: bold; margin-bottom: 4px;">${params.name}</div>
                <div>用户数: <strong>${params.value}</strong></div>
                <div>占比: <strong>${params.percent}%</strong></div>`;
      },
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle',
      textStyle: {
        color: theme.text,
        fontSize: 12,
      },
      formatter: (name: string) => {
        const item = data.find(d => d.role === name);
        return `${name} (${item?.count || 0}人)`;
      },
      itemWidth: 14,
      itemHeight: 14,
      itemGap: 12,
    },
    series: [
      {
        name: '角色分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold',
            color: theme.text,
            formatter: (params: any) => {
              return `${params.name}\n${params.percent}%`;
            },
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        labelLine: {
          show: false,
        },
        data: data.map((item, index) => ({
          value: item.count,
          name: item.role,
          itemStyle: {
            color: theme.roleColors[index % theme.roleColors.length],
          },
        })),
      },
    ],
  };

  return (
    <Card title="角色分布" style={{ height: height + 100 }}>
      <ReactECharts
        option={option}
        style={{ height, width: '100%' }}
        opts={{ renderer: 'canvas' }}
      />
    </Card>
  );
};

export default RoleDistributionChart;
