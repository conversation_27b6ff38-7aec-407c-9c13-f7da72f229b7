import React from 'react';
import { Card, Row, Col, Statistic, Button, Space, Typography, Alert, Progress, Tag, Divider } from 'antd';
import {
  EyeOutlined,
  ExperimentOutlined,
  DatabaseOutlined,
  RobotOutlined,
  PlusOutlined,
  ShopOutlined,
  SafetyOutlined,
  TeamOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { UserRoleBadge } from './UserRoleBadge';
import { RoleGuard } from './PermissionGuard';
import { UserRole } from '../types/permissions';
import AdminDashboard from './AdminDashboard';
import {
  UserActivityChart,
  RoleDistributionChart,
  SystemUsageChart,
  BatchProcessingChart,
} from './Charts';
import {
  refreshManagerDashboardData,
  ManagerDashboardStats,
  UserActivityData,
  RoleDistributionData,
} from '../services/dashboardService';
import {
  getCustomerStats,
  getCustomerPreferences,
  CustomerStats,
  CustomerPreferences,
} from '../services/customerService';
import { checkAuthStatus } from '../store/slices/authSlice';
import { AppDispatch } from '../store';

const { Title, Text } = Typography;

interface DashboardProps {
  user: any;
}

// 管理员仪表板组件已移至 AdminDashboard.tsx

// 店面管理员仪表板
const ManagerDashboard: React.FC<DashboardProps> = ({ user }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const [loading, setLoading] = React.useState(false);
  const [refreshingAuth, setRefreshingAuth] = React.useState(false);
  const [storeStats, setStoreStats] = React.useState<ManagerDashboardStats | null>(null);
  const [userActivity, setUserActivity] = React.useState<UserActivityData[]>([]);
  const [roleDistribution, setRoleDistribution] = React.useState<RoleDistributionData[]>([]);
  const [batchProcessing, setBatchProcessing] = React.useState<any[]>([]);

  // 临时：如果批次数据为空，使用固定数据
  const displayBatchProcessing = batchProcessing.length > 0 ? batchProcessing : [
    { status: '已完成', count: 42, color: '#52c41a' },
    { status: '进行中', count: 6, color: '#1890ff' },
    { status: '待审批', count: 3, color: '#faad14' },
  ];

  // 调试：打印当前状态
  console.log('Manager Dashboard 当前状态:', {
    user: user?.email,
    loading,
    batchProcessingLength: batchProcessing.length,
    displayBatchProcessingLength: displayBatchProcessing.length,
    batchProcessingData: batchProcessing,
    displayBatchProcessingData: displayBatchProcessing
  });

  // 刷新用户认证信息
  const refreshUserAuth = async () => {
    if (refreshingAuth) return;
    
    setRefreshingAuth(true);
    try {
      await dispatch(checkAuthStatus()).unwrap();
      console.log('用户信息已刷新');
    } catch (error) {
      console.error('刷新用户信息失败:', error);
    } finally {
      setRefreshingAuth(false);
    }
  };

  // 加载店面仪表板数据
  const loadManagerDashboardData = async () => {
    // 如果用户信息不完整，先尝试刷新用户信息
    if (!user?.cafeId && localStorage.getItem('token')) {
      console.log('用户信息不完整，尝试重新获取...');
      await refreshUserAuth();
      return;
    }
    
    if (!user?.cafeId) {
      console.warn('用户没有cafeId，无法加载店面数据');
      return;
    }
    
    setLoading(true);
    try {
      const data = await refreshManagerDashboardData(user.cafeId);
      console.log('店面数据加载结果:', {
        stats: !!data.stats,
        userActivity: data.userActivity?.length || 0,
        roleDistribution: data.roleDistribution?.length || 0,
        batchProcessing: data.batchProcessing?.length || 0,
        batchProcessingData: data.batchProcessing
      });
      setStoreStats(data.stats);
      setUserActivity(data.userActivity);
      setRoleDistribution(data.roleDistribution);
      setBatchProcessing(data.batchProcessing);
    } catch (error) {
      console.error('加载店面仪表板数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    loadManagerDashboardData();
  }, [user?.cafeId, refreshingAuth]);

  // 监听用户信息变化，当用户信息从不完整变为完整时重新加载数据
  React.useEffect(() => {
    if (user?.cafeId && !loading) {
      loadManagerDashboardData();
    }
  }, [user?.cafeId]);

  const handleRefresh = () => {
    loadManagerDashboardData();
  };

  // 如果用户信息不完整且没有在刷新，显示提示
  if (!user?.cafeId && !refreshingAuth) {
    return (
      <div>
        <Alert
          message="用户信息不完整"
          description="正在获取用户信息，请稍候..."
          type="warning"
          showIcon
          action={
            <Button 
              size="small" 
              onClick={refreshUserAuth}
              loading={refreshingAuth}
            >
              重新获取
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Title level={3}>店面管理面板</Title>
          <Text type="secondary">管理 {user?.cafeName || '当前店面'} 的运营和质检</Text>
        </div>
        <Button 
          icon={<ReloadOutlined />} 
          onClick={handleRefresh}
          loading={loading || refreshingAuth}
        >
          {refreshingAuth ? '刷新用户信息...' : '刷新数据'}
        </Button>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card >
            <Statistic
              title="店面用户"
              value={storeStats?.totalUsers || 0}
              prefix={<TeamOutlined />}
              suffix="名"
              valueStyle={{ color: '#1890ff' }}
              loading={loading}
            />
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
              活跃: {storeStats?.activeUsers || 0} 名
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card >
            <Statistic
              title="本月批次"
              value={storeStats?.totalBatches || 0}
              prefix={<DatabaseOutlined />}
              suffix="个"
              valueStyle={{ color: '#fa8c16' }}
              loading={loading}
            />
            <Progress 
              percent={storeStats ? Math.round((storeStats.completedBatches / storeStats.totalBatches) * 100) : 0} 
              size="small" 
              strokeColor="#52c41a"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card >
            <Statistic
              title="待审批"
              value={storeStats?.pendingApprovals || 0}
              prefix={<ClockCircleOutlined />}
              suffix="个"
              valueStyle={{ color: '#f5222d' }}
              loading={loading}
            />
            <div style={{ marginTop: '8px' }}>
              <Button type="link" size="small" onClick={() => navigate('/batches')}>
                立即处理 →
              </Button>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card >
            <Statistic
              title="质量评分"
              value={storeStats?.qualityScore || 0}
              prefix={<CheckCircleOutlined />}
              suffix="/10"
              valueStyle={{ color: '#52c41a' }}
              loading={loading}
              precision={1}
            />
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#52c41a' }}>
              优秀水平
            </div>
          </Card>
        </Col>
      </Row>

      {/* 生产进度 */}
      <Card style={{ border: 'none', boxShadow: 'none', marginBottom: '16px' }}>
        <Row align="middle">
          <Col span={12}>
            <Statistic
              title="今日生产"
              value={storeStats?.todayProduction || 0}
              suffix="批"
              valueStyle={{ color: '#1890ff' }}
              loading={loading}
            />
          </Col>
          <Col span={12}>
            <div>
              <Text type="secondary">月度目标完成度</Text>
              <Progress 
                percent={storeStats?.targetProgress || 0} 
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {storeStats ? Math.round(storeStats.targetProgress * storeStats.monthlyTarget / 100) : 0} / {storeStats?.monthlyTarget || 0} 批
              </Text>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 图表区域 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={12}>
          <UserActivityChart 
            data={userActivity} 
            loading={loading} 
            height={300}
          />
        </Col>
        <Col span={12}>
          <RoleDistributionChart 
            data={roleDistribution} 
            loading={loading} 
            height={300}
          />
        </Col>
      </Row>

      {/* 批次状态和快速操作 */}
      <Row gutter={16}>
        <Col span={12}>
          <Card title="批次处理状态" style={{ height: 300 }}>
            <div style={{ height: 200 }}>
              {loading ? (
                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                  <Text type="secondary">加载中...</Text>
                </div>
              ) : displayBatchProcessing.length === 0 ? (
                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', flexDirection: 'column' }}>
                  <Text type="secondary">暂无批次数据</Text>
                  <Text type="secondary" style={{ fontSize: '12px', marginTop: '8px' }}>
                    数据长度: {displayBatchProcessing.length}
                  </Text>
                </div>
              ) : (
                <div>
                  {displayBatchProcessing.map((item, index) => (
                    <div key={index} style={{ marginBottom: '12px', display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '8px', background: '#fafafa', borderRadius: '6px' }}>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <div 
                          style={{ 
                            width: '12px', 
                            height: '12px', 
                            backgroundColor: item.color, 
                            borderRadius: '50%', 
                            marginRight: '8px' 
                          }} 
                        />
                        <Text>{item.status}</Text>
                      </div>
                      <Text strong>{item.count} 个</Text>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="快速操作" style={{ height: 300 }}>
            <div style={{ height: 200, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
              <Row gutter={[8, 8]} style={{ flex: 1 }}>
                <Col span={12}>
                  <Button
                    type="primary"
                    icon={<TeamOutlined />}
                    block
                    size="large"
                    style={{ height: '44px' }}
                    onClick={() => navigate('/users')}
                  >
                    用户管理
                  </Button>
                </Col>
                <Col span={12}>
                  <Button
                    icon={<DatabaseOutlined />}
                    block
                    size="large"
                    style={{ height: '44px' }}
                    onClick={() => navigate('/batches')}
                  >
                    批次管理
                  </Button>
                </Col>
                <Col span={12}>
                  <Button
                    icon={<ExperimentOutlined />}
                    block
                    size="large"
                    style={{ height: '44px' }}
                    onClick={() => navigate('/recipes')}
                  >
                    配方管理
                  </Button>
                </Col>
                <Col span={12}>
                  <Button
                    icon={<RobotOutlined />}
                    block
                    size="large"
                    style={{ height: '44px' }}
                    onClick={() => navigate('/analysis')}
                  >
                    AI分析
                  </Button>
                </Col>
              </Row>
              <Row style={{ marginTop: '8px' }}>
                <Col span={24}>
                  <Button
                    icon={<SafetyOutlined />}
                    block
                    size="large"
                    style={{ height: '44px' }}
                    onClick={() => navigate('/reports')}
                  >
                    查看质量报告
                  </Button>
                </Col>
              </Row>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

// 普通用户仪表板
const UserDashboard: React.FC<DashboardProps> = ({ user }) => {
  const navigate = useNavigate();

  return (
    <div>
      <div style={{ marginBottom: '24px' }}>
        <Title level={3}>我的工作台</Title>
        <Text type="secondary">质检操作和个人数据</Text>
      </div>

      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="我的配方"
              value={12}
              prefix={<ExperimentOutlined />}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="进行中批次"
              value={3}
              prefix={<DatabaseOutlined />}
              suffix="个"
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="本月分析"
              value={28}
              prefix={<RobotOutlined />}
              suffix="次"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均质量分"
              value={8.7}
              prefix={<EyeOutlined />}
              suffix="/10"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Card title="快速操作" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                block
                onClick={() => navigate('/batches/new')}
              >
                新建批次
              </Button>
              <Button
                icon={<RobotOutlined />}
                block
                onClick={() => navigate('/analysis')}
              >
                AI分析
              </Button>
              <Button
                icon={<ExperimentOutlined />}
                block
                onClick={() => navigate('/recipes')}
              >
                我的配方
              </Button>
            </Space>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="最近活动" size="small">
            <div style={{ height: '200px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Text type="secondary">暂无最近活动</Text>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

// 消费者仪表板
const ViewerDashboard: React.FC<DashboardProps> = ({ user }) => {
  const navigate = useNavigate();
  const [loading, setLoading] = React.useState(false);
  const [customerStats, setCustomerStats] = React.useState<CustomerStats>({
    totalOrders: 0,
    totalSpent: 0,
    loyaltyPoints: 0,
    favoriteProduct: '',
    avgOrderValue: 0,
    recentOrders: [],
    monthlyPurchases: [],
    probioticHealthIndex: {
      index: 0,
      level: '初始',
      color: '#d9d9d9',
      advice: '正在加载您的健康数据...',
      breakdown: {
        frequency: 0,
        diversity: 0,
        continuity: 0,
        probioticPreference: 0,
        dietMatch: 0
      }
    }
  });
  const [customerPreferences, setCustomerPreferences] = React.useState<CustomerPreferences>({
    favoriteProducts: [],
    tastePreferences: {},
    dietaryRestrictions: [],
    preferredSize: '',
    preferredTimeSlots: [],
    notes: ''
  });

  // 获取顾客数据的函数
  const loadCustomerData = React.useCallback(async () => {
    setLoading(true);
    try {
      const [stats, preferences] = await Promise.all([
        getCustomerStats(),
        getCustomerPreferences()
      ]);
      
      setCustomerStats(stats);
      setCustomerPreferences(preferences);
    } catch (error) {
      console.error('加载顾客数据失败:', error);
      // 如果API失败，使用模拟数据作为备用
      setCustomerStats({
        totalOrders: 3,
        totalSpent: 70.00,
        loyaltyPoints: 71,
        favoriteProduct: '经典原味酸奶',
        avgOrderValue: 23.33,
        recentOrders: [
          { date: '1天前', amount: 31.60, items: '经典原味酸奶 x2, 蓝莓风味酸奶 x1' },
          { date: '3天前', amount: 12.80, items: '经典原味酸奶 x1' },
          { date: '5天前', amount: 25.60, items: '经典原味酸奶 x1, 蓝莓风味酸奶 x1' }
        ],
        monthlyPurchases: [
          { month: '本月', count: 3, amount: 70.00 },
          { month: '上月', count: 5, amount: 128.50 },
          { month: '两月前', count: 2, amount: 45.20 }
        ],
        probioticHealthIndex: {
          index: 72,
          level: '良好',
          color: '#1890ff',
          advice: '您的益生菌摄入习惯良好，建议增加饮用频率或尝试更多发酵产品种类。',
          breakdown: {
            frequency: 65,
            diversity: 55,
            continuity: 80,
            probioticPreference: 85,
            dietMatch: 95
          }
        }
      });
      // 根据健康指数调整偏好数据，确保一致性
      const indexScore = 72; // 使用模拟数据中的健康指数
      setCustomerPreferences({
        favoriteProducts: [customerStats.favoriteProduct || '经典原味酸奶'],
        tastePreferences: { 
          acidity: indexScore >= 70 ? 'medium' : 'low', 
          sweetness: 'low', 
          texture: indexScore >= 70 ? 'smooth' : 'thick' 
        },
        dietaryRestrictions: indexScore >= 90 ? ['lactose_sensitive'] : [],
        preferredSize: indexScore >= 60 ? '200ml' : '150ml',
        preferredTimeSlots: indexScore >= 80 ? ['morning', 'evening'] : ['morning'],
        notes: ''
      });
    } finally {
      setLoading(false);
    }
  }, []);

  React.useEffect(() => {
    loadCustomerData();
  }, [loadCustomerData]);

  return (
    <div>
      <div style={{ marginBottom: '24px' }}>
        <Title level={3}>
          <SafetyOutlined style={{ marginRight: '8px', color: '#52c41a' }} />
          我的购买记录
        </Title>
        <Text type="secondary">查看您的购买历史、偏好分析和积分信息</Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="累计订单"
              value={customerStats.totalOrders}
              prefix={<ShopOutlined />}
              suffix="笔"
              valueStyle={{ color: '#1890ff' }}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="累计消费"
              value={customerStats.totalSpent}
              prefix={<Text style={{ color: '#52c41a' }}>¥</Text>}
              precision={2}
              valueStyle={{ color: '#52c41a' }}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="积分余额"
              value={customerStats.loyaltyPoints}
              prefix={<Text style={{ color: '#fa8c16' }}>★</Text>}
              suffix="分"
              valueStyle={{ color: '#fa8c16' }}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均订单"
              value={customerStats.avgOrderValue}
              prefix={<Text style={{ color: '#722ed1' }}>¥</Text>}
              precision={2}
              suffix="/笔"
              valueStyle={{ color: '#722ed1' }}
              loading={loading}
            />
          </Card>
        </Col>
      </Row>

      {/* 购买分析区域 - 改进对齐和一致性 */}
      <div style={{ 
        marginBottom: '24px', 
        padding: '20px', 
        backgroundColor: '#fafafa', 
        borderRadius: '8px',
        border: '1px solid #e8e8e8'
      }}>
        <Title level={4} style={{ marginBottom: '20px', textAlign: 'center' }}>
          🛒 购买分析与健康评估
        </Title>
        
        <Row gutter={[24, 24]} align="stretch">
          <Col span={14}>
            <Card 
              title={
                <Space>
                  <span>📊 购买偏好分析</span>
                  <Tag color={customerStats.probioticHealthIndex?.color || '#faad14'}>
                    健康指数: {customerStats.probioticHealthIndex?.index || 65}分
                  </Tag>
                </Space>
              }
              loading={loading}
              style={{ height: '100%' }}
            >
              <div style={{ padding: '8px 0' }}>
                {/* 核心偏好指标 - 与健康指数关联 */}
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <div style={{ 
                      padding: '12px', 
                      backgroundColor: '#f0f9ff', 
                      borderRadius: '6px',
                      border: '1px solid #d1ecf1'
                    }}>
                      <Text strong style={{ color: '#1890ff' }}>最爱产品</Text>
                      <div style={{ marginTop: '4px', fontSize: '16px', fontWeight: 'bold' }}>
                        {customerStats.favoriteProduct}
                      </div>
                      <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                        频率得分: {customerStats.probioticHealthIndex?.breakdown?.frequency || 65}分
                      </div>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div style={{ 
                      padding: '12px', 
                      backgroundColor: '#f6ffed', 
                      borderRadius: '6px',
                      border: '1px solid #b7eb8f'
                    }}>
                      <Text strong style={{ color: '#52c41a' }}>产品多样性</Text>
                      <div style={{ marginTop: '4px', fontSize: '16px', fontWeight: 'bold' }}>
                        {customerPreferences.tastePreferences.texture || '平衡型'}
                      </div>
                      <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                        多样性得分: {customerStats.probioticHealthIndex?.breakdown?.diversity || 55}分
                      </div>
                    </div>
                  </Col>
                </Row>
                
                <Divider style={{ margin: '16px 0' }} />
                
                {/* 详细偏好信息 */}
                <Row gutter={[16, 12]}>
                  <Col span={24}>
                    <div style={{ marginBottom: '12px' }}>
                      <Text strong>口味偏好：</Text>
                      <Text style={{ marginLeft: '8px' }}>
                        {customerPreferences.tastePreferences.acidity && `${customerPreferences.tastePreferences.acidity}酸度`}
                        {customerPreferences.tastePreferences.sweetness && `, ${customerPreferences.tastePreferences.sweetness}甜度`}
                        {customerPreferences.tastePreferences.texture && `, ${customerPreferences.tastePreferences.texture}口感`}
                        {!customerPreferences.tastePreferences.acidity && !customerPreferences.tastePreferences.sweetness && !customerPreferences.tastePreferences.texture && '平衡口味'}
                      </Text>
                      <div style={{ float: 'right' }}>
                        <Tag color={customerStats.probioticHealthIndex?.breakdown?.probioticPreference >= 80 ? 'green' : customerStats.probioticHealthIndex?.breakdown?.probioticPreference >= 60 ? 'blue' : 'orange'}>
                          活菌偏好: {customerStats.probioticHealthIndex?.breakdown?.probioticPreference || 85}分
                        </Tag>
                      </div>
                    </div>
                  </Col>
                  <Col span={24}>
                    <div style={{ marginBottom: '12px' }}>
                      <Text strong>购买习惯：</Text>
                      <Text style={{ marginLeft: '8px' }}>
                        {customerPreferences.preferredSize && `偏好${customerPreferences.preferredSize}规格`}
                        {customerPreferences.preferredTimeSlots.length > 0 && `, 偏好时段: ${customerPreferences.preferredTimeSlots.join(', ')}`}
                        {!customerPreferences.preferredSize && customerPreferences.preferredTimeSlots.length === 0 && '规律购买'}
                      </Text>
                      <div style={{ float: 'right' }}>
                        <Tag color={customerStats.probioticHealthIndex?.breakdown?.continuity >= 80 ? 'green' : customerStats.probioticHealthIndex?.breakdown?.continuity >= 60 ? 'blue' : 'orange'}>
                          连续性: {customerStats.probioticHealthIndex?.breakdown?.continuity || 80}分
                        </Tag>
                      </div>
                    </div>
                  </Col>
                  <Col span={24}>
                    <div>
                      <Text strong>饮食特点：</Text>
                      <Text style={{ marginLeft: '8px', color: '#fa8c16' }}>
                        {customerPreferences.dietaryRestrictions.length > 0 
                          ? customerPreferences.dietaryRestrictions.map(restriction => {
                              const restrictionMap: { [key: string]: string } = {
                                'lactose_sensitive': '乳糖敏感',
                                'vegetarian': '素食主义',
                                'vegan': '纯素食',
                                'gluten_free': '无麸质'
                              };
                              return restrictionMap[restriction] || restriction;
                            }).join(', ')
                          : '无特殊要求'
                        }
                      </Text>
                      <div style={{ float: 'right' }}>
                        <Tag color={customerStats.probioticHealthIndex?.breakdown?.dietMatch >= 90 ? 'green' : 'blue'}>
                          饮食匹配: {customerStats.probioticHealthIndex?.breakdown?.dietMatch || 95}分
                        </Tag>
                      </div>
                    </div>
                  </Col>
                </Row>
              </div>
            </Card>
          </Col>
          
          <Col span={10}>
            <Card 
              title="🧬 益生菌健康指数" 
              loading={loading}
              style={{ height: '100%' }}
              extra={
                <Tag color={customerStats.probioticHealthIndex?.color || '#faad14'}>
                  {customerStats.probioticHealthIndex?.level || '中等'}
                </Tag>
              }
            >
              <div style={{ textAlign: 'center', padding: '8px 0' }}>
                {/* 圆形进度指示器 */}
                <div style={{ position: 'relative', display: 'inline-block', marginBottom: '12px' }}>
                  <div style={{
                    width: '100px',
                    height: '100px',
                    borderRadius: '50%',
                    background: `conic-gradient(${customerStats.probioticHealthIndex?.color || '#faad14'} ${customerStats.probioticHealthIndex?.index || 65}%, #f0f0f0 0%)`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative'
                  }}>
                    <div style={{
                      width: '76px',
                      height: '76px',
                      borderRadius: '50%',
                      backgroundColor: '#fff',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <div style={{ 
                        fontSize: '20px', 
                        fontWeight: 'bold', 
                        color: customerStats.probioticHealthIndex?.color || '#faad14'
                      }}>
                        {customerStats.probioticHealthIndex?.index || 65}
                      </div>
                      <div style={{ fontSize: '10px', color: '#666' }}>
                        健康指数
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* 健康建议 */}
                <div style={{ 
                  padding: '8px', 
                  backgroundColor: '#f9f9f9', 
                  borderRadius: '4px',
                  marginBottom: '12px',
                  fontSize: '11px',
                  lineHeight: '1.3'
                }}>
                  <Text style={{ color: '#666' }}>
                    {customerStats.probioticHealthIndex?.advice || '建议您规律饮用活菌酸奶，有助于维持肠道健康。'}
                  </Text>
                </div>

                {/* 指数分解 - 紧凑显示 */}
                <div style={{ textAlign: 'left' }}>
                  {customerStats.probioticHealthIndex?.breakdown && Object.entries({
                    frequency: '频率',
                    diversity: '多样性', 
                    continuity: '连续性',
                    probioticPreference: '活菌偏好',
                    dietMatch: '饮食匹配'
                  }).map(([key, label]) => {
                    const value = customerStats.probioticHealthIndex.breakdown[key as keyof typeof customerStats.probioticHealthIndex.breakdown];
                    return (
                      <div key={key} style={{ 
                        display: 'flex', 
                        justifyContent: 'space-between', 
                        alignItems: 'center',
                        marginBottom: '3px',
                        fontSize: '10px'
                      }}>
                        <Text style={{ color: '#666' }}>{label}</Text>
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <div style={{
                            width: '32px',
                            height: '3px',
                            backgroundColor: '#f0f0f0',
                            borderRadius: '2px',
                            marginRight: '4px',
                            overflow: 'hidden'
                          }}>
                            <div style={{
                              width: `${value}%`,
                              height: '100%',
                              backgroundColor: value >= 80 ? '#52c41a' : value >= 60 ? '#1890ff' : value >= 40 ? '#faad14' : '#f5222d',
                              borderRadius: '2px'
                            }} />
                          </div>
                          <Text style={{ fontSize: '10px', color: '#666', minWidth: '18px' }}>{value}</Text>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      </div>

      {/* 最近订单 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={24}>
          <Card title="最近订单" loading={loading}>
            <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
              {customerStats.recentOrders.map((order, index) => (
                <div key={index} style={{ 
                  padding: '12px', 
                  borderBottom: index < customerStats.recentOrders.length - 1 ? '1px solid #f0f0f0' : 'none',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'flex-start'
                }}>
                  <div style={{ flex: 1 }}>
                    <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                      ¥{order.amount.toFixed(2)}
                    </div>
                    <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>
                      {order.items}
                    </div>
                    <div style={{ fontSize: '12px', color: '#999' }}>
                      {order.date}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </Col>
      </Row>

      {/* 月度统计和操作 - 对齐优化 */}
      <Row gutter={16} align="stretch">
        <Col span={12}>
          <Card 
            title={
              <Space>
                <span>📈 月度购买统计</span>
                <Tag color="blue">{customerStats.monthlyPurchases.length}个月数据</Tag>
              </Space>
            }
            loading={loading}
            style={{ height: '100%' }}
          >
            <div style={{ minHeight: '200px' }}>
              {customerStats.monthlyPurchases.map((month, index) => (
                <div key={index} style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'center',
                  padding: '12px 0',
                  borderBottom: index < customerStats.monthlyPurchases.length - 1 ? '1px solid #f0f0f0' : 'none',
                  backgroundColor: index === 0 ? '#f6ffed' : 'transparent',
                  marginLeft: index === 0 ? '-12px' : '0',
                  marginRight: index === 0 ? '-12px' : '0',
                  paddingLeft: index === 0 ? '12px' : '0',
                  paddingRight: index === 0 ? '12px' : '0',
                  borderRadius: index === 0 ? '4px' : '0'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div style={{
                      width: '8px',
                      height: '8px',
                      borderRadius: '50%',
                      backgroundColor: index === 0 ? '#52c41a' : '#d9d9d9',
                      marginRight: '8px'
                    }} />
                    <Text strong style={{ color: index === 0 ? '#52c41a' : 'inherit' }}>
                      {month.month}
                    </Text>
                  </div>
                  <div style={{ textAlign: 'right' }}>
                    <div style={{ color: '#1890ff', fontWeight: 'bold' }}>
                      {month.count} 笔订单
                    </div>
                    <div style={{ color: '#52c41a', fontSize: '12px' }}>
                      ¥{month.amount.toFixed(2)}
                    </div>
                  </div>
                </div>
              ))}
              
              {/* 趋势提示 */}
              {customerStats.monthlyPurchases.length >= 2 && (
                <div style={{ 
                  marginTop: '12px', 
                  padding: '8px', 
                  backgroundColor: '#f0f9ff', 
                  borderRadius: '4px',
                  fontSize: '12px',
                  color: '#666'
                }}>
                  {customerStats.monthlyPurchases[0].count > customerStats.monthlyPurchases[1].count ? (
                    <span style={{ color: '#52c41a' }}>
                      📈 购买频率呈上升趋势，继续保持！
                    </span>
                  ) : customerStats.monthlyPurchases[0].count < customerStats.monthlyPurchases[1].count ? (
                    <span style={{ color: '#faad14' }}>
                      📉 购买频率有所下降，建议增加饮用频率
                    </span>
                  ) : (
                    <span style={{ color: '#1890ff' }}>
                      📊 购买频率保持稳定
                    </span>
                  )}
                </div>
              )}
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card 
            title={
              <Space>
                <span>⚡ 快速操作</span>
                <Tag color="green">个性化推荐</Tag>
              </Space>
            }
            style={{ height: '100%' }}
          >
            <div style={{ minHeight: '200px', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <Button
                  type="primary"
                  icon={<ShopOutlined />}
                  block
                  size="large"
                  style={{ height: '48px' }}
                  onClick={() => navigate('/products')}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                    <span>浏览产品</span>
                    <Text style={{ fontSize: '12px', color: 'rgba(255,255,255,0.8)' }}>
                      推荐{customerStats.favoriteProduct}
                    </Text>
                  </div>
                </Button>
                
                <Button
                  icon={<SafetyOutlined />}
                  block
                  size="large"
                  style={{ height: '48px' }}
                  onClick={() => navigate('/quality-results')}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                    <span>查看质量检测</span>
                    <Text style={{ fontSize: '12px', color: '#666' }}>
                      活菌保证
                    </Text>
                  </div>
                </Button>
                
                <Button
                  icon={<Text style={{ color: '#fa8c16' }}>★</Text>}
                  block
                  size="large"
                  style={{ height: '48px' }}
                  onClick={() => {
                    // 积分兑换功能
                    alert('积分兑换功能正在开发中...');
                  }}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                    <span>积分兑换</span>
                    <Text style={{ fontSize: '12px', color: '#fa8c16', fontWeight: 'bold' }}>
                      {customerStats.loyaltyPoints}分可用
                    </Text>
                  </div>
                </Button>
              </Space>
              
              {/* 健康提示 */}
              <div style={{ 
                marginTop: '12px', 
                padding: '8px', 
                backgroundColor: '#fff7e6', 
                borderRadius: '4px',
                border: '1px solid #ffd591'
              }}>
                <Text style={{ fontSize: '12px', color: '#d48806' }}>
                  💡 基于您的健康指数({customerStats.probioticHealthIndex?.index || 65}分)，
                  建议{customerStats.probioticHealthIndex?.index >= 80 ? '保持当前饮用习惯' : '增加益生菌产品摄入频率'}
                </Text>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};



// 主要的基于角色的仪表板组件
const RoleBasedDashboard: React.FC<DashboardProps> = ({ user }) => {

  return (
    <div>
      {/* 用户欢迎信息 */}
      <div style={{ marginBottom: '24px', padding: '16px', background: 'transparent' }}>
        <Space>
          <Text strong style={{ fontSize: '16px' }}>
            欢迎回来，{user?.email || '用户'}！
          </Text>
          <UserRoleBadge role={user?.role || 'USER'} />
        </Space>
        <div style={{ marginTop: '8px' }}>
          <Text type="secondary">
            今天是 {new Date().toLocaleDateString('zh-CN', { 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric',
              weekday: 'long'
            })}
          </Text>
        </div>
      </div>

      {/* 基于角色的仪表板内容 */}
      <RoleGuard roles={[UserRole.ADMIN]} showFallback={false}>
        <AdminDashboard user={user} />
      </RoleGuard>

      <RoleGuard roles={[UserRole.MANAGER]} showFallback={false}>
        <ManagerDashboard user={user} />
      </RoleGuard>

      <RoleGuard roles={[UserRole.USER]} showFallback={false}>
        <UserDashboard user={user} />
      </RoleGuard>

      <RoleGuard roles={[UserRole.VIEWER]} showFallback={false}>
        <ViewerDashboard user={user} />
      </RoleGuard>

      {/* 如果没有匹配的角色，显示默认内容 */}
      {!user?.role && (
        <Card>
          <Alert
            message="角色信息缺失"
            description="无法确定您的角色，请重新登录"
            type="warning"
            showIcon
          />
        </Card>
      )}
    </div>
  );
};

export default RoleBasedDashboard;
