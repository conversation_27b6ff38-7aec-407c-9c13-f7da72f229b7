import React from 'react'
import { Spin } from 'antd'
import { LoadingOutlined } from '@ant-design/icons'

interface LoadingSpinnerProps {
  size?: 'small' | 'default' | 'large'
  tip?: string
  spinning?: boolean
  children?: React.ReactNode
  className?: string
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'large',
  tip = '加载中...',
  spinning = true,
  children,
  className = ''
}) => {
  const antIcon = <LoadingOutlined style={{ fontSize: size === 'large' ? 24 : 16, color: '#F05654' }} spin />

  if (children) {
    return (
      <Spin
        indicator={antIcon}
        spinning={spinning}
        tip={tip}
        className={className}
      >
        {children}
      </Spin>
    )
  }

  return (
    <div className={`loading-container ${className}`} style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '200px',
      padding: '20px'
    }}>
      <Spin
        indicator={antIcon}
        size={size}
      />
      {tip && (
        <div style={{ marginTop: '16px', color: '#666', fontSize: '14px' }}>
          {tip}
        </div>
      )}
    </div>
  )
}

export default LoadingSpinner
