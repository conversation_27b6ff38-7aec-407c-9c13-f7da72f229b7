/* 页面底部样式 */
.app-footer {
  background: #ffffff;
  padding: 16px 24px;
  text-align: center;
  margin-top: auto;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}


.footer-content {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

.copyright-text {
  color: #666666;
  font-size: 14px;
  display: flex;
  align-items: center;
  font-weight: 500;
  transition: color 0.3s ease;
}

.copyright-text:hover {
  color: #333333;
}

.copyright-text .anticon {
  color: #F05654;
  transition: color 0.3s ease;
}

.copyright-text:hover .anticon {
  color: #FF6B69;
}

.version-text {
  color: #999999;
  font-size: 12px;
  font-weight: 400;
  transition: color 0.3s ease;
}

.version-text:hover {
  color: #666666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    gap: 8px;
  }
  
  .copyright-text {
    font-size: 13px;
  }
  
  .version-text {
    font-size: 11px;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .app-footer {
    background: #1f1f1f;
  }

  .copyright-text {
    color: #d9d9d9;
  }

  .copyright-text .anticon {
    color: #FF6B69;
  }

  .version-text {
    color: #8c8c8c;
  }
}
