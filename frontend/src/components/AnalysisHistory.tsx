import React, { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON>, 
  Button, 
  Image, 
  Typography, 
  Space, 
  Tag, 
  Modal, 
  Badge,
  Empty,
  Tooltip,
  Popconfirm
} from 'antd';
import { 
  EyeOutlined, 
  ReloadOutlined, 
  DeleteOutlined, 
  RobotOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { AIAnalysisRecord } from '../services/api/aiAnalysisAPI';
import './AnalysisHistory.css';

const { Text } = Typography;

// 本地分析结果接口
interface LocalAnalysisResult {
  id: string;
  fileName: string;
  imageUrl: string;
  result: string;
  status: 'analyzing' | 'completed' | 'error';
  timestamp: Date;
  processingTime?: number;
}

interface AnalysisHistoryProps {
  localResults: LocalAnalysisResult[];
  savedAnalyses: AIAnalysisRecord[];
  onViewResult: (result: LocalAnalysisResult | AIAnalysisRecord) => void;
  onReanalyze: (result: LocalAnalysisResult | AIAnalysisRecord) => void;
  onDelete: (result: LocalAnalysisResult | AIAnalysisRecord) => void;
  className?: string;
}

const AnalysisHistory: React.FC<AnalysisHistoryProps> = ({
  localResults,
  savedAnalyses,
  onViewResult,
  onReanalyze,
  onDelete,
  className
}) => {
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');

  // 合并本地结果和保存的分析记录
  const allResults = [
    ...localResults.map(item => ({ ...item, source: 'local' as const })),
    ...savedAnalyses.map(item => ({ 
      ...item, 
      result: item.analysisResult || '', // 适配API字段
      source: 'saved' as const 
    }))
  ].sort((a, b) => {
    const timeA = 'timestamp' in a ? a.timestamp.getTime() : new Date(a.createdAt).getTime();
    const timeB = 'timestamp' in b ? b.timestamp.getTime() : new Date(b.createdAt).getTime();
    return timeB - timeA; // 最新的在前
  });

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'analyzing':
        return <ClockCircleOutlined style={{ color: '#1890ff' }} />;
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '分析完成';
      case 'analyzing':
        return '分析中...';
      case 'error':
        return '分析失败';
      default:
        return '已完成';
    }
  };

  // 格式化时间
  const formatTime = (time: Date | string) => {
    const date = typeof time === 'string' ? new Date(time) : time;
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // 格式化处理时间
  const formatProcessingTime = (ms?: number) => {
    if (!ms) return '未知';
    const seconds = ms / 1000;
    if (seconds < 60) {
      return `${seconds.toFixed(1)}秒`;
    } else {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}分${remainingSeconds.toFixed(1)}秒`;
    }
  };

  // 预览图片
  const handlePreviewImage = (imageUrl: string, fileName: string) => {
    setPreviewImage(imageUrl);
    setPreviewTitle(fileName);
    setPreviewVisible(true);
  };

  if (allResults.length === 0) {
    return (
      <Card 
        className={`analysis-history ${className || ''}`}
        title={
          <Space>
            <RobotOutlined style={{ color: '#1890ff' }} />
            <span>分析历史</span>
          </Space>
        }
        size="small"
      >
        <Empty
          image={<RobotOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />}
          description={
            <div>
              <Text type="secondary">暂无分析记录</Text>
              <br />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                上传显微镜图片开始您的第一次AI分析
              </Text>
            </div>
          }
        />
      </Card>
    );
  }

  return (
    <>
      <Card 
        className={`analysis-history ${className || ''}`}
        title={
          <Space>
            <RobotOutlined style={{ color: '#1890ff' }} />
            <span>分析历史</span>
            <Badge count={allResults.length} style={{ backgroundColor: '#52c41a' }} />
          </Space>
        }
        size="small"
      >
        <List
          itemLayout="horizontal"
          dataSource={allResults}
          renderItem={(item) => (
            <List.Item
              actions={[
                <Tooltip title="查看详细结果">
                  <Button
                    type="text"
                    icon={<EyeOutlined />}
                    onClick={() => onViewResult(item)}
                    size="small"
                  >
                    查看
                  </Button>
                </Tooltip>,
                <Tooltip title="重新分析此图片">
                  <Button
                    type="text"
                    icon={<ReloadOutlined />}
                    onClick={() => onReanalyze(item)}
                    size="small"
                    disabled={item.status === 'analyzing'}
                  >
                    重新分析
                  </Button>
                </Tooltip>,
                <Popconfirm
                  title="确定要删除这条分析记录吗？"
                  onConfirm={() => onDelete(item)}
                  okText="确定"
                  cancelText="取消"
                >
                  <Tooltip title="删除记录">
                    <Button
                      type="text"
                      icon={<DeleteOutlined />}
                      danger
                      size="small"
                    >
                      删除
                    </Button>
                  </Tooltip>
                </Popconfirm>
              ]}
            >
              <List.Item.Meta
                avatar={
                  <div 
                    className="analysis-history-image"
                    onClick={() => handlePreviewImage(item.imageUrl, item.fileName)}
                  >
                    <Image
                      width={60}
                      height={60}
                      src={item.imageUrl}
                      alt={item.fileName}
                      style={{ 
                        objectFit: 'cover',
                        borderRadius: '6px',
                        cursor: 'pointer'
                      }}
                      preview={false}
                    />
                  </div>
                }
                title={
                  <div>
                    <Space>
                      <Text strong style={{ fontSize: '14px' }}>
                        {item.fileName}
                      </Text>
                      {getStatusIcon(item.status)}
                      <Tag 
                        color={item.source === 'local' ? 'blue' : 'green'}
                        style={{ fontSize: '12px' }}
                      >
                        {item.source === 'local' ? '本地' : '云端'}
                      </Tag>
                    </Space>
                  </div>
                }
                description={
                  <div>
                    <Space direction="vertical" size={2}>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        分析时间: {formatTime('timestamp' in item ? item.timestamp : item.createdAt)}
                      </Text>
                      <Space size={16}>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          处理耗时: {formatProcessingTime(item.processingTime)}
                        </Text>
                        <Text 
                          type={item.status === 'completed' ? 'success' : item.status === 'error' ? 'danger' : 'warning'}
                          style={{ fontSize: '12px' }}
                        >
                          {getStatusText(item.status)}
                        </Text>
                      </Space>
                    </Space>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </Card>

      {/* 图片预览模态框 */}
      <Modal
        open={previewVisible}
        title={previewTitle}
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        width="80%"
        style={{ maxWidth: '800px' }}
      >
        <Image
          width="100%"
          src={previewImage}
          alt={previewTitle}
        />
      </Modal>
    </>
  );
};

export default AnalysisHistory;
