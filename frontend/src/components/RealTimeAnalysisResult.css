.real-time-analysis-result {
  margin-top: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8f4fd;
  border-radius: 8px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fcff 100%);
}

.real-time-analysis-result .ant-card-head {
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  border-bottom: 1px solid #d4edda;
}

.real-time-analysis-result .ant-card-head-title {
  color: #1890ff;
  font-weight: 600;
}

.analysis-content {
  position: relative;
  transition: all 0.3s ease;
}

.analysis-content:hover {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

/* 打字机光标动画 */
@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.typing-cursor {
  animation: blink 1s infinite;
}

/* 滚动条样式 */
.analysis-content::-webkit-scrollbar {
  width: 6px;
}

.analysis-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.analysis-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.analysis-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 进度条自定义样式 */
.real-time-analysis-result .ant-progress-line {
  margin-bottom: 8px;
}

.real-time-analysis-result .ant-progress-bg {
  background: linear-gradient(90deg, #1890ff 0%, #52c41a 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .real-time-analysis-result {
    margin: 12px 0;
  }
  
  .analysis-content {
    max-height: 300px !important;
    font-size: 12px !important;
  }
  
  .real-time-analysis-result .ant-card-head-title {
    font-size: 14px;
  }
}

/* 内容高亮样式 */
.analysis-content .highlight {
  background-color: #fff3cd;
  padding: 2px 4px;
  border-radius: 3px;
  border-left: 3px solid #ffc107;
  margin: 4px 0;
  display: block;
}

.analysis-content .error-text {
  color: #ff4d4f;
  background-color: #fff2f0;
  padding: 2px 4px;
  border-radius: 3px;
}

.analysis-content .success-text {
  color: #52c41a;
  background-color: #f6ffed;
  padding: 2px 4px;
  border-radius: 3px;
}

/* 加载动画 */
.analysis-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #1890ff;
}

.analysis-loading::after {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 卡片动画 */
.real-time-analysis-result {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 按钮样式优化 */
.real-time-analysis-result .ant-btn {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.real-time-analysis-result .ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 统计信息样式 */
.real-time-analysis-result .ant-divider-vertical {
  border-color: #d9d9d9;
  margin: 0 8px;
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.status-indicator.analyzing {
  background-color: #1890ff;
  animation: pulse 1.5s infinite;
}

.status-indicator.completed {
  background-color: #52c41a;
}

.status-indicator.error {
  background-color: #ff4d4f;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}
