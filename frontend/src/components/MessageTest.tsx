import React from 'react'
import { Button, Space } from 'antd'
import useMessage from '../hooks/useMessage'

/**
 * 测试组件，用于验证 useMessage hook 是否正常工作
 */
const MessageTest: React.FC = () => {
  const message = useMessage()

  const showSuccess = () => {
    message.success('这是一个成功消息')
  }

  const showError = () => {
    message.error('这是一个错误消息')
  }

  const showWarning = () => {
    message.warning('这是一个警告消息')
  }

  const showInfo = () => {
    message.info('这是一个信息消息')
  }

  return (
    <div style={{ padding: '20px' }}>
      <h3>消息测试</h3>
      <Space>
        <Button type="primary" onClick={showSuccess}>
          成功消息
        </Button>
        <Button danger onClick={showError}>
          错误消息
        </Button>
        <Button onClick={showWarning}>
          警告消息
        </Button>
        <Button onClick={showInfo}>
          信息消息
        </Button>
      </Space>
    </div>
  )
}

export default MessageTest
