import React, { useState, useMemo } from 'react'
import { Layout as AntLayout, Menu, Avatar, Dropdown, Button, Badge, Space, Typography } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import { useDispatch } from 'react-redux'
import {
  DashboardOutlined,
  ExperimentOutlined,
  DatabaseOutlined,
  RobotOutlined,
  FileTextOutlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
  <PERSON>Outlined,
  <PERSON>uFoldOutlined,
  <PERSON>uUnfoldOutlined,
  TeamOutlined,
  AuditOutlined,
  CloudUploadOutlined,
  Bar<PERSON>hartOutlined,
  ShopOutlined,
} from '@ant-design/icons'

import { logout } from '../../store/slices/authSlice'
import { useTypedSelector } from '../../hooks/useTypedSelector'
import { usePermissions } from '../../hooks/usePermissions'
import { UserRoleBadge, UserRoleAvatar } from '../UserRoleBadge'
import { Permission, UserRole } from '../../types/permissions'
import NotificationDropdown from '../NotificationDropdown'
import Footer from '../Footer'
import './Layout.css'

const { Header, Sider, Content } = AntLayout
const { Text } = Typography

interface LayoutProps {
  children: React.ReactNode
}

interface MenuItem {
  key: string
  icon: React.ReactNode
  label: string
  permissions?: Permission[]
  roles?: UserRole[]
  children?: MenuItem[]
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()
  const dispatch = useDispatch()
  const { user } = useTypedSelector((state) => state.auth)
  const { filterAccessibleMenuItems, getUserRole, isAdmin } = usePermissions()

  // 基础菜单项配置
  const allMenuItems: MenuItem[] = [
    {
      key: '/',
      icon: <DashboardOutlined style={{ fontSize: '16px' }} />,
      label: '仪表板',
    },
    {
      key: '/recipes',
      icon: <ExperimentOutlined style={{ fontSize: '16px' }} />,
      label: '配方管理',
      permissions: [Permission.RECIPE_READ],
      roles: [UserRole.ADMIN, UserRole.MANAGER, UserRole.USER], // 店面管理员也需要访问
    },
    {
      key: '/batches',
      icon: <DatabaseOutlined style={{ fontSize: '16px' }} />,
      label: '批次管理',
      permissions: [Permission.BATCH_READ],
      roles: [UserRole.ADMIN, UserRole.MANAGER, UserRole.USER], // 店面管理员也需要访问
    },
    {
      key: '/analysis',
      icon: <RobotOutlined style={{ fontSize: '16px' }} />,
      label: 'AI分析',
      permissions: [Permission.AI_ANALYZE, Permission.AI_RESULTS_READ],
      roles: [UserRole.ADMIN, UserRole.MANAGER, UserRole.USER], // 店面管理员也需要访问
    },
    {
      key: '/reports',
      icon: <FileTextOutlined style={{ fontSize: '16px' }} />,
      label: '报告中心',
      permissions: [Permission.REPORT_READ],
      roles: [UserRole.ADMIN, UserRole.MANAGER, UserRole.USER], // 店面管理员也需要访问
    },
    // 消费者专用菜单
    {
      key: '/products',
      icon: <ShopOutlined style={{ fontSize: '16px' }} />,
      label: '产品信息',
      roles: [UserRole.VIEWER],
      permissions: [Permission.CONSUMER_PRODUCT_INFO],
    },
    {
      key: '/quality-results',
      icon: <BarChartOutlined style={{ fontSize: '16px' }} />,
      label: '质量检测结果',
      roles: [UserRole.VIEWER],
      permissions: [Permission.CONSUMER_QUALITY_RESULTS],
    },
    // 店面管理员和操作员专用菜单
    {
      key: '/product-management',
      icon: <ShopOutlined style={{ fontSize: '16px' }} />,
      label: '产品管理',
      roles: [UserRole.MANAGER, UserRole.USER], // 店面管理员和操作员都可以管理产品
      permissions: [Permission.RECIPE_CREATE], // 有创建配方权限的用户也应该有产品管理权限
    },
    // 管理员专用菜单
    {
      key: '/users',
      icon: <TeamOutlined style={{ fontSize: '16px' }} />,
      label: '用户管理',
      permissions: [Permission.USER_READ],
      roles: [UserRole.ADMIN, UserRole.MANAGER], // 店面管理员也可以管理用户
    },
    {
      key: '/audit',
      icon: <AuditOutlined style={{ fontSize: '16px' }} />,
      label: '审计日志',
      permissions: [Permission.AUDIT_READ],
      roles: [UserRole.ADMIN],
    },
    {
      key: '/data',
      icon: <CloudUploadOutlined style={{ fontSize: '16px' }} />,
      label: '数据管理',
      permissions: [Permission.DATA_IMPORT, Permission.DATA_EXPORT],
      roles: [UserRole.ADMIN],
    },
    {
      key: '/settings',
      icon: <SettingOutlined style={{ fontSize: '16px' }} />,
      label: '系统设置',
      permissions: [Permission.SYSTEM_SETTINGS],
      roles: [UserRole.ADMIN],
    },
  ]

  // 根据用户权限过滤菜单项
  const menuItems = useMemo(() => {
    return filterAccessibleMenuItems(allMenuItems)
  }, [filterAccessibleMenuItems, allMenuItems])

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => navigate('/profile'),
    },
    ...(isAdmin() ? [{
      key: 'admin-settings',
      icon: <SettingOutlined />,
      label: '系统设置',
      onClick: () => navigate('/settings'),
    }] : []),
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: () => {
        dispatch(logout())
        navigate('/login')
      },
    },
  ]

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key)
  }

  return (
    <AntLayout className="layout-container">
      {/* 侧边栏 */}
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        className="layout-sider"
        width={240}
        collapsedWidth={80}
      >
        {/* Logo区域 */}
        <div className="layout-logo">
          <div className="logo-icon">🥛</div>
          {!collapsed && (
            <div className="logo-text">
              <div className="logo-title">Yogurt AI QC</div>
              <div className="logo-subtitle">酸奶质控系统</div>
            </div>
          )}
        </div>

        {/* 导航菜单 */}
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          className="layout-menu"
          style={{ 
            border: 'none',
            fontSize: '14px'
          }}
        />
      </Sider>

      {/* 主内容区域 */}
      <AntLayout className="layout-main">
        {/* 顶部导航栏 */}
        <Header className="layout-header">
          <div className="header-left">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              className="collapse-trigger"
            />
          </div>

          <div className="header-right">
            <Space size="middle">
              {/* 通知铃铛 */}
              <NotificationDropdown className="header-action" />

              {/* 用户信息 */}
              <Dropdown
                menu={{ items: userMenuItems }}
                placement="bottomRight"
                arrow
              >
                <div className="user-info">
                  <UserRoleAvatar
                    role={user?.role || 'USER'}
                    size="small"
                    showTooltip={false}
                  />
                  <div className="user-details">
                    <span className="user-name">{user?.email || '用户'}</span>
                    <UserRoleBadge
                      role={user?.role || 'USER'}
                      size="small"
                      showIcon={false}
                      showTooltip={false}
                      style={{ fontSize: '11px', padding: '0 4px', lineHeight: '16px' }}
                    />
                  </div>
                </div>
              </Dropdown>
            </Space>
          </div>
        </Header>

        {/* 页面内容 */}
        <Content className="layout-content">
          <div className="content-wrapper">
            {children}
          </div>
        </Content>
        <Footer />
      </AntLayout>
    </AntLayout>
  )
}

export default Layout
