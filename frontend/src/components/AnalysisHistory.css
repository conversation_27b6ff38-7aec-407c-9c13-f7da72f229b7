.analysis-history {
  margin-top: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8f4fd;
  border-radius: 8px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fcff 100%);
}

.analysis-history .ant-card-head {
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  border-bottom: 1px solid #d4edda;
}

.analysis-history .ant-card-head-title {
  color: #1890ff;
  font-weight: 600;
}

.analysis-history .ant-list-item {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.analysis-history .ant-list-item:hover {
  background-color: #fafafa;
}

.analysis-history .ant-list-item:last-child {
  border-bottom: none;
}

.analysis-history-image {
  position: relative;
  overflow: hidden;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.analysis-history-image:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.analysis-history-image::after {
  content: '🔍';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.analysis-history-image:hover::after {
  opacity: 1;
}

/* 状态标签样式 */
.analysis-history .ant-tag {
  border-radius: 12px;
  font-size: 11px;
  padding: 2px 8px;
  border: none;
}

/* 按钮样式优化 */
.analysis-history .ant-btn {
  border-radius: 6px;
  transition: all 0.3s ease;
  font-size: 12px;
}

.analysis-history .ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.analysis-history .ant-btn-text {
  color: #666;
}

.analysis-history .ant-btn-text:hover {
  color: #1890ff;
  background-color: #f0f9ff;
}

.analysis-history .ant-btn-dangerous:hover {
  color: #ff4d4f;
  background-color: #fff2f0;
}

/* 空状态样式 */
.analysis-history .ant-empty {
  padding: 40px 20px;
}

.analysis-history .ant-empty-description {
  color: #999;
}

/* 列表项元信息样式 */
.analysis-history .ant-list-item-meta-title {
  margin-bottom: 8px;
}

.analysis-history .ant-list-item-meta-description {
  color: #666;
  line-height: 1.4;
}

/* 徽章样式 */
.analysis-history .ant-badge {
  margin-left: 8px;
}

.analysis-history .ant-badge-count {
  font-size: 11px;
  min-width: 18px;
  height: 18px;
  line-height: 18px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .analysis-history {
    margin: 16px 0;
  }
  
  .analysis-history .ant-list-item {
    padding: 12px 16px;
  }
  
  .analysis-history-image {
    width: 50px;
    height: 50px;
  }
  
  .analysis-history .ant-list-item-action {
    margin-left: 8px;
  }
  
  .analysis-history .ant-btn {
    padding: 4px 8px;
    font-size: 11px;
  }
  
  .analysis-history .ant-list-item-meta-title {
    font-size: 13px;
  }
  
  .analysis-history .ant-list-item-meta-description {
    font-size: 11px;
  }
}

/* 动画效果 */
.analysis-history {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态样式 */
.analysis-history .ant-list-item.analyzing {
  background: linear-gradient(90deg, #f0f9ff 0%, #e6f7ff 50%, #f0f9ff 100%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 工具提示样式 */
.analysis-history .ant-tooltip {
  font-size: 12px;
}

/* 确认弹窗样式 */
.analysis-history .ant-popover-inner-content {
  padding: 12px;
}

/* 图片预览模态框样式 */
.analysis-history .ant-modal-content {
  border-radius: 8px;
  overflow: hidden;
}

.analysis-history .ant-modal-header {
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  border-bottom: 1px solid #d4edda;
}

.analysis-history .ant-modal-title {
  color: #1890ff;
  font-weight: 600;
}

/* 列表项状态指示器 */
.analysis-history .status-indicator {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 6px;
}

.analysis-history .status-indicator.completed {
  background-color: #52c41a;
}

.analysis-history .status-indicator.analyzing {
  background-color: #1890ff;
  animation: pulse 1.5s infinite;
}

.analysis-history .status-indicator.error {
  background-color: #ff4d4f;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}
