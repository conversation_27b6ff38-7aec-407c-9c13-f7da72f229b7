import React, { useState } from 'react'
import { Button, Card, Typography, Space, Alert } from 'antd'
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons'

const { Title, Text } = Typography

/**
 * API 连接测试组件
 * 用于验证前端是否能正常连接到后端 API
 */
const ApiTest: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<{
    success: boolean
    message: string
    data?: any
  } | null>(null)

  const testApiConnection = async () => {
    setLoading(true)
    setResult(null)

    try {
      // 测试健康检查端点
      const response = await fetch('/health')
      const data = await response.json()
      
      setResult({
        success: true,
        message: '✅ API 连接成功！',
        data: data
      })
    } catch (error: any) {
      setResult({
        success: false,
        message: `❌ API 连接失败: ${error.message || '未知错误'}`,
        data: error.response?.data
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card title="API 连接测试" style={{ margin: '20px' }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Text>点击下面的按钮测试前端是否能正常连接到后端 API（端口 3010）</Text>
        
        <Button 
          type="primary" 
          onClick={testApiConnection}
          loading={loading}
          icon={result?.success ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
        >
          测试 API 连接
        </Button>

        {result && (
          <Alert
            type={result.success ? 'success' : 'error'}
            message={result.message}
            description={
              result.data && (
                <pre style={{ fontSize: '12px', marginTop: '10px' }}>
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              )
            }
            showIcon
          />
        )}
      </Space>
    </Card>
  )
}

export default ApiTest
