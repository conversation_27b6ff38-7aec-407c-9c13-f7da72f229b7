import { configureStore, combineReducers } from '@reduxjs/toolkit'
import { persistStore, persistReducer } from 'redux-persist'
import storage from 'redux-persist/lib/storage'

// 导入所有的slice
import authSlice from './slices/authSlice'
import appSlice from './slices/appSlice'
import recipeSlice from './slices/recipeSlice'
import batchSlice from './slices/batchSlice'
import analysisSlice from './slices/analysisSlice'

// 持久化配置
const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth'], // 只持久化auth状态
}

// 根reducer
const rootReducer = combineReducers({
  auth: authSlice,
  app: appSlice,
  recipe: recipeSlice,
  batch: batchSlice,
  analysis: analysisSlice,
})

// 持久化reducer
const persistedReducer = persistReducer(persistConfig, rootReducer)

// 配置store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
})

// 创建persistor
export const persistor = persistStore(store)

// 类型定义
export type RootState = ReturnType<typeof rootReducer>
export type PersistedRootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
