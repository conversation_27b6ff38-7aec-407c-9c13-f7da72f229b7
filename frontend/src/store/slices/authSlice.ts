import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { AuthState, User } from '../../types'
import { authAPI } from '../../services/api'

// 初始状态
const initialState: AuthState = {
  user: null,
  token: localStorage.getItem('token'),
  isAuthenticated: false,
  isLoading: false,
}

// 异步actions
export const login = createAsyncThunk(
  'auth/login',
  async (credentials: { identifier: string; password: string; cafeId?: string }, { rejectWithValue }) => {
    try {
      const response = await authAPI.login(credentials)
      return response.data
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '登录失败')
    }
  }
)

export const register = createAsyncThunk(
  'auth/register',
  async (userData: { name: string; email: string; password: string }, { rejectWithValue }) => {
    try {
      const response = await authAPI.register(userData)
      return response.data
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '注册失败')
    }
  }
)

export const checkAuthStatus = createAsyncThunk(
  'auth/checkStatus',
  async (_, { rejectWithValue }) => {
    try {
      // 从localStorage获取token而不是从state
      const token = localStorage.getItem('token')

      if (!token) {
        throw new Error('No token found')
      }

      const response = await authAPI.getCurrentUser()
      return response.data
    } catch (error: any) {
      // 如果验证失败，清理localStorage
      console.warn('认证验证失败:', error.response?.data?.message || error.message)
      localStorage.removeItem('token')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('user')
      return rejectWithValue(error.response?.data?.message || '验证失败')
    }
  }
)

export const updateProfile = createAsyncThunk(
  'auth/updateProfile',
  async (userData: Partial<User>, { rejectWithValue }) => {
    try {
      const response = await authAPI.updateProfile(userData)
      return response.data
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '更新失败')
    }
  }
)

// 创建slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    logout: (state) => {
      state.user = null
      state.token = null
      state.isAuthenticated = false
      state.isLoading = false
      // 清除本地存储的token
      localStorage.removeItem('token')
    },
    clearError: (state) => {
      state.isLoading = false
    },
    initializeAuth: (state) => {
      // 初始化认证状态，确保loading为false
      state.isLoading = false
      const token = localStorage.getItem('token')
      if (!token) {
        state.user = null
        state.token = null
        state.isAuthenticated = false
      }
    },
    setToken: (state, action: PayloadAction<string>) => {
      state.token = action.payload
      localStorage.setItem('token', action.payload)
    },
  },
  extraReducers: (builder) => {
    // 登录
    builder
      .addCase(login.pending, (state) => {
        state.isLoading = true
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false
        state.user = action.payload.data.user
        state.token = action.payload.data.token
        state.isAuthenticated = true
        localStorage.setItem('token', action.payload.data.token)
      })
      .addCase(login.rejected, (state) => {
        state.isLoading = false
        state.user = null
        state.token = null
        state.isAuthenticated = false
      })

    // 注册
    builder
      .addCase(register.pending, (state) => {
        state.isLoading = true
      })
      .addCase(register.fulfilled, (state, action) => {
        state.isLoading = false
        state.user = action.payload.data.user
        state.token = action.payload.data.token
        state.isAuthenticated = true
        localStorage.setItem('token', action.payload.data.token)
      })
      .addCase(register.rejected, (state) => {
        state.isLoading = false
        state.user = null
        state.token = null
        state.isAuthenticated = false
      })

    // 检查认证状态
    builder
      .addCase(checkAuthStatus.pending, (state) => {
        state.isLoading = true
      })
      .addCase(checkAuthStatus.fulfilled, (state, action) => {
        state.isLoading = false
        state.user = action.payload.data.user
        state.isAuthenticated = true
      })
      .addCase(checkAuthStatus.rejected, (state) => {
        state.isLoading = false
        state.user = null
        state.token = null
        state.isAuthenticated = false
        localStorage.removeItem('token')
      })

    // 更新个人资料
    builder
      .addCase(updateProfile.pending, (state) => {
        state.isLoading = true
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        state.isLoading = false
        state.user = action.payload.data.user
      })
      .addCase(updateProfile.rejected, (state) => {
        state.isLoading = false
      })
  },
})

export const { logout, clearError, setToken, initializeAuth } = authSlice.actions
export default authSlice.reducer
