import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@pages': path.resolve(__dirname, './src/pages'),
      '@services': path.resolve(__dirname, './src/services'),
      '@utils': path.resolve(__dirname, './src/utils'),
      '@types': path.resolve(__dirname, './src/types'),
      '@hooks': path.resolve(__dirname, './src/hooks'),
      '@store': path.resolve(__dirname, './src/store'),
      '@assets': path.resolve(__dirname, './src/assets'),
    },
  },
  server: {
    port: 6174,
    host: true,
    // 减少开发服务器日志输出
    hmr: {
      overlay: false, // 禁用错误覆盖层，使用浏览器控制台
    },
    proxy: {
      '/api': {
        target: process.env.VITE_BACKEND_URL || 'http://localhost:3010',
        changeOrigin: true,
        secure: false,
      },
      '/health': {
        target: process.env.VITE_BACKEND_URL || 'http://localhost:3010',
        changeOrigin: true,
        secure: false,
      },
      '/metrics': {
        target: process.env.VITE_BACKEND_URL || 'http://localhost:3010',
        changeOrigin: true,
        secure: false,
      },
      '/images': {
        target: process.env.VITE_BACKEND_URL || 'http://localhost:3010',
        changeOrigin: true,
        secure: false,
        bypass: function (req, res, options) {
          // 对于 SVG 分析图片，不代理到后端，直接从 public 文件夹提供服务
          if (req.url && req.url.includes('/images/svg/')) {
            return req.url; // 返回原始URL，让 Vite 处理
          }
        },
      },
      '/ai': {
        target: process.env.VITE_AI_SERVICE_URL || 'http://localhost:6800',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/ai/, ''),
      },
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd', '@ant-design/icons'],
          charts: ['echarts', 'echarts-for-react'],
          utils: ['lodash', 'dayjs', 'axios'],
        },
      },
    },
    // 优化预加载配置
    modulePreload: {
      polyfill: false, // 禁用模块预加载polyfill
    },
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
  },
})
