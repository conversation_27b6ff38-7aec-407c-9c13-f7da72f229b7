<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品表单测试</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/antd@5.12.8/dist/antd.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/antd@5.12.8/dist/reset.css">
    <style>
        body {
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState } = React;
        const { Modal, Form, Input, Select, Row, Col, Button, message } = antd;
        const { Option } = Select;

        function TestProductForm() {
            const [form] = Form.useForm();
            const [modalVisible, setModalVisible] = useState(false);
            const [editingProduct, setEditingProduct] = useState(null);

            // 预定义选项
            const productTypes = [
                '原味酸奶',
                '果味酸奶', 
                '希腊酸奶',
                '低脂酸奶',
                '有机酸奶',
                '特色酸奶'
            ];

            const commonIngredients = [
                '生牛乳', '乳酸菌', '白砂糖', '蓝莓果粒', '草莓果粒', 
                '芒果果粒', '椰子片', '燕麦', '蜂蜜', '香草精'
            ];

            const commonBenefits = [
                '补充益生菌', '促进消化', '增强免疫力', '抗氧化', 
                '护眼明目', '美容养颜', '补充蛋白质', '强化骨骼'
            ];

            const commonCertifications = [
                '有机认证', 'ISO9001', '绿色食品', 'HACCP', 
                'FDA认证', '无添加剂', '非转基因'
            ];

            const handleSave = (values) => {
                console.log('Form values:', values);
                message.success('表单提交成功！');
                setModalVisible(false);
                form.resetFields();
            };

            const handleCancel = () => {
                setModalVisible(false);
                form.resetFields();
            };

            const openModal = () => {
                setModalVisible(true);
            };

            return (
                <div className="test-container">
                    <h1>产品表单测试</h1>
                    <p>这个测试页面用于验证修复后的产品表单是否没有控制台警告。</p>
                    
                    <Button type="primary" onClick={openModal}>
                        打开产品表单
                    </Button>

                    <Modal
                        title="添加产品"
                        open={modalVisible}
                        onCancel={handleCancel}
                        onOk={() => form.submit()}
                        width={800}
                        okText="保存"
                        cancelText="取消"
                    >
                        <Form
                            key={`product-form-${editingProduct?.id || 'new'}`}
                            form={form}
                            layout="vertical"
                            onFinish={handleSave}
                        >
                            <Row gutter={16}>
                                <Col span={12}>
                                    <Form.Item
                                        name="name"
                                        label="产品名称"
                                        rules={[
                                            { required: true, message: '请输入产品名称' },
                                            { min: 2, max: 100, message: '产品名称长度为2-100字符' }
                                        ]}
                                    >
                                        <Input placeholder="请输入产品名称" />
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item
                                        name="type"
                                        label="产品类型"
                                        rules={[{ required: true, message: '请选择产品类型' }]}
                                    >
                                        <Select placeholder="请选择产品类型">
                                            {productTypes.map((type, index) => (
                                                <Option key={`product-type-${index}-${type}`} value={type}>{type}</Option>
                                            ))}
                                        </Select>
                                    </Form.Item>
                                </Col>
                            </Row>

                            <Row gutter={16}>
                                <Col span={12}>
                                    <Form.Item
                                        name="ingredients"
                                        label="配料表"
                                    >
                                        <Select
                                            key={`ingredients-${editingProduct?.id || 'new'}-${Date.now()}`}
                                            mode="tags"
                                            placeholder="请输入配料，按回车添加"
                                            style={{ width: '100%' }}
                                            tokenSeparators={[',']}
                                            optionFilterProp="children"
                                            options={commonIngredients.map((ingredient, index) => ({
                                                key: `ingredient-${index}-${ingredient}`,
                                                value: ingredient,
                                                label: ingredient
                                            }))}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item
                                        name="benefits"
                                        label="主要功效"
                                    >
                                        <Select
                                            key={`benefits-${editingProduct?.id || 'new'}-${Date.now()}`}
                                            mode="tags"
                                            placeholder="请输入功效，按回车添加"
                                            style={{ width: '100%' }}
                                            tokenSeparators={[',']}
                                            optionFilterProp="children"
                                            options={commonBenefits.map((benefit, index) => ({
                                                key: `benefit-${index}-${benefit}`,
                                                value: benefit,
                                                label: benefit
                                            }))}
                                        />
                                    </Form.Item>
                                </Col>
                            </Row>

                            <Row gutter={16}>
                                <Col span={12}>
                                    <Form.Item
                                        name="certifications"
                                        label="质量认证"
                                    >
                                        <Select
                                            key={`certifications-${editingProduct?.id || 'new'}-${Date.now()}`}
                                            mode="tags"
                                            placeholder="请输入认证，按回车添加"
                                            style={{ width: '100%' }}
                                            tokenSeparators={[',']}
                                            optionFilterProp="children"
                                            options={commonCertifications.map((cert, index) => ({
                                                key: `cert-${index}-${cert}`,
                                                value: cert,
                                                label: cert
                                            }))}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item
                                        name="status"
                                        label="产品状态"
                                        rules={[{ required: true, message: '请选择产品状态' }]}
                                    >
                                        <Select placeholder="请选择状态">
                                            <Option key="status-active" value="active">在售</Option>
                                            <Option key="status-inactive" value="inactive">停售</Option>
                                        </Select>
                                    </Form.Item>
                                </Col>
                            </Row>
                        </Form>
                    </Modal>
                </div>
            );
        }

        ReactDOM.render(<TestProductForm />, document.getElementById('root'));
    </script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</body>
</html>
