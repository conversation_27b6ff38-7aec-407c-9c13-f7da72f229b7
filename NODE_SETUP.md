# Node.js 开发环境设置指南

## 🚨 问题诊断

如果遇到 `zsh: command not found: node` 错误，这通常是因为：

1. **NVM 未正确加载到 PATH**
2. **Shell 配置文件未正确设置**
3. **Node.js 版本未激活**

## 🔧 快速解决方案

### 方法一：使用项目脚本（推荐）

```bash
# 在项目根目录运行
source setup_node_env.sh
```

### 方法二：手动设置

```bash
# 1. 加载 NVM
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

# 2. 使用项目指定的 Node.js 版本
nvm use  # 会自动读取 .nvmrc 文件

# 3. 验证
node --version
npm --version
claude --version
```

### 方法三：重新加载 Shell 配置

```bash
source ~/.zshrc
```

## 📁 项目文件说明

- **`.nvmrc`**: 指定项目使用的 Node.js 版本 (v22.17.1)
- **`setup_node_env.sh`**: 环境设置脚本
- **`.zshrc`**: Shell 配置文件（已优化）

## 🎯 验证环境

运行以下命令确认环境正常：

```bash
# 检查 Node.js
node --version  # 应该显示 v22.17.1

# 检查 npm
npm --version   # 应该显示 11.4.2

# 检查 Claude CLI
claude --version # 应该显示 1.0.55 (Claude Code)

# 检查 NVM
nvm list        # 显示已安装的 Node.js 版本
```

## 🔄 自动化设置

为了确保每次打开终端都能正确加载 Node.js 环境，`.zshrc` 文件已经配置为：

1. 自动加载 NVM
2. 自动使用默认 Node.js 版本
3. 支持项目级别的版本管理

## 🚀 开发工作流

1. **进入项目目录**：
   ```bash
   cd /Volumes/acasis/yoghurt
   ```

2. **确保环境正确**：
   ```bash
   source setup_node_env.sh
   ```

3. **开始开发**：
   ```bash
   claude init  # 或其他 Claude CLI 命令
   ```

## 🛠️ 故障排除

### 问题：NVM 命令不存在

```bash
# 检查 NVM 安装
ls -la ~/.nvm

# 如果不存在，重新安装 NVM
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
```

### 问题：Node.js 版本不匹配

```bash
# 安装项目指定版本
nvm install 22.17.1
nvm use 22.17.1
nvm alias default 22.17.1
```

### 问题：Claude CLI 不可用

```bash
# 检查是否已安装
which claude

# 如果未安装，Claude CLI 通常通过其他方式安装
# 请参考 Claude Code 官方文档
```

## 📝 注意事项

1. **项目特定配置**：此项目使用 Node.js v22.17.1 LTS 版本
2. **环境隔离**：使用 NVM 管理多个 Node.js 版本
3. **自动切换**：进入项目目录时自动使用正确版本
4. **兼容性**：配置与 Conda 环境兼容

## 🎉 成功指标

当看到以下输出时，说明环境设置成功：

```
📊 环境信息:
   Node.js版本: v22.17.1
   npm版本: 11.4.2
   当前路径: /Volumes/acasis/yoghurt
   Claude CLI: 1.0.55 (Claude Code)

🎉 开发环境设置完成！
```