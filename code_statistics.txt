github.com/AlDanial/cloc v 2.06  T=1.40 s (219.5 files/s, 60069.7 lines/s)
----------------------------------------------------------------------------------------------------------------------
File                                                                               blank        comment           code
----------------------------------------------------------------------------------------------------------------------
./package-lock.json                                                                    0              0          15256
./docs/tech/Security_Architecture.md                                                 550              0           3729
./docs/tech/Technology_Stack_Recommendations.md                                      475              0           2758
./.cspell.json                                                                         0              0           2357
./docs/tech/Infrastructure_as_Code.md                                                310              0           2268
./docs/tech/Quality_Assurance_Strategy.md                                            295              3           1221
./docs/plan/Requirements_Optimization.md                                             164              0            895
./docs/plan/PRD.md                                                                   213              0            883
./frontend/src/pages/Reports.tsx                                                      46             13            796
./docs/DEVELOPMENT_GUIDE.md                                                          139              0            789
./docs/plan/Metrics_Framework.md                                                     223              0            654
./frontend/src/pages/BatchDetail.tsx                                                  35             34            620
./frontend/src/pages/EditBatch.tsx                                                    44            116            517
./docs/plan/Roadmap.md                                                               103              0            494
./frontend/src/pages/EditRecipe.tsx                                                   45            176            489
./frontend/src/pages/QualityResults.tsx                                               20              1            488
./frontend/src/pages/ProductManagement.tsx                                            24              9            470
./frontend/src/pages/Products.tsx                                                     26              2            469
./docs/plan/User_Story_Map.md                                                        108              0            447
./frontend/dist/assets/antd-V_nl-lI7.js                                               16             17            445
./frontend/src/pages/Batches.tsx                                                      23            143            400
./frontend/src/pages/Recipes.tsx                                                      18              6            384
./frontend/src/pages/CreateRecipe.tsx                                                 28             71            379
./frontend/src/pages/RecipeDetail.tsx                                                 25              6            375
./frontend/src/pages/Analysis.tsx                                                     18             42            365
./frontend/src/pages/UserManagement.tsx                                               23              6            357
./docs/plan/Documentation_Enhancement_Plan.md                                         84              0            353
./frontend/src/pages/Recipes.css                                                      72             19            328
./README.md                                                                          108              0            322
./backend/dist/routes/batches.js                                                       0            172            317
./frontend/src/components/NotificationDropdown.tsx                                    26             10            313
./backend/src/routes/batches.ts                                                       54            171            312
./docs/DATABASE_STATUS_REPORT.md                                                      87              0            311
./scripts/start-all-services.sh                                                       75             44            307
./backend/src/routes/products.ts                                                      57            171            298
./frontend/src/pages/Dashboard.css                                                    63             14            294
./backend/dist/scripts/seed.js                                                         0             28            282
./frontend/src/pages/Login.tsx                                                        30              3            277
./frontend/src/store/slices/analysisSlice.ts                                          25             15            276
./frontend/src/pages/CreateBatch.tsx                                                  21              5            275
./frontend/src/store/slices/batchSlice.ts                                             18             10            274
./frontend/src/utils/index.ts                                                         47             31            273
./backend/dist/types/database.d.ts                                                     0              1            272
./backend/src/types/database.ts                                                       40             29            272
./ai-service/src/main.py                                                              75             45            270
./ai-service/README.md                                                                81              0            266
./backend/schema.prisma                                                               48             48            266
./STARTUP_GUIDE.md                                                                    94              0            263
./backend/src/routes/notifications.ts                                                 35              8            259
./backend/src/scripts/seed.ts                                                         49             27            258
./docs/database-schema.md                                                             34              0            258
./backend/dist/services/UserService.js                                                 0             26            255
./backend/dist/routes/recipes.js                                                       0            163            254
./frontend/src/components/Layout/index.tsx                                            16              6            252
./frontend/src/index.css                                                              53             21            250
./backend/src/routes/recipes.ts                                                       43            162            249
./backend/DATABASE_DESIGN.md                                                          82              0            246
./frontend/src/components/RoleBasedDashboard.tsx                                      20              4            246
./frontend/src/pages/Login.css                                                        52             17            246
./backend/src/services/UserService.ts                                                 41             25            241
./FRONTEND_BACKEND_STARTUP.md                                                         71              0            239
./CONDA_ENVIRONMENT_GUIDE.md                                                          74              0            236
./frontend/src/services/api/report.ts                                                 21             22            233
./frontend/src/store/slices/recipeSlice.ts                                            16              9            229
./scripts/manage-conda-env.sh                                                         46             24            229
./backend/dist/services/BaseService.js                                                 0             21            228
./scripts/setup-database.sh                                                           35             12            226
./docs/DIAGRAM_SOLUTIONS.md                                                           43              0            225
./frontend/src/components/AdminDashboard.tsx                                          13              4            225
./backend/src/services/BaseService.ts                                                 40             21            221
./frontend/src/components/Layout/Layout.css                                           45             15            221
./scripts/generate-high-res-diagrams.js                                               47             23            218
./backend/dist/routes/upload.js                                                        0            124            214
./frontend/src/types/index.ts                                                         29             18            214
./backend/src/routes/upload.ts                                                        36            123            213
./需求.md                                                                            7              0            207
./frontend/src/services/api/user.ts                                                   26             26            206
./scripts/create-latest-env.sh                                                        46             29            203
./backend/dist/scripts/init-database.js                                                0             22            201
./scripts/install-direnv.sh                                                           31             24            200
./DIRENV_SETUP_GUIDE.md                                                               60              0            199
./CONDA_OPTIMIZATION_ANALYSIS.md                                                      48              0            196
./PROJECT_STATUS_2025-07-23.md                                                        42              0            195
./frontend/src/components/UserRoleBadge.tsx                                           24             18            192
./backend/dist/scripts/migrate.js                                                      0             29            191
./backend/src/scripts/init-database.ts                                                42             21            190
./infrastructure/docker/postgres/init.sql                                             28             28            189
./backend/src/routes/auth.ts                                                          48            184            187
./frontend/src/services/api/analysis.ts                                               28             26            187
./LATEST_VERSION_COMPARISON.md                                                        49              0            186
./frontend/src/components/Charts/BatchProcessingChart.tsx                              7              3            186
./scripts/security-check.sh                                                           46             22            186
./backend/prisma/seed.sql                                                             11              9            185
./scripts/quick-start.sh                                                              50             26            184
./test-frontend-auth.html                                                             21              0            184
./docs/DOCKER_SECURITY_GUIDE.md                                                       61              0            183
./frontend/src/components/Charts/SystemUsageChart.tsx                                 12              3            183
./scripts/start-services-with-logs.sh                                                 43             30            183
./RBAC_REFACTOR_SUMMARY.md                                                            82              0            180
./scripts/setup-secure-env.sh                                                         66             89            179
./backend/dist/index.js                                                                0             23            178
./frontend/src/tests/permissions-test.js                                              32             26            177
./frontend/src/types/permissions.ts                                                   19             25            177
./multi-tenant-architecture.md                                                        45              0            177
./scripts/check-project-status.sh                                                     32             24            177
./backend/src/index.ts                                                                32             22            176
./backend/src/scripts/migrate.ts                                                      40             28            173
./frontend/src/components/Charts/UserActivityChart.tsx                                 7              3            169
./QUICK_REFERENCE.md                                                                  40              0            165
./backend/dist/config/index.js                                                         0             24            165
./scripts/check-status.sh                                                             30             22            164
./frontend/public/images/svg/yogurt-fermentation-process.svg                          37             26            162
./SECURITY_GUIDE.md                                                                   47              0            161
./MACOS_SETUP.md                                                                      59              0            160
./frontend/src/pages/Profile.tsx                                                      17              0            159
./frontend/src/services/api/batch.ts                                                  22             23            159
./backend/src/config/index.ts                                                         26             23            156
./scripts/setup-password-file.sh                                                      58             88            156
./frontend/src/components/PermissionGuard.tsx                                         22             19            155
./frontend/src/store/slices/authSlice.ts                                              12             11            155
./frontend/src/utils/performanceConfig.ts                                             25             61            155
./DIRENV_FINAL_SETUP.md                                                               58              0            152
./QUICK_SETUP.md                                                                      45              0            146
./GETTING_STARTED.md                                                                  56              0            145
./frontend/src/services/dashboardService.ts                                           28             29            141
./RBAC_TESTING_GUIDE.md                                                               46              0            140
./scripts/manage-volumes.sh                                                           27             27            139
./DATABASE_PERSISTENCE_GUIDE.md                                                       45              0            138
./frontend/PERFORMANCE_OPTIMIZATION.md                                                34              0            136
./ai-service/requirements.txt                                                         34              0            134
./scripts/setup-environment.sh                                                        28             20            134
./SECURITY_FIX_SUMMARY.md                                                             40              0            133
./scripts/test-auto-activation.sh                                                     34             15            132
./backend/dist/lib/prisma.js                                                           0             21            131
./scripts/fix-macos-direnv.sh                                                         33             24            131
./frontend/src/utils/fontOptimization.ts                                              30             45            130
./scripts/stop-all-services.sh                                                        35             25            130
./frontend/public/images/svg/streptococcus-bacteria.svg                               13             13            128
./scripts/setup-direnv-macos.sh                                                       32             28            128
./CLAUDE.md                                                                           31              0            127
./RBAC_TEST_RESULTS.md                                                                56              0            127
./backend/dist/config/redis.js                                                         0             11            127
./backend/src/lib/prisma.ts                                                           20             21            124
./docs/PORT_CONFIGURATION.md                                                          40              0            122
./frontend/src/pages/Settings.tsx                                                     15              0            122
./backend/src/middleware/errorHandler.ts                                              17             11            121
./frontend/src/components/Charts/RoleDistributionChart.tsx                             7              3            121
./backend/dist/middleware/errorHandler.js                                              0             12            120
./backend/package.json                                                                 0              0            119
./docs/DATABASE_SECURITY_GUIDE.md                                                     43              0            119
./scripts/test-persistence.sh                                                         34             20            119
./frontend/src/services/api/recipe.ts                                                 21             21            117
./backend/dist/routes/auth.js                                                          0            107            116
./backend/src/middleware/auth.ts                                                      24             15            115
./frontend/dist/assets/vendor-DXnDn7IJ.js                                             44             23            115
./frontend/src/utils/resourcePreloader.ts                                             23             55            114
./backend/src/config/redis.ts                                                         24             10            113
./frontend/src/utils/simplePreloader.ts                                               25             37            111
./PROJECT_STATUS.md                                                                   30              0            108
./frontend/src/main.tsx                                                               13             11            108
./frontend/public/images/svg/yogurt-bacteria-mixed.svg                                25             11            106
./install-security-hooks.sh                                                           18             19            106
./scripts/check-services.sh                                                           19             14            106
./scripts/cleanup-ports.sh                                                            24             12            106
./scripts/restore-database.sh                                                         24             24            106
./test-consumer-permissions.md                                                        20              0            106
./frontend/src/hooks/usePermissions.ts                                                30             32            104
./docker-compose.yml                                                                   6              5            103
./scripts/fix-direnv-path.sh                                                          22             15            103
./backend/dist/middleware/auth.js                                                      0             14            102
./backend/src/scripts/seed-notifications.ts                                           11              5            101
./scripts/start-services-stable.sh                                                    23             17            101
./NODE_SETUP.md                                                                       42              0             99
./ai-service/src/config.py                                                            35             32             99
./scripts/start-with-env.sh                                                           18             14             97
./activate.sh                                                                         17             28             96
./frontend/FIXES.md                                                                   29              0             95
./.githooks/pre-commit                                                                16             13             94
./ai-service/requirements-slim.txt                                                    15              0             94
./backend/dist/config/index.d.ts                                                       0              1             92
./frontend/src/services/api/index.ts                                                  13             14             90
./frontend/src/router/index.tsx                                                       20             11             89
./backend/dist/config/database.js                                                      0              5             87
./ENVIRONMENT_SETUP.md                                                                30              0             84
./scripts/generate-diagrams.sh                                                        15              9             83
./ENVIRONMENT_ACTIVATION_GUIDE.md                                                     31              0             82
./activate-env.sh                                                                     16             22             80
./backend/src/config/database.ts                                                      14              4             78
./backend/src/utils/logger.ts                                                         12             13             78
./frontend/src/components/Footer/Footer.css                                           15              3             77
./scripts/backup-database.sh                                                          23             22             76
./frontend/package.json                                                                0              0             75
./backend/dist/utils/logger.js                                                         0             14             74
./docs/DATABASE_QUICK_REFERENCE.md                                                    24              0             74
./frontend/src/hooks/usePassiveScroll.ts                                              16             27             74
./setup-direnv-complete.sh                                                            22             16             74
./docs/HOW_TO_SAVE_DIAGRAMS.md                                                        27              0             73
./frontend/src/services/api/auth.ts                                                   19             15             72
./scripts/docker-health-check.sh                                                      16             16             72
./docs/images/README_MICROSCOPE_IMAGES.md                                             20              0             71
./frontend/vite.config.ts                                                              1              3             71
./frontend/src/services/notificationAPI.ts                                            11              6             69
./SECURITY_FIXES_README.md                                                            22              0             67
./frontend/src/utils/eventUtils.ts                                                    10             40             67
./frontend/public/images/svg/bifidobacterium-bacteria.svg                             15             15             64
./frontend/src/components/ApiTest.tsx                                                  9              5             63
./frontend/src/store/slices/appSlice.ts                                                6              5             62
./scripts/stop-services.sh                                                            14             11             62
./environment.yml                                                                     13             30             60
./package.json                                                                         0              0             60
./scripts/restart-all-services.sh                                                     19             15             56
./frontend/dist/assets/index-Bp0F6nIS.js                                               9             32             54
./backend/dist/routes/health.js                                                        0             65             52
./frontend/src/components/LoadingSpinner.tsx                                           5              0             52
./frontend/src/components/ErrorFallback.tsx                                            3              0             50
./backend/src/routes/health.ts                                                        11             64             49
./backend/dist/services/BaseService.d.ts                                               0              1             47
./backend/tsconfig.json                                                                0              0             47
./scripts/test-server.js                                                               9              3             47
./environment-latest.yml                                                              18             40             45
./frontend/public/images/svg/lactobacillus-bacteria.svg                               15             16             45
./frontend/src/utils/authUtils.ts                                                      6             16             45
./environment-slim.yml                                                                18             39             44
./frontend/dist/assets/charts-CWvcpI1j.js                                             14             31             44
./frontend/src/App.tsx                                                                 9              4             39
./backend/dist/middleware/auth.d.ts                                                    0              1             38
./frontend/src/components/MessageTest.tsx                                              7              3             38
./frontend/src/components/ProtectedRoute.tsx                                           8              4             38
./frontend/src/utils/antdConfig.ts                                                     3              9             38
./frontend/tsconfig.json                                                               3              0             36
./quick-activate.sh                                                                    9             14             36
./frontend/src/store/index.ts                                                          7              7             35
./backend/dist/types/database.js                                                       0              8             34
./frontend/src/utils/messageHandler.ts                                                 3              4             34
./backend/dist/lib/prisma.d.ts                                                         0              1             33
./backend/dist/services/UserService.d.ts                                               0              1             33
./setup_node_env.sh                                                                    8              8             32
./docs/diagrams/mermaid-config.json                                                    0              0             30
./test-direnv.sh                                                                      11              5             28
./backend/dist/config/redis.d.ts                                                       0              1             26
./frontend/src/pages/Dashboard.tsx                                                     6              2             26
./frontend/src/pages/NotFound.tsx                                                      3              0             26
./docs/temp.md                                                                         9              0             25
./frontend/src/components/Footer/index.tsx                                             4              3             23
./clear-localStorage.js                                                                5              4             22
./frontend/tsconfig.node.json                                                          0              0             22
./backend/dist/middleware/errorHandler.d.ts                                            0              1             21
./frontend/dist/index.html                                                             0              2             20
./frontend/index.html                                                                  0              2             16
./backend/dist/routes/analysis.js                                                      0             13             15
./backend/dist/routes/reports.js                                                       0             13             15
./backend/dist/routes/users.js                                                         0             13             15
./backend/logs/.51b9937067a953ea27fca44cbc08a725b4d30f80-audit.json                    0              0             15
./backend/logs/.881ee3f3de8c41c996523f5239bf92bac83ae38f-audit.json                    0              0             15
./backend/Dockerfile.dev                                                              10             11             14
./backend/src/routes/analysis.ts                                                       3             12             13
./backend/src/routes/reports.ts                                                        3             12             13
./backend/src/routes/users.ts                                                          3             12             13
./backend/dist/config/database.d.ts                                                    0              1             12
./frontend/dist/assets/Dashboard-Dzirec6j.js                                           0              1             12
./backend/start-studio.sh                                                              7              7             11
./scripts/test-env.sh                                                                  5              3             11
./backend/dist/middleware/notFoundHandler.js                                           0              1             10
./frontend/src/types/vite-env.d.ts                                                     2              2             10
./.claude/settings.local.json                                                          0              0              8
./scripts/load-env.sh                                                                  4              7              8
./backend/dist/scripts/init-database.d.ts                                              0              1              7
./backend/src/middleware/notFoundHandler.ts                                            2              0              7
./backend/dist/scripts/seed.d.ts                                                       0              1              6
./backend/dist/utils/logger.d.ts                                                       0              1              6
./frontend/dist/assets/utils-Dq7h7Pqt.js                                               0              1              6
./frontend/src/hooks/useMessage.ts                                                     2              4              6
./backend/dist/scripts/migrate.d.ts                                                    0              1              5
./frontend/src/components/Charts/index.ts                                              1              3              4
./frontend/src/store/hooks.ts                                                          1              1              4
./.vscode/settings.json                                                                0              0              3
./backend/dist/middleware/notFoundHandler.d.ts                                         0              1              3
./frontend/src/hooks/useTypedSelector.ts                                               1              4              3
./backend/dist/routes/analysis.d.ts                                                    0              1              2
./backend/dist/routes/auth.d.ts                                                        0              1              2
./backend/dist/routes/batches.d.ts                                                     0              1              2
./backend/dist/routes/health.d.ts                                                      0              1              2
./backend/dist/routes/recipes.d.ts                                                     0              1              2
./backend/dist/routes/reports.d.ts                                                     0              1              2
./backend/dist/routes/upload.d.ts                                                      0              1              2
./backend/dist/routes/users.d.ts                                                       0              1              2
./backend/dist/index.d.ts                                                              0              1              1
./docs/images/data-flow-diagram.svg                                                    0              0              1
./docs/images/database-er-diagram.svg                                                  0              0              1
./frontend/dist/assets/Analysis-CXEmtlGV.js                                            0              1              1
./frontend/dist/assets/BatchDetail-KBC9PITo.js                                         0              1              1
./frontend/dist/assets/Batches-B3zg2Vig.js                                             0              1              1
./frontend/dist/assets/Dashboard-rkuKFH53.css                                          0              0              1
./frontend/dist/assets/Login-TzQ3fIC_.css                                              0              0              1
./frontend/dist/assets/Login-uorf1Pth.js                                               0              1              1
./frontend/dist/assets/NotFound-Cniyv_t6.js                                            0              1              1
./frontend/dist/assets/PermissionGuard-CL7H9w1K.js                                     0              1              1
./frontend/dist/assets/Profile-BC50ofi7.js                                             0              1              1
./frontend/dist/assets/RecipeDetail-B2v4tBlc.js                                        0              1              1
./frontend/dist/assets/Recipes-DKlqdy0c.css                                            0              0              1
./frontend/dist/assets/Recipes-GZMreiEZ.js                                             0              1              1
./frontend/dist/assets/Reports-Ch7rwjNq.js                                             0              1              1
./frontend/dist/assets/Settings-CygVKAVn.js                                            0              1              1
./frontend/dist/assets/UserManagement-C05KZudC.js                                      0              1              1
./frontend/dist/assets/index-KI4MjNdY.css                                              0              0              1
./frontend/dist/assets/resourcePreloader-DQ6UQbND.js                                   0              1              1
./frontend/public/vite.svg                                                             0              0              1
----------------------------------------------------------------------------------------------------------------------
SUM:                                                                                8676           5093          70239
----------------------------------------------------------------------------------------------------------------------
