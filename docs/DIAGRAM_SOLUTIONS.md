# 数据库架构图高清保存解决方案

## 问题描述
- 无法右键保存渲染的图表
- 命令行工具生成的图片分辨率不够清晰

## 解决方案

### 🎯 方案一：高分辨率命令行生成（推荐）

我已经创建了专门的高分辨率图片生成脚本：

```bash
# 生成高分辨率图片（3200x2400，3倍缩放）
npm run docs:diagrams:hd

# 清理旧图片
npm run docs:diagrams:clean
```

**生成的图片规格：**
- 数据库ER图：3200x2400px，3倍缩放
- 数据流程图：1600x1200px，2倍缩放
- 架构概览图：2400x1600px，2倍缩放
- 同时生成PNG和SVG两种格式

### 🌐 方案二：在线工具生成

#### 选项A：Mermaid Live Editor（推荐）
1. 访问：https://mermaid.live
2. 复制以下代码到编辑器：

```mermaid
erDiagram
    User ||--o{ Recipe : creates
    User ||--o{ Batch : manages
    User ||--o{ SensoryAssessment : evaluates
    User ||--o{ MicroscopeImage : uploads
    User ||--o{ QualityReport : generates
    User ||--o{ SystemLog : performs
    
    Recipe ||--o{ Batch : uses
    Batch ||--o{ SensoryAssessment : has
    Batch ||--o{ MicroscopeImage : contains
    Batch ||--o{ AIAnalysis : analyzes
    Batch ||--o{ QualityReport : produces
    
    MicroscopeImage ||--o{ AIAnalysis : generates
    
    User {
        uuid id PK
        string email UK
        string password_hash
        string name
        enum role
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    Recipe {
        uuid id PK
        uuid user_id FK
        string name
        text description
        jsonb ingredients
        jsonb process
        decimal fermentation_temperature
        integer fermentation_duration
        integer filtration_duration
        integer version
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    Batch {
        uuid id PK
        string batch_number UK
        uuid recipe_id FK
        uuid user_id FK
        enum status
        date production_date
        decimal quantity
        integer actual_fermentation_duration
        decimal actual_temperature
        text notes
        timestamp created_at
        timestamp updated_at
    }
    
    SensoryAssessment {
        uuid id PK
        uuid batch_id FK
        uuid assessor_id FK
        smallint texture_score
        smallint acidity_score
        jsonb flavor_tags
        text flavor_notes
        smallint overall_score
        text notes
        timestamp created_at
        timestamp updated_at
    }
    
    MicroscopeImage {
        uuid id PK
        uuid batch_id FK
        string filename
        string original_name
        string file_path
        integer file_size
        string mime_type
        integer magnification
        string staining_method
        integer image_width
        integer image_height
        jsonb metadata
        uuid uploaded_by FK
        timestamp created_at
        timestamp updated_at
    }
    
    AIAnalysis {
        uuid id PK
        uuid batch_id FK
        uuid microscope_image_id FK
        enum analysis_status
        decimal quality_score
        integer bacterial_count
        boolean contamination_detected
        string contamination_type
        jsonb morphology_analysis
        decimal confidence_score
        text summary_recommendation
        integer processing_time_ms
        string model_version
        string api_provider
        jsonb raw_response
        text error_message
        integer retry_count
        timestamp created_at
        timestamp updated_at
    }
    
    QualityReport {
        uuid id PK
        uuid batch_id FK
        enum report_type
        string title
        text description
        jsonb date_range
        jsonb filters
        jsonb report_data
        uuid generated_by FK
        boolean is_public
        timestamp created_at
        timestamp updated_at
    }
    
    SystemLog {
        uuid id PK
        uuid user_id FK
        string action
        string resource_type
        uuid resource_id
        jsonb details
        inet ip_address
        text user_agent
        timestamp created_at
    }
```

3. 点击 "Actions" → "Download PNG" 或 "Download SVG"
4. 选择高分辨率选项

#### 选项B：其他在线工具
- **Draw.io**: https://app.diagrams.net/ (支持Mermaid导入)
- **Kroki**: https://kroki.io/ (API服务)
- **Mermaid Chart**: https://www.mermaidchart.com/

### 🖥️ 方案三：本地工具

#### VS Code插件方法
1. 安装插件：
   - "Mermaid Preview"
   - "Markdown Preview Mermaid Support"

2. 打开 `docs/diagrams/database-er.mmd`
3. 使用预览功能
4. 截图或导出

#### 专业工具
- **Typora**: Markdown编辑器，支持Mermaid渲染和导出
- **Mark Text**: 免费Markdown编辑器
- **Obsidian**: 知识管理工具，支持Mermaid

### 📱 方案四：截图工具（临时方案）

如果其他方案都不可行，可以使用高质量截图：

1. **Windows**: 使用 Snipping Tool 或 Snagit
2. **macOS**: 使用 Command+Shift+4 或 CleanShot X
3. **Linux**: 使用 Flameshot 或 Shutter

**截图技巧：**
- 放大浏览器到200%再截图
- 使用4K显示器截图
- 截图后用图片编辑软件提高清晰度

### 🔧 故障排除

#### 命令行工具问题
```bash
# 如果mmdc命令不存在
npm install -g @mermaid-js/mermaid-cli

# 如果权限问题
sudo npm install -g @mermaid-js/mermaid-cli

# 使用npx（无需全局安装）
npx @mermaid-js/mermaid-cli -i input.mmd -o output.png --width 3200 --height 2400 --scale 3
```

#### 图片质量问题
```bash
# 生成超高清图片
mmdc -i docs/diagrams/database-er.mmd -o ultra-hd-diagram.png \
  --width 4800 \
  --height 3600 \
  --scale 4 \
  --quality 100 \
  --backgroundColor white
```

#### 文件大小问题
- PNG格式：高质量但文件大
- SVG格式：矢量图，可无限缩放
- 压缩工具：TinyPNG, ImageOptim

### 📋 推荐流程

1. **首选**：运行 `npm run docs:diagrams:hd`
2. **备选**：使用 Mermaid Live Editor
3. **最后**：使用截图工具

### 🎨 自定义样式

如果需要调整图表样式，编辑 `docs/diagrams/mermaid-config.json`：

```json
{
  "theme": "default",
  "themeVariables": {
    "primaryColor": "#4f46e5",
    "primaryTextColor": "#1f2937",
    "fontSize": "16px"
  }
}
```

### 📞 技术支持

如果仍有问题，请检查：
1. Node.js版本 >= 14
2. 网络连接正常
3. 磁盘空间充足
4. 文件权限正确
