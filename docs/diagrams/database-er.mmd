erDiagram
    User ||--o{ Recipe : creates
    User ||--o{ Batch : manages
    User ||--o{ SensoryAssessment : evaluates
    User ||--o{ MicroscopeImage : uploads
    User ||--o{ QualityReport : generates
    User ||--o{ SystemLog : performs
    
    Recipe ||--o{ Batch : uses
    Batch ||--o{ SensoryAssessment : has
    Batch ||--o{ MicroscopeImage : contains
    Batch ||--o{ AIAnalysis : analyzes
    Batch ||--o{ QualityReport : produces
    
    MicroscopeImage ||--o{ AIAnalysis : generates
    
    User {
        uuid id PK
        string email UK
        string password_hash
        string name
        enum role
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    Recipe {
        uuid id PK
        uuid user_id FK
        string name
        text description
        jsonb ingredients
        jsonb process
        decimal fermentation_temperature
        integer fermentation_duration
        integer filtration_duration
        integer version
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    Batch {
        uuid id PK
        string batch_number UK
        uuid recipe_id FK
        uuid user_id FK
        enum status
        date production_date
        decimal quantity
        integer actual_fermentation_duration
        decimal actual_temperature
        text notes
        timestamp created_at
        timestamp updated_at
    }
    
    SensoryAssessment {
        uuid id PK
        uuid batch_id FK
        uuid assessor_id FK
        smallint texture_score
        smallint acidity_score
        jsonb flavor_tags
        text flavor_notes
        smallint overall_score
        text notes
        timestamp created_at
        timestamp updated_at
    }
    
    MicroscopeImage {
        uuid id PK
        uuid batch_id FK
        string filename
        string original_name
        string file_path
        integer file_size
        string mime_type
        integer magnification
        string staining_method
        integer image_width
        integer image_height
        jsonb metadata
        uuid uploaded_by FK
        timestamp created_at
        timestamp updated_at
    }
    
    AIAnalysis {
        uuid id PK
        uuid batch_id FK
        uuid microscope_image_id FK
        enum analysis_status
        decimal quality_score
        integer bacterial_count
        boolean contamination_detected
        string contamination_type
        jsonb morphology_analysis
        decimal confidence_score
        text summary_recommendation
        integer processing_time_ms
        string model_version
        string api_provider
        jsonb raw_response
        text error_message
        integer retry_count
        timestamp created_at
        timestamp updated_at
    }
    
    QualityReport {
        uuid id PK
        uuid batch_id FK
        enum report_type
        string title
        text description
        jsonb date_range
        jsonb filters
        jsonb report_data
        uuid generated_by FK
        boolean is_public
        timestamp created_at
        timestamp updated_at
    }
    
    SystemLog {
        uuid id PK
        uuid user_id FK
        string action
        string resource_type
        uuid resource_id
        jsonb details
        inet ip_address
        text user_agent
        timestamp created_at
    }
