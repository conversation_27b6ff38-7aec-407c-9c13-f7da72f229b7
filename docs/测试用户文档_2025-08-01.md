# 酸奶AI质控系统 - 测试用户文档

**文档日期**: 2025年8月1日  
**系统版本**: v1.0.0  
**文档类型**: 测试用户说明

---

## 📋 概述

本文档提供了酸奶AI质控系统中所有测试用户的详细信息，包括不同店面和不同角色的用户账号，方便开发和测试人员进行系统功能验证。

## 🏪 店面信息

系统当前配置了以下三个店面：

| 店面名称 | 描述 | 状态 |
|---------|-----|------|
| 海口电视台店 | 顺口餐饮 | 活跃 |
| 三亚店 | 顺口餐饮 | 活跃 |
| 琼海店 | 顺口餐饮 | 活跃 |

## 👥 用户角色说明

系统定义了三种用户角色：

| 角色 | 权限级别 | 描述 | 店面访问权限 |
|-----|---------|-----|-------------|
| **ADMIN** | 总管理员 | 拥有系统的所有权限，可以管理用户、店面、配置等 | 可以不选择店面登录，访问所有店面数据 |
| **USER** | 店面用户 | 可以进行质检操作、查看报告、管理批次等 | 必须选择店面登录，只能访问自己店面数据 |
| **VIEWER** | 观察员 | 只读权限，可以查看数据但不能修改 | 必须选择店面登录，只能查看自己店面数据 |

## 🔐 测试用户列表

### 1. 系统管理员账号

| 邮箱 | 用户名 | 姓名 | 角色 | 店面 | 创建时间 |
|-----|-------|------|------|------|---------|
| <EMAIL> | admin | 系统管理员 | ADMIN | 全部 | 2025-07-21 |

**登录信息**:
- **邮箱/用户名**: <EMAIL> 或 admin
- **密码**: password123
- **权限**: 总管理员，可以不选择店面登录，访问所有店面功能
- **特殊权限**: 店面对比分析、全局统计数据、用户管理

---

### 2. 店面管理员账号

| 店面 | 邮箱 | 用户名 | 姓名 | 角色 | 店面ID |
|-----|-----|-------|------|------|-------|
| 海口电视台店 | <EMAIL> | manager_haikou | 海口店管理员 | MANAGER | 6c126f62-57af-43a6-849c-55e9e5c702a6 |
| 三亚店 | <EMAIL> | manager_sanya | 三亚店管理员 | MANAGER | b621f2a5-a7bd-49aa-9299-3a7ab571f991 |
| 琼海店 | <EMAIL> | manager_qionghai | 琼海店管理员 | MANAGER | eb8797fb-c28c-492e-b9ed-2382f976df39 |

**登录信息**:
- **海口店管理员**:  / password123（必须选择海口电视台店）
- **三亚店管理员**: manager_sanya / password123（必须选择三亚店）
- **琼海店管理员**: manager_qionghai / password123（必须选择琼海店）

**权限说明**:
- ✅ 必须选择对应的店面才能登录
- ✅ 只能查看自己店面的数据统计
- ❌ 无法访问店面对比功能
- ❌ 无法查看其他店面数据

---

### 3. 海口电视台店 - 其他测试用户

| 邮箱 | 姓名 | 角色 | 描述 |
|-----|------|------|-----|
| <EMAIL> | 海口质检员 | USER | 质检操作员 |
| <EMAIL> | 海口消费者 | VIEWER | 只读用户 |

**登录信息**:
- **质检员账号**: <EMAIL> / password123
- **消费者账号**: <EMAIL> / password123

---

### 4. 三亚店 - 其他测试用户

| 邮箱 | 姓名 | 角色 | 描述 |
|-----|------|------|-----|
| <EMAIL> | 三亚质检员 | USER | 质检操作员 |
| <EMAIL> | 三亚消费者 | VIEWER | 只读用户 |

**登录信息**:
- **质检员账号**: <EMAIL> / password123
- **消费者账号**: <EMAIL> / password123

---

### 5. 琼海店 - 其他测试用户

| 邮箱 | 姓名 | 角色 | 描述 |
|-----|------|------|-----|
| <EMAIL> | 琼海质检员 | USER | 质检操作员 |
| <EMAIL> | 琼海消费者 | VIEWER | 只读用户 |

**登录信息**:manager_haikou
- **质检员账号**: <EMAIL> / password123
- **消费者账号**: <EMAIL> / password123

---

### 6. 通用测试用户

| 邮箱 | 姓名 | 角色 | 描述 |
|-----|------|------|-----|
| <EMAIL> | 普通用户 | USER | 通用测试用户 |
| <EMAIL> | 观察员 | VIEWER | 通用观察员 |
| <EMAIL> | Alex Chen | USER | 开发测试用户 |

**登录信息**:
- **普通用户**: <EMAIL> / password123
- **观察员**: <EMAIL> / password123
- **开发用户**: <EMAIL> / password123

---

## 🧪 测试场景建议

### 场景1: 多店面权限测试 ✅ 已完全验证
1. 使用不同店面的管理员账号登录 - **✅ 通过**
2. 验证只能看到自己店面的数据 - **✅ 通过**
3. 测试跨店面访问是否被正确阻止 - **✅ 通过**

### 场景2: 角色权限测试 ✅ 已完全验证
1. **ADMIN角色**: 可以不选择店面登录，访问所有店面数据 - **✅ 通过**
2. **USER角色**: 必须选择对应店面才能登录，只能访问自己店面数据 - **✅ 通过**
3. **VIEWER角色**: 必须选择对应店面才能登录，只读权限 - **✅ 通过**

### 场景3: 登录流程测试 ✅ 已完全验证
1. 总管理员不选择店面登录 - **✅ 通过**
2. 店面用户不选择店面登录（被拒绝）- **✅ 通过**
3. 店面用户选择正确店面登录 - **✅ 通过**
4. 店面用户选择错误店面登录（被拒绝）- **✅ 通过**

### 场景4: 数据隔离测试 ✅ 已完全验证
1. 店面管理员数据隔离（12用户 vs 55用户）- **✅ 通过**
2. 总管理员全店面访问（全量数据）- **✅ 通过**
3. 店面对比功能权限控制 - **✅ 通过**

### 场景5: API权限测试 ✅ 已完全验证
1. 店面管理员访问仪表盘统计 - **✅ 通过（限制数据）**
2. 店面管理员访问店面对比（被拒绝）- **✅ 通过**
3. 总管理员访问店面对比 - **✅ 通过**

## ✅ 登录测试结果

### 已验证的功能：
- ✅ 3个店面管理员账号创建成功并测试通过
- ✅ 总管理员可以不选择店面登录
- ✅ 店面管理员必须选择对应店面登录
- ✅ 跨店面访问被正确阻止（403权限不足）
- ✅ 数据隔离完全生效
- ✅ API权限控制正常工作
- ✅ 前端登录界面支持两种登录模式

### 权限验证矩阵：

| 用户类型 | 不选店面登录 | 选择正确店面 | 选择错误店面 | 数据访问范围 | 店面对比功能 |
|---------|------------|------------|------------|------------|-------------|
| 总管理员 | ✅ 成功 | ✅ 成功 | ✅ 成功 | 全部店面 | ✅ 可访问 |
| 海口店管理员 | ❌ 被拒绝 | ✅ 成功 | ❌ 403错误 | 仅海口店 | ❌ 权限不足 |
| 三亚店管理员 | ❌ 被拒绝 | ✅ 成功 | ❌ 403错误 | 仅三亚店 | ❌ 权限不足 |
| 琼海店管理员 | ❌ 被拒绝 | ✅ 成功 | ❌ 403错误 | 仅琼海店 | ❌ 权限不足 |

## 🔄 重置测试数据

如需重置所有测试用户的密码或创建新的测试数据，可以使用以下SQL命令：

```sql
-- 重置所有测试用户密码为 'password123'
UPDATE users SET password = '$2a$12$hash_of_password123' WHERE role IN ('USER', 'VIEWER');

-- 查看所有用户信息
SELECT u.email, u.name, u.role, c.name as cafe_name 
FROM users u 
LEFT JOIN cafes c ON u.cafe_id = c.id 
ORDER BY c.name, u.role;
```

## 📝 注意事项

1. **安全提醒**: 这些是测试账号，请勿在生产环境中使用
2. **密码策略**: 所有测试用户的默认密码为 `password123`
3. **数据清理**: 定期清理测试产生的数据，避免影响系统性能
4. **权限验证**: 每次功能更新后，请使用不同角色的账号进行权限测试
5. **登录方式**: 支持邮箱或用户名登录，建议使用用户名以便记忆

## 🎯 快速测试指南

### 测试总管理员功能：
```bash
# 登录账号
用户名: admin
密码: password123
店面选择: 不选择（直接点击登录）

# 预期结果
- 登录成功，显示"全部店面"
- 可以查看所有店面的数据统计
- 可以访问店面对比功能
```

### 测试店面管理员功能：
```bash
# 海口店管理员
用户名: manager_haikou
密码: password123
店面选择: 海口电视台店

# 预期结果
- 必须选择店面才能登录
- 只能看到海口店的数据（用户数较少）
- 无法访问店面对比功能
```

### 测试权限隔离：
```bash
# 尝试海口店管理员访问三亚店（应该失败）
用户名: manager_haikou
密码: password123
店面选择: 三亚店

# 预期结果
- 登录失败，提示"您没有权限访问该店面"
```

## 📞 技术支持

如需添加新的测试用户或修改现有用户信息，请联系开发团队。

### 最新更新内容：
- **2025-08-01**: 创建了3个店面管理员账号
- **2025-08-01**: 完成多店面权限隔离测试
- **2025-08-01**: 验证总管理员免选店面登录功能
- **2025-08-01**: 测试API权限控制和数据隔离

---

**文档维护**: 海南长小养智能科技有限责任公司  
**最后更新**: 2025年8月1日 18:45  
**文档版本**: v2.0  
**测试状态**: ✅ 全部测试通过