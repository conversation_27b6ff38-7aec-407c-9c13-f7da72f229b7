<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 600" width="800" height="600">
  <defs>
    <radialGradient id="bifidobacteriumGradient" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#98FB98;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#228B22;stop-opacity:1" />
    </radialGradient>
    <radialGradient id="backgroundGradient2" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#F0FFF0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E6FFE6;stop-opacity:1" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="600" fill="url(#backgroundGradient2)"/>
  
  <!-- Title -->
  <text x="400" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2F4F2F">双歧杆菌 (Bifidobacterium) - 显微镜视图</text>
  
  <!-- Y-shaped and branched bacteria (Bifidobacterium) -->
  <!-- Bacteria 1 - Y-shaped -->
  <g transform="translate(150, 120)">
    <path d="M 0 0 L 15 -25 M 0 0 L -15 -25 M 0 0 L 0 30" stroke="url(#bifidobacteriumGradient)" stroke-width="8" stroke-linecap="round" fill="none"/>
    <path d="M 0 0 L 15 -25 M 0 0 L -15 -25 M 0 0 L 0 30" stroke="#90EE90" stroke-width="5" stroke-linecap="round" fill="none" opacity="0.7"/>
  </g>
  
  <!-- Bacteria 2 - Branched -->
  <g transform="translate(350, 180) rotate(30)">
    <path d="M 0 0 L 20 -20 M 0 0 L -20 -20 M 0 0 L 0 35 M 0 15 L 15 5" stroke="url(#bifidobacteriumGradient)" stroke-width="7" stroke-linecap="round" fill="none"/>
    <path d="M 0 0 L 20 -20 M 0 0 L -20 -20 M 0 0 L 0 35 M 0 15 L 15 5" stroke="#90EE90" stroke-width="4" stroke-linecap="round" fill="none" opacity="0.7"/>
  </g>
  
  <!-- Bacteria 3 - V-shaped -->
  <g transform="translate(550, 150) rotate(-45)">
    <path d="M 0 0 L 18 -30 M 0 0 L -18 -30" stroke="url(#bifidobacteriumGradient)" stroke-width="9" stroke-linecap="round" fill="none"/>
    <path d="M 0 0 L 18 -30 M 0 0 L -18 -30" stroke="#90EE90" stroke-width="6" stroke-linecap="round" fill="none" opacity="0.7"/>
  </g>
  
  <!-- Bacteria 4 - Complex branched -->
  <g transform="translate(200, 300) rotate(60)">
    <path d="M 0 0 L 12 -25 M 0 0 L -12 -25 M 0 0 L 0 30 M 0 10 L 20 0 M 0 20 L -15 10" stroke="url(#bifidobacteriumGradient)" stroke-width="6" stroke-linecap="round" fill="none"/>
    <path d="M 0 0 L 12 -25 M 0 0 L -12 -25 M 0 0 L 0 30 M 0 10 L 20 0 M 0 20 L -15 10" stroke="#90EE90" stroke-width="3" stroke-linecap="round" fill="none" opacity="0.7"/>
  </g>
  
  <!-- Bacteria 5 - Y-shaped -->
  <g transform="translate(450, 280) rotate(-30)">
    <path d="M 0 0 L 16 -28 M 0 0 L -16 -28 M 0 0 L 0 32" stroke="url(#bifidobacteriumGradient)" stroke-width="8" stroke-linecap="round" fill="none"/>
    <path d="M 0 0 L 16 -28 M 0 0 L -16 -28 M 0 0 L 0 32" stroke="#90EE90" stroke-width="5" stroke-linecap="round" fill="none" opacity="0.7"/>
  </g>
  
  <!-- Bacteria 6 - Branched -->
  <g transform="translate(650, 250) rotate(90)">
    <path d="M 0 0 L 14 -22 M 0 0 L -14 -22 M 0 0 L 0 28 M 0 12 L 18 8" stroke="url(#bifidobacteriumGradient)" stroke-width="7" stroke-linecap="round" fill="none"/>
    <path d="M 0 0 L 14 -22 M 0 0 L -14 -22 M 0 0 L 0 28 M 0 12 L 18 8" stroke="#90EE90" stroke-width="4" stroke-linecap="round" fill="none" opacity="0.7"/>
  </g>
  
  <!-- Bacteria 7 - Simple Y -->
  <g transform="translate(120, 420) rotate(15)">
    <path d="M 0 0 L 13 -24 M 0 0 L -13 -24 M 0 0 L 0 26" stroke="url(#bifidobacteriumGradient)" stroke-width="6" stroke-linecap="round" fill="none"/>
    <path d="M 0 0 L 13 -24 M 0 0 L -13 -24 M 0 0 L 0 26" stroke="#90EE90" stroke-width="3" stroke-linecap="round" fill="none" opacity="0.7"/>
  </g>
  
  <!-- Bacteria 8 - Complex -->
  <g transform="translate(380, 400) rotate(-60)">
    <path d="M 0 0 L 15 -26 M 0 0 L -15 -26 M 0 0 L 0 30 M 0 8 L 12 -5 M 0 18 L -10 12" stroke="url(#bifidobacteriumGradient)" stroke-width="7" stroke-linecap="round" fill="none"/>
    <path d="M 0 0 L 15 -26 M 0 0 L -15 -26 M 0 0 L 0 30 M 0 8 L 12 -5 M 0 18 L -10 12" stroke="#90EE90" stroke-width="4" stroke-linecap="round" fill="none" opacity="0.7"/>
  </g>
  
  <!-- Bacteria 9 - V-shaped -->
  <g transform="translate(600, 380) rotate(45)">
    <path d="M 0 0 L 17 -28 M 0 0 L -17 -28" stroke="url(#bifidobacteriumGradient)" stroke-width="8" stroke-linecap="round" fill="none"/>
    <path d="M 0 0 L 17 -28 M 0 0 L -17 -28" stroke="#90EE90" stroke-width="5" stroke-linecap="round" fill="none" opacity="0.7"/>
  </g>
  
  <!-- Additional smaller bacteria -->
  <g transform="translate(280, 200) rotate(120)">
    <path d="M 0 0 L 8 -15 M 0 0 L -8 -15 M 0 0 L 0 18" stroke="url(#bifidobacteriumGradient)" stroke-width="4" stroke-linecap="round" fill="none"/>
  </g>
  
  <g transform="translate(500, 350) rotate(-90)">
    <path d="M 0 0 L 10 -18 M 0 0 L -10 -18" stroke="url(#bifidobacteriumGradient)" stroke-width="5" stroke-linecap="round" fill="none"/>
  </g>
  
  <!-- Scale indicator -->
  <g transform="translate(50, 520)">
    <line x1="0" y1="0" x2="50" y2="0" stroke="#2F4F2F" stroke-width="2"/>
    <line x1="0" y1="-5" x2="0" y2="5" stroke="#2F4F2F" stroke-width="2"/>
    <line x1="50" y1="-5" x2="50" y2="5" stroke="#2F4F2F" stroke-width="2"/>
    <text x="25" y="20" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#2F4F2F">3 μm</text>
  </g>
  
  <!-- Information box -->
  <rect x="50" y="550" width="700" height="40" fill="#F0FFF0" stroke="#228B22" stroke-width="1" rx="5"/>
  <text x="60" y="570" font-family="Arial, sans-serif" font-size="14" fill="#2F4F2F">双歧杆菌呈Y形或分叉状，是重要的益生菌，有助于维持肠道健康和免疫系统平衡</text>
</svg>