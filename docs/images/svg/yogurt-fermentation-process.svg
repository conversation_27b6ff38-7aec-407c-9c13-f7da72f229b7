<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 700" width="1000" height="700">
  <defs>
    <linearGradient id="milkGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F5F5F5;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="yogurtGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFACD;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F0E68C;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="bacteriaGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#4682B4;stop-opacity:0.3" />
    </radialGradient>
    <radialGradient id="backgroundGrad" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#F8F8FF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E6E6FA;stop-opacity:1" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1000" height="700" fill="url(#backgroundGrad)"/>
  
  <!-- Title -->
  <text x="500" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="#4B0082">酸奶发酵过程 - 菌群活动示意图</text>
  
  <!-- Stage 1: Fresh Milk -->
  <g>
    <text x="150" y="90" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#4B0082">1. 新鲜牛奶</text>
    <rect x="100" y="100" width="100" height="150" fill="url(#milkGradient)" stroke="#D3D3D3" stroke-width="2" rx="10"/>
    
    <!-- Milk molecules -->
    <circle cx="120" cy="130" r="3" fill="#E0E0E0"/>
    <circle cx="140" cy="140" r="3" fill="#E0E0E0"/>
    <circle cx="160" cy="125" r="3" fill="#E0E0E0"/>
    <circle cx="180" cy="145" r="3" fill="#E0E0E0"/>
    <circle cx="130" cy="170" r="3" fill="#E0E0E0"/>
    <circle cx="170" cy="180" r="3" fill="#E0E0E0"/>
    <circle cx="150" cy="200" r="3" fill="#E0E0E0"/>
    <circle cx="120" cy="220" r="3" fill="#E0E0E0"/>
    <circle cx="180" cy="210" r="3" fill="#E0E0E0"/>
    
    <text x="150" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">乳糖分子</text>
    <text x="150" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">蛋白质</text>
  </g>
  
  <!-- Arrow 1 -->
  <path d="M 220 175 L 280 175" stroke="#4B0082" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="250" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#4B0082">添加菌种</text>
  
  <!-- Stage 2: Inoculation -->
  <g>
    <text x="350" y="90" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#4B0082">2. 接种菌群</text>
    <rect x="300" y="100" width="100" height="150" fill="url(#milkGradient)" stroke="#D3D3D3" stroke-width="2" rx="10"/>
    
    <!-- Initial bacteria -->
    <ellipse cx="320" cy="130" rx="8" ry="2" fill="#87CEEB" stroke="#4682B4" stroke-width="1"/>
    <ellipse cx="360" cy="140" rx="8" ry="2" fill="#87CEEB" stroke="#4682B4" stroke-width="1" transform="rotate(45 360 140)"/>
    <ellipse cx="340" cy="170" rx="8" ry="2" fill="#87CEEB" stroke="#4682B4" stroke-width="1" transform="rotate(-30 340 170)"/>
    <ellipse cx="380" cy="160" rx="8" ry="2" fill="#87CEEB" stroke="#4682B4" stroke-width="1" transform="rotate(60 380 160)"/>
    
    <!-- Y-shaped bacteria -->
    <g transform="translate(330, 200)">
      <path d="M 0 0 L 5 -8 M 0 0 L -5 -8 M 0 0 L 0 12" stroke="#98FB98" stroke-width="2" stroke-linecap="round" fill="none"/>
    </g>
    <g transform="translate(370, 190) rotate(45)">
      <path d="M 0 0 L 5 -8 M 0 0 L -5 -8 M 0 0 L 0 12" stroke="#98FB98" stroke-width="2" stroke-linecap="round" fill="none"/>
    </g>
    
    <!-- Chain bacteria -->
    <circle cx="320" cy="220" r="4" fill="#FFB6C1" stroke="#DC143C" stroke-width="1"/>
    <circle cx="330" cy="220" r="4" fill="#FFB6C1" stroke="#DC143C" stroke-width="1"/>
    <circle cx="340" cy="220" r="4" fill="#FFB6C1" stroke="#DC143C" stroke-width="1"/>
    
    <text x="350" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">乳酸菌</text>
    <text x="350" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">双歧杆菌</text>
  </g>
  
  <!-- Arrow 2 -->
  <path d="M 420 175 L 480 175" stroke="#4B0082" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="450" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#4B0082">发酵开始</text>
  
  <!-- Stage 3: Active Fermentation -->
  <g>
    <text x="550" y="90" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#4B0082">3. 活跃发酵</text>
    <rect x="500" y="100" width="100" height="150" fill="url(#yogurtGradient)" stroke="#DAA520" stroke-width="2" rx="10"/>
    
    <!-- Many active bacteria -->
    <ellipse cx="520" cy="120" rx="10" ry="2" fill="#87CEEB" stroke="#4682B4" stroke-width="1" transform="rotate(15 520 120)"/>
    <ellipse cx="560" cy="130" rx="10" ry="2" fill="#87CEEB" stroke="#4682B4" stroke-width="1" transform="rotate(-30 560 130)"/>
    <ellipse cx="540" cy="150" rx="10" ry="2" fill="#87CEEB" stroke="#4682B4" stroke-width="1" transform="rotate(60 540 150)"/>
    <ellipse cx="580" cy="140" rx="10" ry="2" fill="#87CEEB" stroke="#4682B4" stroke-width="1" transform="rotate(-45 580 140)"/>
    <ellipse cx="510" cy="170" rx="10" ry="2" fill="#87CEEB" stroke="#4682B4" stroke-width="1" transform="rotate(90 510 170)"/>
    <ellipse cx="570" cy="180" rx="10" ry="2" fill="#87CEEB" stroke="#4682B4" stroke-width="1" transform="rotate(30 570 180)"/>
    
    <!-- More Y-shaped bacteria -->
    <g transform="translate(530, 190)">
      <path d="M 0 0 L 6 -10 M 0 0 L -6 -10 M 0 0 L 0 15" stroke="#98FB98" stroke-width="3" stroke-linecap="round" fill="none"/>
    </g>
    <g transform="translate(560, 200) rotate(60)">
      <path d="M 0 0 L 6 -10 M 0 0 L -6 -10 M 0 0 L 0 15" stroke="#98FB98" stroke-width="3" stroke-linecap="round" fill="none"/>
    </g>
    <g transform="translate(520, 210) rotate(-45)">
      <path d="M 0 0 L 6 -10 M 0 0 L -6 -10 M 0 0 L 0 15" stroke="#98FB98" stroke-width="3" stroke-linecap="round" fill="none"/>
    </g>
    
    <!-- Chain bacteria -->
    <circle cx="515" cy="230" r="4" fill="#FFB6C1" stroke="#DC143C" stroke-width="1"/>
    <circle cx="525" cy="230" r="4" fill="#FFB6C1" stroke="#DC143C" stroke-width="1"/>
    <circle cx="535" cy="230" r="4" fill="#FFB6C1" stroke="#DC143C" stroke-width="1"/>
    <circle cx="545" cy="230" r="4" fill="#FFB6C1" stroke="#DC143C" stroke-width="1"/>
    
    <!-- Fermentation bubbles -->
    <circle cx="525" cy="125" r="2" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="575" cy="155" r="2" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="545" cy="185" r="2" fill="#FFFFFF" opacity="0.7"/>
    
    <text x="550" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">乳酸产生</text>
    <text x="550" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">pH下降</text>
  </g>
  
  <!-- Arrow 3 -->
  <path d="M 620 175 L 680 175" stroke="#4B0082" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="650" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#4B0082">凝固成型</text>
  
  <!-- Stage 4: Final Yogurt -->
  <g>
    <text x="750" y="90" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#4B0082">4. 成熟酸奶</text>
    <rect x="700" y="100" width="100" height="150" fill="url(#yogurtGradient)" stroke="#DAA520" stroke-width="2" rx="10"/>
    
    <!-- Stable bacteria population -->
    <ellipse cx="720" cy="130" rx="12" ry="3" fill="#87CEEB" stroke="#4682B4" stroke-width="1" transform="rotate(20 720 130)"/>
    <ellipse cx="760" cy="140" rx="12" ry="3" fill="#87CEEB" stroke="#4682B4" stroke-width="1" transform="rotate(-40 760 140)"/>
    <ellipse cx="740" cy="170" rx="12" ry="3" fill="#87CEEB" stroke="#4682B4" stroke-width="1" transform="rotate(70 740 170)"/>
    <ellipse cx="780" cy="160" rx="12" ry="3" fill="#87CEEB" stroke="#4682B4" stroke-width="1" transform="rotate(-20 780 160)"/>
    
    <!-- Mature Y-shaped bacteria -->
    <g transform="translate(730, 200)">
      <path d="M 0 0 L 7 -12 M 0 0 L -7 -12 M 0 0 L 0 18" stroke="#98FB98" stroke-width="4" stroke-linecap="round" fill="none"/>
    </g>
    <g transform="translate(770, 190) rotate(45)">
      <path d="M 0 0 L 7 -12 M 0 0 L -7 -12 M 0 0 L 0 18" stroke="#98FB98" stroke-width="4" stroke-linecap="round" fill="none"/>
    </g>
    
    <!-- Stable chains -->
    <circle cx="720" cy="230" r="5" fill="#FFB6C1" stroke="#DC143C" stroke-width="1"/>
    <circle cx="732" cy="230" r="5" fill="#FFB6C1" stroke="#DC143C" stroke-width="1"/>
    <circle cx="744" cy="230" r="5" fill="#FFB6C1" stroke="#DC143C" stroke-width="1"/>
    <circle cx="756" cy="230" r="5" fill="#FFB6C1" stroke="#DC143C" stroke-width="1"/>
    <circle cx="768" cy="230" r="5" fill="#FFB6C1" stroke="#DC143C" stroke-width="1"/>
    
    <text x="750" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">稳定菌群</text>
    <text x="750" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">酸甜口感</text>
  </g>
  
  <!-- Chemical process illustration -->
  <g transform="translate(100, 350)">
    <text x="400" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#4B0082">发酵化学过程</text>
    
    <!-- Lactose to Lactic Acid -->
    <rect x="50" y="50" width="120" height="60" fill="#E6F3FF" stroke="#4682B4" stroke-width="2" rx="5"/>
    <text x="110" y="75" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#4682B4">乳糖</text>
    <text x="110" y="90" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#4682B4">(C₁₂H₂₂O₁₁)</text>
    
    <path d="M 190 80 L 250 80" stroke="#4B0082" stroke-width="3" marker-end="url(#arrowhead)"/>
    <text x="220" y="70" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#4B0082">乳酸菌</text>
    <text x="220" y="100" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#4B0082">发酵</text>
    
    <rect x="270" y="50" width="120" height="60" fill="#FFE6E6" stroke="#DC143C" stroke-width="2" rx="5"/>
    <text x="330" y="75" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#DC143C">乳酸</text>
    <text x="330" y="90" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#DC143C">(C₃H₆O₃)</text>
    
    <!-- Benefits -->
    <rect x="450" y="50" width="200" height="60" fill="#E6FFE6" stroke="#228B22" stroke-width="2" rx="5"/>
    <text x="550" y="70" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#228B22">健康益处</text>
    <text x="550" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#228B22">• 促进消化</text>
    <text x="550" y="100" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#228B22">• 增强免疫力</text>
  </g>
  
  <!-- Temperature and time info -->
  <g transform="translate(100, 500)">
    <rect x="0" y="0" width="800" height="120" fill="#F0F8FF" stroke="#4682B4" stroke-width="2" rx="10"/>
    <text x="400" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#4B0082">发酵条件与时间</text>
    
    <g transform="translate(50, 40)">
      <circle cx="50" cy="30" r="25" fill="#FFE4B5" stroke="#FF8C00" stroke-width="2"/>
      <text x="50" y="35" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FF8C00">42°C</text>
      <text x="50" y="65" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">最适温度</text>
    </g>
    
    <g transform="translate(200, 40)">
      <rect x="25" y="10" width="50" height="40" fill="#E6E6FA" stroke="#9370DB" stroke-width="2" rx="5"/>
      <text x="50" y="35" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#9370DB">6-8小时</text>
      <text x="50" y="65" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">发酵时间</text>
    </g>
    
    <g transform="translate(350, 40)">
      <polygon points="50,10 70,30 50,50 30,30" fill="#98FB98" stroke="#228B22" stroke-width="2"/>
      <text x="50" y="35" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#228B22">pH 4.5</text>
      <text x="50" y="65" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">最终酸度</text>
    </g>
    
    <g transform="translate(500, 40)">
      <ellipse cx="50" cy="30" rx="35" ry="20" fill="#FFB6C1" stroke="#DC143C" stroke-width="2"/>
      <text x="50" y="35" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#DC143C">10⁸-10⁹</text>
      <text x="50" y="65" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">菌群密度/mL</text>
    </g>
    
    <g transform="translate(650, 40)">
      <rect x="25" y="10" width="50" height="40" fill="#F0E68C" stroke="#DAA520" stroke-width="2" rx="5"/>
      <text x="50" y="35" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#DAA520">4°C</text>
      <text x="50" y="65" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">储存温度</text>
    </g>
  </g>
  
  <!-- Arrow marker definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4B0082"/>
    </marker>
  </defs>
  
  <!-- Footer info -->
  <text x="500" y="680" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#666">酸奶发酵是一个复杂的生物化学过程，涉及多种益生菌的协同作用</text>
</svg>