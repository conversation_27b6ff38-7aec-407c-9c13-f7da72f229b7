# 🥛 Yogurt AI QC - 开发指南

## 📋 目录

- [项目概述](#项目概述)
- [技术架构](#技术架构)
- [开发环境设置](#开发环境设置)
- [项目结构](#项目结构)
- [开发工作流](#开发工作流)
- [API文档](#api文档)
- [数据库设计](#数据库设计)
- [前端开发](#前端开发)
- [后端开发](#后端开发)
- [AI服务开发](#ai服务开发)
- [测试指南](#测试指南)
- [部署指南](#部署指南)
- [故障排除](#故障排除)

## 📖 项目概述

**Yogurt AI QC** 是一个专为精品咖啡厅和酸奶制作商设计的AI驱动质量控制系统。通过结合传统工艺记录与AI显微分析，将酸奶制作从"经验艺术"提升为"数据科学"。

### 核心功能
- 🧪 配方管理和版本控制
- 📋 批次生产跟踪
- 🔬 AI显微镜图像分析
- 📊 质量数据可视化
- 📈 趋势分析和预测
- 👥 多用户协作管理

### 技术特点
- 现代化全栈架构
- 多模态AI集成
- 实时数据处理
- 企业级安全设计
- 容器化部署

## 🏗️ 技术架构

### 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   后端API       │    │   AI分析服务    │
│   React + TS    │◄──►│   Node.js       │◄──►│   Python        │
│   Ant Design    │    │   Express       │    │   FastAPI       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面      │    │   PostgreSQL    │    │   AI模型        │
│   数据可视化    │    │   Redis缓存     │    │   OpenAI GPT-4V │
│   报告生成      │    │   文件存储      │    │   图像处理      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 技术栈详情

**前端技术栈**
- React 18 + TypeScript
- Vite (构建工具)
- Ant Design 5.26.5 (UI组件库)
- Redux Toolkit (状态管理)
- React Query (数据获取)
- ECharts (数据可视化)
- React Hook Form (表单处理)

**后端技术栈**
- Node.js 18+ + TypeScript
- Express 5.1.0 (Web框架)
- Prisma 6.12.0 (ORM)
- PostgreSQL 15 (主数据库)
- Redis 7 (缓存)
- JWT (认证)
- Swagger (API文档)

**AI服务技术栈**
- Python 3.12
- FastAPI (Web框架)
- OpenAI GPT-4V (多模态AI)
- OpenCV + Pillow (图像处理)
- TensorFlow + PyTorch (机器学习)
- Celery (异步任务)

**基础设施**
- Docker + Docker Compose
- Nginx (反向代理)
- Prometheus (监控)
- Grafana (可视化)


## 🚀 开发环境设置

### 前置要求

- **Node.js** 18+
- **Python** 3.11+
- **Docker** & **Docker Compose**
- **Conda/Miniconda** (必需，用于AI服务环境管理)

### 快速开始

#### 1. 克隆项目
```bash
git clone https://github.com/changxiaoyangbrain/yoghurt-ai-qc.git
cd yoghurt-ai-qc
```

#### 2. 环境配置
```bash
# 创建Conda环境
./scripts/create-latest-env.sh

# 配置自动环境激活
./setup-direnv-complete.sh

# 复制环境变量模板
cp .env.example .env
```

#### 3. 配置环境变量
编辑 `.env` 文件，填入必要的配置：
```env
# OpenAI API密钥（必需）
OPENAI_API_KEY=sk-your-openai-api-key-here

# 数据库配置
DATABASE_URL=postgresql://postgres:your-password@localhost:6432/postgres
DB_PASSWORD=your-secure-database-password

# JWT密钥
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters
```

#### 4. 启动服务
```bash
# 一键启动所有服务
./scripts/start-all-services.sh

# 或分步启动
docker-compose up -d postgres redis  # 启动数据库
npm run dev:frontend                  # 前端 (端口6173)
npm run dev:backend                   # 后端 (端口6000)
npm run dev:ai                        # AI服务 (端口6800)
```

#### 5. 访问应用
- **前端应用**: http://localhost:6173
- **后端API**: http://localhost:6000
- **API文档**: http://localhost:6000/api/docs
- **AI服务**: http://localhost:6800

### 开发工具配置

#### VS Code 推荐插件
```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "ms-python.python",
    "ms-python.flake8",
    "ms-python.black-formatter",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode.vscode-eslint",
    "esbenp.prettier-vscode"
  ]
}
```

#### Git Hooks 设置
```bash
# 安装 pre-commit
pip install pre-commit

# 安装 hooks
pre-commit install
```

## 📁 项目结构

```
yoghurt-ai-qc/
├── frontend/                 # React前端应用
│   ├── src/
│   │   ├── components/      # 可复用组件
│   │   ├── pages/          # 页面组件
│   │   ├── services/       # API服务
│   │   ├── store/          # Redux状态管理
│   │   ├── types/          # TypeScript类型定义
│   │   ├── utils/          # 工具函数
│   │   └── styles/         # 样式文件
│   ├── public/             # 静态资源
│   └── package.json
├── backend/                  # Node.js后端API
│   ├── src/
│   │   ├── controllers/    # 控制器
│   │   ├── services/       # 业务逻辑
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # 路由定义
│   │   ├── middleware/     # 中间件
│   │   ├── utils/          # 工具函数
│   │   └── config/         # 配置文件
│   ├── prisma/             # 数据库模式
│   └── package.json
├── ai-service/              # Python AI分析服务
│   ├── src/
│   │   ├── services/       # AI分析服务
│   │   ├── models/         # 数据模型
│   │   ├── utils/          # 工具函数
│   │   └── config/         # 配置文件
│   └── requirements.txt
├── docs/                    # 项目文档
├── scripts/                 # 脚本文件
├── docker-compose.yml       # Docker Compose配置
├── environment.yml          # Conda环境配置
└── README.md
```

### 核心目录说明

**frontend/src/**
- `components/`: 可复用的React组件
- `pages/`: 页面级组件
- `services/`: API调用服务
- `store/`: Redux状态管理
- `types/`: TypeScript类型定义
- `utils/`: 工具函数和帮助类

**backend/src/**
- `controllers/`: 处理HTTP请求的控制器
- `services/`: 业务逻辑层
- `models/`: 数据模型定义
- `routes/`: API路由定义
- `middleware/`: Express中间件
- `config/`: 配置文件

**ai-service/src/**
- `services/`: AI分析核心服务
- `models/`: 数据模型和AI模型
- `utils/`: 图像处理和工具函数
- `config/`: 服务配置


## 🔄 开发工作流

### Git 工作流

#### 分支策略
```
main          # 主分支，生产环境代码
├── develop   # 开发分支，集成最新功能
├── feature/* # 功能分支
├── bugfix/*  # 修复分支
└── hotfix/*  # 紧急修复分支
```

#### 提交规范
使用 Conventional Commits 规范：
```
feat: 添加新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

示例：
```bash
git commit -m "feat(auth): 添加JWT认证中间件"
git commit -m "fix(api): 修复用户登录接口错误处理"
git commit -m "docs: 更新API文档"
```

### 开发流程

#### 1. 功能开发
```bash
# 创建功能分支
git checkout -b feature/recipe-management

# 开发过程中定期提交
git add .
git commit -m "feat(recipe): 添加配方创建功能"

# 推送到远程
git push origin feature/recipe-management

# 创建Pull Request
```

#### 2. 代码审查
- 所有代码必须经过Code Review
- 至少需要一个审查者批准
- 确保通过所有自动化测试
- 检查代码规范和最佳实践

#### 3. 测试流程
```bash
# 运行所有测试
npm run test

# 运行特定模块测试
npm run test:frontend
npm run test:backend
cd ai-service && pytest

# 代码覆盖率检查
npm run test:coverage
```

#### 4. 部署流程
```bash
# 构建生产版本
npm run build

# Docker构建
docker-compose build

# 部署到测试环境
docker-compose -f docker-compose.test.yml up -d

# 部署到生产环境
docker-compose -f docker-compose.prod.yml up -d
```

## 📚 API文档

### 认证API

#### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

响应：
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "name": "用户名",
      "role": "USER"
    }
  }
}
```

#### 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "用户名"
}
```

### 配方管理API

#### 获取配方列表
```http
GET /api/recipes
Authorization: Bearer <token>
```

#### 创建配方
```http
POST /api/recipes
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "经典低温10小时配方",
  "description": "适合制作口感顺滑的酸奶",
  "ingredients": {
    "milk": {
      "brand": "朝日唯品全脂鲜奶",
      "amount": "1000ml"
    },
    "starter": {
      "type": "川秀15菌菌粉",
      "amount": "1g"
    }
  },
  "process": {
    "fermentationTemperature": 42,
    "fermentationDuration": 10,
    "filtrationDuration": 4
  }
}
```

### 批次管理API

#### 创建批次
```http
POST /api/batches
Authorization: Bearer <token>
Content-Type: application/json

{
  "recipeId": "recipe-uuid",
  "productionDate": "2024-01-15",
  "quantity": 2.5,
  "notes": "测试批次"
}
```

#### 更新批次状态
```http
PATCH /api/batches/:id/status
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "COMPLETED",
  "actualFermentationDuration": 10.5,
  "actualTemperature": 42.2
}
```

### AI分析API

#### 提交图像分析
```http
POST /api/analysis/analyze
Authorization: Bearer <token>
Content-Type: multipart/form-data

files: [image1.jpg, image2.jpg]
batchId: batch-uuid
magnification: 1000
stainingMethod: "Gram Staining"
```

响应：
```json
{
  "success": true,
  "data": {
    "analysisId": "analysis-uuid",
    "qualityScore": 92,
    "bacterialCount": 1500000,
    "contaminationDetected": false,
    "morphologyAnalysis": {
      "lactobacillus": {
        "count": 900000,
        "percentage": 60,
        "morphology": "rod-shaped"
      },
      "streptococcus": {
        "count": 600000,
        "percentage": 40,
        "morphology": "chain-like cocci"
      }
    },
    "summaryRecommendation": "菌群健康度92分。杆菌与球菌比例均衡，活性良好。未发现明显污染。"
  }
}
```

### 错误处理

所有API遵循统一的错误响应格式：
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": {
      "field": "email",
      "message": "邮箱格式不正确"
    }
  }
}
```

常见错误码：
- `VALIDATION_ERROR`: 请求参数验证失败
- `AUTHENTICATION_ERROR`: 认证失败
- `AUTHORIZATION_ERROR`: 权限不足
- `NOT_FOUND`: 资源不存在
- `INTERNAL_ERROR`: 服务器内部错误


## 🗄️ 数据库设计

### 数据库架构

#### 核心表结构

**用户表 (users)**
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  name VARCHAR(100) NOT NULL,
  role user_role DEFAULT 'USER',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP(6) DEFAULT now(),
  updated_at TIMESTAMP(6) DEFAULT now()
);
```

**配方表 (recipes)**
```sql
CREATE TABLE recipes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  ingredients JSONB NOT NULL,
  process JSONB NOT NULL,
  fermentation_temperature DECIMAL(4,2),
  fermentation_duration INTEGER,
  filtration_duration INTEGER,
  version INTEGER DEFAULT 1,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP(6) DEFAULT now(),
  updated_at TIMESTAMP(6) DEFAULT now()
);
```

**批次表 (batches)**
```sql
CREATE TABLE batches (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  batch_number VARCHAR(50) UNIQUE NOT NULL,
  recipe_id UUID REFERENCES recipes(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  status batch_status DEFAULT 'PLANNING',
  production_date DATE NOT NULL,
  quantity DECIMAL(8,2),
  actual_fermentation_duration INTEGER,
  actual_temperature DECIMAL(4,2),
  notes TEXT,
  created_at TIMESTAMP(6) DEFAULT now(),
  updated_at TIMESTAMP(6) DEFAULT now()
);
```

#### 关系图
```
users (1) ──── (N) recipes
users (1) ──── (N) batches
recipes (1) ──── (N) batches
batches (1) ──── (N) microscope_images
batches (1) ──── (N) ai_analyses
batches (1) ──── (N) sensory_assessments
```

### 数据库操作

#### 连接配置
```typescript
// backend/src/config/database.ts
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  }
})

export { prisma }
```

#### 常用查询示例

**获取用户的所有配方**
```typescript
const recipes = await prisma.recipe.findMany({
  where: {
    userId: userId,
    isActive: true
  },
  include: {
    batches: {
      select: {
        id: true,
        batchNumber: true,
        status: true,
        productionDate: true
      }
    }
  },
  orderBy: {
    createdAt: 'desc'
  }
})
```

**创建批次并关联配方**
```typescript
const batch = await prisma.batch.create({
  data: {
    batchNumber: generateBatchNumber(),
    recipeId: recipeId,
    userId: userId,
    productionDate: new Date(),
    quantity: 2.5,
    status: 'PLANNING'
  },
  include: {
    recipe: true,
    user: {
      select: {
        id: true,
        name: true,
        email: true
      }
    }
  }
})
```

#### 数据库迁移

```bash
# 生成迁移文件
npx prisma migrate dev --name add_quality_reports

# 应用迁移
npx prisma migrate deploy

# 重置数据库
npx prisma migrate reset

# 生成Prisma客户端
npx prisma generate
```

#### 数据库种子数据

```typescript
// backend/src/scripts/seed.ts
import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  // 创建测试用户
  const hashedPassword = await bcrypt.hash('password123', 12)
  
  const admin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      name: '管理员',
      role: 'ADMIN'
    }
  })

  // 创建示例配方
  const recipe = await prisma.recipe.create({
    data: {
      userId: admin.id,
      name: '经典低温10小时配方',
      description: '适合制作口感顺滑的酸奶',
      ingredients: {
        milk: {
          brand: '朝日唯品全脂鲜奶',
          amount: '1000ml'
        },
        starter: {
          type: '川秀15菌菌粉',
          amount: '1g'
        }
      },
      process: {
        fermentationTemperature: 42,
        fermentationDuration: 10,
        filtrationDuration: 4
      }
    }
  })
}

main()
  .catch(console.error)
  .finally(() => prisma.$disconnect())
```

## 🎨 前端开发

### 组件架构

#### 目录结构
```
src/
├── components/          # 通用组件
│   ├── common/         # 基础组件
│   ├── forms/          # 表单组件
│   ├── charts/         # 图表组件
│   └── layout/         # 布局组件
├── pages/              # 页面组件
│   ├── auth/           # 认证页面
│   ├── dashboard/      # 仪表板
│   ├── recipes/        # 配方管理
│   ├── batches/        # 批次管理
│   └── analysis/       # 分析页面
├── services/           # API服务
├── store/              # 状态管理
├── types/              # 类型定义
└── utils/              # 工具函数
```

#### 组件开发规范

**函数组件模板**
```typescript
import React from 'react'
import { Button, Card } from 'antd'
import type { FC } from 'react'

interface Props {
  title: string
  onSubmit: () => void
}

const MyComponent: FC<Props> = ({ title, onSubmit }) => {
  return (
    <Card title={title}>
      <Button type="primary" onClick={onSubmit}>
        提交
      </Button>
    </Card>
  )
}

export default MyComponent
```

**Hooks使用示例**
```typescript
import { useState, useEffect } from 'react'
import { useQuery, useMutation } from '@tanstack/react-query'
import { useAppDispatch, useAppSelector } from '../store/hooks'

const useRecipes = () => {
  const dispatch = useAppDispatch()
  const { user } = useAppSelector(state => state.auth)

  const {
    data: recipes,
    isLoading,
    error
  } = useQuery({
    queryKey: ['recipes', user?.id],
    queryFn: () => recipeService.getRecipes(),
    enabled: !!user
  })

  const createRecipeMutation = useMutation({
    mutationFn: recipeService.createRecipe,
    onSuccess: () => {
      // 刷新列表
      queryClient.invalidateQueries(['recipes'])
    }
  })

  return {
    recipes,
    isLoading,
    error,
    createRecipe: createRecipeMutation.mutate
  }
}
```

### 状态管理

#### Redux Store 配置
```typescript
// src/store/index.ts
import { configureStore } from '@reduxjs/toolkit'
import { persistStore, persistReducer } from 'redux-persist'
import storage from 'redux-persist/lib/storage'

import authSlice from './slices/authSlice'
import uiSlice from './slices/uiSlice'

const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth'] // 只持久化认证状态
}

const persistedAuthReducer = persistReducer(persistConfig, authSlice)

export const store = configureStore({
  reducer: {
    auth: persistedAuthReducer,
    ui: uiSlice
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE']
      }
    })
})

export const persistor = persistStore(store)
export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
```

#### Slice 示例
```typescript
// src/store/slices/authSlice.ts
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import type { PayloadAction } from '@reduxjs/toolkit'
import { authService } from '../../services/authService'

interface User {
  id: string
  email: string
  name: string
  role: string
}

interface AuthState {
  user: User | null
  token: string | null
  isLoading: boolean
  error: string | null
}

const initialState: AuthState = {
  user: null,
  token: null,
  isLoading: false,
  error: null
}

export const loginAsync = createAsyncThunk(
  'auth/login',
  async (credentials: { email: string; password: string }) => {
    const response = await authService.login(credentials)
    return response.data
  }
)

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    logout: (state) => {
      state.user = null
      state.token = null
    },
    clearError: (state) => {
      state.error = null
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(loginAsync.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(loginAsync.fulfilled, (state, action) => {
        state.isLoading = false
        state.user = action.payload.user
        state.token = action.payload.token
      })
      .addCase(loginAsync.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.error.message || '登录失败'
      })
  }
})

export const { logout, clearError } = authSlice.actions
export default authSlice.reducer
```

### 样式和主题

#### Ant Design 主题配置
```typescript
// src/theme/index.ts
import type { ThemeConfig } from 'antd'

const theme: ThemeConfig = {
  token: {
    colorPrimary: '#1890ff',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#f5222d',
    borderRadius: 6,
    fontSize: 14
  },
  components: {
    Button: {
      borderRadius: 6
    },
    Card: {
      borderRadius: 8
    }
  }
}

export default theme
```

