# 如何保存数据库架构图片

## 方法一：从当前渲染的图表保存（最简单）

我已经在上面渲染了数据库实体关系图，您可以：

1. **右键点击图表** → 选择"另存为图片"
2. **使用图表工具栏**：点击图表右上角的下载按钮 📥
3. 选择PNG或SVG格式保存

## 方法二：使用Mermaid在线编辑器

1. 访问 [Mermaid Live Editor](https://mermaid.live)
2. 复制 `docs/diagrams/database-er.mmd` 文件内容
3. 粘贴到编辑器中
4. 点击 "Actions" → "Download PNG/SVG"

## 方法三：使用VS Code插件

如果您使用VS Code：

1. 安装 "Mermaid Preview" 插件
2. 打开 `docs/diagrams/database-er.mmd` 文件
3. 按 `Ctrl+Shift+P` 打开命令面板
4. 输入 "Mermaid: Preview" 并选择
5. 在预览窗口右键保存图片

## 方法四：使用Mermaid CLI（自动化）

### 安装Mermaid CLI
```bash
npm install -g @mermaid-js/mermaid-cli
```

### 生成图片
```bash
# 生成实体关系图
mmdc -i docs/diagrams/database-er.mmd -o docs/images/database-er-diagram.png -t dark -b white

# 生成数据流程图
mmdc -i docs/diagrams/data-flow.mmd -o docs/images/data-flow-diagram.png -t dark -b white

# 或者使用项目脚本
npm run docs:diagrams
```

## 可用的图表文件

项目中包含以下Mermaid图表文件：

- `docs/diagrams/database-er.mmd` - 数据库实体关系图
- `docs/diagrams/data-flow.mmd` - 数据流程图
- `docs/database-schema.md` - 完整的架构文档（包含多个图表）

## 推荐的图片格式

- **PNG**: 适合文档嵌入，文件较小
- **SVG**: 矢量格式，可无损缩放，适合打印
- **PDF**: 适合正式文档

## 图片保存位置建议

```
docs/
├── images/
│   ├── database-er-diagram.png
│   ├── data-flow-diagram.png
│   └── index-design-diagram.png
├── diagrams/
│   ├── database-er.mmd
│   └── data-flow.mmd
└── database-schema.md
```

## 自定义图表样式

您可以在Mermaid代码中添加样式：

```mermaid
%%{init: {'theme':'dark', 'themeVariables': { 'primaryColor': '#ff0000'}}}%%
erDiagram
    User ||--o{ Recipe : creates
    ...
```

## 故障排除

### 如果图表无法显示：
1. 检查Mermaid语法是否正确
2. 确保网络连接正常
3. 尝试刷新页面

### 如果CLI工具报错：
1. 确保Node.js版本 >= 14
2. 检查是否有权限问题
3. 尝试使用 `npx` 而不是全局安装

```bash
npx @mermaid-js/mermaid-cli -i input.mmd -o output.png
```
