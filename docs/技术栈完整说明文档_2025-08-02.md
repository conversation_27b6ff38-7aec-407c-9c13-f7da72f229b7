# 🥛 酸奶质控AI系统 - 技术栈完整说明文档

**文档版本**: v2.0  
**创建日期**: 2025年8月2日  
**最后更新**: 2025年8月2日  
**项目名称**: 酸奶质控AI系统 (Yogurt Quality Control AI System)  
**开发公司**: 海南长小养智能科技有限责任公司  

---

## 📋 目录

- [项目概述](#项目概述)
- [整体架构](#整体架构)
- [前端技术栈](#前端技术栈)
- [后端技术栈](#后端技术栈)
- [AI服务技术栈](#ai服务技术栈)
- [数据库技术栈](#数据库技术栈)
- [基础设施技术栈](#基础设施技术栈)
- [开发工具链](#开发工具链)
- [部署架构](#部署架构)
- [版本信息](#版本信息)
- [技术选型理由](#技术选型理由)

---

## 🎯 项目概述

### 项目简介
酸奶质控AI系统是一个专为精品咖啡厅和酸奶制作商设计的AI驱动质量控制平台。通过结合传统工艺记录与AI显微分析，将酸奶制作从"经验艺术"提升为"数据科学"。

### 核心功能
- 🧪 **配方管理**: 版本控制、成分追踪、工艺记录
- 📋 **批次生产**: 生产跟踪、质量监控、数据记录
- 🔬 **AI分析**: 显微镜图像分析、质量评估、异常检测
- 📊 **数据可视化**: 实时监控、趋势分析、报告生成
- 👥 **多租户管理**: 咖啡厅隔离、用户权限、数据安全

### 技术特点
- **现代化全栈架构**: 前后端分离，微服务设计
- **多模态AI集成**: 图像识别、自然语言处理
- **实时数据处理**: WebSocket通信、流式计算
- **企业级安全**: JWT认证、数据加密、权限控制
- **容器化部署**: Docker容器、Kubernetes编排

---

## 🏗️ 整体架构

### 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   后端API       │    │   AI分析服务    │
│   React + TS    │◄──►│   Node.js       │◄──►│   Python        │
│   Ant Design    │    │   Express       │    │   FastAPI       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面      │    │   PostgreSQL    │    │   AI模型        │
│   数据可视化    │    │   Redis缓存     │    │   OpenAI GPT-4V │
│   报告生成      │    │   文件存储      │    │   图像处理      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 服务端口配置
- **前端服务**: http://localhost:6173
- **后端API**: http://localhost:6000
- **AI服务**: http://localhost:6800
- **PostgreSQL**: localhost:6432
- **Redis**: localhost:6479

---

## 🎨 前端技术栈

### 核心框架
- **React**: 18.3.1 - 现代化UI框架
- **TypeScript**: 5.8.3 - 类型安全的JavaScript
- **Vite**: 7.0.5 - 快速构建工具

### UI组件库
- **Ant Design**: 5.26.5 - 企业级UI设计语言
- **@ant-design/icons**: 5.6.1 - 图标库

### 状态管理
- **Redux Toolkit**: 2.8.2 - 现代化状态管理
- **React Redux**: 9.2.0 - React绑定
- **Redux Persist**: 6.0.0 - 状态持久化

### 路由和导航
- **React Router DOM**: 7.7.0 - 客户端路由

### 数据获取
- **TanStack React Query**: 5.83.0 - 服务器状态管理
- **Axios**: 1.10.0 - HTTP客户端

### 表单处理
- **React Hook Form**: 7.60.0 - 高性能表单库
- **@hookform/resolvers**: 5.1.1 - 表单验证
- **Yup**: 1.6.1 - 模式验证

### 数据可视化
- **ECharts**: 5.6.0 - 数据可视化图表库
- **ECharts for React**: 3.0.2 - React集成

### 图像处理
- **React Image Crop**: 11.0.10 - 图像裁剪
- **React Dropzone**: 14.3.8 - 文件拖拽上传

### 工具库
- **Lodash**: 4.17.21 - 实用工具库
- **Day.js**: 1.11.13 - 日期处理
- **Classnames**: 2.5.1 - CSS类名管理

### 开发工具
- **ESLint**: 9.31.0 - 代码检查
- **Prettier**: 3.6.2 - 代码格式化
- **Vitest**: 3.2.4 - 单元测试框架
- **@testing-library/react**: 14.3.1 - 测试工具

---

## ⚙️ 后端技术栈

### 核心框架
- **Node.js**: >=18.0.0 - JavaScript运行时
- **Express**: 5.1.0 - Web应用框架
- **TypeScript**: 5.8.3 - 类型安全

### 数据库ORM
- **Prisma**: 6.12.0 - 现代化数据库工具包
- **@prisma/client**: 6.12.0 - 数据库客户端

### 认证和安全
- **JSON Web Token**: 9.0.2 - JWT认证
- **bcryptjs**: 3.0.2 - 密码加密
- **Helmet**: 8.1.0 - 安全中间件
- **CORS**: 2.8.5 - 跨域资源共享

### 缓存和数据库
- **IORedis**: 5.3.2 - Redis客户端
- **pg**: 8.11.3 - PostgreSQL客户端

### 文件处理
- **Multer**: 2.0.2 - 文件上传中间件
- **Sharp**: 0.34.3 - 图像处理

### API文档
- **Swagger JSDoc**: 6.2.8 - API文档生成
- **Swagger UI Express**: 5.0.1 - API文档界面

### 监控和日志
- **Winston**: 3.17.0 - 日志记录
- **Winston Daily Rotate File**: 5.0.0 - 日志轮转
- **Prometheus Client**: 15.1.3 - 监控指标
- **Express Prom Bundle**: 8.0.0 - Express监控

### 性能优化
- **Compression**: 1.8.1 - 响应压缩
- **Express Rate Limit**: 8.0.1 - 请求限流
- **Express Slow Down**: 2.0.1 - 慢速响应

### 任务调度
- **Node Cron**: 4.2.1 - 定时任务

### 数据验证
- **Express Validator**: 7.2.1 - 请求验证
- **Joi**: 17.13.3 - 数据验证

### 工具库
- **Lodash**: 4.17.21 - 实用工具
- **Day.js**: 1.11.10 - 日期处理
- **UUID**: 11.1.0 - 唯一标识符生成

### 开发工具
- **Nodemon**: 3.1.10 - 开发服务器
- **Jest**: 30.0.4 - 测试框架
- **ESLint**: 9.31.0 - 代码检查
- **Prettier**: 3.6.2 - 代码格式化

---

## 🤖 AI服务技术栈

### 核心框架
- **Python**: 3.12+ - 编程语言
- **FastAPI**: 0.104.1 - 现代化Web框架
- **Uvicorn**: 0.24.0 - ASGI服务器
- **Pydantic**: 2.4.2 - 数据验证

### AI和机器学习
- **OpenAI**: >=1.0.0 - GPT-4V多模态AI
- **TensorFlow**: 2.14.0 - 深度学习框架
- **PyTorch**: 2.1.1 - 深度学习框架
- **Transformers**: 4.35.2 - 预训练模型
- **Scikit-learn**: 1.3.2 - 机器学习库

### 图像处理
- **OpenCV**: ******** - 计算机视觉库
- **Pillow**: 10.0.1 - 图像处理库
- **Scikit-image**: 0.22.0 - 图像分析
- **Albumentations**: 1.3.1 - 图像增强

### 科学计算
- **NumPy**: 1.24.3 - 数值计算
- **SciPy**: 1.11.4 - 科学计算
- **Pandas**: 2.1.3 - 数据分析

### 数据可视化
- **Matplotlib**: 3.8.2 - 绘图库
- **Seaborn**: 0.13.0 - 统计可视化
- **Plotly**: 5.17.0 - 交互式图表

### 异步任务
- **Celery**: 5.3.4 - 分布式任务队列
- **Redis**: 5.0.1 - 消息代理

### 数据库
- **AsyncPG**: 0.29.0 - 异步PostgreSQL客户端
- **SQLAlchemy**: 2.0.23 - ORM框架
- **Alembic**: 1.12.1 - 数据库迁移

### 模型管理
- **MLflow**: 2.8.1 - 机器学习生命周期管理
- **BentoML**: 1.1.10 - 模型服务化

### 监控和日志
- **Loguru**: 0.7.2 - 日志记录
- **Prometheus Client**: 0.19.0 - 监控指标
- **Sentry SDK**: 1.38.0 - 错误追踪

### 开发工具
- **Pytest**: 7.4.3 - 测试框架
- **Black**: 23.11.0 - 代码格式化
- **Flake8**: 6.1.0 - 代码检查
- **MyPy**: 1.7.1 - 类型检查

---

## 🗄️ 数据库技术栈

### 主数据库
- **PostgreSQL**: 15-alpine - 关系型数据库
  - 端口: 6432
  - 特性: ACID事务、JSON支持、全文搜索
  - 用途: 业务数据存储、用户管理、配方管理

### 缓存数据库
- **Redis**: 7-alpine - 内存数据库
  - 端口: 6479
  - 特性: 高性能、数据结构丰富
  - 用途: 会话缓存、任务队列、实时数据

### 数据持久化
- **Docker Volumes**: 数据卷持久化
  - postgres_data: PostgreSQL数据
  - redis_data: Redis数据
  - ai_models: AI模型缓存

---

## 🐳 基础设施技术栈

### 容器化
- **Docker**: 容器化平台
- **Docker Compose**: 多容器编排
- **Alpine Linux**: 轻量级基础镜像

### 网络配置
- **Docker Network**: bridge网络模式
- **服务发现**: 容器间通信
- **端口映射**: 外部访问配置

### 健康检查
- **PostgreSQL**: pg_isready检查
- **Redis**: redis-cli ping检查
- **服务依赖**: depends_on配置

---

## 🛠️ 开发工具链

### 版本控制
- **Git**: 分布式版本控制
- **GitHub**: 代码托管平台

### 包管理
- **npm**: Node.js包管理器
- **npm workspaces**: 单仓库多包管理
- **Conda**: Python环境管理

### 代码质量
- **ESLint**: JavaScript/TypeScript代码检查
- **Prettier**: 代码格式化
- **TypeScript**: 静态类型检查
- **SonarLint**: 代码质量分析

### 测试框架
- **Vitest**: 前端单元测试
- **Jest**: 后端单元测试
- **Pytest**: Python测试框架
- **Testing Library**: React组件测试

### 构建工具
- **Vite**: 前端构建工具
- **TypeScript Compiler**: 后端编译
- **Webpack**: 打包工具（备选）

### 环境管理
- **direnv**: 自动环境变量加载
- **dotenv**: 环境变量管理
- **Conda**: Python虚拟环境

---

## 🚀 部署架构

### 开发环境
- **本地开发**: npm workspaces + Docker Compose
- **热重载**: Vite HMR + Nodemon
- **环境隔离**: Docker容器化

### 生产环境（规划）
- **容器编排**: Kubernetes
- **负载均衡**: Nginx/Ingress
- **数据库**: 托管PostgreSQL + Redis
- **文件存储**: 对象存储服务
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack

### CI/CD流程（规划）
- **代码检查**: ESLint + Prettier
- **自动测试**: Jest + Vitest + Pytest
- **构建镜像**: Docker Build
- **部署**: Kubernetes Rolling Update

---

## 📦 版本信息

### 当前版本
- **项目版本**: v1.0.0
- **Node.js要求**: >=18.0.0
- **Python要求**: >=3.12.0
- **npm要求**: >=9.0.0

### 主要依赖版本
| 技术栈 | 组件 | 版本 |
|--------|------|------|
| 前端 | React | 18.3.1 |
| 前端 | TypeScript | 5.8.3 |
| 前端 | Vite | 7.0.5 |
| 前端 | Ant Design | 5.26.5 |
| 后端 | Express | 5.1.0 |
| 后端 | Prisma | 6.12.0 |
| 数据库 | PostgreSQL | 15 |
| 数据库 | Redis | 7 |
| AI | FastAPI | 0.104.1 |
| AI | TensorFlow | 2.14.0 |

---

## 🎯 技术选型理由

### 前端技术选型
- **React**: 组件化开发，生态丰富，团队熟悉
- **TypeScript**: 类型安全，减少运行时错误
- **Vite**: 快速构建，优秀的开发体验
- **Ant Design**: 企业级UI，组件完整

### 后端技术选型
- **Node.js**: JavaScript全栈，开发效率高
- **Express**: 成熟稳定，中间件丰富
- **Prisma**: 类型安全的ORM，开发体验好
- **PostgreSQL**: 功能强大，支持JSON，适合复杂查询

### AI服务选型
- **Python**: AI/ML生态最完善
- **FastAPI**: 现代化框架，自动API文档
- **TensorFlow**: 成熟的深度学习框架
- **OpenAI**: 先进的多模态AI能力

### 基础设施选型
- **Docker**: 环境一致性，部署简化
- **Redis**: 高性能缓存，支持多种数据结构
- **PostgreSQL**: 企业级数据库，ACID保证

---

## 📊 性能指标

### 前端性能
- **首屏加载时间**: < 2秒
- **路由切换**: < 500ms
- **图表渲染**: < 1秒
- **文件上传**: 支持大文件分片上传

### 后端性能
- **API响应时间**: < 200ms (95%分位)
- **并发处理**: 1000+ QPS
- **数据库连接池**: 20个连接
- **缓存命中率**: > 90%

### AI服务性能
- **图像分析**: < 5秒/张
- **模型推理**: < 2秒
- **批量处理**: 支持队列处理
- **GPU加速**: 支持CUDA加速

---

## 🔒 安全特性

### 认证授权
- **JWT认证**: 无状态认证机制
- **角色权限**: RBAC权限控制
- **多租户隔离**: 数据完全隔离
- **会话管理**: Redis会话存储

### 数据安全
- **传输加密**: HTTPS/TLS 1.3
- **存储加密**: 敏感数据加密存储
- **SQL注入防护**: Prisma ORM防护
- **XSS防护**: CSP策略

### API安全
- **请求限流**: 防止API滥用
- **CORS配置**: 跨域访问控制
- **输入验证**: 严格的数据验证
- **错误处理**: 不泄露敏感信息

---

## 📈 监控体系

### 应用监控
- **性能监控**: Prometheus指标收集
- **错误追踪**: Sentry错误监控
- **日志管理**: Winston结构化日志
- **健康检查**: 服务状态监控

### 基础设施监控
- **容器监控**: Docker容器状态
- **资源监控**: CPU、内存、磁盘
- **网络监控**: 网络延迟、吞吐量
- **数据库监控**: 连接数、查询性能

### 业务监控
- **用户行为**: 页面访问、功能使用
- **业务指标**: 分析次数、成功率
- **异常检测**: 业务异常告警
- **性能分析**: 关键路径性能

---

## 🔄 开发流程

### 代码规范
- **命名规范**: 驼峰命名、语义化
- **注释规范**: JSDoc、Python docstring
- **提交规范**: Conventional Commits
- **分支策略**: Git Flow工作流

### 质量保证
- **代码审查**: Pull Request必须审查
- **自动化测试**: 单元测试、集成测试
- **代码覆盖率**: > 80%覆盖率要求
- **性能测试**: 关键接口性能测试

### 发布流程
- **版本管理**: 语义化版本控制
- **环境部署**: dev -> staging -> prod
- **回滚机制**: 快速回滚能力
- **发布通知**: 自动化发布通知

---

## 🚀 扩展性设计

### 水平扩展
- **无状态设计**: 服务无状态，易于扩展
- **负载均衡**: 支持多实例部署
- **数据库分片**: 支持数据库水平分片
- **缓存集群**: Redis集群支持

### 功能扩展
- **插件架构**: 支持功能插件扩展
- **API版本**: 支持API版本管理
- **多语言**: 国际化支持
- **移动端**: React Native扩展

### 技术演进
- **微服务**: 支持微服务架构演进
- **云原生**: Kubernetes部署支持
- **边缘计算**: 边缘AI推理支持
- **实时通信**: WebSocket实时功能

---

## 📚 学习资源

### 官方文档
- [React官方文档](https://react.dev/)
- [TypeScript官方文档](https://www.typescriptlang.org/)
- [Ant Design官方文档](https://ant.design/)
- [Express官方文档](https://expressjs.com/)
- [Prisma官方文档](https://www.prisma.io/)
- [FastAPI官方文档](https://fastapi.tiangolo.com/)

### 最佳实践
- [React最佳实践](https://react.dev/learn)
- [Node.js最佳实践](https://github.com/goldbergyoni/nodebestpractices)
- [Python最佳实践](https://docs.python-guide.org/)
- [Docker最佳实践](https://docs.docker.com/develop/dev-best-practices/)

---

## 🤝 贡献指南

### 开发环境搭建
1. 克隆项目: `git clone <repository>`
2. 安装依赖: `npm install`
3. 配置环境: 复制`.env.example`到`.env`
4. 启动服务: `npm run dev:all`

### 代码贡献
1. Fork项目到个人仓库
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交代码: `git commit -m "feat: add new feature"`
4. 推送分支: `git push origin feature/new-feature`
5. 创建Pull Request

### 问题反馈
- 使用GitHub Issues报告问题
- 提供详细的问题描述和复现步骤
- 包含环境信息和错误日志

---

## 📞 联系信息

**开发公司**: 海南长小养智能科技有限责任公司
**项目仓库**: https://github.com/changxiaoyangbrain/yoghurt-ai-qc
**技术支持**: 通过GitHub Issues联系
**商务合作**: 请通过官方渠道联系

---

## 📝 更新日志

### v1.0.0 (2025-08-02)
- ✅ 完成基础架构搭建
- ✅ 实现用户认证系统
- ✅ 完成多租户架构
- ✅ 集成AI分析服务
- ✅ 实现数据可视化
- ✅ 完成登录页面优化

### 计划中的功能
- 🔄 移动端适配
- 🔄 高级AI功能
- 🔄 实时协作功能
- 🔄 数据导出功能
- 🔄 API开放平台

---

**文档维护**: 本文档将随项目发展持续更新
**联系方式**: 海南长小养智能科技有限责任公司
**最后更新**: 2025年8月2日
**文档版本**: v2.0
