# 生成超高清图片（推荐）
npm run docs:diagrams:hd

# 完整初始化数据库
npm run db:init

# 查看数据库状态
npm run db:init:status

# 打开数据库管理界面
npm run db:studio

默认测试账户：

管理员：<EMAIL> (密码：password123)
普通用户：<EMAIL> (密码：password123)
咖啡厅用户：<EMAIL> (密码：password123)
观察员：<EMAIL> (密码：password123)

# 数据库管理
npm run db:studio          # 打开数据库管理界面
npm run db:init:status      # 检查数据库状态
npm run migrate:status      # 检查迁移状态

# 数据操作
npm run db:seed:status      # 查看数据统计
npm run db:seed:test-users  # 创建更多测试用户
npm run db:seed:sample-data # 创建示例数据

# 数据库维护
npm run db:backup          # 备份数据库
npm run db:init:reset      # 重置数据库

cd backend && bash start-studio.sh