# 安全架构与合规策略

**文档版本**: v1.0  
**创建日期**: 2025年01月  
**负责人**: 安全团队、技术团队  
**审核人**: CISO、技术负责人

---

## 1. 文档概述

### 1.1 文档目的

本文档定义了Yogurt-AI-QC系统的全面安全架构，包括零信任安全模型、数据保护策略、身份认证与授权、网络安全、应用安全、合规性要求等方面的具体实施方案。

### 1.2 安全目标

- **机密性**: 确保敏感数据只能被授权用户访问
- **完整性**: 保证数据在传输和存储过程中不被篡改
- **可用性**: 确保系统服务的持续可用性
- **可审计性**: 建立完整的安全审计和监控体系
- **合规性**: 满足相关法规和行业标准要求

### 1.3 安全原则

- **零信任**: 永不信任，始终验证
- **最小权限**: 用户和系统只获得完成任务所需的最小权限
- **深度防御**: 多层安全控制措施
- **安全左移**: 在开发生命周期早期集成安全
- **持续监控**: 实时安全监控和威胁检测

---

## 2. 零信任安全架构

### 2.1 零信任模型概述

```mermaid
graph TB
    subgraph "零信任安全架构"
        User["用户/设备"]
        IAM["身份认证<br/>Identity & Access Management"]
        PEP["策略执行点<br/>Policy Enforcement Point"]
        PDP["策略决策点<br/>Policy Decision Point"]
        
        subgraph "受保护资源"
            API["API网关"]
            App["应用服务"]
            Data["数据存储"]
            AI["AI服务"]
        end
        
        subgraph "安全控制"
            MFA["多因子认证"]
            Device["设备信任"]
            Network["网络分段"]
            Encrypt["端到端加密"]
            Monitor["持续监控"]
        end
    end
    
    User --> IAM
    IAM --> MFA
    IAM --> Device
    IAM --> PEP
    PEP --> PDP
    PDP --> API
    API --> App
    API --> Data
    API --> AI
    
    Network -.-> App
    Encrypt -.-> Data
    Monitor -.-> PEP
    
    style IAM fill:#ff9999
    style PEP fill:#99ccff
    style PDP fill:#99ff99
    style Monitor fill:#ffcc99
```

### 2.2 身份认证与授权 (IAM)

#### 2.2.1 多因子认证 (MFA)

**实施策略**:
- 所有用户账户强制启用MFA
- 支持多种认证因子：SMS、TOTP、硬件密钥
- 管理员账户要求硬件密钥认证

**技术实现**:

```typescript
// backend/src/auth/MFAService.ts
import speakeasy from 'speakeasy';
import QRCode from 'qrcode';
import { User } from '../entities/User';
import { MFAToken } from '../entities/MFAToken';

export class MFAService {
  async setupTOTP(user: User): Promise<{ secret: string; qrCode: string }> {
    const secret = speakeasy.generateSecret({
      name: `Yogurt QC (${user.email})`,
      issuer: 'Yogurt AI QC System',
      length: 32,
    });

    // 保存密钥到数据库
    await this.saveMFASecret(user.id, secret.base32);

    // 生成QR码
    const qrCode = await QRCode.toDataURL(secret.otpauth_url!);

    return {
      secret: secret.base32,
      qrCode,
    };
  }

  async verifyTOTP(userId: string, token: string): Promise<boolean> {
    const mfaToken = await this.getMFAToken(userId);
    if (!mfaToken) {
      throw new Error('MFA not configured for user');
    }

    const verified = speakeasy.totp.verify({
      secret: mfaToken.secret,
      encoding: 'base32',
      token,
      window: 2, // 允许时间窗口偏差
    });

    if (verified) {
      // 记录成功的MFA验证
      await this.logMFAVerification(userId, 'TOTP', true);
    } else {
      // 记录失败的MFA验证
      await this.logMFAVerification(userId, 'TOTP', false);
      
      // 检查是否需要锁定账户
      await this.checkBruteForceProtection(userId);
    }

    return verified;
  }

  async sendSMSCode(userId: string, phoneNumber: string): Promise<void> {
    const code = this.generateSMSCode();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5分钟过期

    // 保存SMS验证码
    await this.saveSMSCode(userId, code, expiresAt);

    // 发送SMS (集成第三方SMS服务)
    await this.smsProvider.send(phoneNumber, 
      `Your Yogurt QC verification code is: ${code}. Valid for 5 minutes.`
    );

    // 记录SMS发送
    await this.logMFAAction(userId, 'SMS_SENT', phoneNumber);
  }

  async verifySMSCode(userId: string, code: string): Promise<boolean> {
    const smsCode = await this.getSMSCode(userId);
    
    if (!smsCode || smsCode.expiresAt < new Date()) {
      await this.logMFAVerification(userId, 'SMS', false, 'EXPIRED');
      return false;
    }

    const verified = smsCode.code === code;
    
    if (verified) {
      // 删除已使用的验证码
      await this.deleteSMSCode(userId);
      await this.logMFAVerification(userId, 'SMS', true);
    } else {
      await this.logMFAVerification(userId, 'SMS', false, 'INVALID_CODE');
      await this.checkBruteForceProtection(userId);
    }

    return verified;
  }

  private async checkBruteForceProtection(userId: string): Promise<void> {
    const failedAttempts = await this.getFailedMFAAttempts(userId, 15); // 15分钟内
    
    if (failedAttempts >= 5) {
      // 锁定账户15分钟
      await this.lockAccount(userId, 15 * 60 * 1000);
      
      // 发送安全警报
      await this.sendSecurityAlert(userId, 'ACCOUNT_LOCKED_MFA_FAILURES');
    }
  }

  private generateSMSCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }
}
```

#### 2.2.2 基于角色的访问控制 (RBAC)

**角色定义**:

```typescript
// backend/src/auth/roles.ts
export enum Role {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  QUALITY_MANAGER = 'quality_manager',
  PRODUCTION_USER = 'production_user',
  VIEWER = 'viewer',
  API_CLIENT = 'api_client',
}

export enum Permission {
  // 用户管理
  USER_CREATE = 'user:create',
  USER_READ = 'user:read',
  USER_UPDATE = 'user:update',
  USER_DELETE = 'user:delete',
  
  // 配方管理
  RECIPE_CREATE = 'recipe:create',
  RECIPE_READ = 'recipe:read',
  RECIPE_UPDATE = 'recipe:update',
  RECIPE_DELETE = 'recipe:delete',
  
  // 批次管理
  BATCH_CREATE = 'batch:create',
  BATCH_READ = 'batch:read',
  BATCH_UPDATE = 'batch:update',
  BATCH_DELETE = 'batch:delete',
  
  // AI分析
  AI_ANALYZE = 'ai:analyze',
  AI_RESULTS_READ = 'ai:results:read',
  AI_MODEL_MANAGE = 'ai:model:manage',
  
  // 质量评估
  QUALITY_ASSESS = 'quality:assess',
  QUALITY_READ = 'quality:read',
  QUALITY_APPROVE = 'quality:approve',
  
  // 系统管理
  SYSTEM_CONFIG = 'system:config',
  SYSTEM_LOGS = 'system:logs',
  SYSTEM_BACKUP = 'system:backup',
}

export const RolePermissions: Record<Role, Permission[]> = {
  [Role.SUPER_ADMIN]: Object.values(Permission),
  
  [Role.ADMIN]: [
    Permission.USER_CREATE,
    Permission.USER_READ,
    Permission.USER_UPDATE,
    Permission.RECIPE_CREATE,
    Permission.RECIPE_READ,
    Permission.RECIPE_UPDATE,
    Permission.RECIPE_DELETE,
    Permission.BATCH_CREATE,
    Permission.BATCH_READ,
    Permission.BATCH_UPDATE,
    Permission.AI_ANALYZE,
    Permission.AI_RESULTS_READ,
    Permission.QUALITY_ASSESS,
    Permission.QUALITY_READ,
    Permission.QUALITY_APPROVE,
    Permission.SYSTEM_LOGS,
  ],
  
  [Role.QUALITY_MANAGER]: [
    Permission.RECIPE_READ,
    Permission.BATCH_READ,
    Permission.BATCH_UPDATE,
    Permission.AI_ANALYZE,
    Permission.AI_RESULTS_READ,
    Permission.QUALITY_ASSESS,
    Permission.QUALITY_READ,
    Permission.QUALITY_APPROVE,
  ],
  
  [Role.PRODUCTION_USER]: [
    Permission.RECIPE_READ,
    Permission.BATCH_CREATE,
    Permission.BATCH_READ,
    Permission.BATCH_UPDATE,
    Permission.AI_ANALYZE,
    Permission.AI_RESULTS_READ,
    Permission.QUALITY_ASSESS,
    Permission.QUALITY_READ,
  ],
  
  [Role.VIEWER]: [
    Permission.RECIPE_READ,
    Permission.BATCH_READ,
    Permission.AI_RESULTS_READ,
    Permission.QUALITY_READ,
  ],
  
  [Role.API_CLIENT]: [
    Permission.RECIPE_READ,
    Permission.BATCH_CREATE,
    Permission.BATCH_READ,
    Permission.AI_ANALYZE,
    Permission.AI_RESULTS_READ,
  ],
};
```

**权限验证中间件**:

```typescript
// backend/src/middleware/authMiddleware.ts
import { Request, Response, NextFunction } from 'express';
import { JWTService } from '../auth/JWTService';
import { Permission, RolePermissions } from '../auth/roles';
import { AuditService } from '../services/AuditService';

export function requireAuth(req: Request, res: Response, next: NextFunction) {
  const token = req.headers.authorization?.replace('Bearer ', '');
  
  if (!token) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  try {
    const payload = JWTService.verify(token);
    req.user = payload;
    next();
  } catch (error) {
    return res.status(401).json({ error: 'Invalid token' });
  }
}

export function requirePermission(permission: Permission) {
  return async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userPermissions = RolePermissions[req.user.role] || [];
    
    if (!userPermissions.includes(permission)) {
      // 记录未授权访问尝试
      await AuditService.logUnauthorizedAccess({
        userId: req.user.id,
        permission,
        endpoint: req.path,
        method: req.method,
        ip: req.ip,
        userAgent: req.headers['user-agent'],
      });
      
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        required: permission 
      });
    }

    next();
  };
}

export function requireMFA(req: Request, res: Response, next: NextFunction) {
  if (!req.user?.mfaVerified) {
    return res.status(403).json({ 
      error: 'MFA verification required',
      mfaRequired: true 
    });
  }
  
  next();
}
```

### 2.3 网络安全

#### 2.3.1 网络分段策略

```yaml
# kubernetes/network-policies/default-deny.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all
  namespace: yogurt-qc
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
---
# 允许前端访问后端API
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: frontend-to-backend
  namespace: yogurt-qc
spec:
  podSelector:
    matchLabels:
      app: backend
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: frontend
    ports:
    - protocol: TCP
      port: 3000
---
# 允许后端访问数据库
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: backend-to-database
  namespace: yogurt-qc
spec:
  podSelector:
    matchLabels:
      app: postgres
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: backend
    ports:
    - protocol: TCP
      port: 5432
---
# 允许AI服务访问
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: ai-service-access
  namespace: yogurt-qc
spec:
  podSelector:
    matchLabels:
      app: ai-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: backend
    ports:
    - protocol: TCP
      port: 8000
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  - to: []  # 允许访问外部AI模型API
    ports:
    - protocol: TCP
      port: 443
```

#### 2.3.2 WAF (Web Application Firewall) 配置

```yaml
# terraform/waf.tf
resource "aws_wafv2_web_acl" "yogurt_qc_waf" {
  name  = "yogurt-qc-waf"
  scope = "REGIONAL"

  default_action {
    allow {}
  }

  # SQL注入防护
  rule {
    name     = "SQLInjectionRule"
    priority = 1

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesSQLiRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "SQLInjectionRule"
      sampled_requests_enabled   = true
    }
  }

  # XSS防护
  rule {
    name     = "XSSRule"
    priority = 2

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "XSSRule"
      sampled_requests_enabled   = true
    }
  }

  # 速率限制
  rule {
    name     = "RateLimitRule"
    priority = 3

    action {
      block {}
    }

    statement {
      rate_based_statement {
        limit              = 2000
        aggregate_key_type = "IP"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "RateLimitRule"
      sampled_requests_enabled   = true
    }
  }

  # 地理位置限制
  rule {
    name     = "GeoBlockRule"
    priority = 4

    action {
      block {}
    }

    statement {
      geo_match_statement {
        country_codes = ["CN", "RU", "KP"]  # 根据需要调整
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "GeoBlockRule"
      sampled_requests_enabled   = true
    }
  }

  tags = {
    Environment = var.environment
    Project     = "yogurt-qc"
  }
}
```

---

## 3. 数据保护策略

### 3.1 数据分类与标记

**数据分类标准**:

```typescript
// backend/src/types/DataClassification.ts
export enum DataClassification {
  PUBLIC = 'public',           // 公开数据
  INTERNAL = 'internal',       // 内部数据
  CONFIDENTIAL = 'confidential', // 机密数据
  RESTRICTED = 'restricted',   // 限制数据
}

export enum DataType {
  USER_PII = 'user_pii',           // 用户个人信息
  BUSINESS_DATA = 'business_data', // 业务数据
  TECHNICAL_DATA = 'technical_data', // 技术数据
  AUDIT_LOG = 'audit_log',         // 审计日志
  AI_MODEL = 'ai_model',           // AI模型数据
}

export interface DataProtectionPolicy {
  classification: DataClassification;
  dataType: DataType;
  encryptionRequired: boolean;
  retentionPeriod: number; // 天数
  accessControls: string[];
  auditRequired: boolean;
  backupRequired: boolean;
  anonymizationRequired: boolean;
}

export const DataProtectionPolicies: Record<string, DataProtectionPolicy> = {
  'user_profile': {
    classification: DataClassification.CONFIDENTIAL,
    dataType: DataType.USER_PII,
    encryptionRequired: true,
    retentionPeriod: 2555, // 7年
    accessControls: ['user:read', 'admin:read'],
    auditRequired: true,
    backupRequired: true,
    anonymizationRequired: true,
  },
  
  'recipe_data': {
    classification: DataClassification.INTERNAL,
    dataType: DataType.BUSINESS_DATA,
    encryptionRequired: true,
    retentionPeriod: 1825, // 5年
    accessControls: ['recipe:read'],
    auditRequired: true,
    backupRequired: true,
    anonymizationRequired: false,
  },
  
  'ai_analysis_results': {
    classification: DataClassification.CONFIDENTIAL,
    dataType: DataType.BUSINESS_DATA,
    encryptionRequired: true,
    retentionPeriod: 3650, // 10年
    accessControls: ['ai:results:read'],
    auditRequired: true,
    backupRequired: true,
    anonymizationRequired: false,
  },
  
  'audit_logs': {
    classification: DataClassification.RESTRICTED,
    dataType: DataType.AUDIT_LOG,
    encryptionRequired: true,
    retentionPeriod: 2555, // 7年
    accessControls: ['system:logs'],
    auditRequired: false, // 审计日志本身不需要审计
    backupRequired: true,
    anonymizationRequired: false,
  },
};
```

### 3.2 加密策略

#### 3.2.1 传输加密

**TLS配置**:

```nginx
# nginx/ssl.conf
server {
    listen 443 ssl http2;
    server_name api.yogurt-qc.com;

    # SSL证书配置
    ssl_certificate /etc/ssl/certs/yogurt-qc.crt;
    ssl_certificate_key /etc/ssl/private/yogurt-qc.key;

    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    
    # 其他安全头
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self' https://api.yogurt-qc.com; frame-ancestors 'none';" always;
    
    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    ssl_trusted_certificate /etc/ssl/certs/ca-certificates.crt;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;

    location / {
        proxy_pass http://backend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 安全头
        proxy_hide_header X-Powered-By;
        proxy_hide_header Server;
    }
}

# 强制HTTPS重定向
server {
    listen 80;
    server_name api.yogurt-qc.com;
    return 301 https://$server_name$request_uri;
}
```

#### 3.2.2 静态数据加密

**数据库加密**:

```typescript
// backend/src/utils/encryption.ts
import crypto from 'crypto';
import { promisify } from 'util';

const scrypt = promisify(crypto.scrypt);

export class EncryptionService {
  private static readonly ALGORITHM = 'aes-256-gcm';
  private static readonly KEY_LENGTH = 32;
  private static readonly IV_LENGTH = 16;
  private static readonly TAG_LENGTH = 16;
  private static readonly SALT_LENGTH = 32;

  // 从环境变量获取主密钥
  private static getMasterKey(): Buffer {
    const masterKey = process.env.ENCRYPTION_MASTER_KEY;
    if (!masterKey) {
      throw new Error('ENCRYPTION_MASTER_KEY environment variable is required');
    }
    return Buffer.from(masterKey, 'hex');
  }

  // 派生加密密钥
  private static async deriveKey(salt: Buffer): Promise<Buffer> {
    const masterKey = this.getMasterKey();
    return (await scrypt(masterKey, salt, this.KEY_LENGTH)) as Buffer;
  }

  // 加密敏感数据
  static async encrypt(plaintext: string): Promise<string> {
    const salt = crypto.randomBytes(this.SALT_LENGTH);
    const iv = crypto.randomBytes(this.IV_LENGTH);
    const key = await this.deriveKey(salt);

    const cipher = crypto.createCipher(this.ALGORITHM, key, iv);
    
    let encrypted = cipher.update(plaintext, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const tag = cipher.getAuthTag();

    // 组合: salt + iv + tag + encrypted
    const combined = Buffer.concat([
      salt,
      iv,
      tag,
      Buffer.from(encrypted, 'hex')
    ]);

    return combined.toString('base64');
  }

  // 解密敏感数据
  static async decrypt(encryptedData: string): Promise<string> {
    const combined = Buffer.from(encryptedData, 'base64');
    
    const salt = combined.slice(0, this.SALT_LENGTH);
    const iv = combined.slice(this.SALT_LENGTH, this.SALT_LENGTH + this.IV_LENGTH);
    const tag = combined.slice(
      this.SALT_LENGTH + this.IV_LENGTH,
      this.SALT_LENGTH + this.IV_LENGTH + this.TAG_LENGTH
    );
    const encrypted = combined.slice(this.SALT_LENGTH + this.IV_LENGTH + this.TAG_LENGTH);

    const key = await this.deriveKey(salt);
    const decipher = crypto.createDecipher(this.ALGORITHM, key, iv);
    decipher.setAuthTag(tag);

    let decrypted = decipher.update(encrypted, null, 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }

  // 哈希密码
  static async hashPassword(password: string): Promise<string> {
    const salt = crypto.randomBytes(16);
    const hash = await scrypt(password, salt, 64) as Buffer;
    return salt.toString('hex') + ':' + hash.toString('hex');
  }

  // 验证密码
  static async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    const [saltHex, hashHex] = hashedPassword.split(':');
    const salt = Buffer.from(saltHex, 'hex');
    const hash = Buffer.from(hashHex, 'hex');
    
    const derivedHash = await scrypt(password, salt, 64) as Buffer;
    return crypto.timingSafeEqual(hash, derivedHash);
  }

  // 生成安全的随机令牌
  static generateSecureToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }
}
```

**数据库字段加密装饰器**:

```typescript
// backend/src/decorators/Encrypted.ts
import { Transform } from 'class-transformer';
import { EncryptionService } from '../utils/encryption';

export function Encrypted() {
  return Transform({
    to: async (value: string) => {
      if (!value) return value;
      return await EncryptionService.encrypt(value);
    },
    from: async (value: string) => {
      if (!value) return value;
      return await EncryptionService.decrypt(value);
    },
  });
}

// 使用示例
export class User {
  @Column()
  public email!: string;

  @Column()
  @Encrypted()
  public phoneNumber?: string;

  @Column()
  @Encrypted()
  public personalNotes?: string;
}
```

### 3.3 数据脱敏与匿名化

```typescript
// backend/src/utils/DataAnonymization.ts
export class DataAnonymizationService {
  // 邮箱脱敏
  static maskEmail(email: string): string {
    const [username, domain] = email.split('@');
    const maskedUsername = username.length > 2 
      ? username[0] + '*'.repeat(username.length - 2) + username[username.length - 1]
      : '*'.repeat(username.length);
    return `${maskedUsername}@${domain}`;
  }

  // 手机号脱敏
  static maskPhoneNumber(phone: string): string {
    if (phone.length < 4) return '*'.repeat(phone.length);
    return phone.slice(0, 3) + '*'.repeat(phone.length - 6) + phone.slice(-3);
  }

  // 姓名脱敏
  static maskName(name: string): string {
    if (name.length <= 1) return '*';
    if (name.length === 2) return name[0] + '*';
    return name[0] + '*'.repeat(name.length - 2) + name[name.length - 1];
  }

  // IP地址脱敏
  static maskIPAddress(ip: string): string {
    const parts = ip.split('.');
    if (parts.length === 4) {
      return `${parts[0]}.${parts[1]}.*.* `;
    }
    return ip;
  }

  // 数据匿名化（用于分析和报告）
  static anonymizeUserData(userData: any): any {
    return {
      ...userData,
      id: this.generateAnonymousId(userData.id),
      email: this.maskEmail(userData.email),
      name: this.maskName(userData.name),
      phoneNumber: userData.phoneNumber ? this.maskPhoneNumber(userData.phoneNumber) : null,
      // 移除其他敏感字段
      passwordHash: undefined,
      personalNotes: undefined,
    };
  }

  // 生成匿名ID
  private static generateAnonymousId(originalId: string): string {
    const hash = crypto.createHash('sha256');
    hash.update(originalId + process.env.ANONYMIZATION_SALT);
    return hash.digest('hex').substring(0, 16);
  }

  // 批量数据匿名化
  static anonymizeDataset(dataset: any[], fields: string[]): any[] {
    return dataset.map(item => {
      const anonymized = { ...item };
      
      fields.forEach(field => {
        if (anonymized[field]) {
          switch (field) {
            case 'email':
              anonymized[field] = this.maskEmail(anonymized[field]);
              break;
            case 'phoneNumber':
              anonymized[field] = this.maskPhoneNumber(anonymized[field]);
              break;
            case 'name':
              anonymized[field] = this.maskName(anonymized[field]);
              break;
            default:
              anonymized[field] = '*'.repeat(anonymized[field].length);
          }
        }
      });
      
      return anonymized;
    });
  }
}
```

---

## 4. 应用安全

### 4.1 输入验证与清理

```typescript
// backend/src/validation/SecurityValidation.ts
import Joi from 'joi';
import DOMPurify from 'isomorphic-dompurify';
import { Request, Response, NextFunction } from 'express';

export class SecurityValidation {
  // 通用安全验证规则
  static readonly commonRules = {
    // 防止SQL注入的字符串验证
    safeString: Joi.string()
      .pattern(/^[a-zA-Z0-9\s\-_.,!?()\[\]{}:;"'@#$%^&*+=<>\/\\|~`]*$/)
      .message('String contains potentially dangerous characters'),
    
    // 邮箱验证
    email: Joi.string()
      .email({ tlds: { allow: false } })
      .max(254)
      .lowercase(),
    
    // 密码强度验证
    password: Joi.string()
      .min(12)
      .max(128)
      .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&].*$/)
      .message('Password must contain at least 12 characters with uppercase, lowercase, number and special character'),
    
    // 用户名验证
    username: Joi.string()
      .alphanum()
      .min(3)
      .max(30),
    
    // URL验证
    url: Joi.string()
      .uri({ scheme: ['http', 'https'] })
      .max(2048),
    
    // 文件名验证
    filename: Joi.string()
      .pattern(/^[a-zA-Z0-9._-]+$/)
      .max(255),
  };

  // 配方数据验证
  static readonly recipeSchema = Joi.object({
    name: this.commonRules.safeString.required().max(100),
    description: this.commonRules.safeString.max(1000),
    ingredients: Joi.array()
      .items(this.commonRules.safeString.max(100))
      .min(1)
      .max(50),
    fermentationTime: Joi.number().integer().min(1).max(168), // 最多7天
    temperature: Joi.number().min(0).max(100),
    notes: this.commonRules.safeString.max(2000),
  });

  // 批次数据验证
  static readonly batchSchema = Joi.object({
    recipeId: Joi.string().uuid().required(),
    batchNumber: Joi.string().alphanum().max(50).required(),
    startDate: Joi.date().iso().required(),
    expectedEndDate: Joi.date().iso().min(Joi.ref('startDate')),
    notes: this.commonRules.safeString.max(2000),
    qualityTargets: Joi.object({
      minBacteriaCount: Joi.number().min(0),
      maxBacteriaCount: Joi.number().min(Joi.ref('minBacteriaCount')),
      targetPH: Joi.number().min(0).max(14),
    }),
  });

  // 用户注册验证
  static readonly userRegistrationSchema = Joi.object({
    email: this.commonRules.email.required(),
    password: this.commonRules.password.required(),
    name: this.commonRules.safeString.required().max(100),
    phoneNumber: Joi.string()
      .pattern(/^\+?[1-9]\d{1,14}$/)
      .message('Invalid phone number format'),
    role: Joi.string().valid('production_user', 'viewer').default('production_user'),
  });

  // XSS防护 - HTML清理
  static sanitizeHTML(input: string): string {
    return DOMPurify.sanitize(input, {
      ALLOWED_TAGS: [], // 不允许任何HTML标签
      ALLOWED_ATTR: [],
    });
  }

  // SQL注入防护 - 参数化查询验证
  static validateSQLParams(params: any): boolean {
    const dangerousPatterns = [
      /('|(\-\-)|(;)|(\||\|)|(\*|\*))/i,
      /(exec(\s|\+)+(s|x)p\w+)/i,
      /union.*select/i,
      /insert.*into/i,
      /delete.*from/i,
      /update.*set/i,
      /drop.*table/i,
    ];

    const stringParams = JSON.stringify(params);
    return !dangerousPatterns.some(pattern => pattern.test(stringParams));
  }

  // 文件上传安全验证
  static validateFileUpload(file: Express.Multer.File): { valid: boolean; error?: string } {
    // 检查文件类型
    const allowedMimeTypes = [
      'image/jpeg',
      'image/png',
      'image/tiff',
      'application/pdf',
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      return { valid: false, error: 'File type not allowed' };
    }

    // 检查文件大小 (50MB)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      return { valid: false, error: 'File size too large' };
    }

    // 检查文件名
    if (!this.commonRules.filename.validate(file.originalname).error) {
      return { valid: false, error: 'Invalid filename' };
    }

    // 检查文件内容（简单的魔数检查）
    const buffer = file.buffer;
    if (this.containsMaliciousContent(buffer)) {
      return { valid: false, error: 'File contains malicious content' };
    }

    return { valid: true };
  }

  private static containsMaliciousContent(buffer: Buffer): boolean {
    // 检查常见的恶意文件签名
    const maliciousSignatures = [
      Buffer.from([0x4D, 0x5A]), // PE executable
      Buffer.from([0x7F, 0x45, 0x4C, 0x46]), // ELF executable
      Buffer.from([0xCA, 0xFE, 0xBA, 0xBE]), // Java class file
    ];

    return maliciousSignatures.some(signature => 
      buffer.indexOf(signature) === 0
    );
  }
}

// 验证中间件
export function validateRequest(schema: Joi.ObjectSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
      }));

      return res.status(400).json({
        error: 'Validation failed',
        details: errors,
      });
    }

    // 清理HTML内容
    req.body = this.sanitizeObject(value);
    
    // 验证SQL注入
    if (!SecurityValidation.validateSQLParams(req.body)) {
      return res.status(400).json({
        error: 'Request contains potentially dangerous content',
      });
    }

    next();
  };
}

function sanitizeObject(obj: any): any {
  if (typeof obj === 'string') {
    return SecurityValidation.sanitizeHTML(obj);
  }
  
  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject);
  }
  
  if (obj && typeof obj === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      sanitized[key] = sanitizeObject(value);
    }
    return sanitized;
  }
  
  return obj;
}
```

### 4.2 API安全

#### 4.2.1 速率限制

```typescript
// backend/src/middleware/rateLimiter.ts
import rateLimit from 'express-rate-limit';
import RedisStore from 'rate-limit-redis';
import Redis from 'ioredis';
import { Request, Response } from 'express';

const redis = new Redis(process.env.REDIS_URL!);

// 通用速率限制
export const generalLimiter = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args),
  }),
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 每个IP每15分钟最多1000个请求
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: '15 minutes',
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    // 对认证用户使用用户ID，对匿名用户使用IP
    return req.user?.id || req.ip;
  },
});

// 登录接口严格限制
export const loginLimiter = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args),
  }),
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 每个IP每15分钟最多5次登录尝试
  message: {
    error: 'Too many login attempts, please try again later.',
    retryAfter: '15 minutes',
  },
  skipSuccessfulRequests: true, // 成功的请求不计入限制
});

// AI分析接口限制
export const aiAnalysisLimiter = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args),
  }),
  windowMs: 60 * 60 * 1000, // 1小时
  max: 100, // 每个用户每小时最多100次AI分析
  message: {
    error: 'AI analysis quota exceeded, please try again later.',
    retryAfter: '1 hour',
  },
  keyGenerator: (req: Request) => {
    return `ai_analysis:${req.user?.id || req.ip}`;
  },
});

// 文件上传限制
export const uploadLimiter = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args),
  }),
  windowMs: 60 * 60 * 1000, // 1小时
  max: 50, // 每个用户每小时最多50次文件上传
  message: {
    error: 'File upload quota exceeded, please try again later.',
    retryAfter: '1 hour',
  },
});

// 动态速率限制（基于用户行为）
export class DynamicRateLimiter {
  private static suspiciousActivityThreshold = 10;
  
  static async checkSuspiciousActivity(userId: string): Promise<boolean> {
    const key = `suspicious:${userId}`;
    const count = await redis.incr(key);
    
    if (count === 1) {
      await redis.expire(key, 3600); // 1小时过期
    }
    
    return count > this.suspiciousActivityThreshold;
  }
  
  static async recordSuspiciousActivity(userId: string, activity: string): Promise<void> {
    const key = `suspicious:${userId}`;
    await redis.incr(key);
    
    // 记录具体的可疑活动
    const logKey = `suspicious_log:${userId}`;
    await redis.lpush(logKey, JSON.stringify({
      activity,
      timestamp: new Date().toISOString(),
    }));
    await redis.ltrim(logKey, 0, 99); // 保留最近100条记录
    await redis.expire(logKey, 86400); // 24小时过期
  }
  
  static createAdaptiveLimiter(baseLimit: number) {
    return rateLimit({
      store: new RedisStore({
        sendCommand: (...args: string[]) => redis.call(...args),
      }),
      windowMs: 15 * 60 * 1000,
      max: async (req: Request) => {
        if (!req.user) return Math.floor(baseLimit * 0.5); // 未认证用户限制更严格
        
        const isSuspicious = await DynamicRateLimiter.checkSuspiciousActivity(req.user.id);
        return isSuspicious ? Math.floor(baseLimit * 0.2) : baseLimit;
      },
      keyGenerator: (req: Request) => req.user?.id || req.ip,
    });
  }
}
```

#### 4.2.2 API安全头

```typescript
// backend/src/middleware/securityHeaders.ts
import helmet from 'helmet';
import { Request, Response, NextFunction } from 'express';

export const securityHeaders = helmet({
  // Content Security Policy
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"], // 生产环境应移除unsafe-inline
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      fontSrc: ["'self'"],
      connectSrc: ["'self'", "https://api.yogurt-qc.com"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      workerSrc: ["'self'"],
      childSrc: ["'none'"],
      formAction: ["'self'"],
      frameAncestors: ["'none'"],
      baseUri: ["'self'"],
      upgradeInsecureRequests: [],
    },
  },
  
  // HTTP Strict Transport Security
  hsts: {
    maxAge: 31536000, // 1年
    includeSubDomains: true,
    preload: true,
  },
  
  // X-Frame-Options
  frameguard: {
    action: 'deny',
  },
  
  // X-Content-Type-Options
  noSniff: true,
  
  // X-XSS-Protection
  xssFilter: true,
  
  // Referrer Policy
  referrerPolicy: {
    policy: 'strict-origin-when-cross-origin',
  },
  
  // 隐藏X-Powered-By
  hidePoweredBy: true,
  
  // DNS Prefetch Control
  dnsPrefetchControl: {
    allow: false,
  },
  
  // IE No Open
  ieNoOpen: true,
  
  // Don't Sniff Mimetype
  noSniff: true,
});

// 自定义安全头
export function customSecurityHeaders(req: Request, res: Response, next: NextFunction) {
  // API版本头
  res.setHeader('X-API-Version', '1.0');
  
  // 请求ID（用于追踪）
  const requestId = req.headers['x-request-id'] || generateRequestId();
  res.setHeader('X-Request-ID', requestId);
  
  // 安全相关头
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  
  // CORS安全配置
  const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'];
  const origin = req.headers.origin;
  
  if (origin && allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  }
  
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, X-Request-ID');
  res.setHeader('Access-Control-Max-Age', '86400'); // 24小时
  
  // 移除敏感信息
  res.removeHeader('X-Powered-By');
  res.removeHeader('Server');
  
  next();
}

function generateRequestId(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}
```

---

## 5. 安全监控与审计

### 5.1 安全事件监控

```typescript
// backend/src/services/SecurityMonitoringService.ts
import { EventEmitter } from 'events';
import { AuditService } from './AuditService';
import { AlertService } from './AlertService';

export enum SecurityEventType {
  FAILED_LOGIN = 'failed_login',
  SUCCESSFUL_LOGIN = 'successful_login',
  UNAUTHORIZED_ACCESS = 'unauthorized_access',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  DATA_ACCESS = 'data_access',
  PRIVILEGE_ESCALATION = 'privilege_escalation',
  ACCOUNT_LOCKED = 'account_locked',
  PASSWORD_CHANGED = 'password_changed',
  MFA_ENABLED = 'mfa_enabled',
  MFA_DISABLED = 'mfa_disabled',
  API_ABUSE = 'api_abuse',
  FILE_UPLOAD = 'file_upload',
  DATA_EXPORT = 'data_export',
}

export interface SecurityEvent {
  type: SecurityEventType;
  userId?: string;
  ip: string;
  userAgent: string;
  timestamp: Date;
  details: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: string;
}

export class SecurityMonitoringService extends EventEmitter {
  private static instance: SecurityMonitoringService;
  private alertThresholds: Map<SecurityEventType, number> = new Map();
  private eventCounts: Map<string, number> = new Map();

  constructor() {
    super();
    this.setupAlertThresholds();
    this.setupEventHandlers();
  }

  static getInstance(): SecurityMonitoringService {
    if (!SecurityMonitoringService.instance) {
      SecurityMonitoringService.instance = new SecurityMonitoringService();
    }
    return SecurityMonitoringService.instance;
  }

  private setupAlertThresholds(): void {
    this.alertThresholds.set(SecurityEventType.FAILED_LOGIN, 5);
    this.alertThresholds.set(SecurityEventType.UNAUTHORIZED_ACCESS, 3);
    this.alertThresholds.set(SecurityEventType.SUSPICIOUS_ACTIVITY, 10);
    this.alertThresholds.set(SecurityEventType.API_ABUSE, 100);
  }

  private setupEventHandlers(): void {
    this.on('securityEvent', this.handleSecurityEvent.bind(this));
  }

  async logSecurityEvent(event: SecurityEvent): Promise<void> {
    // 记录到审计日志
    await AuditService.logSecurityEvent(event);

    // 发出事件供其他组件处理
    this.emit('securityEvent', event);

    // 实时分析和告警
    await this.analyzeEvent(event);
  }

  private async handleSecurityEvent(event: SecurityEvent): Promise<void> {
    console.log(`Security event: ${event.type}`, {
      userId: event.userId,
      ip: event.ip,
      severity: event.severity,
      timestamp: event.timestamp,
    });

    // 根据事件类型和严重程度采取行动
    switch (event.severity) {
      case 'critical':
        await this.handleCriticalEvent(event);
        break;
      case 'high':
        await this.handleHighSeverityEvent(event);
        break;
      case 'medium':
        await this.handleMediumSeverityEvent(event);
        break;
      default:
        // 低严重程度事件只记录
        break;
    }
  }

  private async analyzeEvent(event: SecurityEvent): Promise<void> {
    const key = `${event.type}:${event.ip}:${event.userId || 'anonymous'}`;
    const count = (this.eventCounts.get(key) || 0) + 1;
    this.eventCounts.set(key, count);

    // 检查是否超过阈值
    const threshold = this.alertThresholds.get(event.type);
    if (threshold && count >= threshold) {
      await this.triggerAlert(event, count);
    }

    // 清理过期计数（15分钟窗口）
    setTimeout(() => {
      this.eventCounts.delete(key);
    }, 15 * 60 * 1000);
  }

  private async triggerAlert(event: SecurityEvent, count: number): Promise<void> {
    const alert = {
      type: 'SECURITY_THRESHOLD_EXCEEDED',
      message: `Security event ${event.type} exceeded threshold`,
      details: {
        eventType: event.type,
        count,
        threshold: this.alertThresholds.get(event.type),
        userId: event.userId,
        ip: event.ip,
        timeWindow: '15 minutes',
      },
      severity: 'high' as const,
      timestamp: new Date(),
    };

    await AlertService.sendAlert(alert);
  }

  private async handleCriticalEvent(event: SecurityEvent): Promise<void> {
    // 立即发送紧急告警
    await AlertService.sendEmergencyAlert({
      type: 'CRITICAL_SECURITY_EVENT',
      message: `Critical security event detected: ${event.type}`,
      details: event,
      timestamp: new Date(),
    });

    // 可能需要自动响应措施
    switch (event.type) {
      case SecurityEventType.PRIVILEGE_ESCALATION:
        await this.handlePrivilegeEscalation(event);
        break;
      case SecurityEventType.DATA_EXPORT:
        await this.handleSuspiciousDataExport(event);
        break;
    }
  }

  private async handlePrivilegeEscalation(event: SecurityEvent): Promise<void> {
    if (event.userId) {
      // 立即锁定账户
      await this.lockUserAccount(event.userId, 'PRIVILEGE_ESCALATION_DETECTED');
      
      // 撤销所有活动会话
      await this.revokeAllUserSessions(event.userId);
      
      // 通知安全团队
      await AlertService.notifySecurityTeam({
        type: 'PRIVILEGE_ESCALATION',
        userId: event.userId,
        details: event.details,
      });
    }
  }

  private async handleSuspiciousDataExport(event: SecurityEvent): Promise<void> {
    // 记录数据导出事件
    await AuditService.logDataExport({
      userId: event.userId,
      dataType: event.details.dataType,
      recordCount: event.details.recordCount,
      timestamp: event.timestamp,
      ip: event.ip,
    });

    // 如果导出量异常大，立即告警
    if (event.details.recordCount > 10000) {
      await AlertService.sendAlert({
        type: 'LARGE_DATA_EXPORT',
        message: 'Large data export detected',
        details: event.details,
        severity: 'high',
        timestamp: new Date(),
      });
    }
  }

  // 用户账户锁定
  private async lockUserAccount(userId: string, reason: string): Promise<void> {
    // 实现账户锁定逻辑
    console.log(`Locking user account ${userId} due to ${reason}`);
  }

  // 撤销用户所有会话
  private async revokeAllUserSessions(userId: string): Promise<void> {
    // 实现会话撤销逻辑
    console.log(`Revoking all sessions for user ${userId}`);
  }
}
```

### 5.2 审计日志系统

```typescript
// backend/src/services/AuditService.ts
import { DataSource } from 'typeorm';
import { AuditLog } from '../entities/AuditLog';
import { SecurityEvent } from './SecurityMonitoringService';

export enum AuditEventType {
  USER_LOGIN = 'user_login',
  USER_LOGOUT = 'user_logout',
  USER_CREATED = 'user_created',
  USER_UPDATED = 'user_updated',
  USER_DELETED = 'user_deleted',
  RECIPE_CREATED = 'recipe_created',
  RECIPE_UPDATED = 'recipe_updated',
  RECIPE_DELETED = 'recipe_deleted',
  BATCH_CREATED = 'batch_created',
  BATCH_UPDATED = 'batch_updated',
  AI_ANALYSIS_PERFORMED = 'ai_analysis_performed',
  QUALITY_ASSESSMENT_CREATED = 'quality_assessment_created',
  DATA_EXPORTED = 'data_exported',
  SYSTEM_CONFIG_CHANGED = 'system_config_changed',
  SECURITY_EVENT = 'security_event',
}

export interface AuditLogEntry {
  eventType: AuditEventType;
  userId?: string;
  entityType?: string;
  entityId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  ip: string;
  userAgent: string;
  timestamp: Date;
  details?: Record<string, any>;
}

export class AuditService {
  private static dataSource: DataSource;

  static initialize(dataSource: DataSource): void {
    this.dataSource = dataSource;
  }

  static async logEvent(entry: AuditLogEntry): Promise<void> {
    const auditLog = new AuditLog();
    Object.assign(auditLog, entry);
    
    await this.dataSource.getRepository(AuditLog).save(auditLog);
    
    // 异步发送到外部日志系统（如ELK Stack）
    this.sendToExternalLogging(entry).catch(console.error);
  }

  static async logSecurityEvent(event: SecurityEvent): Promise<void> {
    await this.logEvent({
      eventType: AuditEventType.SECURITY_EVENT,
      userId: event.userId,
      ip: event.ip,
      userAgent: event.userAgent,
      timestamp: event.timestamp,
      details: {
        securityEventType: event.type,
        severity: event.severity,
        source: event.source,
        ...event.details,
      },
    });
  }

  static async logDataExport(exportInfo: {
    userId?: string;
    dataType: string;
    recordCount: number;
    timestamp: Date;
    ip: string;
  }): Promise<void> {
    await this.logEvent({
      eventType: AuditEventType.DATA_EXPORTED,
      userId: exportInfo.userId,
      ip: exportInfo.ip,
      userAgent: 'system',
      timestamp: exportInfo.timestamp,
      details: {
        dataType: exportInfo.dataType,
        recordCount: exportInfo.recordCount,
      },
    });
  }

  static async logUnauthorizedAccess(accessInfo: {
    userId?: string;
    permission: string;
    endpoint: string;
    method: string;
    ip: string;
    userAgent?: string;
  }): Promise<void> {
    await this.logEvent({
      eventType: AuditEventType.SECURITY_EVENT,
      userId: accessInfo.userId,
      ip: accessInfo.ip,
      userAgent: accessInfo.userAgent || 'unknown',
      timestamp: new Date(),
      details: {
        securityEventType: 'unauthorized_access',
        permission: accessInfo.permission,
        endpoint: accessInfo.endpoint,
        method: accessInfo.method,
      },
    });
  }

  private static async sendToExternalLogging(entry: AuditLogEntry): Promise<void> {
    // 发送到ELK Stack或其他日志聚合系统
    try {
      // 示例：发送到Elasticsearch
      const logData = {
        '@timestamp': entry.timestamp.toISOString(),
        event_type: entry.eventType,
        user_id: entry.userId,
        entity_type: entry.entityType,
        entity_id: entry.entityId,
        ip_address: entry.ip,
        user_agent: entry.userAgent,
        old_values: entry.oldValues,
        new_values: entry.newValues,
        details: entry.details,
        application: 'yogurt-qc',
        environment: process.env.NODE_ENV,
      };

      // 这里应该集成实际的日志系统
      console.log('Audit log:', JSON.stringify(logData));
     } catch (error) {
       console.error('Failed to send audit log to external system:', error);
     }
   }
 }
 ```

---

## 6. 合规性要求

### 6.1 数据保护法规合规

#### 6.1.1 GDPR (通用数据保护条例) 合规

**个人数据处理原则**:

```typescript
// backend/src/compliance/GDPRCompliance.ts
export class GDPRComplianceService {
  // 数据主体权利实现
  
  // 1. 访问权 (Right of Access)
  async getPersonalData(userId: string): Promise<PersonalDataExport> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    const recipes = await this.recipeRepository.find({ where: { createdBy: userId } });
    const batches = await this.batchRepository.find({ where: { createdBy: userId } });
    const auditLogs = await this.auditRepository.find({ where: { userId } });

    return {
      personalInfo: {
        id: user.id,
        email: user.email,
        name: user.name,
        phoneNumber: user.phoneNumber,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt,
      },
      activityData: {
        recipesCreated: recipes.length,
        batchesCreated: batches.length,
        lastActivity: auditLogs[0]?.timestamp,
      },
      dataProcessingPurposes: [
        'Account management',
        'Service provision',
        'Quality control analysis',
        'System security and audit',
      ],
      dataRetentionPeriod: '7 years from last activity',
      dataProcessingLegalBasis: 'Legitimate interest for service provision',
    };
  }

  // 2. 更正权 (Right of Rectification)
  async updatePersonalData(userId: string, updates: Partial<UserData>): Promise<void> {
    // 验证更新请求
    const validatedUpdates = await this.validatePersonalDataUpdates(updates);
    
    // 记录更改前的数据
    const oldData = await this.userRepository.findOne({ where: { id: userId } });
    
    // 执行更新
    await this.userRepository.update(userId, validatedUpdates);
    
    // 审计日志
    await AuditService.logEvent({
      eventType: AuditEventType.USER_UPDATED,
      userId,
      entityType: 'User',
      entityId: userId,
      oldValues: oldData,
      newValues: validatedUpdates,
      ip: 'system',
      userAgent: 'gdpr-compliance',
      timestamp: new Date(),
      details: { reason: 'GDPR_RECTIFICATION_REQUEST' },
    });
  }

  // 3. 删除权 (Right to Erasure)
  async deletePersonalData(userId: string, reason: string): Promise<void> {
    // 检查是否有法律义务保留数据
    const retentionCheck = await this.checkDataRetentionRequirements(userId);
    
    if (retentionCheck.mustRetain) {
      throw new Error(`Cannot delete data: ${retentionCheck.reason}`);
    }

    // 软删除用户数据
    await this.userRepository.update(userId, {
      deletedAt: new Date(),
      email: `deleted_${userId}@example.com`,
      name: 'Deleted User',
      phoneNumber: null,
      personalNotes: null,
    });

    // 匿名化相关数据
    await this.anonymizeUserRelatedData(userId);

    // 记录删除操作
    await AuditService.logEvent({
      eventType: AuditEventType.USER_DELETED,
      userId,
      entityType: 'User',
      entityId: userId,
      ip: 'system',
      userAgent: 'gdpr-compliance',
      timestamp: new Date(),
      details: { reason, type: 'GDPR_ERASURE_REQUEST' },
    });
  }

  // 4. 数据可携带权 (Right to Data Portability)
  async exportPersonalDataPortable(userId: string): Promise<Buffer> {
    const personalData = await this.getPersonalData(userId);
    
    // 转换为标准格式 (JSON)
    const exportData = {
      exportDate: new Date().toISOString(),
      dataSubject: userId,
      format: 'JSON',
      version: '1.0',
      data: personalData,
    };

    // 记录导出操作
    await AuditService.logDataExport({
      userId,
      dataType: 'PERSONAL_DATA_EXPORT',
      recordCount: 1,
      timestamp: new Date(),
      ip: 'system',
    });

    return Buffer.from(JSON.stringify(exportData, null, 2));
  }

  // 5. 限制处理权 (Right to Restrict Processing)
  async restrictDataProcessing(userId: string, restrictions: string[]): Promise<void> {
    await this.userRepository.update(userId, {
      processingRestrictions: restrictions,
      restrictedAt: new Date(),
    });

    await AuditService.logEvent({
      eventType: AuditEventType.USER_UPDATED,
      userId,
      entityType: 'User',
      entityId: userId,
      ip: 'system',
      userAgent: 'gdpr-compliance',
      timestamp: new Date(),
      details: { 
        reason: 'GDPR_PROCESSING_RESTRICTION',
        restrictions 
      },
    });
  }

  // 数据保护影响评估 (DPIA)
  async conductDataProtectionImpactAssessment(): Promise<DPIAReport> {
    return {
      assessmentDate: new Date(),
      dataProcessingActivities: [
        {
          activity: 'User account management',
          personalDataTypes: ['email', 'name', 'phone'],
          purposes: ['Authentication', 'Communication'],
          riskLevel: 'low',
          safeguards: ['Encryption', 'Access controls', 'Audit logging'],
        },
        {
          activity: 'AI-powered quality analysis',
          personalDataTypes: ['usage patterns', 'preferences'],
          purposes: ['Service improvement', 'Quality control'],
          riskLevel: 'medium',
          safeguards: ['Data minimization', 'Anonymization', 'Consent management'],
        },
      ],
      overallRiskAssessment: 'medium',
      mitigationMeasures: [
        'Regular security audits',
        'Staff training on data protection',
        'Incident response procedures',
        'Regular review of data processing activities',
      ],
      reviewDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1年后
    };
  }

  private async checkDataRetentionRequirements(userId: string): Promise<{
    mustRetain: boolean;
    reason?: string;
  }> {
    // 检查法律要求（如税务、审计要求）
    const hasActiveContracts = await this.checkActiveContracts(userId);
    const hasLegalHold = await this.checkLegalHold(userId);
    
    if (hasActiveContracts) {
      return { mustRetain: true, reason: 'Active contracts require data retention' };
    }
    
    if (hasLegalHold) {
      return { mustRetain: true, reason: 'Legal hold in effect' };
    }
    
    return { mustRetain: false };
  }

  private async anonymizeUserRelatedData(userId: string): Promise<void> {
    // 匿名化配方数据
    await this.recipeRepository.update(
      { createdBy: userId },
      { createdBy: 'anonymous', createdByName: 'Anonymous User' }
    );

    // 匿名化批次数据
    await this.batchRepository.update(
      { createdBy: userId },
      { createdBy: 'anonymous', createdByName: 'Anonymous User' }
    );
  }
}

interface PersonalDataExport {
  personalInfo: {
    id: string;
    email: string;
    name: string;
    phoneNumber?: string;
    createdAt: Date;
    lastLoginAt?: Date;
  };
  activityData: {
    recipesCreated: number;
    batchesCreated: number;
    lastActivity?: Date;
  };
  dataProcessingPurposes: string[];
  dataRetentionPeriod: string;
  dataProcessingLegalBasis: string;
}

interface DPIAReport {
  assessmentDate: Date;
  dataProcessingActivities: Array<{
    activity: string;
    personalDataTypes: string[];
    purposes: string[];
    riskLevel: 'low' | 'medium' | 'high';
    safeguards: string[];
  }>;
  overallRiskAssessment: 'low' | 'medium' | 'high';
  mitigationMeasures: string[];
  reviewDate: Date;
}
```

#### 6.1.2 食品安全合规

**HACCP (危害分析和关键控制点) 合规**:

```typescript
// backend/src/compliance/FoodSafetyCompliance.ts
export class FoodSafetyComplianceService {
  // HACCP原则实施
  
  // 原则1: 危害分析
  async conductHazardAnalysis(recipeId: string): Promise<HazardAnalysisReport> {
    const recipe = await this.recipeRepository.findOne({ where: { id: recipeId } });
    
    const biologicalHazards = this.identifyBiologicalHazards(recipe);
    const chemicalHazards = this.identifyChemicalHazards(recipe);
    const physicalHazards = this.identifyPhysicalHazards(recipe);
    
    return {
      recipeId,
      analysisDate: new Date(),
      hazards: {
        biological: biologicalHazards,
        chemical: chemicalHazards,
        physical: physicalHazards,
      },
      riskAssessment: this.assessOverallRisk([...biologicalHazards, ...chemicalHazards, ...physicalHazards]),
      controlMeasures: this.recommendControlMeasures(recipe),
    };
  }

  // 原则2: 确定关键控制点 (CCP)
  async identifyCriticalControlPoints(recipeId: string): Promise<CriticalControlPoint[]> {
    const recipe = await this.recipeRepository.findOne({ where: { id: recipeId } });
    
    return [
      {
        id: 'CCP-1',
        step: 'Pasteurization',
        hazard: 'Pathogenic bacteria',
        criticalLimit: {
          parameter: 'Temperature',
          value: '85°C for 30 seconds',
          tolerance: '±2°C',
        },
        monitoringProcedure: 'Continuous temperature monitoring',
        correctiveActions: ['Re-pasteurize', 'Discard batch if temperature not achieved'],
        verification: 'Daily calibration of temperature sensors',
        recordKeeping: 'Temperature logs with timestamps',
      },
      {
        id: 'CCP-2',
        step: 'Fermentation pH Control',
        hazard: 'Pathogenic bacteria growth',
        criticalLimit: {
          parameter: 'pH',
          value: '< 4.6',
          tolerance: '±0.1',
        },
        monitoringProcedure: 'pH measurement every 2 hours',
        correctiveActions: ['Adjust fermentation conditions', 'Extend fermentation time'],
        verification: 'Weekly pH meter calibration',
        recordKeeping: 'pH measurement logs',
      },
      {
        id: 'CCP-3',
        step: 'Cold Storage',
        hazard: 'Bacterial multiplication',
        criticalLimit: {
          parameter: 'Temperature',
          value: '≤ 4°C',
          tolerance: '±1°C',
        },
        monitoringProcedure: 'Continuous temperature monitoring',
        correctiveActions: ['Immediate temperature correction', 'Product evaluation'],
        verification: 'Daily temperature log review',
        recordKeeping: 'Cold storage temperature records',
      },
    ];
  }

  // 原则3-7: 建立关键限值、监控程序、纠正措施、验证程序、记录保持
  async generateHACCPPlan(recipeId: string): Promise<HACCPPlan> {
    const hazardAnalysis = await this.conductHazardAnalysis(recipeId);
    const ccps = await this.identifyCriticalControlPoints(recipeId);
    
    return {
      recipeId,
      planVersion: '1.0',
      effectiveDate: new Date(),
      reviewDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1年后
      hazardAnalysis,
      criticalControlPoints: ccps,
      monitoringSchedule: this.createMonitoringSchedule(ccps),
      trainingRequirements: [
        'HACCP principles training',
        'Food safety fundamentals',
        'Equipment operation and calibration',
        'Record keeping procedures',
      ],
      documentControl: {
        approvedBy: 'Quality Manager',
        reviewedBy: 'Food Safety Team',
        distributionList: ['Production Team', 'Quality Control', 'Management'],
      },
    };
  }

  // 可追溯性要求
  async generateTraceabilityRecord(batchId: string): Promise<TraceabilityRecord> {
    const batch = await this.batchRepository.findOne({ 
      where: { id: batchId },
      relations: ['recipe', 'ingredients', 'qualityAssessments']
    });

    return {
      batchId,
      productName: batch.recipe.name,
      productionDate: batch.startDate,
      expiryDate: batch.expectedEndDate,
      lotNumber: batch.batchNumber,
      ingredients: batch.ingredients.map(ingredient => ({
        name: ingredient.name,
        supplier: ingredient.supplier,
        lotNumber: ingredient.lotNumber,
        expiryDate: ingredient.expiryDate,
        quantity: ingredient.quantity,
        unit: ingredient.unit,
      })),
      productionSteps: [
        {
          step: 'Ingredient preparation',
          timestamp: batch.startDate,
          operator: batch.createdByName,
          parameters: batch.recipe.ingredients,
        },
        {
          step: 'Fermentation',
          timestamp: new Date(batch.startDate.getTime() + 2 * 60 * 60 * 1000),
          operator: batch.createdByName,
          parameters: {
            temperature: batch.fermentationTemperature,
            duration: batch.fermentationTime,
          },
        },
      ],
      qualityControls: batch.qualityAssessments.map(qa => ({
        testType: qa.testType,
        result: qa.result,
        timestamp: qa.createdAt,
        operator: qa.createdByName,
        specification: qa.specification,
        status: qa.status,
      })),
      distributionRecords: [], // 待实现
    };
  }

  private identifyBiologicalHazards(recipe: any): BiologicalHazard[] {
    return [
      {
        hazard: 'Salmonella',
        source: 'Raw milk',
        severity: 'high',
        likelihood: 'medium',
        controlMeasure: 'Pasteurization',
      },
      {
        hazard: 'Listeria monocytogenes',
        source: 'Environmental contamination',
        severity: 'high',
        likelihood: 'low',
        controlMeasure: 'Sanitation procedures',
      },
    ];
  }

  private identifyChemicalHazards(recipe: any): ChemicalHazard[] {
    return [
      {
        hazard: 'Cleaning chemical residues',
        source: 'Equipment cleaning',
        severity: 'medium',
        likelihood: 'low',
        controlMeasure: 'Proper rinsing procedures',
      },
    ];
  }

  private identifyPhysicalHazards(recipe: any): PhysicalHazard[] {
     return [
       {
         hazard: 'Metal fragments',
         source: 'Equipment wear',
         severity: 'high',
         likelihood: 'low',
         controlMeasure: 'Metal detection',
       },
     ];
   }
 }

 // 接口定义
 interface HazardAnalysisReport {
   recipeId: string;
   analysisDate: Date;
   hazards: {
     biological: BiologicalHazard[];
     chemical: ChemicalHazard[];
     physical: PhysicalHazard[];
   };
   riskAssessment: string;
   controlMeasures: string[];
 }

 interface CriticalControlPoint {
   id: string;
   step: string;
   hazard: string;
   criticalLimit: {
     parameter: string;
     value: string;
     tolerance: string;
   };
   monitoringProcedure: string;
   correctiveActions: string[];
   verification: string;
   recordKeeping: string;
 }

 interface HACCPPlan {
   recipeId: string;
   planVersion: string;
   effectiveDate: Date;
   reviewDate: Date;
   hazardAnalysis: HazardAnalysisReport;
   criticalControlPoints: CriticalControlPoint[];
   monitoringSchedule: any;
   trainingRequirements: string[];
   documentControl: {
     approvedBy: string;
     reviewedBy: string;
     distributionList: string[];
   };
 }

 interface TraceabilityRecord {
   batchId: string;
   productName: string;
   productionDate: Date;
   expiryDate: Date;
   lotNumber: string;
   ingredients: Array<{
     name: string;
     supplier: string;
     lotNumber: string;
     expiryDate: Date;
     quantity: number;
     unit: string;
   }>;
   productionSteps: Array<{
     step: string;
     timestamp: Date;
     operator: string;
     parameters: any;
   }>;
   qualityControls: Array<{
     testType: string;
     result: any;
     timestamp: Date;
     operator: string;
     specification: any;
     status: string;
   }>;
   distributionRecords: any[];
 }

 interface BiologicalHazard {
   hazard: string;
   source: string;
   severity: 'low' | 'medium' | 'high';
   likelihood: 'low' | 'medium' | 'high';
   controlMeasure: string;
 }

 interface ChemicalHazard {
   hazard: string;
   source: string;
   severity: 'low' | 'medium' | 'high';
   likelihood: 'low' | 'medium' | 'high';
   controlMeasure: string;
 }

 interface PhysicalHazard {
   hazard: string;
   source: string;
   severity: 'low' | 'medium' | 'high';
   likelihood: 'low' | 'medium' | 'high';
   controlMeasure: string;
 }
 ```

---

## 7. 威胁建模与风险评估

### 7.1 STRIDE威胁建模

**威胁分析框架**:

```mermaid
graph TB
    subgraph "威胁建模流程"
        A["识别资产"] --> B["创建架构图"]
        B --> C["识别威胁"]
        C --> D["评估风险"]
        D --> E["制定缓解措施"]
        E --> F["验证控制措施"]
    end
    
    subgraph "STRIDE威胁类型"
        S["Spoofing 欺骗"]
        T["Tampering 篡改"]
        R["Repudiation 抵赖"]
        I["Information Disclosure 信息泄露"]
        D2["Denial of Service 拒绝服务"]
        E2["Elevation of Privilege 权限提升"]
    end
```

**威胁分析实现**:

```typescript
// backend/src/security/ThreatModeling.ts
export class ThreatModelingService {
  // STRIDE威胁分析
  async conductSTRIDEAnalysis(component: string): Promise<STRIDEThreatAnalysis> {
    const threats = await this.identifySTRIDEThreats(component);
    const riskAssessment = await this.assessRisks(threats);
    const mitigations = await this.recommendMitigations(threats);
    
    return {
      component,
      analysisDate: new Date(),
      threats,
      riskAssessment,
      mitigations,
      residualRisk: this.calculateResidualRisk(threats, mitigations),
    };
  }

  private async identifySTRIDEThreats(component: string): Promise<STRIDEThreat[]> {
    const threatDatabase = {
      'web-application': [
        {
          id: 'T001',
          type: 'Spoofing',
          description: 'Attacker impersonates legitimate user',
          asset: 'User authentication system',
          impact: 'Unauthorized access to user accounts',
          likelihood: 'medium',
          severity: 'high',
          attackVector: 'Credential stuffing, phishing',
          prerequisites: 'Valid user credentials',
        },
        {
          id: 'T002',
          type: 'Tampering',
          description: 'Modification of recipe data in transit',
          asset: 'Recipe data',
          impact: 'Data integrity compromise',
          likelihood: 'low',
          severity: 'medium',
          attackVector: 'Man-in-the-middle attack',
          prerequisites: 'Network access',
        },
        {
          id: 'T003',
          type: 'Repudiation',
          description: 'User denies performing critical actions',
          asset: 'Audit logs',
          impact: 'Loss of accountability',
          likelihood: 'low',
          severity: 'medium',
          attackVector: 'Log manipulation',
          prerequisites: 'System access',
        },
        {
          id: 'T004',
          type: 'Information Disclosure',
          description: 'Unauthorized access to sensitive recipe data',
          asset: 'Proprietary recipes',
          impact: 'Intellectual property theft',
          likelihood: 'medium',
          severity: 'high',
          attackVector: 'SQL injection, API abuse',
          prerequisites: 'Application vulnerabilities',
        },
        {
          id: 'T005',
          type: 'Denial of Service',
          description: 'Service unavailability during critical production',
          asset: 'Application availability',
          impact: 'Production downtime',
          likelihood: 'medium',
          severity: 'high',
          attackVector: 'DDoS, resource exhaustion',
          prerequisites: 'Network connectivity',
        },
        {
          id: 'T006',
          type: 'Elevation of Privilege',
          description: 'Regular user gains administrative access',
          asset: 'Administrative functions',
          impact: 'System compromise',
          likelihood: 'low',
          severity: 'critical',
          attackVector: 'Privilege escalation vulnerabilities',
          prerequisites: 'User account access',
        },
      ],
      'ai-service': [
        {
          id: 'T007',
          type: 'Tampering',
          description: 'AI model poisoning through malicious training data',
          asset: 'AI models',
          impact: 'Incorrect quality assessments',
          likelihood: 'low',
          severity: 'high',
          attackVector: 'Data poisoning',
          prerequisites: 'Access to training pipeline',
        },
        {
          id: 'T008',
          type: 'Information Disclosure',
          description: 'Model inversion attacks to extract training data',
          asset: 'Training data',
          impact: 'Sensitive data exposure',
          likelihood: 'low',
          severity: 'medium',
          attackVector: 'Model inversion, membership inference',
          prerequisites: 'Model access',
        },
      ],
    };

    return threatDatabase[component] || [];
  }

  private async assessRisks(threats: STRIDEThreat[]): Promise<RiskAssessment[]> {
    return threats.map(threat => {
      const likelihoodScore = this.getLikelihoodScore(threat.likelihood);
      const severityScore = this.getSeverityScore(threat.severity);
      const riskScore = likelihoodScore * severityScore;
      
      return {
        threatId: threat.id,
        likelihoodScore,
        severityScore,
        riskScore,
        riskLevel: this.getRiskLevel(riskScore),
        businessImpact: this.assessBusinessImpact(threat),
      };
    });
  }

  private async recommendMitigations(threats: STRIDEThreat[]): Promise<MitigationStrategy[]> {
    const mitigationDatabase = {
      'Spoofing': [
        {
          control: 'Multi-factor authentication',
          implementation: 'TOTP-based 2FA',
          effectiveness: 'high',
          cost: 'medium',
        },
        {
          control: 'Account lockout policies',
          implementation: 'Progressive delays after failed attempts',
          effectiveness: 'medium',
          cost: 'low',
        },
      ],
      'Tampering': [
        {
          control: 'Data integrity checks',
          implementation: 'HMAC signatures',
          effectiveness: 'high',
          cost: 'low',
        },
        {
          control: 'TLS encryption',
          implementation: 'TLS 1.3 for all communications',
          effectiveness: 'high',
          cost: 'low',
        },
      ],
      'Repudiation': [
        {
          control: 'Comprehensive audit logging',
          implementation: 'Immutable audit trail',
          effectiveness: 'high',
          cost: 'medium',
        },
        {
          control: 'Digital signatures',
          implementation: 'PKI-based signatures for critical actions',
          effectiveness: 'high',
          cost: 'high',
        },
      ],
      'Information Disclosure': [
        {
          control: 'Data encryption at rest',
          implementation: 'AES-256 encryption',
          effectiveness: 'high',
          cost: 'low',
        },
        {
          control: 'Access controls',
          implementation: 'RBAC with principle of least privilege',
          effectiveness: 'high',
          cost: 'medium',
        },
      ],
      'Denial of Service': [
        {
          control: 'Rate limiting',
          implementation: 'API rate limits and DDoS protection',
          effectiveness: 'medium',
          cost: 'low',
        },
        {
          control: 'Auto-scaling',
          implementation: 'Kubernetes HPA',
          effectiveness: 'high',
          cost: 'medium',
        },
      ],
      'Elevation of Privilege': [
        {
          control: 'Principle of least privilege',
          implementation: 'Fine-grained RBAC',
          effectiveness: 'high',
          cost: 'medium',
        },
        {
          control: 'Regular security assessments',
          implementation: 'Automated vulnerability scanning',
          effectiveness: 'medium',
          cost: 'medium',
        },
      ],
    };

    const mitigations: MitigationStrategy[] = [];
    
    threats.forEach(threat => {
      const threatMitigations = mitigationDatabase[threat.type] || [];
      threatMitigations.forEach(mitigation => {
        mitigations.push({
          threatId: threat.id,
          threatType: threat.type,
          ...mitigation,
        });
      });
    });

    return mitigations;
  }

  // 攻击面分析
  async analyzeAttackSurface(): Promise<AttackSurfaceAnalysis> {
    return {
      analysisDate: new Date(),
      networkAttackSurface: {
        exposedPorts: [
          { port: 80, service: 'HTTP', risk: 'medium' },
          { port: 443, service: 'HTTPS', risk: 'low' },
          { port: 22, service: 'SSH', risk: 'high' },
        ],
        publicEndpoints: [
          { endpoint: '/api/auth/login', risk: 'high' },
          { endpoint: '/api/recipes', risk: 'medium' },
          { endpoint: '/api/health', risk: 'low' },
        ],
      },
      applicationAttackSurface: {
        inputVectors: [
          { vector: 'User registration form', risk: 'medium' },
          { vector: 'Recipe upload', risk: 'high' },
          { vector: 'Image upload', risk: 'high' },
        ],
        authenticationMechanisms: [
          { mechanism: 'JWT tokens', risk: 'medium' },
          { mechanism: 'Session cookies', risk: 'medium' },
        ],
      },
      dataAttackSurface: {
        sensitiveDataStores: [
          { store: 'User credentials', risk: 'critical' },
          { store: 'Recipe formulations', risk: 'high' },
          { store: 'Quality assessment data', risk: 'medium' },
        ],
        dataFlows: [
          { flow: 'Client to API', encryption: 'TLS', risk: 'low' },
          { flow: 'API to Database', encryption: 'TLS', risk: 'low' },
          { flow: 'Backup storage', encryption: 'AES-256', risk: 'low' },
        ],
      },
      recommendations: [
        'Implement Web Application Firewall (WAF)',
        'Regular penetration testing',
        'Automated vulnerability scanning',
        'Security code review process',
        'Incident response plan',
      ],
    };
  }

  private getLikelihoodScore(likelihood: string): number {
    const scores = { low: 1, medium: 2, high: 3, critical: 4 };
    return scores[likelihood] || 1;
  }

  private getSeverityScore(severity: string): number {
    const scores = { low: 1, medium: 2, high: 3, critical: 4 };
    return scores[severity] || 1;
  }

  private getRiskLevel(riskScore: number): string {
    if (riskScore >= 12) return 'critical';
    if (riskScore >= 9) return 'high';
    if (riskScore >= 6) return 'medium';
    return 'low';
  }

  private assessBusinessImpact(threat: STRIDEThreat): string {
    // 业务影响评估逻辑
    const impactMap = {
      'User authentication system': 'Customer trust and regulatory compliance',
      'Recipe data': 'Intellectual property and competitive advantage',
      'Application availability': 'Production efficiency and customer satisfaction',
      'AI models': 'Quality control accuracy and brand reputation',
    };
    
    return impactMap[threat.asset] || 'Operational disruption';
  }

  private calculateResidualRisk(threats: STRIDEThreat[], mitigations: MitigationStrategy[]): ResidualRiskAssessment {
    // 计算实施缓解措施后的剩余风险
    const totalThreats = threats.length;
    const mitigatedThreats = mitigations.filter(m => m.effectiveness === 'high').length;
    
    return {
      overallRiskReduction: (mitigatedThreats / totalThreats) * 100,
      remainingHighRiskThreats: threats.filter(t => t.severity === 'critical' || t.severity === 'high').length,
      recommendedActions: [
        'Implement high-effectiveness mitigations first',
        'Regular risk reassessment',
        'Continuous monitoring',
      ],
    };
  }
}

// 接口定义
interface STRIDEThreat {
  id: string;
  type: 'Spoofing' | 'Tampering' | 'Repudiation' | 'Information Disclosure' | 'Denial of Service' | 'Elevation of Privilege';
  description: string;
  asset: string;
  impact: string;
  likelihood: 'low' | 'medium' | 'high' | 'critical';
  severity: 'low' | 'medium' | 'high' | 'critical';
  attackVector: string;
  prerequisites: string;
}

interface STRIDEThreatAnalysis {
  component: string;
  analysisDate: Date;
  threats: STRIDEThreat[];
  riskAssessment: RiskAssessment[];
  mitigations: MitigationStrategy[];
  residualRisk: ResidualRiskAssessment;
}

interface RiskAssessment {
  threatId: string;
  likelihoodScore: number;
  severityScore: number;
  riskScore: number;
  riskLevel: string;
  businessImpact: string;
}

interface MitigationStrategy {
  threatId: string;
  threatType: string;
  control: string;
  implementation: string;
  effectiveness: 'low' | 'medium' | 'high';
  cost: 'low' | 'medium' | 'high';
}

interface AttackSurfaceAnalysis {
  analysisDate: Date;
  networkAttackSurface: {
    exposedPorts: Array<{ port: number; service: string; risk: string }>;
    publicEndpoints: Array<{ endpoint: string; risk: string }>;
  };
  applicationAttackSurface: {
    inputVectors: Array<{ vector: string; risk: string }>;
    authenticationMechanisms: Array<{ mechanism: string; risk: string }>;
  };
  dataAttackSurface: {
    sensitiveDataStores: Array<{ store: string; risk: string }>;
    dataFlows: Array<{ flow: string; encryption: string; risk: string }>;
  };
  recommendations: string[];
}

interface ResidualRiskAssessment {
   overallRiskReduction: number;
   remainingHighRiskThreats: number;
   recommendedActions: string[];
 }
 ```

---

## 8. 安全事件响应计划

### 8.1 事件响应流程

**事件响应生命周期**:

```mermaid
graph TB
    subgraph "事件响应流程"
        A["准备阶段"] --> B["检测与分析"]
        B --> C["遏制、根除与恢复"]
        C --> D["事后活动"]
        D --> A
    end
    
    subgraph "响应团队"
        E["事件响应经理"]
        F["安全分析师"]
        G["系统管理员"]
        H["法务顾问"]
        I["公关团队"]
    end
    
    subgraph "严重级别"
        J["P0 - 关键"]
        K["P1 - 高"]
        L["P2 - 中"]
        M["P3 - 低"]
    end
```

**事件响应实现**:

```typescript
// backend/src/security/IncidentResponse.ts
export class IncidentResponseService {
  // 事件检测与分类
  async detectAndClassifyIncident(alertData: SecurityAlert): Promise<SecurityIncident> {
    const incident = await this.createIncident(alertData);
    const classification = await this.classifyIncident(incident);
    const severity = await this.assessSeverity(incident);
    
    // 自动触发响应流程
    if (severity === 'critical' || severity === 'high') {
      await this.triggerEmergencyResponse(incident);
    }
    
    return {
      ...incident,
      classification,
      severity,
      responseTeam: await this.assignResponseTeam(severity),
      timeline: this.createResponseTimeline(severity),
    };
  }

  // 事件遏制
  async containIncident(incidentId: string, containmentActions: ContainmentAction[]): Promise<ContainmentResult> {
    const incident = await this.getIncident(incidentId);
    const results: ContainmentActionResult[] = [];
    
    for (const action of containmentActions) {
      try {
        const result = await this.executeContainmentAction(action);
        results.push({
          action: action.type,
          status: 'success',
          timestamp: new Date(),
          details: result,
        });
        
        // 记录遏制操作
        await AuditService.logEvent({
          eventType: AuditEventType.SECURITY_INCIDENT,
          userId: 'system',
          entityType: 'SecurityIncident',
          entityId: incidentId,
          ip: 'system',
          userAgent: 'incident-response',
          timestamp: new Date(),
          details: {
            phase: 'containment',
            action: action.type,
            result: 'success',
          },
        });
      } catch (error) {
        results.push({
          action: action.type,
          status: 'failed',
          timestamp: new Date(),
          error: error.message,
        });
      }
    }
    
    return {
      incidentId,
      containmentActions: results,
      overallStatus: results.every(r => r.status === 'success') ? 'contained' : 'partial',
      nextSteps: this.determineNextSteps(results),
    };
  }

  // 自动化响应操作
  private async executeContainmentAction(action: ContainmentAction): Promise<any> {
    switch (action.type) {
      case 'isolate_user_account':
        return await this.isolateUserAccount(action.targetUserId);
      
      case 'block_ip_address':
        return await this.blockIPAddress(action.targetIP);
      
      case 'disable_api_endpoint':
        return await this.disableAPIEndpoint(action.targetEndpoint);
      
      case 'rotate_credentials':
        return await this.rotateCredentials(action.credentialType);
      
      case 'scale_down_service':
        return await this.scaleDownService(action.serviceName);
      
      case 'enable_maintenance_mode':
        return await this.enableMaintenanceMode();
      
      default:
        throw new Error(`Unknown containment action: ${action.type}`);
    }
  }

  private async isolateUserAccount(userId: string): Promise<void> {
    // 禁用用户账户
    await this.userRepository.update(userId, {
      isActive: false,
      suspendedAt: new Date(),
      suspensionReason: 'Security incident - automated isolation',
    });
    
    // 撤销所有活跃会话
    await this.sessionService.revokeAllUserSessions(userId);
    
    // 撤销API令牌
    await this.tokenService.revokeAllUserTokens(userId);
  }

  private async blockIPAddress(ipAddress: string): Promise<void> {
    // 添加到WAF黑名单
    await this.wafService.addToBlacklist(ipAddress);
    
    // 更新防火墙规则
    await this.firewallService.blockIP(ipAddress);
    
    // 记录阻断操作
    await this.securityLogService.logIPBlock({
      ipAddress,
      reason: 'Security incident response',
      timestamp: new Date(),
      duration: '24h', // 默认24小时
    });
  }

  private async disableAPIEndpoint(endpoint: string): Promise<void> {
    // 在API网关中禁用端点
    await this.apiGatewayService.disableEndpoint(endpoint);
    
    // 更新路由配置
    await this.routingService.removeRoute(endpoint);
  }

  private async rotateCredentials(credentialType: string): Promise<void> {
    switch (credentialType) {
      case 'database':
        await this.databaseService.rotateCredentials();
        break;
      case 'api_keys':
        await this.apiKeyService.rotateAllKeys();
        break;
      case 'jwt_secret':
        await this.jwtService.rotateSecret();
        break;
      default:
        throw new Error(`Unknown credential type: ${credentialType}`);
    }
  }

  // 事件恢复
  async recoverFromIncident(incidentId: string, recoveryPlan: RecoveryPlan): Promise<RecoveryResult> {
    const incident = await this.getIncident(incidentId);
    const recoverySteps: RecoveryStepResult[] = [];
    
    for (const step of recoveryPlan.steps) {
      try {
        const result = await this.executeRecoveryStep(step);
        recoverySteps.push({
          step: step.name,
          status: 'completed',
          timestamp: new Date(),
          details: result,
        });
      } catch (error) {
        recoverySteps.push({
          step: step.name,
          status: 'failed',
          timestamp: new Date(),
          error: error.message,
        });
        
        // 如果关键步骤失败，停止恢复过程
        if (step.critical) {
          break;
        }
      }
    }
    
    return {
      incidentId,
      recoverySteps,
      overallStatus: recoverySteps.every(s => s.status === 'completed') ? 'recovered' : 'partial',
      systemStatus: await this.assessSystemHealth(),
    };
  }

  // 事后分析
  async conductPostIncidentAnalysis(incidentId: string): Promise<PostIncidentReport> {
    const incident = await this.getIncident(incidentId);
    const timeline = await this.reconstructTimeline(incidentId);
    const rootCause = await this.analyzeRootCause(incident);
    const lessons = await this.extractLessonsLearned(incident);
    
    return {
      incidentId,
      summary: {
        title: incident.title,
        severity: incident.severity,
        duration: this.calculateIncidentDuration(incident),
        affectedSystems: incident.affectedSystems,
        businessImpact: incident.businessImpact,
      },
      timeline,
      rootCauseAnalysis: rootCause,
      lessonsLearned: lessons,
      actionItems: await this.generateActionItems(rootCause, lessons),
      recommendations: await this.generateRecommendations(incident),
    };
  }

  // 通知与沟通
  async notifyStakeholders(incident: SecurityIncident, notificationType: NotificationType): Promise<void> {
    const stakeholders = await this.getStakeholders(incident.severity);
    
    for (const stakeholder of stakeholders) {
      const message = await this.generateNotificationMessage(incident, stakeholder.role, notificationType);
      
      switch (stakeholder.preferredChannel) {
        case 'email':
          await this.emailService.sendSecurityAlert(stakeholder.email, message);
          break;
        case 'sms':
          await this.smsService.sendAlert(stakeholder.phone, message.summary);
          break;
        case 'slack':
          await this.slackService.sendToChannel(stakeholder.slackChannel, message);
          break;
        case 'pagerduty':
          await this.pagerDutyService.createIncident(incident, stakeholder);
          break;
      }
    }
  }

  private async classifyIncident(incident: SecurityIncident): Promise<IncidentClassification> {
    // 基于NIST分类标准
    const categories = {
      'malware': /virus|trojan|ransomware|malware/i,
      'unauthorized_access': /login|authentication|privilege|access/i,
      'denial_of_service': /ddos|dos|unavailable|timeout/i,
      'data_breach': /leak|breach|exposure|unauthorized.*data/i,
      'insider_threat': /insider|employee|internal/i,
      'web_attack': /injection|xss|csrf|web.*attack/i,
    };
    
    for (const [category, pattern] of Object.entries(categories)) {
      if (pattern.test(incident.description)) {
        return {
          category,
          subcategory: await this.determineSubcategory(category, incident),
          confidence: 0.8,
        };
      }
    }
    
    return {
      category: 'unknown',
      subcategory: 'requires_manual_classification',
      confidence: 0.1,
    };
  }

  private async assessSeverity(incident: SecurityIncident): Promise<IncidentSeverity> {
    let score = 0;
    
    // 影响范围评分
    if (incident.affectedSystems.includes('production')) score += 3;
    if (incident.affectedSystems.includes('database')) score += 2;
    if (incident.affectedSystems.includes('authentication')) score += 2;
    
    // 数据敏感性评分
    if (incident.involvesPII) score += 2;
    if (incident.involvesFinancialData) score += 3;
    if (incident.involvesTradeSecrets) score += 2;
    
    // 业务影响评分
    if (incident.causesServiceOutage) score += 3;
    if (incident.affectsCustomers) score += 2;
    if (incident.hasRegulatoryImplications) score += 2;
    
    // 确定严重级别
    if (score >= 8) return 'critical';
    if (score >= 6) return 'high';
    if (score >= 3) return 'medium';
    return 'low';
  }
}

// 接口定义
interface SecurityAlert {
  id: string;
  source: string;
  timestamp: Date;
  type: string;
  description: string;
  rawData: any;
}

interface SecurityIncident {
  id: string;
  title: string;
  description: string;
  severity: IncidentSeverity;
  classification: IncidentClassification;
  affectedSystems: string[];
  involvesPII: boolean;
  involvesFinancialData: boolean;
  involvesTradeSecrets: boolean;
  causesServiceOutage: boolean;
  affectsCustomers: boolean;
  hasRegulatoryImplications: boolean;
  businessImpact: string;
  responseTeam: string[];
  timeline: ResponseTimeline;
  status: 'open' | 'investigating' | 'contained' | 'resolved' | 'closed';
  createdAt: Date;
  updatedAt: Date;
}

type IncidentSeverity = 'critical' | 'high' | 'medium' | 'low';

interface IncidentClassification {
  category: string;
  subcategory: string;
  confidence: number;
}

interface ContainmentAction {
  type: string;
  targetUserId?: string;
  targetIP?: string;
  targetEndpoint?: string;
  credentialType?: string;
  serviceName?: string;
}

interface ContainmentResult {
  incidentId: string;
  containmentActions: ContainmentActionResult[];
  overallStatus: 'contained' | 'partial' | 'failed';
  nextSteps: string[];
}

interface ContainmentActionResult {
  action: string;
  status: 'success' | 'failed';
  timestamp: Date;
  details?: any;
  error?: string;
}

interface RecoveryPlan {
  steps: RecoveryStep[];
}

interface RecoveryStep {
  name: string;
  description: string;
  critical: boolean;
  estimatedDuration: string;
}

interface RecoveryResult {
  incidentId: string;
  recoverySteps: RecoveryStepResult[];
  overallStatus: 'recovered' | 'partial' | 'failed';
  systemStatus: SystemHealthStatus;
}

interface RecoveryStepResult {
  step: string;
  status: 'completed' | 'failed' | 'skipped';
  timestamp: Date;
  details?: any;
  error?: string;
}

interface PostIncidentReport {
  incidentId: string;
  summary: {
    title: string;
    severity: IncidentSeverity;
    duration: string;
    affectedSystems: string[];
    businessImpact: string;
  };
  timeline: IncidentTimelineEvent[];
  rootCauseAnalysis: RootCauseAnalysis;
  lessonsLearned: string[];
  actionItems: ActionItem[];
  recommendations: string[];
}

type NotificationType = 'initial' | 'update' | 'resolution' | 'post_incident';

interface ResponseTimeline {
  detectionTime: Date;
  responseTime: Date;
  containmentTime?: Date;
  resolutionTime?: Date;
}

interface SystemHealthStatus {
  overall: 'healthy' | 'degraded' | 'critical';
  services: Array<{
    name: string;
    status: 'up' | 'down' | 'degraded';
    lastCheck: Date;
  }>;
}

interface IncidentTimelineEvent {
  timestamp: Date;
  event: string;
  actor: string;
  details: string;
}

interface RootCauseAnalysis {
  primaryCause: string;
  contributingFactors: string[];
  timeline: string;
  evidence: string[];
}

interface ActionItem {
  id: string;
  description: string;
  assignee: string;
  priority: 'high' | 'medium' | 'low';
  dueDate: Date;
  status: 'open' | 'in_progress' | 'completed';
}
```

### 8.2 自动化响应规则

**响应规则配置**:

```yaml
# config/incident-response-rules.yml
response_rules:
  - name: "Brute Force Attack Detection"
    trigger:
      event_type: "failed_login"
      threshold: 5
      time_window: "5m"
    actions:
      - type: "block_ip"
        duration: "1h"
      - type: "notify_security_team"
        severity: "medium"
      - type: "increase_monitoring"
        target: "authentication_service"

  - name: "Data Exfiltration Detection"
    trigger:
      event_type: "large_data_export"
      threshold: "100MB"
      time_window: "1m"
    actions:
      - type: "isolate_user_account"
      - type: "notify_security_team"
        severity: "high"
      - type: "preserve_evidence"
      - type: "legal_hold"

  - name: "Privilege Escalation Detection"
    trigger:
      event_type: "privilege_change"
      conditions:
        - "new_role == 'admin'"
        - "previous_role != 'admin'"
    actions:
      - type: "revert_privilege_change"
      - type: "isolate_user_account"
      - type: "notify_security_team"
        severity: "critical"
      - type: "emergency_response"

  - name: "System Compromise Detection"
    trigger:
      event_type: "malware_detected"
    actions:
      - type: "isolate_system"
      - type: "preserve_evidence"
      - type: "notify_security_team"
        severity: "critical"
      - type: "activate_incident_response_team"
```

---

## 9. 安全培训与意识提升

### 9.1 安全培训计划

**培训体系架构**:

```mermaid
graph TB
    subgraph "安全培训体系"
        A["基础安全意识培训"] --> B["角色专项培训"]
        B --> C["高级安全培训"]
        C --> D["持续教育与更新"]
    end
    
    subgraph "培训对象"
        E["全体员工"]
        F["开发人员"]
        G["运维人员"]
        H["管理人员"]
        I["安全团队"]
    end
    
    subgraph "培训方式"
        J["在线课程"]
        K["实践演练"]
        L["安全竞赛"]
        M["案例分析"]
        N["模拟攻击"]
    end
```

**培训内容实现**:

```typescript
// backend/src/security/SecurityTraining.ts
export class SecurityTrainingService {
  // 培训计划管理
  async createTrainingPlan(planData: TrainingPlanData): Promise<TrainingPlan> {
    const plan = await this.trainingRepository.create({
      ...planData,
      status: 'draft',
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    
    // 自动分配培训对象
    await this.assignTrainingTargets(plan.id, planData.targetRoles);
    
    // 创建培训进度跟踪
    await this.initializeProgressTracking(plan.id);
    
    return plan;
  }

  // 个性化培训推荐
  async getPersonalizedTraining(userId: string): Promise<TrainingRecommendation[]> {
    const user = await this.userService.getUser(userId);
    const userRole = user.role;
    const completedTrainings = await this.getCompletedTrainings(userId);
    const securityIncidents = await this.getUserSecurityIncidents(userId);
    
    const recommendations: TrainingRecommendation[] = [];
    
    // 基于角色的必修培训
    const mandatoryTrainings = await this.getMandatoryTrainingsByRole(userRole);
    for (const training of mandatoryTrainings) {
      if (!completedTrainings.includes(training.id)) {
        recommendations.push({
          trainingId: training.id,
          priority: 'high',
          reason: 'mandatory_for_role',
          dueDate: this.calculateDueDate(training.type),
        });
      }
    }
    
    // 基于安全事件的针对性培训
    for (const incident of securityIncidents) {
      const relatedTrainings = await this.getTrainingsByIncidentType(incident.type);
      for (const training of relatedTrainings) {
        if (!completedTrainings.includes(training.id)) {
          recommendations.push({
            trainingId: training.id,
            priority: 'medium',
            reason: 'incident_related',
            relatedIncident: incident.id,
            dueDate: this.calculateDueDate(training.type, 'urgent'),
          });
        }
      }
    }
    
    // 基于技能差距的推荐培训
    const skillGaps = await this.assessSkillGaps(userId);
    for (const gap of skillGaps) {
      const skillTrainings = await this.getTrainingsBySkill(gap.skill);
      for (const training of skillTrainings) {
        recommendations.push({
          trainingId: training.id,
          priority: 'low',
          reason: 'skill_improvement',
          skillGap: gap.skill,
          dueDate: this.calculateDueDate(training.type, 'flexible'),
        });
      }
    }
    
    return this.prioritizeRecommendations(recommendations);
  }

  // 培训进度跟踪
  async trackTrainingProgress(userId: string, trainingId: string, progressData: TrainingProgressData): Promise<void> {
    await this.progressRepository.upsert({
      userId,
      trainingId,
      ...progressData,
      lastUpdated: new Date(),
    });
    
    // 检查是否完成培训
    if (progressData.completionPercentage >= 100) {
      await this.markTrainingCompleted(userId, trainingId);
      
      // 颁发证书
      await this.issueCertificate(userId, trainingId);
      
      // 更新用户技能档案
      await this.updateUserSkillProfile(userId, trainingId);
    }
    
    // 发送进度通知
    if (progressData.completionPercentage % 25 === 0) {
      await this.sendProgressNotification(userId, trainingId, progressData.completionPercentage);
    }
  }

  // 安全意识评估
  async conductSecurityAwarenessAssessment(userId: string): Promise<SecurityAwarenessReport> {
    const assessment = await this.createAssessment(userId);
    const questions = await this.getAssessmentQuestions(assessment.type);
    
    return {
      assessmentId: assessment.id,
      userId,
      questions: questions.map(q => ({
        id: q.id,
        question: q.text,
        type: q.type,
        options: q.options,
      })),
      timeLimit: assessment.timeLimit,
      startTime: new Date(),
    };
  }

  // 评估结果分析
  async analyzeAssessmentResults(assessmentId: string, answers: AssessmentAnswer[]): Promise<AssessmentResult> {
    const assessment = await this.getAssessment(assessmentId);
    const questions = await this.getAssessmentQuestions(assessment.type);
    
    let totalScore = 0;
    let maxScore = 0;
    const categoryScores: Record<string, { score: number; max: number }> = {};
    const weakAreas: string[] = [];
    
    for (const answer of answers) {
      const question = questions.find(q => q.id === answer.questionId);
      if (!question) continue;
      
      const score = this.calculateQuestionScore(question, answer);
      totalScore += score;
      maxScore += question.maxScore;
      
      // 按类别统计
      if (!categoryScores[question.category]) {
        categoryScores[question.category] = { score: 0, max: 0 };
      }
      categoryScores[question.category].score += score;
      categoryScores[question.category].max += question.maxScore;
      
      // 识别薄弱环节
      if (score / question.maxScore < 0.6) {
        weakAreas.push(question.category);
      }
    }
    
    const overallScore = (totalScore / maxScore) * 100;
    const level = this.determineSecurityAwarenessLevel(overallScore);
    
    // 生成改进建议
    const recommendations = await this.generateImprovementRecommendations(weakAreas, level);
    
    const result: AssessmentResult = {
      assessmentId,
      userId: assessment.userId,
      overallScore,
      level,
      categoryScores,
      weakAreas,
      recommendations,
      completedAt: new Date(),
    };
    
    // 保存结果
    await this.saveAssessmentResult(result);
    
    // 触发后续培训推荐
    await this.triggerFollowUpTraining(assessment.userId, weakAreas);
    
    return result;
  }

  // 模拟钓鱼邮件测试
  async conductPhishingSimulation(targetUsers: string[], campaignConfig: PhishingCampaignConfig): Promise<PhishingCampaign> {
    const campaign = await this.createPhishingCampaign({
      ...campaignConfig,
      targetUsers,
      status: 'active',
      startDate: new Date(),
    });
    
    // 发送模拟钓鱼邮件
    for (const userId of targetUsers) {
      const user = await this.userService.getUser(userId);
      const personalizedEmail = await this.generatePhishingEmail(user, campaignConfig.template);
      
      await this.emailService.sendPhishingSimulation({
        to: user.email,
        subject: personalizedEmail.subject,
        content: personalizedEmail.content,
        trackingId: this.generateTrackingId(campaign.id, userId),
      });
      
      // 记录发送日志
      await this.logPhishingEmailSent(campaign.id, userId);
    }
    
    // 设置自动监控
    await this.setupPhishingMonitoring(campaign.id);
    
    return campaign;
  }

  // 钓鱼测试结果分析
  async analyzePhishingResults(campaignId: string): Promise<PhishingAnalysisReport> {
    const campaign = await this.getPhishingCampaign(campaignId);
    const interactions = await this.getPhishingInteractions(campaignId);
    
    const stats = {
      totalSent: campaign.targetUsers.length,
      opened: interactions.filter(i => i.type === 'email_opened').length,
      clicked: interactions.filter(i => i.type === 'link_clicked').length,
      reported: interactions.filter(i => i.type === 'reported_suspicious').length,
      credentialsEntered: interactions.filter(i => i.type === 'credentials_entered').length,
    };
    
    const rates = {
      openRate: (stats.opened / stats.totalSent) * 100,
      clickRate: (stats.clicked / stats.totalSent) * 100,
      reportRate: (stats.reported / stats.totalSent) * 100,
      compromiseRate: (stats.credentialsEntered / stats.totalSent) * 100,
    };
    
    // 识别高风险用户
    const highRiskUsers = interactions
      .filter(i => i.type === 'credentials_entered' || i.type === 'link_clicked')
      .map(i => i.userId);
    
    // 识别安全意识较强的用户
    const securityAwareUsers = interactions
      .filter(i => i.type === 'reported_suspicious')
      .map(i => i.userId);
    
    const report: PhishingAnalysisReport = {
      campaignId,
      stats,
      rates,
      highRiskUsers,
      securityAwareUsers,
      departmentBreakdown: await this.analyzeByDepartment(interactions),
      recommendations: await this.generatePhishingRecommendations(rates, highRiskUsers),
      generatedAt: new Date(),
    };
    
    // 自动安排后续培训
    await this.scheduleFollowUpTraining(highRiskUsers, 'phishing_awareness');
    
    return report;
  }

  // 安全文化指标监控
  async monitorSecurityCultureMetrics(): Promise<SecurityCultureMetrics> {
    const metrics = {
      // 培训参与度
      trainingParticipation: await this.calculateTrainingParticipation(),
      
      // 安全事件报告率
      incidentReportingRate: await this.calculateIncidentReportingRate(),
      
      // 密码安全合规率
      passwordComplianceRate: await this.calculatePasswordComplianceRate(),
      
      // 钓鱼测试表现
      phishingResistance: await this.calculatePhishingResistance(),
      
      // 安全政策认知度
      policyAwarenessLevel: await this.calculatePolicyAwarenessLevel(),
      
      // 安全工具使用率
      securityToolAdoption: await this.calculateSecurityToolAdoption(),
    };
    
    // 计算综合安全文化评分
    const overallScore = this.calculateOverallSecurityCultureScore(metrics);
    
    // 生成趋势分析
    const trends = await this.analyzeSecurityCultureTrends(metrics);
    
    return {
      ...metrics,
      overallScore,
      trends,
      benchmarks: await this.getIndustryBenchmarks(),
      recommendations: await this.generateCultureImprovementRecommendations(metrics),
      measuredAt: new Date(),
    };
  }

  private async calculateTrainingParticipation(): Promise<number> {
    const totalEmployees = await this.userService.getTotalEmployeeCount();
    const activeTrainees = await this.getActiveTraineeCount();
    return (activeTrainees / totalEmployees) * 100;
  }

  private async calculateIncidentReportingRate(): Promise<number> {
    const detectedIncidents = await this.getDetectedIncidentCount();
    const reportedIncidents = await this.getReportedIncidentCount();
    return (reportedIncidents / Math.max(detectedIncidents, 1)) * 100;
  }

  private async calculatePasswordComplianceRate(): Promise<number> {
    const totalUsers = await this.userService.getTotalUserCount();
    const compliantUsers = await this.getPasswordCompliantUserCount();
    return (compliantUsers / totalUsers) * 100;
  }

  private async calculatePhishingResistance(): Promise<number> {
    const recentCampaigns = await this.getRecentPhishingCampaigns();
    if (recentCampaigns.length === 0) return 0;
    
    const totalTargets = recentCampaigns.reduce((sum, c) => sum + c.targetUsers.length, 0);
    const totalCompromised = recentCampaigns.reduce((sum, c) => sum + c.compromisedCount, 0);
    
    return ((totalTargets - totalCompromised) / totalTargets) * 100;
  }

  private calculateOverallSecurityCultureScore(metrics: Partial<SecurityCultureMetrics>): number {
    const weights = {
      trainingParticipation: 0.2,
      incidentReportingRate: 0.2,
      passwordComplianceRate: 0.15,
      phishingResistance: 0.25,
      policyAwarenessLevel: 0.1,
      securityToolAdoption: 0.1,
    };
    
    let weightedSum = 0;
    let totalWeight = 0;
    
    for (const [metric, value] of Object.entries(metrics)) {
      if (weights[metric] && typeof value === 'number') {
        weightedSum += value * weights[metric];
        totalWeight += weights[metric];
      }
    }
    
    return totalWeight > 0 ? weightedSum / totalWeight : 0;
  }
}

// 接口定义
interface TrainingPlanData {
  title: string;
  description: string;
  type: 'mandatory' | 'optional' | 'incident_response';
  targetRoles: string[];
  duration: number; // 分钟
  modules: TrainingModule[];
  assessmentRequired: boolean;
  validityPeriod: number; // 月
}

interface TrainingPlan {
  id: string;
  title: string;
  description: string;
  type: string;
  targetRoles: string[];
  duration: number;
  modules: TrainingModule[];
  assessmentRequired: boolean;
  validityPeriod: number;
  status: 'draft' | 'active' | 'archived';
  createdAt: Date;
  updatedAt: Date;
}

interface TrainingModule {
  id: string;
  title: string;
  content: string;
  type: 'video' | 'text' | 'interactive' | 'quiz';
  duration: number;
  order: number;
}

interface TrainingRecommendation {
  trainingId: string;
  priority: 'high' | 'medium' | 'low';
  reason: string;
  dueDate: Date;
  relatedIncident?: string;
  skillGap?: string;
}

interface TrainingProgressData {
  completionPercentage: number;
  currentModule: string;
  timeSpent: number; // 分钟
  lastAccessed: Date;
}

interface SecurityAwarenessReport {
  assessmentId: string;
  userId: string;
  questions: AssessmentQuestion[];
  timeLimit: number; // 分钟
  startTime: Date;
}

interface AssessmentQuestion {
  id: string;
  question: string;
  type: 'multiple_choice' | 'true_false' | 'scenario';
  options?: string[];
}

interface AssessmentAnswer {
  questionId: string;
  answer: string | string[];
  timeSpent: number;
}

interface AssessmentResult {
  assessmentId: string;
  userId: string;
  overallScore: number;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  categoryScores: Record<string, { score: number; max: number }>;
  weakAreas: string[];
  recommendations: string[];
  completedAt: Date;
}

interface PhishingCampaignConfig {
  name: string;
  template: string;
  difficulty: 'easy' | 'medium' | 'hard';
  duration: number; // 天
}

interface PhishingCampaign {
  id: string;
  name: string;
  targetUsers: string[];
  template: string;
  difficulty: string;
  status: 'active' | 'completed' | 'cancelled';
  startDate: Date;
  endDate?: Date;
  compromisedCount: number;
}

interface PhishingAnalysisReport {
  campaignId: string;
  stats: {
    totalSent: number;
    opened: number;
    clicked: number;
    reported: number;
    credentialsEntered: number;
  };
  rates: {
    openRate: number;
    clickRate: number;
    reportRate: number;
    compromiseRate: number;
  };
  highRiskUsers: string[];
  securityAwareUsers: string[];
  departmentBreakdown: Record<string, any>;
  recommendations: string[];
  generatedAt: Date;
}

interface SecurityCultureMetrics {
  trainingParticipation: number;
  incidentReportingRate: number;
  passwordComplianceRate: number;
  phishingResistance: number;
  policyAwarenessLevel: number;
  securityToolAdoption: number;
  overallScore: number;
  trends: Record<string, number[]>;
  benchmarks: Record<string, number>;
  recommendations: string[];
  measuredAt: Date;
}
```

### 9.2 安全意识提升活动

**活动计划**:

```yaml
# config/security-awareness-activities.yml
activities:
  monthly:
    - name: "安全主题月"
      description: "每月聚焦一个安全主题进行深度宣传"
      themes:
        - "密码安全月"
        - "钓鱼防范月"
        - "数据保护月"
        - "移动安全月"
      activities:
        - security_newsletters
        - lunch_and_learn_sessions
        - security_tips_emails
        - poster_campaigns

  quarterly:
    - name: "安全竞赛"
      description: "团队间安全知识竞赛"
      format: "capture_the_flag"
      prizes:
        - "安全冠军团队奖"
        - "最佳创新奖"
        - "安全新星奖"
    
    - name: "安全演练"
      description: "全公司安全事件响应演练"
      scenarios:
        - "数据泄露事件"
        - "勒索软件攻击"
        - "内部威胁"
        - "供应链攻击"

  annually:
    - name: "安全文化调查"
      description: "全员安全文化现状调查"
      metrics:
        - security_awareness_level
        - training_satisfaction
        - policy_understanding
        - incident_reporting_willingness
    
    - name: "安全创新大赛"
      description: "鼓励员工提出安全改进建议"
      categories:
        - "最佳安全工具创意"
         - "最佳流程改进建议"
         - "最佳安全文化倡议"
```

---

## 10. 实施路线图与总结

### 10.1 分阶段实施计划

**实施路线图**:

```mermaid
gantt
    title 安全架构实施路线图
    dateFormat  YYYY-MM-DD
    section 第一阶段 (基础安全)
    身份认证系统           :a1, 2024-01-01, 30d
    基础访问控制           :a2, after a1, 20d
    数据加密实施           :a3, after a1, 25d
    基础监控部署           :a4, after a2, 15d
    
    section 第二阶段 (深度防护)
    零信任架构部署         :b1, after a4, 45d
    威胁检测系统           :b2, after a4, 35d
    安全事件响应           :b3, after b1, 30d
    合规性实施             :b4, after b2, 40d
    
    section 第三阶段 (高级安全)
    AI安全防护             :c1, after b3, 35d
    高级威胁分析           :c2, after b4, 30d
    自动化响应             :c3, after c1, 25d
    安全文化建设           :c4, after c2, 60d
    
    section 第四阶段 (持续优化)
    安全成熟度评估         :d1, after c3, 20d
    持续改进计划           :d2, after c4, 30d
    安全创新研究           :d3, after d1, 45d
```

**阶段详细计划**:

```typescript
// backend/src/security/ImplementationPlan.ts
export class SecurityImplementationPlan {
  // 第一阶段：基础安全建设 (0-3个月)
  static readonly PHASE_1_FOUNDATION = {
    name: '基础安全建设',
    duration: '3个月',
    priority: 'P0',
    objectives: [
      '建立基础身份认证和授权体系',
      '实施数据加密和基础访问控制',
      '部署基础安全监控和日志系统',
      '制定基础安全政策和流程',
    ],
    deliverables: [
      {
        name: 'JWT身份认证系统',
        description: '实现用户登录、令牌管理、会话控制',
        timeline: '第1个月',
        owner: 'backend_team',
        dependencies: [],
      },
      {
        name: 'RBAC权限控制',
        description: '实现基于角色的访问控制',
        timeline: '第1-2个月',
        owner: 'backend_team',
        dependencies: ['JWT身份认证系统'],
      },
      {
        name: '数据加密实施',
        description: '敏感数据加密存储和传输',
        timeline: '第2个月',
        owner: 'backend_team',
        dependencies: [],
      },
      {
        name: '基础监控系统',
        description: 'Prometheus + Grafana监控部署',
        timeline: '第2-3个月',
        owner: 'devops_team',
        dependencies: [],
      },
      {
        name: '安全日志系统',
        description: '集中化日志收集和基础分析',
        timeline: '第3个月',
        owner: 'devops_team',
        dependencies: ['基础监控系统'],
      },
    ],
    successCriteria: [
      '100%用户通过安全认证访问系统',
      '所有敏感数据实现加密存储',
      '基础安全事件监控覆盖率达到80%',
      '安全日志完整性达到95%',
    ],
  };

  // 第二阶段：深度防护体系 (3-6个月)
  static readonly PHASE_2_DEFENSE = {
    name: '深度防护体系',
    duration: '3个月',
    priority: 'P1',
    objectives: [
      '实施零信任安全架构',
      '建立威胁检测和响应能力',
      '完善合规性管理体系',
      '加强API和应用安全',
    ],
    deliverables: [
      {
        name: '零信任网络架构',
        description: '微分段、动态访问控制、持续验证',
        timeline: '第4-5个月',
        owner: 'security_team',
        dependencies: ['RBAC权限控制'],
      },
      {
        name: 'WAF和API安全',
        description: 'Web应用防火墙和API安全网关',
        timeline: '第4个月',
        owner: 'devops_team',
        dependencies: [],
      },
      {
        name: '威胁检测系统',
        description: 'SIEM、异常检测、威胁情报',
        timeline: '第5个月',
        owner: 'security_team',
        dependencies: ['安全日志系统'],
      },
      {
        name: '事件响应流程',
        description: '自动化事件响应和处置流程',
        timeline: '第5-6个月',
        owner: 'security_team',
        dependencies: ['威胁检测系统'],
      },
      {
        name: 'GDPR合规实施',
        description: '数据保护和隐私合规管理',
        timeline: '第6个月',
        owner: 'legal_team',
        dependencies: ['数据加密实施'],
      },
    ],
    successCriteria: [
      '零信任架构覆盖所有关键资源',
      '威胁检测准确率达到90%',
      '安全事件平均响应时间<15分钟',
      'GDPR合规性评估通过',
    ],
  };

  // 第三阶段：高级安全能力 (6-9个月)
  static readonly PHASE_3_ADVANCED = {
    name: '高级安全能力',
    duration: '3个月',
    priority: 'P2',
    objectives: [
      '建设AI驱动的安全防护',
      '实现高级威胁分析能力',
      '完善自动化安全运营',
      '建立安全文化体系',
    ],
    deliverables: [
      {
        name: 'AI安全分析平台',
        description: '机器学习驱动的威胁分析和预测',
        timeline: '第7-8个月',
        owner: 'ai_team',
        dependencies: ['威胁检测系统'],
      },
      {
        name: '高级威胁狩猎',
        description: '主动威胁狩猎和APT检测',
        timeline: '第7个月',
        owner: 'security_team',
        dependencies: ['威胁检测系统'],
      },
      {
        name: '安全编排自动化',
        description: 'SOAR平台和自动化响应',
        timeline: '第8个月',
        owner: 'security_team',
        dependencies: ['事件响应流程'],
      },
      {
        name: '安全培训体系',
        description: '全员安全意识培训和技能提升',
        timeline: '第8-9个月',
        owner: 'hr_team',
        dependencies: [],
      },
      {
        name: '渗透测试计划',
        description: '定期安全评估和漏洞管理',
        timeline: '第9个月',
        owner: 'security_team',
        dependencies: ['AI安全分析平台'],
      },
    ],
    successCriteria: [
      'AI威胁检测误报率<5%',
      '自动化响应覆盖率达到70%',
      '员工安全意识评分>85分',
      '渗透测试发现的高危漏洞<3个',
    ],
  };

  // 第四阶段：持续优化 (9-12个月)
  static readonly PHASE_4_OPTIMIZATION = {
    name: '持续优化',
    duration: '3个月',
    priority: 'P3',
    objectives: [
      '评估安全成熟度水平',
      '制定持续改进计划',
      '探索安全创新技术',
      '建立安全卓越中心',
    ],
    deliverables: [
      {
        name: '安全成熟度评估',
        description: '基于NIST框架的成熟度评估',
        timeline: '第10个月',
        owner: 'security_team',
        dependencies: ['渗透测试计划'],
      },
      {
        name: '持续改进框架',
        description: '建立持续改进的流程和机制',
        timeline: '第10-11个月',
        owner: 'security_team',
        dependencies: ['安全成熟度评估'],
      },
      {
        name: '安全创新实验室',
        description: '新兴安全技术研究和试点',
        timeline: '第11-12个月',
        owner: 'research_team',
        dependencies: [],
      },
      {
        name: '安全卓越中心',
        description: '建立安全专业能力中心',
        timeline: '第12个月',
        owner: 'security_team',
        dependencies: ['持续改进框架'],
      },
    ],
    successCriteria: [
      '安全成熟度达到Level 4 (优化级)',
      '持续改进流程有效运行',
      '安全创新项目启动3个以上',
      '安全卓越中心正式运营',
    ],
  };

  // 获取当前实施状态
  static async getCurrentImplementationStatus(): Promise<ImplementationStatus> {
    // 这里应该从数据库或配置中获取实际状态
    return {
      currentPhase: 1,
      overallProgress: 25,
      phaseProgress: {
        1: 75,
        2: 0,
        3: 0,
        4: 0,
      },
      completedDeliverables: [
        'JWT身份认证系统',
        'RBAC权限控制',
        '数据加密实施',
      ],
      inProgressDeliverables: [
        '基础监控系统',
      ],
      upcomingMilestones: [
        {
          name: '安全日志系统',
          dueDate: new Date('2024-03-31'),
          phase: 1,
        },
        {
          name: '零信任网络架构',
          dueDate: new Date('2024-05-31'),
          phase: 2,
        },
      ],
      risks: [
        {
          description: '基础监控系统部署延期',
          impact: 'medium',
          probability: 'low',
          mitigation: '增加DevOps团队资源投入',
        },
      ],
    };
  }

  // 生成实施报告
  static async generateImplementationReport(): Promise<ImplementationReport> {
    const status = await this.getCurrentImplementationStatus();
    
    return {
      reportDate: new Date(),
      overallStatus: status,
      phaseDetails: [
        this.PHASE_1_FOUNDATION,
        this.PHASE_2_DEFENSE,
        this.PHASE_3_ADVANCED,
        this.PHASE_4_OPTIMIZATION,
      ],
      keyAchievements: [
        '成功实施JWT身份认证系统，用户登录安全性提升100%',
        '完成RBAC权限控制，实现细粒度访问管理',
        '敏感数据加密覆盖率达到100%',
      ],
      upcomingPriorities: [
        '完成基础监控系统部署',
        '启动零信任架构设计',
        '制定威胁检测系统需求',
      ],
      resourceRequirements: {
        personnel: {
          security_engineers: 2,
          devops_engineers: 1,
          backend_developers: 1,
        },
        budget: {
          tools_and_licenses: 50000,
          training_and_certification: 20000,
          external_consulting: 30000,
        },
        timeline: {
          phase1_completion: new Date('2024-03-31'),
          phase2_start: new Date('2024-04-01'),
        },
      },
      recommendations: [
        '优先完成基础安全建设，为后续阶段奠定基础',
        '加强团队安全培训，提升整体安全意识',
        '建立定期安全评估机制，确保实施质量',
      ],
    };
  }
}

// 接口定义
interface ImplementationStatus {
  currentPhase: number;
  overallProgress: number;
  phaseProgress: Record<number, number>;
  completedDeliverables: string[];
  inProgressDeliverables: string[];
  upcomingMilestones: Array<{
    name: string;
    dueDate: Date;
    phase: number;
  }>;
  risks: Array<{
    description: string;
    impact: 'low' | 'medium' | 'high';
    probability: 'low' | 'medium' | 'high';
    mitigation: string;
  }>;
}

interface ImplementationReport {
  reportDate: Date;
  overallStatus: ImplementationStatus;
  phaseDetails: any[];
  keyAchievements: string[];
  upcomingPriorities: string[];
  resourceRequirements: {
    personnel: Record<string, number>;
    budget: Record<string, number>;
    timeline: Record<string, Date>;
  };
  recommendations: string[];
}
```

### 10.2 成功关键因素

**关键成功因素**:

1. **领导层支持与承诺**
   - 高级管理层的安全投入承诺
   - 充足的预算和资源分配
   - 安全优先级的明确定位

2. **跨团队协作**
   - 安全团队与开发团队的紧密合作
   - DevSecOps文化的建立
   - 统一的安全目标和责任分工

3. **技术能力建设**
   - 团队安全技能的持续提升
   - 安全工具和平台的有效利用
   - 自动化程度的不断提高

4. **持续改进机制**
   - 定期安全评估和审计
   - 威胁情报的及时更新
   - 安全策略的动态调整

### 10.3 风险控制

**主要风险及应对策略**:

| 风险类型 | 风险描述 | 影响程度 | 应对策略 |
|---------|---------|---------|----------|
| 技术风险 | 新技术实施复杂度高 | 中 | 分阶段实施，充分测试验证 |
| 人员风险 | 安全专业人才不足 | 高 | 加强培训，外部专家支持 |
| 预算风险 | 安全投入预算超支 | 中 | 分阶段投入，ROI评估 |
| 合规风险 | 法规要求变化 | 中 | 持续关注法规动态 |
| 业务风险 | 安全措施影响业务效率 | 低 | 平衡安全与效率 |

### 10.4 总结

本安全架构文档为酸奶AI质控系统提供了全面、系统的安全保障方案。通过零信任架构、多层防护、AI驱动的威胁检测、全面的合规管理和持续的安全文化建设，确保系统在各个层面都具备强大的安全防护能力。

**核心价值**:
- **全面性**: 覆盖从基础设施到应用层的全栈安全
- **前瞻性**: 采用最新的安全技术和最佳实践
- **实用性**: 提供详细的实施指导和代码示例
- **可扩展性**: 支持业务发展和技术演进
- **合规性**: 满足GDPR、HACCP等法规要求

**预期效果**:
- 安全事件发生率降低90%以上
- 安全响应时间缩短至15分钟以内
- 合规性评估通过率达到100%
- 员工安全意识水平提升80%以上
- 客户数据安全保护达到行业领先水平

通过严格按照本文档执行安全架构的设计和实施，酸奶AI质控系统将建立起坚实的安全防护体系，为业务的安全、稳定、可持续发展提供强有力的保障。