# 技术栈建议与架构设计
**Yogurt-AI-QC 酸奶AI质控系统**

---

## 1. 技术栈概览

### 1.1 整体架构设计原则

**设计原则**:
- **微服务架构**: 模块化设计，便于独立开发和部署，支持团队自治
- **云原生**: 充分利用云服务的弹性和可扩展性，容器化部署
- **API优先**: 前后端分离，支持多端接入，GraphQL + REST混合
- **数据驱动**: 完善的数据收集、存储和分析体系，实时+批处理
- **安全第一**: 全方位的安全防护机制，零信任架构
- **可观测性**: 完善的监控、日志和追踪体系，AIOps智能运维
- **DevOps文化**: 持续集成/持续部署，基础设施即代码
- **质量内建**: 测试左移，自动化质量保证
- **性能优先**: 高性能架构设计，边缘计算支持
- **弹性设计**: 容错机制，优雅降级，混沌工程实践

**架构演进策略**:
- **Phase 1**: 单体应用快速验证（MVP阶段）
- **Phase 2**: 微服务拆分（业务增长阶段）
- **Phase 3**: 云原生架构（规模化阶段）
- **Phase 4**: 智能化运维（成熟阶段）

### 1.2 技术栈选型矩阵

| 技术层次 | 推荐技术 | 备选方案 | 选择理由 |
|---------|---------|---------|----------|
| **前端框架** | React + TypeScript | Vue.js, Angular | 生态丰富，团队熟悉度高 |
| **移动端** | React Native | Flutter, 原生开发 | 代码复用，快速开发 |
| **后端框架** | Node.js + Express | Python + FastAPI | JavaScript全栈，开发效率高 |
| **数据库** | PostgreSQL + Redis | MySQL + MongoDB | 关系型+缓存，性能优异 |
| **AI/ML** | Python + TensorFlow | PyTorch, OpenAI API | 成熟的ML生态 |
| **云服务** | AWS | Google Cloud, Azure | 服务完善，成本合理 |
| **容器化** | Docker + Kubernetes | Docker Swarm | 标准化部署，易于扩展 |
| **监控** | Prometheus + Grafana | DataDog, New Relic | 开源，功能强大 |

---

## 1.3 微服务架构设计

### 1.3.1 服务拆分策略

**领域驱动设计(DDD)原则**:
```mermaid
graph TB
    subgraph "用户界面层"
        WebApp["Web应用<br/>React + TypeScript"]
        MobileApp["移动应用<br/>React Native"]
        AdminPanel["管理后台<br/>Next.js"]
    end
    
    subgraph "API网关层"
        Gateway["API网关<br/>Kong/Envoy"]
        Auth["认证服务<br/>OAuth 2.0"]
        RateLimit["限流服务"]
    end
    
    subgraph "业务服务层"
        UserService["用户服务<br/>Node.js"]
        RecipeService["配方服务<br/>Node.js"]
        BatchService["批次服务<br/>Node.js"]
        AnalysisService["分析服务<br/>Python"]
        ReportService["报告服务<br/>Node.js"]
        NotificationService["通知服务<br/>Node.js"]
    end
    
    subgraph "AI/ML服务层"
        ImageService["图像处理服务<br/>Python + OpenCV"]
        MLService["机器学习服务<br/>Python + TensorFlow"]
        ModelService["模型管理服务<br/>MLflow"]
        FeatureService["特征工程服务<br/>Python"]
    end
    
    subgraph "数据服务层"
        DataAPI["数据API服务<br/>GraphQL"]
        SearchService["搜索服务<br/>Elasticsearch"]
        CacheService["缓存服务<br/>Redis"]
        FileService["文件服务<br/>MinIO"]
    end
    
    subgraph "基础设施层"
        ConfigCenter["配置中心<br/>Consul"]
        ServiceMesh["服务网格<br/>Istio"]
        Monitoring["监控系统<br/>Prometheus"]
        Logging["日志系统<br/>ELK Stack"]
        Tracing["链路追踪<br/>Jaeger"]
    end
    
    subgraph "数据存储层"
        PostgreSQL[("PostgreSQL<br/>主数据库")]
        MongoDB[("MongoDB<br/>文档存储")]
        Redis[("Redis<br/>缓存+会话")]
        S3[("对象存储<br/>AWS S3/MinIO")]
        DataLake[("数据湖<br/>Apache Iceberg")]
        TimeSeries[("时序数据库<br/>InfluxDB")]
    end
```

**服务边界定义**:

| 服务名称 | 职责范围 | 数据所有权 | 团队归属 |
|----------|----------|------------|----------|
| 用户服务 | 用户管理、认证授权 | 用户信息、权限数据 | 平台团队 |
| 配方服务 | 配方CRUD、版本管理 | 配方数据、模板数据 | 业务团队 |
| 批次服务 | 批次管理、状态跟踪 | 批次数据、生产记录 | 业务团队 |
| 分析服务 | AI分析、结果处理 | 分析结果、模型输出 | AI团队 |
| 报告服务 | 报告生成、数据聚合 | 报告数据、统计信息 | 数据团队 |

### 1.3.2 服务间通信机制

**同步通信**:
```typescript
// gRPC服务定义示例
service RecipeService {
  rpc CreateRecipe(CreateRecipeRequest) returns (Recipe);
  rpc GetRecipe(GetRecipeRequest) returns (Recipe);
  rpc UpdateRecipe(UpdateRecipeRequest) returns (Recipe);
  rpc DeleteRecipe(DeleteRecipeRequest) returns (Empty);
  rpc ListRecipes(ListRecipesRequest) returns (ListRecipesResponse);
}

// GraphQL联邦架构
type Recipe @key(fields: "id") {
  id: ID!
  name: String!
  ingredients: [Ingredient!]!
  batches: [Batch!]! @requires(fields: "id")
}

extend type Batch @key(fields: "id") {
  id: ID! @external
  recipe: Recipe!
}
```

**异步通信**:
```yaml
# 事件驱动架构 - CloudEvents规范
apiVersion: v1
kind: ConfigMap
metadata:
  name: event-schema
data:
  recipe-created: |
    {
      "specversion": "1.0",
      "type": "com.yogurt.recipe.created",
      "source": "recipe-service",
      "subject": "recipe/123",
      "datacontenttype": "application/json",
      "data": {
        "recipeId": "123",
        "name": "经典原味酸奶",
        "version": "1.0",
        "createdBy": "user-456"
      }
    }
```

### 1.3.3 数据一致性策略

**Saga模式实现**:
```typescript
// 批次创建的分布式事务
class BatchCreationSaga {
  async execute(batchData: BatchData) {
    const saga = new SagaOrchestrator();
    
    saga
      .step('validateRecipe')
        .invoke(() => this.recipeService.validateRecipe(batchData.recipeId))
        .compensate(() => this.recipeService.releaseRecipeLock(batchData.recipeId))
      .step('createBatch')
        .invoke(() => this.batchService.createBatch(batchData))
        .compensate(() => this.batchService.deleteBatch(batchData.id))
      .step('initializeAnalysis')
        .invoke(() => this.analysisService.initializeAnalysis(batchData.id))
        .compensate(() => this.analysisService.cleanupAnalysis(batchData.id))
      .step('sendNotification')
        .invoke(() => this.notificationService.sendBatchCreated(batchData))
        .compensate(() => this.notificationService.sendBatchCreationFailed(batchData));
    
    return saga.execute();
  }
}
```

**事件溯源(Event Sourcing)**:
```typescript
// 批次状态变更事件
interface BatchEvent {
  eventId: string;
  aggregateId: string;
  eventType: string;
  eventData: any;
  timestamp: Date;
  version: number;
}

class BatchAggregate {
  private events: BatchEvent[] = [];
  
  createBatch(data: BatchData): void {
    const event: BatchEvent = {
      eventId: uuid(),
      aggregateId: this.id,
      eventType: 'BatchCreated',
      eventData: data,
      timestamp: new Date(),
      version: this.version + 1
    };
    
    this.apply(event);
    this.events.push(event);
  }
  
  startAnalysis(): void {
    if (this.status !== 'Created') {
      throw new Error('Batch must be in Created status to start analysis');
    }
    
    const event: BatchEvent = {
      eventId: uuid(),
      aggregateId: this.id,
      eventType: 'AnalysisStarted',
      eventData: { startedAt: new Date() },
      timestamp: new Date(),
      version: this.version + 1
    };
    
    this.apply(event);
    this.events.push(event);
  }
}
```

## 1.4 云原生架构实践

### 1.4.1 容器化策略

**多阶段构建优化**:
```dockerfile
# Node.js服务的生产级Dockerfile
FROM node:18-alpine AS base
RUN apk add --no-cache libc6-compat
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build
RUN npm prune --production

FROM node:18-alpine AS runner
WORKDIR /app
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
COPY --from=base /app/node_modules ./node_modules
COPY --from=builder --chown=nextjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nextjs:nodejs /app/package.json ./package.json

USER nextjs
EXPOSE 3000
ENV NODE_ENV=production
ENV PORT=3000

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

CMD ["npm", "start"]
```

**Python AI服务容器化**:
```dockerfile
# Python AI服务的优化Dockerfile
FROM python:3.11-slim AS base
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

FROM base AS builder
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

FROM base AS runner
WORKDIR /app
RUN groupadd -r appuser && useradd -r -g appuser appuser
COPY --from=builder /root/.local /home/<USER>/.local
COPY --chown=appuser:appuser . .
USER appuser
ENV PATH=/home/<USER>/.local/bin:$PATH
EXPOSE 8000

HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD python -c "import requests; requests.get('http://localhost:8000/health')"

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 1.4.2 Kubernetes部署策略

**服务部署配置**:
```yaml
# recipe-service部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: recipe-service
  labels:
    app: recipe-service
    version: v1
spec:
  replicas: 3
  selector:
    matchLabels:
      app: recipe-service
  template:
    metadata:
      labels:
        app: recipe-service
        version: v1
    spec:
      containers:
      - name: recipe-service
        image: yogurt-ai/recipe-service:v1.2.0
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: cache
          mountPath: /app/cache
      volumes:
      - name: tmp
        emptyDir: {}
      - name: cache
        emptyDir: {}
      serviceAccountName: recipe-service
      automountServiceAccountToken: false
---
apiVersion: v1
kind: Service
metadata:
  name: recipe-service
spec:
  selector:
    app: recipe-service
  ports:
  - port: 80
    targetPort: 3000
  type: ClusterIP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: recipe-service-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: recipe-service
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

**Istio服务网格配置**:
```yaml
# 服务网格流量管理
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: recipe-service
spec:
  hosts:
  - recipe-service
  http:
  - match:
    - headers:
        canary:
          exact: "true"
    route:
    - destination:
        host: recipe-service
        subset: canary
      weight: 100
  - route:
    - destination:
        host: recipe-service
        subset: stable
      weight: 90
    - destination:
        host: recipe-service
        subset: canary
      weight: 10
    fault:
      delay:
        percentage:
          value: 0.1
        fixedDelay: 5s
---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: recipe-service
spec:
  host: recipe-service
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: 100
      http:
        http1MaxPendingRequests: 50
        maxRequestsPerConnection: 10
    circuitBreaker:
      consecutiveErrors: 3
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 50
  subsets:
  - name: stable
    labels:
      version: v1
  - name: canary
    labels:
      version: v2
```

---

## 2. 前端技术栈

### 2.1 Web前端架构

#### 2.1.1 核心技术选型

**React 18 + TypeScript**
```json
{
  "react": "^18.2.0",
  "typescript": "^5.0.0",
  "@types/react": "^18.2.0",
  "@types/react-dom": "^18.2.0"
}
```

**选择理由**:
- 组件化开发，代码复用性高
- TypeScript提供类型安全
- 丰富的生态系统和社区支持
- 团队技术栈匹配

#### 2.1.2 状态管理

**Redux Toolkit + RTK Query**
```json
{
  "@reduxjs/toolkit": "^1.9.0",
  "react-redux": "^8.1.0",
  "redux-persist": "^6.0.0"
}
```

**状态管理架构**:
```typescript
// store/index.ts
import { configureStore } from '@reduxjs/toolkit';
import { authApi } from './api/authApi';
import { recipeApi } from './api/recipeApi';
import { analysisApi } from './api/analysisApi';
import authSlice from './slices/authSlice';
import uiSlice from './slices/uiSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    ui: uiSlice,
    [authApi.reducerPath]: authApi.reducer,
    [recipeApi.reducerPath]: recipeApi.reducer,
    [analysisApi.reducerPath]: analysisApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware()
      .concat(authApi.middleware)
      .concat(recipeApi.middleware)
      .concat(analysisApi.middleware),
});
```

#### 2.1.3 UI组件库

**Ant Design + Styled Components**
```json
{
  "antd": "^5.0.0",
  "styled-components": "^6.0.0",
  "@ant-design/icons": "^5.0.0"
}
```

**自定义主题配置**:
```typescript
// theme/index.ts
import { ThemeConfig } from 'antd';

export const theme: ThemeConfig = {
  token: {
    colorPrimary: '#1890ff',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#ff4d4f',
    borderRadius: 6,
    fontSize: 14,
  },
  components: {
    Button: {
      borderRadius: 6,
    },
    Card: {
      borderRadius: 8,
    },
  },
};
```

#### 2.1.4 路由管理

**React Router v6**
```typescript
// router/index.tsx
import { createBrowserRouter } from 'react-router-dom';
import Layout from '../components/Layout';
import Dashboard from '../pages/Dashboard';
import Recipes from '../pages/Recipes';
import Analysis from '../pages/Analysis';
import Reports from '../pages/Reports';

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    children: [
      { index: true, element: <Dashboard /> },
      { path: 'recipes', element: <Recipes /> },
      { path: 'analysis', element: <Analysis /> },
      { path: 'reports', element: <Reports /> },
    ],
  },
]);
```

#### 2.1.5 数据可视化

**ECharts + React**
```json
{
  "echarts": "^5.4.0",
  "echarts-for-react": "^3.0.0"
}
```

**图表组件示例**:
```typescript
// components/Charts/QualityTrendChart.tsx
import ReactECharts from 'echarts-for-react';

interface QualityTrendChartProps {
  data: QualityData[];
}

export const QualityTrendChart: React.FC<QualityTrendChartProps> = ({ data }) => {
  const option = {
    title: { text: '质量趋势分析' },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: data.map(item => item.date),
    },
    yAxis: { type: 'value' },
    series: [
      {
        name: '质量评分',
        type: 'line',
        data: data.map(item => item.score),
        smooth: true,
      },
    ],
  };

  return <ReactECharts option={option} style={{ height: '400px' }} />;
};
```

### 2.2 移动端技术栈

#### 2.2.1 React Native架构

**核心依赖**:
```json
{
  "react-native": "^0.72.0",
  "@react-navigation/native": "^6.1.0",
  "@react-navigation/stack": "^6.3.0",
  "react-native-vector-icons": "^10.0.0",
  "react-native-image-picker": "^5.0.0"
}
```

**导航结构**:
```typescript
// navigation/AppNavigator.tsx
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';

const Stack = createStackNavigator();

export const AppNavigator = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator initialRouteName="Dashboard">
        <Stack.Screen name="Dashboard" component={DashboardScreen} />
        <Stack.Screen name="BatchCreate" component={BatchCreateScreen} />
        <Stack.Screen name="Analysis" component={AnalysisScreen} />
      </Stack.Navigator>
    </NavigationContainer>
  );
};
```

#### 2.2.2 原生功能集成

**相机和图片处理**:
```typescript
// utils/imageUtils.ts
import { launchImageLibrary, ImagePickerResponse } from 'react-native-image-picker';

export const pickImage = (): Promise<string> => {
  return new Promise((resolve, reject) => {
    launchImageLibrary(
      {
        mediaType: 'photo',
        quality: 0.8,
        maxWidth: 1024,
        maxHeight: 1024,
      },
      (response: ImagePickerResponse) => {
        if (response.assets && response.assets[0]) {
          resolve(response.assets[0].uri!);
        } else {
          reject(new Error('No image selected'));
        }
      }
    );
  });
};
```

---

## 3. 后端技术栈

### 3.1 Node.js后端架构

#### 3.1.1 核心框架选型

**Express.js + TypeScript**
```json
{
  "express": "^4.18.0",
  "typescript": "^5.0.0",
  "@types/express": "^4.17.0",
  "ts-node": "^10.9.0",
  "nodemon": "^3.0.0"
}
```

**项目结构**:
```
src/
├── controllers/     # 控制器层
├── services/        # 业务逻辑层
├── models/          # 数据模型
├── middleware/      # 中间件
├── routes/          # 路由定义
├── utils/           # 工具函数
├── config/          # 配置文件
└── types/           # TypeScript类型定义
```

#### 3.1.2 API设计规范

**RESTful API设计**:
```typescript
// routes/recipes.ts
import { Router } from 'express';
import { RecipeController } from '../controllers/RecipeController';
import { authMiddleware } from '../middleware/auth';
import { validateRecipe } from '../middleware/validation';

const router = Router();
const recipeController = new RecipeController();

// GET /api/recipes - 获取配方列表
router.get('/', authMiddleware, recipeController.getRecipes);

// POST /api/recipes - 创建新配方
router.post('/', authMiddleware, validateRecipe, recipeController.createRecipe);

// GET /api/recipes/:id - 获取特定配方
router.get('/:id', authMiddleware, recipeController.getRecipeById);

// PUT /api/recipes/:id - 更新配方
router.put('/:id', authMiddleware, validateRecipe, recipeController.updateRecipe);

// DELETE /api/recipes/:id - 删除配方
router.delete('/:id', authMiddleware, recipeController.deleteRecipe);

export default router;
```

#### 3.1.3 数据验证与序列化

**Joi数据验证**:
```typescript
// middleware/validation.ts
import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';

const recipeSchema = Joi.object({
  name: Joi.string().required().min(1).max(100),
  description: Joi.string().max(500),
  ingredients: Joi.array().items(
    Joi.object({
      name: Joi.string().required(),
      amount: Joi.number().positive().required(),
      unit: Joi.string().required(),
    })
  ).required(),
  process: Joi.array().items(
    Joi.object({
      step: Joi.number().integer().positive().required(),
      description: Joi.string().required(),
      duration: Joi.number().positive(),
      temperature: Joi.number(),
    })
  ).required(),
});

export const validateRecipe = (req: Request, res: Response, next: NextFunction) => {
  const { error } = recipeSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      details: error.details,
    });
  }
  next();
};
```

#### 3.1.4 错误处理机制

**全局错误处理**:
```typescript
// middleware/errorHandler.ts
import { Request, Response, NextFunction } from 'express';
import { AppError } from '../utils/AppError';
import { logger } from '../utils/logger';

export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  logger.error(err.stack);

  if (err instanceof AppError) {
    return res.status(err.statusCode).json({
      success: false,
      message: err.message,
      ...(process.env.NODE_ENV === 'development' && { stack: err.stack }),
    });
  }

  // 未知错误
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack }),
  });
};
```

### 3.2 数据库设计

#### 3.2.1 PostgreSQL主数据库

**数据库连接配置**:
```typescript
// config/database.ts
import { Pool } from 'pg';
import { config } from './index';

export const pool = new Pool({
  host: config.database.host,
  port: config.database.port,
  database: config.database.name,
  user: config.database.user,
  password: config.database.password,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});
```

**核心数据表设计**:
```sql
-- 用户表
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  name VARCHAR(100) NOT NULL,
  role VARCHAR(50) DEFAULT 'user',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 配方表
CREATE TABLE recipes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  ingredients JSONB NOT NULL,
  process JSONB NOT NULL,
  version INTEGER DEFAULT 1,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 生产批次表
CREATE TABLE batches (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  batch_number VARCHAR(50) UNIQUE NOT NULL,
  recipe_id UUID REFERENCES recipes(id),
  user_id UUID REFERENCES users(id),
  status VARCHAR(50) DEFAULT 'in_progress',
  actual_parameters JSONB,
  quality_score DECIMAL(3,2),
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP
);

-- AI分析记录表
CREATE TABLE ai_analyses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  batch_id UUID REFERENCES batches(id),
  image_urls TEXT[] NOT NULL,
  analysis_result JSONB NOT NULL,
  confidence_score DECIMAL(3,2),
  processing_time INTEGER, -- 毫秒
  model_version VARCHAR(50),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 质量评估表
CREATE TABLE quality_assessments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  batch_id UUID REFERENCES batches(id),
  assessor_id UUID REFERENCES users(id),
  sensory_scores JSONB NOT NULL, -- 感官评分
  overall_score DECIMAL(3,2),
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 3.2.2 Redis缓存策略

**缓存配置**:
```typescript
// config/redis.ts
import Redis from 'ioredis';
import { config } from './index';

export const redis = new Redis({
  host: config.redis.host,
  port: config.redis.port,
  password: config.redis.password,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
});

// 缓存工具类
export class CacheService {
  static async get<T>(key: string): Promise<T | null> {
    const value = await redis.get(key);
    return value ? JSON.parse(value) : null;
  }

  static async set(key: string, value: any, ttl: number = 3600): Promise<void> {
    await redis.setex(key, ttl, JSON.stringify(value));
  }

  static async del(key: string): Promise<void> {
    await redis.del(key);
  }

  static async invalidatePattern(pattern: string): Promise<void> {
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }
  }
}
```

**缓存策略**:
```typescript
// services/RecipeService.ts
export class RecipeService {
  async getRecipes(userId: string, page: number = 1, limit: number = 10) {
    const cacheKey = `recipes:${userId}:${page}:${limit}`;
    
    // 尝试从缓存获取
    let recipes = await CacheService.get(cacheKey);
    
    if (!recipes) {
      // 从数据库查询
      recipes = await this.fetchRecipesFromDB(userId, page, limit);
      
      // 缓存结果（5分钟）
      await CacheService.set(cacheKey, recipes, 300);
    }
    
    return recipes;
  }

  async createRecipe(recipeData: CreateRecipeDto) {
    const recipe = await this.insertRecipeToDB(recipeData);
    
    // 清除相关缓存
    await CacheService.invalidatePattern(`recipes:${recipeData.userId}:*`);
    
    return recipe;
  }
}
```

---

## 4. AI/ML技术栈

### 4.1 AI服务架构

#### 4.1.1 Python AI服务

**核心依赖**:
```python
# requirements.txt
fastapi==0.104.1
uvicorn==0.24.0
tensorflow==2.14.0
opencv-python==********
numpy==1.24.3
pillow==10.0.1
pydantic==2.4.2
celery==5.3.4
redis==5.0.1
requests==2.31.0
```

**FastAPI服务结构**:
```python
# main.py
from fastapi import FastAPI, UploadFile, File, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import asyncio
from typing import List

from services.image_analyzer import ImageAnalyzer
from services.model_manager import ModelManager
from utils.image_processor import ImageProcessor

app = FastAPI(title="Yogurt AI Analysis Service", version="1.0.0")

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境需要限制
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化服务
image_analyzer = ImageAnalyzer()
model_manager = ModelManager()
image_processor = ImageProcessor()

class AnalysisRequest(BaseModel):
    batch_id: str
    magnification: int
    staining_method: str

class AnalysisResult(BaseModel):
    quality_score: float
    bacterial_count: int
    contamination_detected: bool
    morphology_analysis: dict
    confidence_score: float
    processing_time_ms: int

@app.post("/analyze", response_model=AnalysisResult)
async def analyze_images(
    files: List[UploadFile] = File(...),
    request: AnalysisRequest = None
):
    try:
        # 图像预处理
        processed_images = []
        for file in files:
            image_data = await file.read()
            processed_image = image_processor.preprocess(image_data)
            processed_images.append(processed_image)
        
        # AI分析
        result = await image_analyzer.analyze_batch(
            processed_images,
            request.magnification,
            request.staining_method
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "healthy", "models_loaded": model_manager.get_loaded_models()}
```

#### 4.1.2 图像分析模型

**多模态分析架构**:
```python
# services/image_analyzer.py
import tensorflow as tf
import cv2
import numpy as np
from typing import List, Dict
import time

class ImageAnalyzer:
    def __init__(self):
        self.quality_model = tf.keras.models.load_model('models/quality_assessment_v1.h5')
        self.contamination_model = tf.keras.models.load_model('models/contamination_detection_v1.h5')
        self.morphology_model = tf.keras.models.load_model('models/morphology_analysis_v1.h5')
    
    async def analyze_batch(self, images: List[np.ndarray], magnification: int, staining: str) -> Dict:
        start_time = time.time()
        
        # 并行分析
        quality_scores = await self._analyze_quality(images)
        contamination_results = await self._detect_contamination(images)
        morphology_results = await self._analyze_morphology(images)
        
        # 综合评估
        overall_score = self._calculate_overall_score(
            quality_scores, contamination_results, morphology_results
        )
        
        processing_time = int((time.time() - start_time) * 1000)
        
        return {
            "quality_score": float(overall_score),
            "bacterial_count": int(morphology_results['bacterial_count']),
            "contamination_detected": bool(contamination_results['detected']),
            "morphology_analysis": morphology_results,
            "confidence_score": float(self._calculate_confidence(quality_scores, contamination_results)),
            "processing_time_ms": processing_time
        }
    
    async def _analyze_quality(self, images: List[np.ndarray]) -> List[float]:
        """质量评估分析"""
        scores = []
        for image in images:
            # 预处理
            processed = self._preprocess_for_quality(image)
            # 模型推理
            prediction = self.quality_model.predict(np.expand_dims(processed, axis=0))
            scores.append(float(prediction[0][0]))
        return scores
    
    async def _detect_contamination(self, images: List[np.ndarray]) -> Dict:
        """污染检测"""
        contamination_scores = []
        for image in images:
            processed = self._preprocess_for_contamination(image)
            prediction = self.contamination_model.predict(np.expand_dims(processed, axis=0))
            contamination_scores.append(float(prediction[0][0]))
        
        max_contamination = max(contamination_scores)
        return {
            "detected": max_contamination > 0.5,
            "confidence": max_contamination,
            "affected_areas": self._identify_contaminated_areas(images, contamination_scores)
        }
    
    async def _analyze_morphology(self, images: List[np.ndarray]) -> Dict:
        """形态学分析"""
        total_bacteria = 0
        morphology_features = []
        
        for image in images:
            # 细菌计数
            bacteria_count = self._count_bacteria(image)
            total_bacteria += bacteria_count
            
            # 形态特征提取
            features = self._extract_morphology_features(image)
            morphology_features.append(features)
        
        return {
            "bacterial_count": total_bacteria,
            "average_size": np.mean([f['size'] for f in morphology_features]),
            "shape_distribution": self._analyze_shape_distribution(morphology_features),
            "clustering_analysis": self._analyze_clustering(morphology_features)
        }
```

#### 4.1.3 模型管理与版本控制

**模型管理系统**:
```python
# services/model_manager.py
import os
import json
from typing import Dict, List
import tensorflow as tf
from datetime import datetime

class ModelManager:
    def __init__(self):
        self.models = {}
        self.model_configs = {}
        self.load_models()
    
    def load_models(self):
        """加载所有可用模型"""
        model_dir = "models/"
        config_file = os.path.join(model_dir, "model_config.json")
        
        with open(config_file, 'r') as f:
            self.model_configs = json.load(f)
        
        for model_name, config in self.model_configs.items():
            if config.get("active", False):
                model_path = os.path.join(model_dir, config["path"])
                self.models[model_name] = tf.keras.models.load_model(model_path)
                print(f"Loaded model: {model_name} v{config['version']}")
    
    def get_model(self, model_name: str):
        """获取指定模型"""
        return self.models.get(model_name)
    
    def get_loaded_models(self) -> List[str]:
        """获取已加载的模型列表"""
        return list(self.models.keys())
    
    def update_model(self, model_name: str, model_path: str, version: str):
        """更新模型"""
        # 备份当前模型
        if model_name in self.models:
            backup_path = f"models/backup/{model_name}_v{self.model_configs[model_name]['version']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.h5"
            self.models[model_name].save(backup_path)
        
        # 加载新模型
        new_model = tf.keras.models.load_model(model_path)
        self.models[model_name] = new_model
        
        # 更新配置
        self.model_configs[model_name]["version"] = version
        self.model_configs[model_name]["updated_at"] = datetime.now().isoformat()
        
        # 保存配置
        with open("models/model_config.json", 'w') as f:
            json.dump(self.model_configs, f, indent=2)
```

### 4.2 异步任务处理

#### 4.2.1 Celery任务队列

**Celery配置**:
```python
# celery_app.py
from celery import Celery
import os

# Celery配置
celery_app = Celery(
    "yogurt_ai",
    broker=os.getenv("REDIS_URL", "redis://localhost:6379/0"),
    backend=os.getenv("REDIS_URL", "redis://localhost:6379/0"),
    include=["tasks.analysis_tasks", "tasks.report_tasks"]
)

# 任务配置
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_routes={
        "tasks.analysis_tasks.analyze_images": {"queue": "analysis"},
        "tasks.report_tasks.generate_report": {"queue": "reports"},
    },
    task_annotations={
        "tasks.analysis_tasks.analyze_images": {"rate_limit": "10/m"},
    }
)
```

**分析任务定义**:
```python
# tasks/analysis_tasks.py
from celery import current_task
from celery_app import celery_app
from services.image_analyzer import ImageAnalyzer
import requests
import json

@celery_app.task(bind=True)
def analyze_images(self, batch_id: str, image_urls: list, analysis_params: dict):
    """异步图像分析任务"""
    try:
        # 更新任务状态
        self.update_state(state='PROGRESS', meta={'progress': 0, 'status': 'Starting analysis'})
        
        analyzer = ImageAnalyzer()
        
        # 下载图像
        images = []
        for i, url in enumerate(image_urls):
            response = requests.get(url)
            images.append(response.content)
            
            # 更新进度
            progress = int((i + 1) / len(image_urls) * 50)  # 下载占50%
            self.update_state(state='PROGRESS', meta={'progress': progress, 'status': f'Downloaded {i+1}/{len(image_urls)} images'})
        
        # 执行分析
        self.update_state(state='PROGRESS', meta={'progress': 50, 'status': 'Analyzing images'})
        
        result = analyzer.analyze_batch(
            images,
            analysis_params['magnification'],
            analysis_params['staining_method']
        )
        
        # 保存结果到数据库
        self.update_state(state='PROGRESS', meta={'progress': 90, 'status': 'Saving results'})
        
        # 调用Node.js API保存结果
        save_result = requests.post(
            f"{os.getenv('API_BASE_URL')}/api/analyses",
            json={
                'batch_id': batch_id,
                'result': result,
                'task_id': self.request.id
            }
        )
        
        self.update_state(state='SUCCESS', meta={'progress': 100, 'status': 'Analysis completed'})
        
        return result
        
    except Exception as e:
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise
```

---

## 5. 云服务架构

### 5.1 AWS云服务选型

#### 5.1.1 核心服务架构

**服务架构图**:
```mermaid
graph TB
    subgraph "用户层"
        A[Web应用] --> B[CloudFront CDN]
        C[移动应用] --> B
    end
    
    subgraph "应用层"
        B --> D[Application Load Balancer]
        D --> E[ECS Fargate - Node.js API]
        D --> F[ECS Fargate - AI Service]
    end
    
    subgraph "数据层"
        E --> G[RDS PostgreSQL]
        E --> H[ElastiCache Redis]
        F --> I[S3 - 图像存储]
        E --> I
    end
    
    subgraph "监控层"
        J[CloudWatch] --> E
        J --> F
        J --> G
        K[X-Ray] --> E
        K --> F
    end
```

#### 5.1.2 服务配置详情

**ECS Fargate配置**:
```yaml
# docker-compose.yml (用于本地开发)
version: '3.8'
services:
  api:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=******************************/yogurt_qc
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
    volumes:
      - ./backend:/app
      - /app/node_modules

  ai-service:
    build:
      context: ./ai-service
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    volumes:
      - ./ai-service:/app
      - ./models:/app/models

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=yogurt_qc
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

**Terraform基础设施配置**:
```hcl
# infrastructure/main.tf
provider "aws" {
  region = var.aws_region
}

# VPC配置
resource "aws_vpc" "main" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true
  
  tags = {
    Name = "yogurt-qc-vpc"
  }
}

# 子网配置
resource "aws_subnet" "private" {
  count             = 2
  vpc_id            = aws_vpc.main.id
  cidr_block        = "10.0.${count.index + 1}.0/24"
  availability_zone = data.aws_availability_zones.available.names[count.index]
  
  tags = {
    Name = "yogurt-qc-private-${count.index + 1}"
  }
}

resource "aws_subnet" "public" {
  count                   = 2
  vpc_id                  = aws_vpc.main.id
  cidr_block              = "10.0.${count.index + 10}.0/24"
  availability_zone       = data.aws_availability_zones.available.names[count.index]
  map_public_ip_on_launch = true
  
  tags = {
    Name = "yogurt-qc-public-${count.index + 1}"
  }
}

# RDS数据库
resource "aws_db_instance" "main" {
  identifier     = "yogurt-qc-db"
  engine         = "postgres"
  engine_version = "15.4"
  instance_class = "db.t3.micro"
  
  allocated_storage     = 20
  max_allocated_storage = 100
  storage_type          = "gp2"
  storage_encrypted     = true
  
  db_name  = "yogurt_qc"
  username = var.db_username
  password = var.db_password
  
  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = aws_db_subnet_group.main.name
  
  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  
  skip_final_snapshot = true
  
  tags = {
    Name = "yogurt-qc-database"
  }
}

# ElastiCache Redis
resource "aws_elasticache_subnet_group" "main" {
  name       = "yogurt-qc-cache-subnet"
  subnet_ids = aws_subnet.private[*].id
}

resource "aws_elasticache_cluster" "main" {
  cluster_id           = "yogurt-qc-cache"
  engine               = "redis"
  node_type            = "cache.t3.micro"
  num_cache_nodes      = 1
  parameter_group_name = "default.redis7"
  port                 = 6379
  subnet_group_name    = aws_elasticache_subnet_group.main.name
  security_group_ids   = [aws_security_group.redis.id]
  
  tags = {
    Name = "yogurt-qc-cache"
  }
}
```

### 5.2 容器化部署

#### 5.2.1 Docker配置

**Node.js API Dockerfile**:
```dockerfile
# backend/Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app

# 复制package文件
COPY package*.json ./
COPY tsconfig.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY src/ ./src/

# 构建应用
RUN npm run build

# 生产镜像
FROM node:18-alpine AS production

WORKDIR /app

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# 复制构建结果
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

# 设置用户
USER nodejs

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# 启动应用
CMD ["node", "dist/index.js"]
```

**AI服务Dockerfile**:
```dockerfile
# ai-service/Dockerfile
FROM python:3.11-slim AS base

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制requirements
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app
USER app

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

# 启动应用
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 5.2.2 Kubernetes部署配置

**API服务部署**:
```yaml
# k8s/api-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yogurt-qc-api
  labels:
    app: yogurt-qc-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: yogurt-qc-api
  template:
    metadata:
      labels:
        app: yogurt-qc-api
    spec:
      containers:
      - name: api
        image: yogurt-qc/api:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: yogurt-qc-api-service
spec:
  selector:
    app: yogurt-qc-api
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: ClusterIP
```

---

## 6. 监控与运维

### 6.1 监控体系

#### 6.1.1 Prometheus + Grafana

**Prometheus配置**:
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'yogurt-qc-api'
    static_configs:
      - targets: ['api:3000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'yogurt-qc-ai'
    static_configs:
      - targets: ['ai-service:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
```

**应用指标收集**:
```typescript
// backend/src/middleware/metrics.ts
import { Request, Response, NextFunction } from 'express';
import client from 'prom-client';

// 创建指标
const httpRequestDuration = new client.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
});

const httpRequestTotal = new client.Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code']
});

const aiAnalysisTotal = new client.Counter({
  name: 'ai_analysis_total',
  help: 'Total number of AI analyses',
  labelNames: ['status']
});

const aiAnalysisDuration = new client.Histogram({
  name: 'ai_analysis_duration_seconds',
  help: 'Duration of AI analysis in seconds',
  buckets: [1, 5, 10, 15, 30, 60]
});

// 中间件
export const metricsMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000;
    const route = req.route?.path || req.path;
    
    httpRequestDuration
      .labels(req.method, route, res.statusCode.toString())
      .observe(duration);
    
    httpRequestTotal
      .labels(req.method, route, res.statusCode.toString())
      .inc();
  });
  
  next();
};

// 指标端点
export const metricsHandler = async (req: Request, res: Response) => {
  res.set('Content-Type', client.register.contentType);
  const metrics = await client.register.metrics();
  res.send(metrics);
};
```

#### 6.1.2 日志管理

**结构化日志配置**:
```typescript
// backend/src/utils/logger.ts
import winston from 'winston';
import { ElasticsearchTransport } from 'winston-elasticsearch';

const esTransportOpts = {
  level: 'info',
  clientOpts: {
    node: process.env.ELASTICSEARCH_URL || 'http://localhost:9200'
  },
  index: 'yogurt-qc-logs'
};

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: {
    service: 'yogurt-qc-api',
    version: process.env.APP_VERSION || '1.0.0'
  },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error'
    }),
    new winston.transports.File({
      filename: 'logs/combined.log'
    })
  ]
});

// 生产环境添加Elasticsearch传输
if (process.env.NODE_ENV === 'production') {
  logger.add(new ElasticsearchTransport(esTransportOpts));
}

// 请求日志中间件
export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    
    logger.info('HTTP Request', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      userId: req.user?.id
    });
  });
  
  next();
};
```

### 6.2 安全配置

#### 6.2.1 认证与授权

**JWT认证实现**:
```typescript
// backend/src/middleware/auth.ts
import jwt from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';
import { UserService } from '../services/UserService';
import { logger } from '../utils/logger';

interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  iat: number;
  exp: number;
}

export const authMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ success: false, message: 'Access denied. No token provided.' });
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JWTPayload;
    
    // 验证用户是否仍然存在
    const userService = new UserService();
    const user = await userService.findById(decoded.userId);
    
    if (!user) {
      return res.status(401).json({ success: false, message: 'Invalid token. User not found.' });
    }
    
    req.user = user;
    next();
    
  } catch (error) {
    logger.error('Authentication error', { error: error.message, token: req.header('Authorization') });
    res.status(401).json({ success: false, message: 'Invalid token.' });
  }
};

// 角色权限检查
export const requireRole = (roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ success: false, message: 'Authentication required.' });
    }
    
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ success: false, message: 'Insufficient permissions.' });
    }
    
    next();
  };
};
```

#### 6.2.2 数据加密与安全

**敏感数据加密**:
```typescript
// backend/src/utils/encryption.ts
import crypto from 'crypto';
import bcrypt from 'bcrypt';

const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY!;
const ALGORITHM = 'aes-256-gcm';

export class EncryptionService {
  // 密码哈希
  static async hashPassword(password: string): Promise<string> {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
  }
  
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }
  
  // 敏感数据加密
  static encrypt(text: string): { encrypted: string; iv: string; tag: string } {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(ALGORITHM, ENCRYPTION_KEY);
    cipher.setAAD(Buffer.from('yogurt-qc', 'utf8'));
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const tag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex')
    };
  }
  
  static decrypt(encrypted: string, iv: string, tag: string): string {
    const decipher = crypto.createDecipher(ALGORITHM, ENCRYPTION_KEY);
    decipher.setAAD(Buffer.from('yogurt-qc', 'utf8'));
    decipher.setAuthTag(Buffer.from(tag, 'hex'));
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}
```

---

## 7. 开发与部署流程

### 7.1 CI/CD流程

#### 7.1.1 GitHub Actions配置

**主要工作流**:
```yaml
# .github/workflows/main.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'

jobs:
  test-backend:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json
    
    - name: Install dependencies
      working-directory: ./backend
      run: npm ci
    
    - name: Run linting
      working-directory: ./backend
      run: npm run lint
    
    - name: Run type checking
      working-directory: ./backend
      run: npm run type-check
    
    - name: Run tests
      working-directory: ./backend
      run: npm run test:coverage
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-secret
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage/lcov.info
        flags: backend

  test-ai-service:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
        cache-dependency-path: ai-service/requirements.txt
    
    - name: Install dependencies
      working-directory: ./ai-service
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-cov
    
    - name: Run linting
      working-directory: ./ai-service
      run: |
        pip install flake8 black
        flake8 .
        black --check .
    
    - name: Run tests
      working-directory: ./ai-service
      run: pytest --cov=. --cov-report=xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./ai-service/coverage.xml
        flags: ai-service

  build-and-deploy:
    needs: [test-backend, test-ai-service]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-west-2
    
    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2
    
    - name: Build and push API image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: yogurt-qc-api
        IMAGE_TAG: ${{ github.sha }}
      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG ./backend
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        docker tag $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY:latest
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest
    
    - name: Build and push AI service image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: yogurt-qc-ai
        IMAGE_TAG: ${{ github.sha }}
      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG ./ai-service
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        docker tag $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY:latest
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest
    
    - name: Deploy to ECS
      run: |
        aws ecs update-service --cluster yogurt-qc-cluster --service yogurt-qc-api-service --force-new-deployment
        aws ecs update-service --cluster yogurt-qc-cluster --service yogurt-qc-ai-service --force-new-deployment
```

### 7.2 环境管理

#### 7.2.1 多环境配置

**环境配置管理**:
```typescript
// backend/src/config/index.ts
import dotenv from 'dotenv';

// 根据环境加载配置文件
const envFile = `.env.${process.env.NODE_ENV || 'development'}`;
dotenv.config({ path: envFile });

interface Config {
  port: number;
  nodeEnv: string;
  database: {
    host: string;
    port: number;
    name: string;
    user: string;
    password: string;
    ssl: boolean;
  };
  redis: {
    host: string;
    port: number;
    password?: string;
  };
  jwt: {
    secret: string;
    expiresIn: string;
  };
  aws: {
    region: string;
    s3Bucket: string;
    accessKeyId: string;
    secretAccessKey: string;
  };
  ai: {
    serviceUrl: string;
    timeout: number;
  };
}

export const config: Config = {
  port: parseInt(process.env.PORT || '3000'),
  nodeEnv: process.env.NODE_ENV || 'development',
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    name: process.env.DB_NAME || 'yogurt_qc',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    ssl: process.env.DB_SSL === 'true',
  },
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
  },
  jwt: {
    secret: process.env.JWT_SECRET || 'your-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  },
  aws: {
    region: process.env.AWS_REGION || 'us-west-2',
    s3Bucket: process.env.AWS_S3_BUCKET || 'yogurt-qc-images',
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },
  ai: {
    serviceUrl: process.env.AI_SERVICE_URL || 'http://localhost:8000',
    timeout: parseInt(process.env.AI_TIMEOUT || '30000'),
  },
};
```

#### 7.2.2 数据库迁移

**迁移脚本管理**:
```typescript
// backend/src/database/migrations/001_initial_schema.ts
import { Pool } from 'pg';

export const up = async (pool: Pool): Promise<void> => {
  await pool.query(`
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    
    CREATE TABLE users (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      email VARCHAR(255) UNIQUE NOT NULL,
      password_hash VARCHAR(255) NOT NULL,
      name VARCHAR(100) NOT NULL,
      role VARCHAR(50) DEFAULT 'user',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    CREATE INDEX idx_users_email ON users(email);
    CREATE INDEX idx_users_role ON users(role);
  `);
};

export const down = async (pool: Pool): Promise<void> => {
  await pool.query('DROP TABLE IF EXISTS users CASCADE;');
};
```

---

## 8. 性能优化策略

### 8.1 前端性能优化

#### 8.1.1 代码分割与懒加载

```typescript
// frontend/src/router/index.tsx
import { lazy, Suspense } from 'react';
import { createBrowserRouter } from 'react-router-dom';
import Layout from '../components/Layout';
import LoadingSpinner from '../components/LoadingSpinner';

// 懒加载页面组件
const Dashboard = lazy(() => import('../pages/Dashboard'));
const Recipes = lazy(() => import('../pages/Recipes'));
const Analysis = lazy(() => import('../pages/Analysis'));
const Reports = lazy(() => import('../pages/Reports'));

const LazyWrapper = ({ children }: { children: React.ReactNode }) => (
  <Suspense fallback={<LoadingSpinner />}>
    {children}
  </Suspense>
);

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        index: true,
        element: <LazyWrapper><Dashboard /></LazyWrapper>,
      },
      {
        path: 'recipes',
        element: <LazyWrapper><Recipes /></LazyWrapper>,
      },
      {
        path: 'analysis',
        element: <LazyWrapper><Analysis /></LazyWrapper>,
      },
      {
        path: 'reports',
        element: <LazyWrapper><Reports /></LazyWrapper>,
      },
    ],
  },
]);
```

#### 8.1.2 图片优化

```typescript
// frontend/src/components/OptimizedImage.tsx
import React, { useState } from 'react';
import { Skeleton } from 'antd';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className,
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  // 生成不同尺寸的图片URL
  const generateSrcSet = (baseSrc: string) => {
    const sizes = [480, 768, 1024, 1280];
    return sizes
      .map(size => `${baseSrc}?w=${size} ${size}w`)
      .join(', ');
  };

  if (error) {
    return (
      <div className={`error-placeholder ${className}`} style={{ width, height }}>
        图片加载失败
      </div>
    );
  }

  return (
    <>
      {loading && (
        <Skeleton.Image
          style={{ width, height }}
          className={className}
        />
      )}
      <img
        src={src}
        srcSet={generateSrcSet(src)}
        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 25vw"
        alt={alt}
        width={width}
        height={height}
        className={className}
        loading="lazy"
        onLoad={() => setLoading(false)}
        onError={() => {
          setLoading(false);
          setError(true);
        }}
        style={{ display: loading ? 'none' : 'block' }}
      />
    </>
  );
};
```

### 8.2 后端性能优化

#### 8.2.1 数据库查询优化

```typescript
// backend/src/services/RecipeService.ts
export class RecipeService {
  // 使用连接查询减少数据库往返
  async getRecipeWithDetails(id: string): Promise<Recipe | null> {
    const query = `
      SELECT 
        r.*,
        u.name as creator_name,
        COUNT(b.id) as batch_count,
        AVG(qa.overall_score) as avg_quality_score
      FROM recipes r
      LEFT JOIN users u ON r.user_id = u.id
      LEFT JOIN batches b ON r.id = b.recipe_id
      LEFT JOIN quality_assessments qa ON b.id = qa.batch_id
      WHERE r.id = $1 AND r.is_active = true
      GROUP BY r.id, u.name
    `;
    
    const result = await pool.query(query, [id]);
    return result.rows[0] || null;
  }

  // 分页查询优化
  async getRecipesPaginated(userId: string, page: number, limit: number) {
    const offset = (page - 1) * limit;
    
    // 使用窗口函数进行高效分页
    const query = `
      WITH recipe_stats AS (
        SELECT 
          r.*,
          COUNT(b.id) OVER (PARTITION BY r.id) as batch_count,
          ROW_NUMBER() OVER (ORDER BY r.updated_at DESC) as row_num,
          COUNT(*) OVER () as total_count
        FROM recipes r
        LEFT JOIN batches b ON r.id = b.recipe_id
        WHERE r.user_id = $1 AND r.is_active = true
      )
      SELECT * FROM recipe_stats
      WHERE row_num > $2 AND row_num <= $3
    `;
    
    const result = await pool.query(query, [userId, offset, offset + limit]);
    
    return {
      recipes: result.rows,
      total: result.rows[0]?.total_count || 0,
      page,
      limit,
      totalPages: Math.ceil((result.rows[0]?.total_count || 0) / limit)
    };
  }
}
```

---

## 9. DevOps实践与监控体系

### 9.1 GitOps工作流

#### 9.1.1 Git分支策略

```mermaid
gitGraph
    commit id: "Initial"
    branch develop
    checkout develop
    commit id: "Feature A"
    branch feature/ai-analysis
    checkout feature/ai-analysis
    commit id: "AI Model"
    commit id: "API Integration"
    checkout develop
    merge feature/ai-analysis
    commit id: "Integration Test"
    checkout main
    merge develop
    commit id: "Release v1.0"
    branch hotfix/security-patch
    checkout hotfix/security-patch
    commit id: "Security Fix"
    checkout main
    merge hotfix/security-patch
    commit id: "Hotfix v1.0.1"
```

**分支管理规范**:
- `main`: 生产环境代码，只接受来自 `develop` 和 `hotfix` 的合并
- `develop`: 开发主分支，集成所有功能分支
- `feature/*`: 功能开发分支，从 `develop` 创建
- `hotfix/*`: 紧急修复分支，从 `main` 创建
- `release/*`: 发布准备分支，从 `develop` 创建

#### 9.1.2 CI/CD流水线增强

```yaml
# .github/workflows/advanced-ci-cd.yml
name: Advanced CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  security-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
      
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

  code-quality:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run ESLint
        run: npm run lint:ci
      
      - name: Run Prettier check
        run: npm run format:check
      
      - name: Run type check
        run: npm run type-check
      
      - name: SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  test-coverage:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm run test:unit -- --coverage
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          REDIS_URL: redis://localhost:6379
      
      - name: Run integration tests
        run: npm run test:integration
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          REDIS_URL: redis://localhost:6379
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  build-and-deploy:
    needs: [security-scan, code-quality, test-coverage]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2
      
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      
      - name: Build and push Docker images
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: yogurt-qc
          IMAGE_TAG: ${{ github.sha }}
        run: |
          # Build multi-stage Docker image
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:latest .
          
          # Push images
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest
      
      - name: Deploy to ECS
        run: |
          # Update ECS service
          aws ecs update-service \
            --cluster yogurt-qc-cluster \
            --service yogurt-qc-service \
            --force-new-deployment
```

### 9.2 监控与可观测性体系

#### 9.2.1 Prometheus监控配置

```yaml
# monitoring/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"
  - "recording_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Application metrics
  - job_name: 'yogurt-qc-backend'
    static_configs:
      - targets: ['backend:3000']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  # AI service metrics
  - job_name: 'yogurt-qc-ai'
    static_configs:
      - targets: ['ai-service:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Infrastructure metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # Database metrics
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Redis metrics
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']

  # Kubernetes metrics
  - job_name: 'kubernetes-apiservers'
    kubernetes_sd_configs:
      - role: endpoints
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: default;kubernetes;https
```

#### 9.2.2 告警规则配置

```yaml
# monitoring/prometheus/alert_rules.yml
groups:
  - name: yogurt-qc-alerts
    rules:
      # Application health alerts
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "{{ $labels.job }} has been down for more than 1 minute."

      - alert: HighErrorRate
        expr: |
          (
            rate(http_requests_total{status=~"5.."}[5m]) /
            rate(http_requests_total[5m])
          ) > 0.05
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for {{ $labels.job }}"

      - alert: HighResponseTime
        expr: |
          histogram_quantile(0.95, 
            rate(http_request_duration_seconds_bucket[5m])
          ) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s for {{ $labels.job }}"

      # Database alerts
      - alert: DatabaseConnectionsHigh
        expr: |
          (
            pg_stat_database_numbackends /
            pg_settings_max_connections
          ) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Database connections usage is high"
          description: "Database connections usage is {{ $value | humanizePercentage }}"

      - alert: DatabaseSlowQueries
        expr: |
          rate(pg_stat_statements_mean_time_seconds[5m]) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Slow database queries detected"
          description: "Average query time is {{ $value }}s"

      # AI service alerts
      - alert: AIModelInferenceSlowdown
        expr: |
          histogram_quantile(0.95,
            rate(ai_inference_duration_seconds_bucket[5m])
          ) > 30
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "AI model inference is slow"
          description: "95th percentile inference time is {{ $value }}s"

      - alert: AIModelAccuracyDrop
        expr: |
          ai_model_accuracy < 0.85
        for: 10m
        labels:
          severity: critical
        annotations:
          summary: "AI model accuracy has dropped"
          description: "Model accuracy is {{ $value | humanizePercentage }}"

      # Infrastructure alerts
      - alert: HighCPUUsage
        expr: |
          100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: HighMemoryUsage
        expr: |
          (
            (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) /
            node_memory_MemTotal_bytes
          ) > 0.85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}"

      - alert: DiskSpaceLow
        expr: |
          (
            node_filesystem_avail_bytes{mountpoint="/"} /
            node_filesystem_size_bytes{mountpoint="/"}
          ) < 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Disk space is running low"
          description: "Available disk space is {{ $value | humanizePercentage }} on {{ $labels.instance }}"
```

#### 9.2.3 Grafana仪表板配置

```json
{
  "dashboard": {
    "id": null,
    "title": "Yogurt QC System Overview",
    "tags": ["yogurt-qc", "overview"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Service Health",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=~\"yogurt-qc-.*\"}",
            "legendFormat": "{{ job }}"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "green", "value": 1}
              ]
            }
          }
        }
      },
      {
        "id": 2,
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{ method }} {{ handler }}"
          }
        ]
      },
      {
        "id": 3,
        "title": "Response Time (95th percentile)",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "{{ handler }}"
          }
        ]
      },
      {
        "id": 4,
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m])",
            "legendFormat": "Error Rate"
          }
        ]
      },
      {
        "id": 5,
        "title": "AI Model Performance",
        "type": "graph",
        "targets": [
          {
            "expr": "ai_model_accuracy",
            "legendFormat": "Accuracy"
          },
          {
            "expr": "histogram_quantile(0.95, rate(ai_inference_duration_seconds_bucket[5m]))",
            "legendFormat": "Inference Time (95th)"
          }
        ]
      },
      {
        "id": 6,
        "title": "Database Performance",
        "type": "graph",
        "targets": [
          {
            "expr": "pg_stat_database_numbackends",
            "legendFormat": "Active Connections"
          },
          {
            "expr": "rate(pg_stat_statements_calls[5m])",
            "legendFormat": "Query Rate"
          }
        ]
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
```

### 9.3 安全架构与零信任模型

#### 9.3.1 零信任网络架构

```mermaid
graph TB
    subgraph "External"
        User[用户]
        Mobile[移动应用]
    end
    
    subgraph "Edge Security"
        WAF[Web Application Firewall]
        CDN[CloudFront CDN]
        LB[Application Load Balancer]
    end
    
    subgraph "Identity & Access"
        IAM[AWS IAM]
        Cognito[Amazon Cognito]
        RBAC[Role-Based Access Control]
    end
    
    subgraph "Application Layer"
        API[API Gateway]
        Auth[Authentication Service]
        Backend[Backend Services]
    end
    
    subgraph "Data Layer"
        RDS[(PostgreSQL RDS)]
        Redis[(ElastiCache Redis)]
        S3[(S3 Bucket)]
    end
    
    subgraph "Security Services"
        KMS[AWS KMS]
        Secrets[AWS Secrets Manager]
        GuardDuty[AWS GuardDuty]
        Inspector[AWS Inspector]
    end
    
    User --> WAF
    Mobile --> WAF
    WAF --> CDN
    CDN --> LB
    LB --> API
    
    API --> Auth
    Auth --> IAM
    Auth --> Cognito
    Auth --> RBAC
    
    API --> Backend
    Backend --> RDS
    Backend --> Redis
    Backend --> S3
    
    Backend --> KMS
    Backend --> Secrets
    
    GuardDuty -.-> Backend
    Inspector -.-> Backend
```

#### 9.3.2 Kubernetes安全策略

```yaml
# security/network-policies/default-deny.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all
  namespace: yogurt-qc
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress

---
# security/network-policies/backend-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: backend-network-policy
  namespace: yogurt-qc
spec:
  podSelector:
    matchLabels:
      app: backend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: frontend
    - podSelector:
        matchLabels:
          app: api-gateway
    ports:
    - protocol: TCP
      port: 3000
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: postgres
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  - to: []
    ports:
    - protocol: TCP
      port: 443  # HTTPS outbound
    - protocol: TCP
      port: 53   # DNS
    - protocol: UDP
      port: 53   # DNS

---
# security/pod-security-policy.yaml
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: yogurt-qc-psp
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
  readOnlyRootFilesystem: true
  seccompProfile:
    type: RuntimeDefault

---
# security/rbac.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: yogurt-qc
  name: yogurt-qc-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: yogurt-qc-rolebinding
  namespace: yogurt-qc
subjects:
- kind: ServiceAccount
  name: yogurt-qc-serviceaccount
  namespace: yogurt-qc
roleRef:
  kind: Role
  name: yogurt-qc-role
  apiGroup: rbac.authorization.k8s.io
```

---

## 10. 总结与建议

### 9.1 技术栈总结

本技术栈建议为Yogurt-AI-QC系统提供了完整的解决方案：

**前端技术栈**:
- **Web**: React 18 + TypeScript + Ant Design
- **移动端**: React Native
- **状态管理**: Redux Toolkit + RTK Query
- **可视化**: ECharts

**后端技术栈**:
- **API服务**: Node.js + Express + TypeScript
- **数据库**: PostgreSQL + Redis
- **AI服务**: Python + FastAPI + TensorFlow
- **任务队列**: Celery + Redis

**云服务与运维**:
- **云平台**: AWS (ECS, RDS, ElastiCache, S3)
- **容器化**: Docker + Kubernetes
- **监控**: Prometheus + Grafana
- **CI/CD**: GitHub Actions

### 9.2 实施建议

#### 9.2.1 开发阶段建议

1. **MVP阶段** (1-3个月):
   - 优先实现核心功能：配方管理、基础AI分析
   - 使用简化的技术栈：单体应用 + SQLite
   - 快速验证产品概念

2. **产品优化阶段** (3-6个月):
   - 迁移到推荐的完整技术栈
   - 实现微服务架构
   - 添加高级AI功能和数据分析

3. **商业化阶段** (6-12个月):
   - 完善监控和运维体系
   - 实现多租户架构
   - 添加企业级功能

#### 9.2.2 团队配置建议

**技术团队结构**:
- **前端工程师** (2人): React/React Native开发
- **后端工程师** (2人): Node.js API开发
- **AI工程师** (1人): Python ML模型开发
- **DevOps工程师** (1人): 基础设施和部署
- **测试工程师** (1人): 自动化测试和质量保证

#### 9.2.3 风险控制

**技术风险**:
- AI模型准确性：建立完善的模型验证和A/B测试机制
- 性能瓶颈：实施渐进式优化和负载测试
- 数据安全：严格的访问控制和加密策略

**业务风险**:
- 用户接受度：持续的用户反馈收集和产品迭代
- 竞争压力：保持技术创新和功能差异化
- 合规要求：及时跟进行业法规变化

### 9.3 后续优化方向

1. **AI能力增强**:
   - 引入更先进的深度学习模型
   - 实现联邦学习保护数据隐私
   - 添加预测性分析功能

2. **平台化发展**:
   - 开放API生态
   - 支持第三方插件
   - 多行业适配能力

3. **智能化运维**:
   - AIOps自动化运维
   - 智能告警和故障预测
   - 自动扩缩容优化

---

**文档版本**: v1.0  
**最后更新**: 2025年07月  
**负责人**: 产品技术团队