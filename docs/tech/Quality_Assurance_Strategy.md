# 质量保证与测试策略

**文档版本**: v1.0  
**创建日期**: 2025年01月  
**负责人**: 技术团队  
**审核人**: 产品经理、技术负责人

---

## 1. 文档概述

### 1.1 文档目的

本文档定义了Yogurt-AI-QC系统的全面质量保证策略，包括测试策略、代码质量标准、自动化测试框架、性能测试、安全测试等方面的具体实施方案。

### 1.2 质量目标

- **代码覆盖率**: 单元测试覆盖率 ≥ 85%，集成测试覆盖率 ≥ 70%
- **缺陷密度**: 生产环境缺陷密度 ≤ 0.1 缺陷/KLOC
- **性能指标**: API响应时间P95 ≤ 500ms，AI推理时间 ≤ 30s
- **可用性**: 系统可用性 ≥ 99.9%
- **安全性**: 零高危安全漏洞，中危漏洞修复时间 ≤ 7天

---

## 2. 测试策略与框架

### 2.1 测试金字塔模型

```mermaid
graph TB
    subgraph "测试金字塔"
        E2E["端到端测试<br/>10%<br/>- 用户场景测试<br/>- 跨系统集成"]
        Integration["集成测试<br/>20%<br/>- API测试<br/>- 数据库集成<br/>- 第三方服务"]
        Unit["单元测试<br/>70%<br/>- 函数级测试<br/>- 组件测试<br/>- Mock依赖"]
    end
    
    E2E --> Integration
    Integration --> Unit
    
    style E2E fill:#ff9999
    style Integration fill:#99ccff
    style Unit fill:#99ff99
```

### 2.2 测试分层策略

#### 2.2.1 单元测试 (70%)

**目标**: 验证单个函数、方法、组件的正确性

**技术栈**:
- **前端**: Jest + React Testing Library + MSW
- **后端**: Jest + Supertest + Sinon
- **AI服务**: pytest + pytest-mock + pytest-asyncio

**示例配置**:

```typescript
// frontend/jest.config.js
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/test/setup.ts'],
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/test/**',
    '!src/stories/**',
  ],
  coverageThreshold: {
    global: {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85,
    },
  },
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
};
```

```typescript
// frontend/src/test/setup.ts
import '@testing-library/jest-dom';
import { server } from './mocks/server';

// 启动MSW服务器
beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));
```

**单元测试示例**:

```typescript
// frontend/src/components/RecipeCard/RecipeCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { RecipeCard } from './RecipeCard';
import { mockRecipe } from '../../test/mocks/recipe';

describe('RecipeCard', () => {
  it('should render recipe information correctly', () => {
    render(<RecipeCard recipe={mockRecipe} />);
    
    expect(screen.getByText(mockRecipe.name)).toBeInTheDocument();
    expect(screen.getByText(mockRecipe.description)).toBeInTheDocument();
    expect(screen.getByRole('img')).toHaveAttribute('alt', mockRecipe.name);
  });

  it('should call onEdit when edit button is clicked', () => {
    const onEdit = jest.fn();
    render(<RecipeCard recipe={mockRecipe} onEdit={onEdit} />);
    
    fireEvent.click(screen.getByRole('button', { name: /edit/i }));
    
    expect(onEdit).toHaveBeenCalledWith(mockRecipe.id);
  });

  it('should display quality score with correct color', () => {
    const highQualityRecipe = { ...mockRecipe, averageQualityScore: 9.5 };
    render(<RecipeCard recipe={highQualityRecipe} />);
    
    const scoreElement = screen.getByTestId('quality-score');
    expect(scoreElement).toHaveTextContent('9.5');
    expect(scoreElement).toHaveClass('score-excellent');
  });
});
```

```typescript
// backend/src/services/RecipeService.test.ts
import { RecipeService } from './RecipeService';
import { mockRepository } from '../test/mocks/repository';
import { mockRecipe, mockUser } from '../test/mocks/entities';

describe('RecipeService', () => {
  let recipeService: RecipeService;
  let mockRecipeRepo: jest.Mocked<typeof mockRepository>;

  beforeEach(() => {
    mockRecipeRepo = mockRepository;
    recipeService = new RecipeService(mockRecipeRepo);
  });

  describe('createRecipe', () => {
    it('should create recipe successfully', async () => {
      const recipeData = {
        name: 'Test Recipe',
        description: 'Test Description',
        ingredients: ['milk', 'culture'],
      };

      mockRecipeRepo.save.mockResolvedValue(mockRecipe);

      const result = await recipeService.createRecipe(mockUser.id, recipeData);

      expect(mockRecipeRepo.save).toHaveBeenCalledWith(
        expect.objectContaining({
          ...recipeData,
          userId: mockUser.id,
          isActive: true,
        })
      );
      expect(result).toEqual(mockRecipe);
    });

    it('should throw error for invalid ingredients', async () => {
      const invalidRecipeData = {
        name: 'Test Recipe',
        description: 'Test Description',
        ingredients: [], // 空数组应该抛出错误
      };

      await expect(
        recipeService.createRecipe(mockUser.id, invalidRecipeData)
      ).rejects.toThrow('Recipe must have at least one ingredient');
    });
  });
});
```

#### 2.2.2 集成测试 (20%)

**目标**: 验证模块间交互、API端点、数据库操作

**技术栈**:
- **API测试**: Supertest + Jest
- **数据库测试**: TestContainers + PostgreSQL
- **消息队列测试**: TestContainers + Redis

```typescript
// backend/src/test/integration/recipe.integration.test.ts
import request from 'supertest';
import { app } from '../../app';
import { setupTestDatabase, cleanupTestDatabase } from '../helpers/database';
import { createTestUser, createAuthToken } from '../helpers/auth';

describe('Recipe API Integration Tests', () => {
  let authToken: string;
  let testUser: any;

  beforeAll(async () => {
    await setupTestDatabase();
    testUser = await createTestUser();
    authToken = createAuthToken(testUser);
  });

  afterAll(async () => {
    await cleanupTestDatabase();
  });

  describe('POST /api/recipes', () => {
    it('should create recipe and return 201', async () => {
      const recipeData = {
        name: 'Integration Test Recipe',
        description: 'Test recipe for integration testing',
        ingredients: ['milk', 'yogurt culture'],
        fermentationTime: 8,
        temperature: 42,
      };

      const response = await request(app)
        .post('/api/recipes')
        .set('Authorization', `Bearer ${authToken}`)
        .send(recipeData)
        .expect(201);

      expect(response.body).toMatchObject({
        id: expect.any(String),
        name: recipeData.name,
        description: recipeData.description,
        userId: testUser.id,
        isActive: true,
        createdAt: expect.any(String),
      });
    });

    it('should return 400 for invalid recipe data', async () => {
      const invalidRecipeData = {
        name: '', // 空名称应该失败
        ingredients: [],
      };

      const response = await request(app)
        .post('/api/recipes')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidRecipeData)
        .expect(400);

      expect(response.body).toHaveProperty('errors');
      expect(response.body.errors).toContain('Recipe name is required');
    });

    it('should return 401 for unauthenticated request', async () => {
      const recipeData = {
        name: 'Test Recipe',
        ingredients: ['milk'],
      };

      await request(app)
        .post('/api/recipes')
        .send(recipeData)
        .expect(401);
    });
  });

  describe('GET /api/recipes', () => {
    it('should return paginated recipes', async () => {
      // 创建测试数据
      await createTestRecipes(testUser.id, 15);

      const response = await request(app)
        .get('/api/recipes?page=1&limit=10')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        recipes: expect.arrayContaining([
          expect.objectContaining({
            id: expect.any(String),
            name: expect.any(String),
            userId: testUser.id,
          }),
        ]),
        pagination: {
          page: 1,
          limit: 10,
          total: expect.any(Number),
          totalPages: expect.any(Number),
        },
      });

      expect(response.body.recipes).toHaveLength(10);
    });
  });
});
```

#### 2.2.3 端到端测试 (10%)

**目标**: 验证完整用户场景和跨系统集成

**技术栈**: Playwright + TypeScript

```typescript
// e2e/tests/recipe-management.spec.ts
import { test, expect } from '@playwright/test';
import { LoginPage } from '../pages/LoginPage';
import { RecipePage } from '../pages/RecipePage';

test.describe('Recipe Management E2E', () => {
  let loginPage: LoginPage;
  let recipePage: RecipePage;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    recipePage = new RecipePage(page);
    
    // 登录测试用户
    await loginPage.goto();
    await loginPage.login('<EMAIL>', 'password123');
  });

  test('should create, edit, and delete recipe', async ({ page }) => {
    // 创建配方
    await recipePage.goto();
    await recipePage.clickCreateRecipe();
    
    await recipePage.fillRecipeForm({
      name: 'E2E Test Recipe',
      description: 'Recipe created by E2E test',
      ingredients: ['Milk', 'Yogurt Culture'],
      fermentationTime: '8',
      temperature: '42',
    });
    
    await recipePage.submitRecipe();
    
    // 验证配方创建成功
    await expect(page.locator('[data-testid="success-message"]'))
      .toContainText('Recipe created successfully');
    
    await expect(page.locator('[data-testid="recipe-card"]'))
      .toContainText('E2E Test Recipe');
    
    // 编辑配方
    await recipePage.editRecipe('E2E Test Recipe');
    await recipePage.updateRecipeDescription('Updated description');
    await recipePage.submitRecipe();
    
    // 验证配方更新成功
    await expect(page.locator('[data-testid="recipe-card"]'))
      .toContainText('Updated description');
    
    // 删除配方
    await recipePage.deleteRecipe('E2E Test Recipe');
    await recipePage.confirmDelete();
    
    // 验证配方删除成功
    await expect(page.locator('[data-testid="recipe-card"]'))
      .not.toContainText('E2E Test Recipe');
  });

  test('should perform AI analysis workflow', async ({ page }) => {
    // 创建批次
    await recipePage.createBatch('Test Recipe');
    
    // 上传显微镜图片
    await recipePage.uploadMicroscopeImage('test-images/sample.jpg');
    
    // 等待AI分析完成
    await expect(page.locator('[data-testid="analysis-status"]'))
      .toContainText('Analysis Complete', { timeout: 60000 });
    
    // 验证分析结果
    await expect(page.locator('[data-testid="bacteria-count"]'))
      .toBeVisible();
    
    await expect(page.locator('[data-testid="quality-score"]'))
      .toBeVisible();
    
    // 保存质量评估
    await recipePage.saveQualityAssessment({
      overallScore: '8.5',
      notes: 'Good bacterial activity observed',
    });
    
    await expect(page.locator('[data-testid="success-message"]'))
      .toContainText('Quality assessment saved');
  });
});
```

---

## 3. 代码质量标准

### 3.1 静态代码分析

#### 3.1.1 ESLint配置

```json
{
  "extends": [
    "@typescript-eslint/recommended",
    "@typescript-eslint/recommended-requiring-type-checking",
    "prettier"
  ],
  "plugins": [
    "@typescript-eslint",
    "import",
    "jsx-a11y",
    "react-hooks",
    "security"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "warn",
    "@typescript-eslint/no-explicit-any": "error",
    "@typescript-eslint/prefer-nullish-coalescing": "error",
    "@typescript-eslint/prefer-optional-chain": "error",
    "import/order": [
      "error",
      {
        "groups": [
          "builtin",
          "external",
          "internal",
          "parent",
          "sibling",
          "index"
        ],
        "newlines-between": "always"
      }
    ],
    "security/detect-object-injection": "error",
    "security/detect-non-literal-regexp": "error",
    "complexity": ["error", 10],
    "max-depth": ["error", 4],
    "max-lines-per-function": ["error", 50]
  }
}
```

#### 3.1.2 SonarQube质量门禁

```yaml
# sonar-project.properties
sonar.projectKey=yogurt-ai-qc
sonar.organization=your-org
sonar.sources=src
sonar.tests=src
sonar.test.inclusions=**/*.test.ts,**/*.test.tsx,**/*.spec.ts
sonar.typescript.lcov.reportPaths=coverage/lcov.info
sonar.coverage.exclusions=**/*.test.ts,**/*.test.tsx,**/*.spec.ts,**/test/**,**/mocks/**

# 质量门禁标准
sonar.qualitygate.wait=true
sonar.coverage.minimum=85
sonar.duplicated_lines_density.maximum=3
sonar.maintainability_rating.minimum=A
sonar.reliability_rating.minimum=A
sonar.security_rating.minimum=A
```

### 3.2 代码审查标准

#### 3.2.1 Pull Request模板

```markdown
## 变更描述

### 变更类型
- [ ] 新功能 (feature)
- [ ] 缺陷修复 (bugfix)
- [ ] 性能优化 (performance)
- [ ] 重构 (refactor)
- [ ] 文档更新 (docs)
- [ ] 测试相关 (test)

### 变更内容
<!-- 详细描述本次变更的内容 -->

### 测试情况
- [ ] 单元测试已通过
- [ ] 集成测试已通过
- [ ] 手动测试已完成
- [ ] 性能测试已完成 (如适用)

### 检查清单
- [ ] 代码符合团队编码规范
- [ ] 已添加必要的测试用例
- [ ] 已更新相关文档
- [ ] 已考虑向后兼容性
- [ ] 已考虑安全性影响
- [ ] 已考虑性能影响

### 相关Issue
<!-- 关联的Issue编号 -->
Closes #

### 截图/演示
<!-- 如有UI变更，请提供截图或演示 -->
```

#### 3.2.2 代码审查检查点

**功能性检查**:
- 代码是否实现了预期功能
- 边界条件是否正确处理
- 错误处理是否完善
- 业务逻辑是否正确

**代码质量检查**:
- 代码可读性和可维护性
- 函数和类的单一职责原则
- 代码复杂度是否合理
- 命名是否清晰准确

**安全性检查**:
- 输入验证是否充分
- 是否存在SQL注入风险
- 敏感信息是否正确处理
- 权限控制是否正确

**性能检查**:
- 是否存在性能瓶颈
- 数据库查询是否优化
- 缓存策略是否合理
- 资源使用是否高效

---

## 4. 性能测试策略

### 4.1 性能测试类型

#### 4.1.1 负载测试

**目标**: 验证系统在预期负载下的性能表现

**工具**: K6 + InfluxDB + Grafana

```javascript
// performance/load-test.js
import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

const errorRate = new Rate('errors');

export const options = {
  stages: [
    { duration: '2m', target: 10 },   // 预热
    { duration: '5m', target: 50 },   // 正常负载
    { duration: '10m', target: 100 }, // 高负载
    { duration: '5m', target: 50 },   // 降负载
    { duration: '2m', target: 0 },    // 冷却
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95%请求响应时间<500ms
    http_req_failed: ['rate<0.01'],   // 错误率<1%
    errors: ['rate<0.01'],
  },
};

const BASE_URL = 'https://api.yogurt-qc.com';
const AUTH_TOKEN = 'your-test-token';

export default function () {
  const params = {
    headers: {
      'Authorization': `Bearer ${AUTH_TOKEN}`,
      'Content-Type': 'application/json',
    },
  };

  // 测试获取配方列表
  let response = http.get(`${BASE_URL}/api/recipes`, params);
  check(response, {
    'recipes list status is 200': (r) => r.status === 200,
    'recipes list response time < 500ms': (r) => r.timings.duration < 500,
  }) || errorRate.add(1);

  sleep(1);

  // 测试创建配方
  const recipeData = {
    name: `Load Test Recipe ${__VU}-${__ITER}`,
    description: 'Recipe created during load testing',
    ingredients: ['milk', 'culture'],
    fermentationTime: 8,
    temperature: 42,
  };

  response = http.post(`${BASE_URL}/api/recipes`, JSON.stringify(recipeData), params);
  check(response, {
    'create recipe status is 201': (r) => r.status === 201,
    'create recipe response time < 1000ms': (r) => r.timings.duration < 1000,
  }) || errorRate.add(1);

  sleep(2);

  // 测试AI分析接口
  const analysisData = {
    imageUrl: 'https://example.com/test-image.jpg',
    batchId: 'test-batch-id',
  };

  response = http.post(`${BASE_URL}/api/ai/analyze`, JSON.stringify(analysisData), params);
  check(response, {
    'ai analysis status is 200': (r) => r.status === 200,
    'ai analysis response time < 30000ms': (r) => r.timings.duration < 30000,
  }) || errorRate.add(1);

  sleep(3);
}
```

#### 4.1.2 压力测试

```javascript
// performance/stress-test.js
export const options = {
  stages: [
    { duration: '5m', target: 100 },  // 快速增加到正常负载
    { duration: '10m', target: 200 }, // 增加到2倍负载
    { duration: '10m', target: 400 }, // 增加到4倍负载
    { duration: '10m', target: 800 }, // 增加到8倍负载
    { duration: '5m', target: 0 },    // 快速降到0
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 在压力下允许更高的响应时间
    http_req_failed: ['rate<0.05'],    // 允许5%的错误率
  },
};
```

### 4.2 AI模型性能测试

```python
# ai-service/tests/performance/model_performance_test.py
import time
import asyncio
import statistics
from concurrent.futures import ThreadPoolExecutor
from typing import List

import pytest
from PIL import Image
import numpy as np

from src.models.bacteria_analyzer import BacteriaAnalyzer
from src.utils.image_processor import ImageProcessor

class ModelPerformanceTest:
    def __init__(self):
        self.analyzer = BacteriaAnalyzer()
        self.processor = ImageProcessor()
        self.test_images = self._load_test_images()
    
    def _load_test_images(self) -> List[np.ndarray]:
        """加载测试图片"""
        images = []
        for i in range(100):  # 100张测试图片
            # 生成模拟显微镜图片
            img = np.random.randint(0, 255, (1024, 1024, 3), dtype=np.uint8)
            images.append(img)
        return images
    
    def test_single_inference_performance(self):
        """测试单次推理性能"""
        inference_times = []
        
        for image in self.test_images[:10]:  # 测试10张图片
            start_time = time.time()
            
            # 预处理
            processed_image = self.processor.preprocess(image)
            
            # AI推理
            result = self.analyzer.analyze(processed_image)
            
            # 后处理
            formatted_result = self.processor.postprocess(result)
            
            end_time = time.time()
            inference_time = end_time - start_time
            inference_times.append(inference_time)
        
        # 性能断言
        avg_time = statistics.mean(inference_times)
        p95_time = np.percentile(inference_times, 95)
        
        assert avg_time < 20.0, f"Average inference time {avg_time:.2f}s exceeds 20s"
        assert p95_time < 30.0, f"P95 inference time {p95_time:.2f}s exceeds 30s"
        
        print(f"Average inference time: {avg_time:.2f}s")
        print(f"P95 inference time: {p95_time:.2f}s")
    
    def test_concurrent_inference_performance(self):
        """测试并发推理性能"""
        def analyze_image(image):
            start_time = time.time()
            processed_image = self.processor.preprocess(image)
            result = self.analyzer.analyze(processed_image)
            formatted_result = self.processor.postprocess(result)
            return time.time() - start_time
        
        # 测试不同并发级别
        for concurrency in [1, 2, 4, 8]:
            with ThreadPoolExecutor(max_workers=concurrency) as executor:
                start_time = time.time()
                
                futures = []
                for i in range(concurrency * 2):  # 每个线程处理2张图片
                    image = self.test_images[i % len(self.test_images)]
                    future = executor.submit(analyze_image, image)
                    futures.append(future)
                
                inference_times = [future.result() for future in futures]
                total_time = time.time() - start_time
                
                throughput = len(futures) / total_time
                avg_time = statistics.mean(inference_times)
                
                print(f"Concurrency {concurrency}: {throughput:.2f} images/s, avg time: {avg_time:.2f}s")
                
                # 并发性能断言
                if concurrency <= 4:
                    assert throughput > 0.1, f"Throughput {throughput:.2f} too low for concurrency {concurrency}"
    
    def test_memory_usage(self):
        """测试内存使用情况"""
        import psutil
        import gc
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 处理多张图片
        for i in range(50):
            image = self.test_images[i % len(self.test_images)]
            processed_image = self.processor.preprocess(image)
            result = self.analyzer.analyze(processed_image)
            
            # 每10张图片检查一次内存
            if i % 10 == 0:
                gc.collect()
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_increase = current_memory - initial_memory
                
                print(f"After {i+1} images: {current_memory:.1f}MB (+{memory_increase:.1f}MB)")
                
                # 内存增长不应超过500MB
                assert memory_increase < 500, f"Memory increase {memory_increase:.1f}MB too high"

if __name__ == "__main__":
    test = ModelPerformanceTest()
    test.test_single_inference_performance()
    test.test_concurrent_inference_performance()
    test.test_memory_usage()
```

---

## 5. 安全测试

### 5.1 自动化安全扫描

#### 5.1.1 依赖漏洞扫描

```yaml
# .github/workflows/security-scan.yml
name: Security Scan

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]
  schedule:
    - cron: '0 2 * * 1'  # 每周一凌晨2点

jobs:
  dependency-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Run npm audit
        run: |
          npm audit --audit-level=moderate
          npm audit fix --dry-run
      
      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=medium
      
      - name: Run OWASP Dependency Check
        uses: dependency-check/Dependency-Check_Action@main
        with:
          project: 'yogurt-qc'
          path: '.'
          format: 'ALL'
          args: >
            --enableRetired
            --enableExperimental
            --failOnCVSS 7
  
  code-security-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Run CodeQL Analysis
        uses: github/codeql-action/init@v2
        with:
          languages: typescript, javascript
          queries: security-and-quality
      
      - name: Autobuild
        uses: github/codeql-action/autobuild@v2
      
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2
      
      - name: Run Semgrep
        uses: returntocorp/semgrep-action@v1
        with:
          config: >-
            p/security-audit
            p/secrets
            p/owasp-top-ten
```

#### 5.1.2 渗透测试

```python
# security/penetration_test.py
import requests
import json
from typing import Dict, List

class SecurityPenetrationTest:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = requests.Session()
        self.vulnerabilities = []
    
    def test_sql_injection(self):
        """SQL注入测试"""
        payloads = [
            "' OR '1'='1",
            "'; DROP TABLE users; --",
            "' UNION SELECT * FROM users --",
            "1' AND (SELECT COUNT(*) FROM users) > 0 --"
        ]
        
        endpoints = [
            "/api/recipes",
            "/api/batches",
            "/api/users"
        ]
        
        for endpoint in endpoints:
            for payload in payloads:
                # 测试GET参数
                response = self.session.get(
                    f"{self.base_url}{endpoint}",
                    params={"id": payload}
                )
                
                if self._detect_sql_error(response):
                    self.vulnerabilities.append({
                        "type": "SQL Injection",
                        "endpoint": endpoint,
                        "payload": payload,
                        "severity": "HIGH"
                    })
    
    def test_xss_vulnerabilities(self):
        """XSS漏洞测试"""
        payloads = [
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "<svg onload=alert('XSS')>"
        ]
        
        # 测试用户输入字段
        test_data = {
            "name": payloads[0],
            "description": payloads[1],
            "notes": payloads[2]
        }
        
        response = self.session.post(
            f"{self.base_url}/api/recipes",
            json=test_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        if any(payload in response.text for payload in payloads):
            self.vulnerabilities.append({
                "type": "XSS",
                "endpoint": "/api/recipes",
                "payload": str(test_data),
                "severity": "MEDIUM"
            })
    
    def test_authentication_bypass(self):
        """认证绕过测试"""
        protected_endpoints = [
            "/api/recipes",
            "/api/batches",
            "/api/ai/analyze",
            "/api/users/profile"
        ]
        
        for endpoint in protected_endpoints:
            # 测试无认证访问
            response = self.session.get(f"{self.base_url}{endpoint}")
            
            if response.status_code != 401:
                self.vulnerabilities.append({
                    "type": "Authentication Bypass",
                    "endpoint": endpoint,
                    "status_code": response.status_code,
                    "severity": "HIGH"
                })
            
            # 测试无效token
            response = self.session.get(
                f"{self.base_url}{endpoint}",
                headers={"Authorization": "Bearer invalid-token"}
            )
            
            if response.status_code not in [401, 403]:
                self.vulnerabilities.append({
                    "type": "Invalid Token Acceptance",
                    "endpoint": endpoint,
                    "status_code": response.status_code,
                    "severity": "HIGH"
                })
    
    def test_rate_limiting(self):
        """速率限制测试"""
        endpoint = f"{self.base_url}/api/auth/login"
        
        # 快速发送100个请求
        responses = []
        for i in range(100):
            response = self.session.post(endpoint, json={
                "email": "<EMAIL>",
                "password": "wrong-password"
            })
            responses.append(response.status_code)
        
        # 检查是否有速率限制
        rate_limited = any(status == 429 for status in responses)
        
        if not rate_limited:
            self.vulnerabilities.append({
                "type": "No Rate Limiting",
                "endpoint": endpoint,
                "severity": "MEDIUM",
                "description": "Endpoint allows unlimited requests"
            })
    
    def _detect_sql_error(self, response) -> bool:
        """检测SQL错误信息"""
        sql_errors = [
            "SQL syntax error",
            "mysql_fetch_array",
            "ORA-01756",
            "Microsoft OLE DB Provider",
            "PostgreSQL query failed"
        ]
        
        return any(error.lower() in response.text.lower() for error in sql_errors)
    
    def generate_report(self) -> Dict:
        """生成安全测试报告"""
        high_severity = [v for v in self.vulnerabilities if v["severity"] == "HIGH"]
        medium_severity = [v for v in self.vulnerabilities if v["severity"] == "MEDIUM"]
        
        return {
            "total_vulnerabilities": len(self.vulnerabilities),
            "high_severity_count": len(high_severity),
            "medium_severity_count": len(medium_severity),
            "vulnerabilities": self.vulnerabilities,
            "security_score": max(0, 100 - (len(high_severity) * 20 + len(medium_severity) * 10))
        }

if __name__ == "__main__":
    tester = SecurityPenetrationTest("https://api.yogurt-qc.com")
    
    tester.test_sql_injection()
    tester.test_xss_vulnerabilities()
    tester.test_authentication_bypass()
    tester.test_rate_limiting()
    
    report = tester.generate_report()
    print(json.dumps(report, indent=2))
```

---

## 6. 测试数据管理

### 6.1 测试数据策略

#### 6.1.1 测试数据分类

**静态测试数据**:
- 用户账户数据
- 基础配方数据
- 参考图片数据

**动态测试数据**:
- 批次记录
- AI分析结果
- 质量评估数据

**敏感测试数据**:
- 脱敏的生产数据
- 合成的个人信息
- 模拟的商业数据

#### 6.1.2 测试数据工厂

```typescript
// backend/src/test/factories/UserFactory.ts
import { faker } from '@faker-js/faker';
import { User } from '../entities/User';

export class UserFactory {
  static create(overrides: Partial<User> = {}): User {
    return {
      id: faker.string.uuid(),
      email: faker.internet.email(),
      name: faker.person.fullName(),
      role: 'user',
      passwordHash: faker.string.alphanumeric(60),
      isActive: true,
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      ...overrides,
    };
  }

  static createAdmin(overrides: Partial<User> = {}): User {
    return this.create({
      role: 'admin',
      ...overrides,
    });
  }

  static createBatch(count: number, overrides: Partial<User> = {}): User[] {
    return Array.from({ length: count }, () => this.create(overrides));
  }
}
```

```typescript
// backend/src/test/factories/RecipeFactory.ts
export class RecipeFactory {
  static create(overrides: Partial<Recipe> = {}): Recipe {
    return {
      id: faker.string.uuid(),
      name: faker.commerce.productName(),
      description: faker.lorem.paragraph(),
      ingredients: [
        'Whole milk',
        'Yogurt culture',
        faker.commerce.productMaterial(),
      ],
      fermentationTime: faker.number.int({ min: 4, max: 12 }),
      temperature: faker.number.int({ min: 35, max: 45 }),
      userId: faker.string.uuid(),
      isActive: true,
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      ...overrides,
    };
  }

  static createWithBatches(batchCount: number = 3): Recipe & { batches: Batch[] } {
    const recipe = this.create();
    const batches = BatchFactory.createBatch(batchCount, { recipeId: recipe.id });
    
    return {
      ...recipe,
      batches,
    };
  }
}
```

### 6.2 测试环境管理

#### 6.2.1 Docker测试环境

```yaml
# docker-compose.test.yml
version: '3.8'

services:
  postgres-test:
    image: postgres:15
    environment:
      POSTGRES_DB: yogurt_qc_test
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
    ports:
      - "5433:5432"
    volumes:
      - ./test/sql:/docker-entrypoint-initdb.d
    tmpfs:
      - /var/lib/postgresql/data

  redis-test:
    image: redis:7
    ports:
      - "6380:6379"
    tmpfs:
      - /data

  minio-test:
    image: minio/minio
    environment:
      MINIO_ROOT_USER: testuser
      MINIO_ROOT_PASSWORD: testpassword
    ports:
      - "9001:9000"
      - "9002:9001"
    command: server /data --console-address ":9001"
    tmpfs:
      - /data

  backend-test:
    build:
      context: .
      dockerfile: Dockerfile.test
    environment:
      NODE_ENV: test
      DATABASE_URL: *******************************************************/yogurt_qc_test
      REDIS_URL: redis://redis-test:6379
      S3_ENDPOINT: http://minio-test:9000
      S3_ACCESS_KEY: testuser
      S3_SECRET_KEY: testpassword
    depends_on:
      - postgres-test
      - redis-test
      - minio-test
    volumes:
      - .:/app
      - /app/node_modules
    command: npm run test:integration
```

---

## 7. 持续集成与质量门禁

### 7.1 质量门禁标准

```yaml
# .github/workflows/quality-gate.yml
name: Quality Gate

on:
  pull_request:
    branches: [main, develop]

jobs:
  quality-gate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # 获取完整历史用于SonarQube分析
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      # 代码质量检查
      - name: Run ESLint
        run: npm run lint:ci
      
      - name: Run Prettier check
        run: npm run format:check
      
      - name: Run TypeScript check
        run: npm run type-check
      
      # 测试执行
      - name: Run unit tests
        run: npm run test:unit -- --coverage
      
      - name: Run integration tests
        run: npm run test:integration
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
      
      # 安全扫描
      - name: Run security audit
        run: npm audit --audit-level=moderate
      
      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      
      # SonarQube分析
      - name: SonarQube Scan
        uses: sonarqube-quality-gate-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      
      # 质量门禁检查
      - name: Quality Gate Check
        run: |
          # 检查测试覆盖率
          COVERAGE=$(cat coverage/coverage-summary.json | jq '.total.lines.pct')
          if (( $(echo "$COVERAGE < 85" | bc -l) )); then
            echo "Test coverage $COVERAGE% is below 85%"
            exit 1
          fi
          
          # 检查代码复杂度
          npm run complexity:check
          
          # 检查包大小
          npm run bundle:analyze
      
      # 性能测试
      - name: Run performance tests
        run: npm run test:performance
      
      # 生成质量报告
      - name: Generate quality report
        run: |
          npm run report:generate
          echo "## Quality Gate Results" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Code coverage: $COVERAGE%" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Security scan: Passed" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Performance tests: Passed" >> $GITHUB_STEP_SUMMARY
```

### 7.2 质量指标监控

```typescript
// scripts/quality-metrics.ts
import fs from 'fs';
import path from 'path';

interface QualityMetrics {
  testCoverage: number;
  codeComplexity: number;
  duplicatedLines: number;
  technicalDebt: string;
  securityIssues: number;
  performanceScore: number;
}

class QualityMetricsCollector {
  async collectMetrics(): Promise<QualityMetrics> {
    const coverage = await this.getTestCoverage();
    const complexity = await this.getCodeComplexity();
    const duplication = await this.getDuplicatedLines();
    const debt = await this.getTechnicalDebt();
    const security = await this.getSecurityIssues();
    const performance = await this.getPerformanceScore();

    return {
      testCoverage: coverage,
      codeComplexity: complexity,
      duplicatedLines: duplication,
      technicalDebt: debt,
      securityIssues: security,
      performanceScore: performance,
    };
  }

  private async getTestCoverage(): Promise<number> {
    const coverageFile = path.join(process.cwd(), 'coverage/coverage-summary.json');
    if (!fs.existsSync(coverageFile)) {
      throw new Error('Coverage file not found');
    }

    const coverage = JSON.parse(fs.readFileSync(coverageFile, 'utf8'));
    return coverage.total.lines.pct;
  }

  private async getCodeComplexity(): Promise<number> {
    // 使用 ESLint complexity 规则的结果
    const eslintResults = JSON.parse(
      fs.readFileSync('eslint-results.json', 'utf8')
    );

    let totalComplexity = 0;
    let functionCount = 0;

    eslintResults.forEach((file: any) => {
      file.messages.forEach((message: any) => {
        if (message.ruleId === 'complexity') {
          totalComplexity += message.complexity || 0;
          functionCount++;
        }
      });
    });

    return functionCount > 0 ? totalComplexity / functionCount : 0;
  }

  private async getDuplicatedLines(): Promise<number> {
    // 从 SonarQube 或 jscpd 获取重复代码行数
    const jscpdResults = JSON.parse(
      fs.readFileSync('jscpd-report.json', 'utf8')
    );

    return jscpdResults.statistics.total.duplicatedLines || 0;
  }

  private async getTechnicalDebt(): Promise<string> {
    // 从 SonarQube 获取技术债务
    const sonarResults = JSON.parse(
      fs.readFileSync('sonar-results.json', 'utf8')
    );

    return sonarResults.measures.find(
      (m: any) => m.metric === 'sqale_index'
    )?.value || '0min';
  }

  private async getSecurityIssues(): Promise<number> {
    // 从安全扫描工具获取安全问题数量
    const snykResults = JSON.parse(
      fs.readFileSync('snyk-results.json', 'utf8')
    );

    return snykResults.vulnerabilities?.length || 0;
  }

  private async getPerformanceScore(): Promise<number> {
    // 从性能测试结果获取性能分数
    const k6Results = JSON.parse(
      fs.readFileSync('k6-results.json', 'utf8')
    );

    const p95ResponseTime = k6Results.metrics.http_req_duration.values.p95;
    const errorRate = k6Results.metrics.http_req_failed.values.rate;

    // 基于响应时间和错误率计算性能分数
    let score = 100;
    if (p95ResponseTime > 500) score -= 20;
    if (p95ResponseTime > 1000) score -= 30;
    if (errorRate > 0.01) score -= 25;
    if (errorRate > 0.05) score -= 50;

    return Math.max(0, score);
  }

  async generateReport(): Promise<void> {
    const metrics = await this.collectMetrics();
    
    const report = `
# Quality Metrics Report

Generated: ${new Date().toISOString()}

## Summary

| Metric | Value | Status |
|--------|-------|--------|
| Test Coverage | ${metrics.testCoverage}% | ${metrics.testCoverage >= 85 ? '✅' : '❌'} |
| Code Complexity | ${metrics.codeComplexity.toFixed(2)} | ${metrics.codeComplexity <= 10 ? '✅' : '❌'} |
| Duplicated Lines | ${metrics.duplicatedLines} | ${metrics.duplicatedLines <= 100 ? '✅' : '❌'} |
| Technical Debt | ${metrics.technicalDebt} | ℹ️ |
| Security Issues | ${metrics.securityIssues} | ${metrics.securityIssues === 0 ? '✅' : '❌'} |
| Performance Score | ${metrics.performanceScore} | ${metrics.performanceScore >= 80 ? '✅' : '❌'} |

## Quality Gate Status

${this.getQualityGateStatus(metrics)}

## Recommendations

${this.getRecommendations(metrics)}
`;

    fs.writeFileSync('quality-report.md', report);
    console.log('Quality report generated: quality-report.md');
  }

  private getQualityGateStatus(metrics: QualityMetrics): string {
    const passed = 
      metrics.testCoverage >= 85 &&
      metrics.codeComplexity <= 10 &&
      metrics.securityIssues === 0 &&
      metrics.performanceScore >= 80;

    return passed ? '✅ **PASSED**' : '❌ **FAILED**';
  }

  private getRecommendations(metrics: QualityMetrics): string {
    const recommendations: string[] = [];

    if (metrics.testCoverage < 85) {
      recommendations.push('- Increase test coverage to at least 85%');
    }

    if (metrics.codeComplexity > 10) {
      recommendations.push('- Reduce code complexity by refactoring complex functions');
    }

    if (metrics.duplicatedLines > 100) {
      recommendations.push('- Reduce code duplication by extracting common functionality');
    }

    if (metrics.securityIssues > 0) {
      recommendations.push('- Fix all security vulnerabilities');
    }

    if (metrics.performanceScore < 80) {
      recommendations.push('- Improve application performance');
    }

    return recommendations.length > 0 
      ? recommendations.join('\n')
      : '- All quality metrics are within acceptable ranges';
  }
}

// 执行质量指标收集
if (require.main === module) {
  const collector = new QualityMetricsCollector();
  collector.generateReport().catch(console.error);
}

export { QualityMetricsCollector, QualityMetrics };
```

---

## 8. 总结与实施建议

### 8.1 实施路线图

#### 阶段1: 基础测试框架搭建 (2-3周)
- 配置单元测试环境
- 建立代码质量标准
- 实施基础CI/CD流水线
- 达成目标：单元测试覆盖率 > 70%

#### 阶段2: 集成测试与自动化 (3-4周)
- 搭建集成测试环境
- 实施API自动化测试
- 配置性能测试基线
- 达成目标：集成测试覆盖率 > 60%

#### 阶段3: 端到端测试与监控 (4-5周)
- 实施E2E测试框架
- 建立质量监控体系
- 完善安全测试流程
- 达成目标：完整的质量保证体系

#### 阶段4: 优化与持续改进 (持续)
- 优化测试执行效率
- 完善质量指标监控
- 持续改进测试策略
- 达成目标：世界级的质量标准

### 8.2 成功关键因素

1. **团队承诺**: 全团队对质量的承诺和投入
2. **工具选择**: 选择合适的测试工具和框架
3. **流程规范**: 建立清晰的测试流程和标准
4. **持续改进**: 基于数据驱动的持续优化
5. **文化建设**: 培养质量优先的团队文化

### 8.3 风险控制

**技术风险**:
- 测试环境不稳定：使用容器化和基础设施即代码
- 测试数据管理：建立完善的测试数据策略
- 测试执行时间过长：并行化和优化测试执行

**流程风险**:
- 质量门禁过严：渐进式提高质量标准
- 开发效率影响：平衡质量和效率
- 团队抗拒：加强培训和文化建设

---

**文档版本**: v1.0  
**最后更新**: 2025年01月  
**负责人**: 技术团队