# 基础设施即代码 (Infrastructure as Code)

## 文档概述

### 1.1 文档目的
本文档为酸奶AI质控系统提供完整的基础设施即代码(IaC)解决方案，包括Terraform配置、Kubernetes部署、Helm Charts和GitOps工作流，确保基础设施的可重复性、可维护性和可扩展性。

### 1.2 技术栈
- **IaC工具**: Terraform, Terragrunt
- **容器编排**: Kubernetes, Helm
- **云平台**: AWS (主要), 支持多云架构
- **GitOps**: ArgoCD, Flux
- **监控**: Prometheus, Grafana
- **日志**: ELK Stack, Fluentd

### 1.3 架构原则
- **模块化设计**: 可重用的Terraform模块
- **环境隔离**: 开发、测试、生产环境完全隔离
- **安全优先**: 零信任网络架构
- **可观测性**: 全面的监控和日志收集
- **自动化**: GitOps驱动的自动化部署

---

## 2. Terraform基础设施配置

### 2.1 项目结构

```

#### 4.2.2 应用配置模板

```yaml
# argocd/applications/yoghurt-ai-backend.yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: yoghurt-ai-backend
  namespace: argocd
  labels:
    app.kubernetes.io/name: yoghurt-ai-backend
    app.kubernetes.io/part-of: yoghurt-ai
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: https://github.com/yoghurt-ai/k8s-manifests
    targetRevision: HEAD
    path: charts/yoghurt-ai
    helm:
      valueFiles:
        - values-production.yaml
      parameters:
        - name: image.tag
          value: "v1.2.3"
        - name: replicaCount
          value: "3"
  destination:
    server: https://kubernetes.default.svc
    namespace: yoghurt-ai-prod
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 10
  ignoreDifferences:
    - group: apps
      kind: Deployment
      jsonPointers:
        - /spec/replicas
    - group: ""
      kind: Secret
      jsonPointers:
        - /data
```

#### 4.2.3 应用集配置

```yaml
# argocd/applicationsets/yoghurt-ai-environments.yaml
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: yoghurt-ai-environments
  namespace: argocd
spec:
  generators:
    - clusters:
        selector:
          matchLabels:
            environment: production
    - list:
        elements:
          - cluster: dev
            url: https://dev-cluster.yoghurt-ai.com
            environment: development
            namespace: yoghurt-ai-dev
            replicas: "1"
            resources:
              requests:
                cpu: "100m"
                memory: "256Mi"
              limits:
                cpu: "500m"
                memory: "1Gi"
          - cluster: staging
            url: https://staging-cluster.yoghurt-ai.com
            environment: staging
            namespace: yoghurt-ai-staging
            replicas: "2"
            resources:
              requests:
                cpu: "200m"
                memory: "512Mi"
              limits:
                cpu: "1000m"
                memory: "2Gi"
          - cluster: prod
            url: https://prod-cluster.yoghurt-ai.com
            environment: production
            namespace: yoghurt-ai-prod
            replicas: "3"
            resources:
              requests:
                cpu: "500m"
                memory: "1Gi"
              limits:
                cpu: "2000m"
                memory: "4Gi"
  template:
    metadata:
      name: '{{cluster}}-yoghurt-ai'
      labels:
        environment: '{{environment}}'
    spec:
      project: default
      source:
        repoURL: https://github.com/yoghurt-ai/k8s-manifests
        targetRevision: HEAD
        path: charts/yoghurt-ai
        helm:
          valueFiles:
            - 'values-{{environment}}.yaml'
          parameters:
            - name: replicaCount
              value: '{{replicas}}'
            - name: resources.requests.cpu
              value: '{{resources.requests.cpu}}'
            - name: resources.requests.memory
              value: '{{resources.requests.memory}}'
            - name: resources.limits.cpu
              value: '{{resources.limits.cpu}}'
            - name: resources.limits.memory
              value: '{{resources.limits.memory}}'
      destination:
        server: '{{url}}'
        namespace: '{{namespace}}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
```

### 4.3 环境管理与配置

#### 4.3.1 多环境配置策略

```yaml
# environments/base/kustomization.yaml
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - namespace.yaml
  - configmap.yaml
  - secret.yaml
  - deployment.yaml
  - service.yaml
  - ingress.yaml
  - hpa.yaml
  - pdb.yaml
  - networkpolicy.yaml

commonLabels:
  app.kubernetes.io/name: yoghurt-ai
  app.kubernetes.io/part-of: yoghurt-ai-platform

commonAnnotations:
  app.kubernetes.io/version: "1.0.0"
  app.kubernetes.io/managed-by: argocd

images:
  - name: yoghurt-ai-backend
    newTag: latest
  - name: yoghurt-ai-ai-service
    newTag: latest

replicas:
  - name: yoghurt-ai-backend
    count: 1
  - name: yoghurt-ai-ai-service
    count: 1
```

```yaml
# environments/development/kustomization.yaml
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: yoghurt-ai-dev

resources:
  - ../base

patchesStrategicMerge:
  - deployment-patch.yaml
  - ingress-patch.yaml

configMapGenerator:
  - name: app-config
    literals:
      - NODE_ENV=development
      - LOG_LEVEL=debug
      - API_BASE_URL=https://api-dev.yoghurt-ai.com
      - FRONTEND_URL=https://dev.yoghurt-ai.com
      - ENABLE_SWAGGER=true
      - RATE_LIMIT_WINDOW=60000
      - RATE_LIMIT_MAX=1000

secretGenerator:
  - name: app-secrets
    literals:
      - DATABASE_URL=******************************************/yoghurt_ai_dev
      - REDIS_URL=redis://dev-redis:6379
      - JWT_SECRET=dev-jwt-secret-key
      - AWS_ACCESS_KEY_ID=dev-access-key
      - AWS_SECRET_ACCESS_KEY=dev-secret-key

images:
  - name: yoghurt-ai-backend
    newTag: dev-latest
  - name: yoghurt-ai-ai-service
    newTag: dev-latest

replicas:
  - name: yoghurt-ai-backend
    count: 1
  - name: yoghurt-ai-ai-service
    count: 1
```

```yaml
# environments/production/kustomization.yaml
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: yoghurt-ai-prod

resources:
  - ../base
  - monitoring.yaml
  - backup-cronjob.yaml

patchesStrategicMerge:
  - deployment-patch.yaml
  - ingress-patch.yaml
  - hpa-patch.yaml

configMapGenerator:
  - name: app-config
    literals:
      - NODE_ENV=production
      - LOG_LEVEL=info
      - API_BASE_URL=https://api.yoghurt-ai.com
      - FRONTEND_URL=https://yoghurt-ai.com
      - ENABLE_SWAGGER=false
      - RATE_LIMIT_WINDOW=60000
      - RATE_LIMIT_MAX=100
      - METRICS_ENABLED=true
      - HEALTH_CHECK_INTERVAL=30

secretGenerator:
  - name: app-secrets
    literals:
      - DATABASE_URL=postgresql://prod_user:${PROD_DB_PASSWORD}@prod-db-cluster:5432/yoghurt_ai
      - REDIS_URL=redis://prod-redis-cluster:6379
      - JWT_SECRET=${PROD_JWT_SECRET}
      - AWS_ACCESS_KEY_ID=${PROD_AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${PROD_AWS_SECRET_ACCESS_KEY}
      - OPENAI_API_KEY=${PROD_OPENAI_API_KEY}

images:
  - name: yoghurt-ai-backend
    newTag: v1.2.3
  - name: yoghurt-ai-ai-service
    newTag: v1.2.3

replicas:
  - name: yoghurt-ai-backend
    count: 3
  - name: yoghurt-ai-ai-service
    count: 2
```

#### 4.3.2 环境特定补丁

```yaml
# environments/production/deployment-patch.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yoghurt-ai-backend
spec:
  template:
    spec:
      containers:
        - name: backend
          resources:
            requests:
              cpu: 500m
              memory: 1Gi
            limits:
              cpu: 2000m
              memory: 4Gi
          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /ready
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
          env:
            - name: NODE_OPTIONS
              value: "--max-old-space-size=3072"
            - name: UV_THREADPOOL_SIZE
              value: "128"
      nodeSelector:
        kubernetes.io/arch: amd64
        node.kubernetes.io/instance-type: m5.xlarge
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app.kubernetes.io/name
                      operator: In
                      values:
                        - yoghurt-ai-backend
                topologyKey: kubernetes.io/hostname
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yoghurt-ai-ai-service
spec:
  template:
    spec:
      containers:
        - name: ai-service
          resources:
            requests:
              cpu: 1000m
              memory: 2Gi
              nvidia.com/gpu: 1
            limits:
              cpu: 4000m
              memory: 8Gi
              nvidia.com/gpu: 1
          env:
            - name: CUDA_VISIBLE_DEVICES
              value: "0"
            - name: PYTORCH_CUDA_ALLOC_CONF
              value: "max_split_size_mb:512"
      nodeSelector:
        kubernetes.io/arch: amd64
        accelerator: nvidia-tesla-t4
      tolerations:
        - key: nvidia.com/gpu
          operator: Exists
          effect: NoSchedule
```

---

## 5. 监控与可观测性

### 5.1 Prometheus监控配置

```yaml
# monitoring/prometheus/values.yaml
prometheus:
  prometheusSpec:
    retention: 30d
    retentionSize: 50GB
    storageSpec:
      volumeClaimTemplate:
        spec:
          storageClassName: gp3
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 100Gi
    
    resources:
      requests:
        cpu: 500m
        memory: 2Gi
      limits:
        cpu: 2000m
        memory: 8Gi
    
    additionalScrapeConfigs:
      - job_name: 'yoghurt-ai-backend'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - yoghurt-ai-prod
                - yoghurt-ai-staging
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: yoghurt-ai-backend
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: metrics
      
      - job_name: 'yoghurt-ai-ai-service'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - yoghurt-ai-prod
                - yoghurt-ai-staging
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: yoghurt-ai-ai-service
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: metrics
    
    ruleSelector:
      matchLabels:
        app: yoghurt-ai
        prometheus: kube-prometheus
    
    serviceMonitorSelector:
      matchLabels:
        app: yoghurt-ai

grafana:
  enabled: true
  adminPassword: ${GRAFANA_ADMIN_PASSWORD}
  
  ingress:
    enabled: true
    ingressClassName: nginx
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      cert-manager.io/cluster-issuer: "letsencrypt-prod"
    hosts:
      - grafana.yoghurt-ai.com
    tls:
      - secretName: grafana-tls
        hosts:
          - grafana.yoghurt-ai.com
  
  persistence:
    enabled: true
    storageClassName: gp3
    size: 10Gi
  
  resources:
    requests:
      cpu: 100m
      memory: 256Mi
    limits:
      cpu: 500m
      memory: 1Gi
  
  dashboardProviders:
    dashboardproviders.yaml:
      apiVersion: 1
      providers:
        - name: 'yoghurt-ai'
          orgId: 1
          folder: 'Yoghurt AI'
          type: file
          disableDeletion: false
          editable: true
          options:
            path: /var/lib/grafana/dashboards/yoghurt-ai
  
  dashboards:
    yoghurt-ai:
      application-overview:
        gnetId: 15661
        revision: 1
        datasource: Prometheus
      kubernetes-cluster:
        gnetId: 7249
        revision: 1
        datasource: Prometheus
      nodejs-application:
        gnetId: 11159
        revision: 1
        datasource: Prometheus

alertmanager:
  enabled: true
  alertmanagerSpec:
    storage:
      volumeClaimTemplate:
        spec:
          storageClassName: gp3
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 10Gi
    
    resources:
      requests:
        cpu: 100m
        memory: 256Mi
      limits:
        cpu: 500m
        memory: 1Gi
  
  config:
    global:
      smtp_smarthost: 'smtp.gmail.com:587'
      smtp_from: '<EMAIL>'
      smtp_auth_username: '<EMAIL>'
      smtp_auth_password: '${SMTP_PASSWORD}'
    
    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'web.hook'
      routes:
        - match:
            severity: critical
          receiver: 'critical-alerts'
        - match:
            severity: warning
          receiver: 'warning-alerts'
    
    receivers:
      - name: 'web.hook'
        webhook_configs:
          - url: '*****************************************************************************'
            send_resolved: true
      
      - name: 'critical-alerts'
        email_configs:
          - to: '<EMAIL>'
            subject: '[CRITICAL] {{ .GroupLabels.alertname }}'
            body: |
              {{ range .Alerts }}
              Alert: {{ .Annotations.summary }}
              Description: {{ .Annotations.description }}
              {{ end }}
        slack_configs:
          - api_url: '*****************************************************************************'
            channel: '#alerts-critical'
            title: '[CRITICAL] {{ .GroupLabels.alertname }}'
            text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
      
      - name: 'warning-alerts'
        slack_configs:
          - api_url: '*****************************************************************************'
            channel: '#alerts-warning'
            title: '[WARNING] {{ .GroupLabels.alertname }}'
            text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
```

### 5.2 日志管理与ELK Stack

```yaml
# logging/elasticsearch/values.yaml
elasticsearch:
  clusterName: "yoghurt-ai-logs"
  nodeGroup: "master"
  
  # Master nodes
  masterService: "yoghurt-ai-logs-master"
  roles:
    master: "true"
    ingest: "false"
    data: "false"
    remote_cluster_client: "false"
  
  replicas: 3
  minimumMasterNodes: 2
  
  esMajorVersion: "8"
  
  clusterHealthCheckParams: "wait_for_status=yellow&timeout=1s"
  
  esConfig:
    elasticsearch.yml: |
      cluster.name: yoghurt-ai-logs
      network.host: 0.0.0.0
      xpack.security.enabled: true
      xpack.security.transport.ssl.enabled: true
      xpack.security.transport.ssl.verification_mode: certificate
      xpack.security.transport.ssl.client_authentication: required
      xpack.security.transport.ssl.keystore.path: /usr/share/elasticsearch/config/certs/elastic-certificates.p12
      xpack.security.transport.ssl.truststore.path: /usr/share/elasticsearch/config/certs/elastic-certificates.p12
      xpack.security.http.ssl.enabled: true
      xpack.security.http.ssl.keystore.path: /usr/share/elasticsearch/config/certs/elastic-certificates.p12
  
  extraEnvs:
    - name: ELASTIC_PASSWORD
      valueFrom:
        secretKeyRef:
          name: elasticsearch-master-credentials
          key: password
    - name: ELASTIC_USERNAME
      valueFrom:
        secretKeyRef:
          name: elasticsearch-master-credentials
          key: username
  
  secretMounts:
    - name: elastic-certificates
      secretName: elastic-certificates
      path: /usr/share/elasticsearch/config/certs
      defaultMode: 0755
  
  resources:
    requests:
      cpu: "1000m"
      memory: "2Gi"
    limits:
      cpu: "2000m"
      memory: "4Gi"
  
  volumeClaimTemplate:
    accessModes: ["ReadWriteOnce"]
    storageClassName: "gp3"
    resources:
      requests:
        storage: 100Gi
  
  persistence:
    enabled: true
    labels:
      enabled: false

---
# Data nodes configuration
elasticsearch-data:
  clusterName: "yoghurt-ai-logs"
  nodeGroup: "data"
  
  masterService: "yoghurt-ai-logs-master"
  roles:
    master: "false"
    ingest: "true"
    data: "true"
    remote_cluster_client: "false"
  
  replicas: 3
  
  resources:
    requests:
      cpu: "1000m"
      memory: "4Gi"
    limits:
      cpu: "2000m"
      memory: "8Gi"
  
  volumeClaimTemplate:
    accessModes: ["ReadWriteOnce"]
    storageClassName: "gp3"
    resources:
      requests:
        storage: 500Gi
```

```yaml
# logging/logstash/values.yaml
logstash:
  replicas: 2
  
  logstashConfig:
    logstash.yml: |
      http.host: 0.0.0.0
      xpack.monitoring.elasticsearch.hosts: ["https://yoghurt-ai-logs-master:9200"]
      xpack.monitoring.enabled: true
      xpack.monitoring.elasticsearch.username: elastic
      xpack.monitoring.elasticsearch.password: "${ELASTIC_PASSWORD}"
      xpack.monitoring.elasticsearch.ssl.certificate_authority: /usr/share/logstash/config/certs/ca.crt
  
  logstashPipeline:
    logstash.conf: |
      input {
        beats {
          port => 5044
        }
        http {
          port => 8080
          codec => json
        }
      }
      
      filter {
        if [kubernetes] {
          mutate {
            add_field => { "cluster" => "yoghurt-ai" }
          }
          
          # Parse application logs
          if [kubernetes][container][name] == "yoghurt-ai-backend" {
            grok {
              match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:level} %{GREEDYDATA:message}" }
              overwrite => [ "message" ]
            }
            
            date {
              match => [ "timestamp", "ISO8601" ]
            }
            
            # Parse JSON logs
            if [message] =~ /^\{.*\}$/ {
              json {
                source => "message"
              }
            }
          }
          
          # Parse AI service logs
          if [kubernetes][container][name] == "yoghurt-ai-ai-service" {
            grok {
              match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} - %{WORD:logger} - %{LOGLEVEL:level} - %{GREEDYDATA:message}" }
              overwrite => [ "message" ]
            }
            
            date {
              match => [ "timestamp", "ISO8601" ]
            }
          }
          
          # Add environment information
          if [kubernetes][namespace] == "yoghurt-ai-prod" {
            mutate {
              add_field => { "environment" => "production" }
            }
          } else if [kubernetes][namespace] == "yoghurt-ai-staging" {
            mutate {
              add_field => { "environment" => "staging" }
            }
          } else if [kubernetes][namespace] == "yoghurt-ai-dev" {
            mutate {
              add_field => { "environment" => "development" }
            }
          }
        }
        
        # Security log parsing
        if [fields][logtype] == "security" {
          grok {
            match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} %{WORD:event_type} %{GREEDYDATA:details}" }
          }
          
          mutate {
            add_field => { "log_type" => "security" }
          }
        }
        
        # Performance log parsing
        if [fields][logtype] == "performance" {
          grok {
            match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} %{WORD:metric_name} %{NUMBER:metric_value:float} %{WORD:unit}" }
          }
          
          mutate {
            add_field => { "log_type" => "performance" }
          }
        }
      }
      
      output {
        elasticsearch {
          hosts => ["https://yoghurt-ai-logs-master:9200"]
          user => "elastic"
          password => "${ELASTIC_PASSWORD}"
          ssl => true
          cacert => "/usr/share/logstash/config/certs/ca.crt"
          
          # Dynamic index naming
          index => "yoghurt-ai-%{environment}-%{+YYYY.MM.dd}"
          
          # Template management
          template_name => "yoghurt-ai"
          template_pattern => "yoghurt-ai-*"
          template => "/usr/share/logstash/templates/yoghurt-ai-template.json"
          template_overwrite => true
        }
        
        # Debug output for development
        if [environment] == "development" {
          stdout {
            codec => rubydebug
          }
        }
      }
  
  extraEnvs:
    - name: ELASTIC_PASSWORD
      valueFrom:
        secretKeyRef:
          name: elasticsearch-master-credentials
          key: password
  
  secretMounts:
    - name: elastic-certificates
      secretName: elastic-certificates
      path: /usr/share/logstash/config/certs
      defaultMode: 0755
  
  resources:
    requests:
      cpu: 500m
      memory: 1Gi
    limits:
      cpu: 1000m
      memory: 2Gi
  
  persistence:
    enabled: true
    size: 10Gi
```

```yaml
# logging/kibana/values.yaml
kibana:
  elasticsearchHosts: "https://yoghurt-ai-logs-master:9200"
  
  kibanaConfig:
    kibana.yml: |
      server.host: 0.0.0.0
      elasticsearch.hosts: ["https://yoghurt-ai-logs-master:9200"]
      elasticsearch.username: elastic
      elasticsearch.password: "${ELASTIC_PASSWORD}"
      elasticsearch.ssl.certificateAuthorities: ["/usr/share/kibana/config/certs/ca.crt"]
      xpack.security.enabled: true
      xpack.encryptedSavedObjects.encryptionKey: "${KIBANA_ENCRYPTION_KEY}"
      xpack.reporting.encryptionKey: "${KIBANA_REPORTING_KEY}"
      xpack.security.encryptionKey: "${KIBANA_SECURITY_KEY}"
      
      # Custom branding
      xpack.branding.logo: "/usr/share/kibana/config/logo/yoghurt-ai-logo.svg"
      xpack.branding.mark: "/usr/share/kibana/config/logo/yoghurt-ai-mark.svg"
      
      # Dashboard settings
      kibana.defaultAppId: "dashboard"
      
      # Security settings
      server.ssl.enabled: true
      server.ssl.certificate: "/usr/share/kibana/config/certs/tls.crt"
      server.ssl.key: "/usr/share/kibana/config/certs/tls.key"
  
  extraEnvs:
    - name: ELASTIC_PASSWORD
      valueFrom:
        secretKeyRef:
          name: elasticsearch-master-credentials
          key: password
    - name: KIBANA_ENCRYPTION_KEY
      valueFrom:
        secretKeyRef:
          name: kibana-encryption-keys
          key: encryptionKey
    - name: KIBANA_REPORTING_KEY
      valueFrom:
        secretKeyRef:
          name: kibana-encryption-keys
          key: reportingKey
    - name: KIBANA_SECURITY_KEY
      valueFrom:
        secretKeyRef:
          name: kibana-encryption-keys
          key: securityKey
  
  secretMounts:
    - name: elastic-certificates
      secretName: elastic-certificates
      path: /usr/share/kibana/config/certs
      defaultMode: 0755
    - name: kibana-tls
      secretName: kibana-tls
      path: /usr/share/kibana/config/certs
      defaultMode: 0755
  
  ingress:
    enabled: true
    className: nginx
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
      cert-manager.io/cluster-issuer: "letsencrypt-prod"
      nginx.ingress.kubernetes.io/auth-type: basic
      nginx.ingress.kubernetes.io/auth-secret: kibana-basic-auth
      nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required - Yoghurt AI Logs'
    hosts:
      - host: logs.yoghurt-ai.com
        paths:
          - path: /
            pathType: Prefix
    tls:
      - secretName: kibana-ingress-tls
        hosts:
          - logs.yoghurt-ai.com
  
  resources:
    requests:
      cpu: 500m
      memory: 1Gi
    limits:
      cpu: 1000m
      memory: 2Gi
```

### 5.3 分布式追踪与Jaeger

```yaml
# tracing/jaeger/values.yaml
jaeger:
  strategy: production
  
  collector:
    replicaCount: 2
    resources:
      requests:
        cpu: 500m
        memory: 1Gi
      limits:
        cpu: 1000m
        memory: 2Gi
    
    service:
      type: ClusterIP
      grpc:
        port: 14250
      http:
        port: 14268
      zipkin:
        port: 9411
  
  query:
    replicaCount: 2
    resources:
      requests:
        cpu: 200m
        memory: 512Mi
      limits:
        cpu: 500m
        memory: 1Gi
    
    ingress:
      enabled: true
      className: nginx
      annotations:
        nginx.ingress.kubernetes.io/ssl-redirect: "true"
        cert-manager.io/cluster-issuer: "letsencrypt-prod"
      hosts:
        - host: tracing.yoghurt-ai.com
          paths:
            - path: /
              pathType: Prefix
      tls:
        - secretName: jaeger-query-tls
          hosts:
            - tracing.yoghurt-ai.com
  
  storage:
    type: elasticsearch
    elasticsearch:
      host: yoghurt-ai-logs-master
      port: 9200
      scheme: https
      user: elastic
      password: "${ELASTIC_PASSWORD}"
      tls:
        ca: /etc/ssl/certs/ca.crt
      indexPrefix: jaeger
      
  agent:
    daemonset:
      enabled: true
    resources:
      requests:
        cpu: 100m
        memory: 128Mi
      limits:
        cpu: 200m
        memory: 256Mi
```

---

## 6. 备份与灾难恢复

### 6.1 数据库备份策略

```yaml
# backup/postgres-backup.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: postgres-backup
  namespace: yoghurt-ai-prod
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 7
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          containers:
            - name: postgres-backup
              image: postgres:15-alpine
              env:
                - name: PGHOST
                  value: "prod-yoghurt-ai.cluster-xyz.us-west-2.rds.amazonaws.com"
                - name: PGPORT
                  value: "5432"
                - name: PGDATABASE
                  value: "yoghurt_ai"
                - name: PGUSER
                  valueFrom:
                    secretKeyRef:
                      name: postgres-credentials
                      key: username
                - name: PGPASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: postgres-credentials
                      key: password
                - name: AWS_ACCESS_KEY_ID
                  valueFrom:
                    secretKeyRef:
                      name: aws-credentials
                      key: access-key-id
                - name: AWS_SECRET_ACCESS_KEY
                  valueFrom:
                    secretKeyRef:
                      name: aws-credentials
                      key: secret-access-key
                - name: AWS_DEFAULT_REGION
                  value: "us-west-2"
                - name: S3_BUCKET
                  value: "yoghurt-ai-backups"
              command:
                - /bin/sh
                - -c
                - |
                  set -e
                  
                  # Install AWS CLI
                  apk add --no-cache aws-cli
                  
                  # Create backup filename with timestamp
                  BACKUP_FILE="postgres-backup-$(date +%Y%m%d-%H%M%S).sql.gz"
                  
                  # Create database backup
                  echo "Creating database backup..."
                  pg_dump --verbose --clean --no-owner --no-privileges | gzip > "/tmp/${BACKUP_FILE}"
                  
                  # Upload to S3
                  echo "Uploading backup to S3..."
                  aws s3 cp "/tmp/${BACKUP_FILE}" "s3://${S3_BUCKET}/postgres/${BACKUP_FILE}"
                  
                  # Verify upload
                  aws s3 ls "s3://${S3_BUCKET}/postgres/${BACKUP_FILE}"
                  
                  # Clean up old backups (keep last 30 days)
                  echo "Cleaning up old backups..."
                  aws s3 ls "s3://${S3_BUCKET}/postgres/" | while read -r line; do
                    createDate=$(echo $line | awk '{print $1" "$2}')
                    createDate=$(date -d "$createDate" +%s)
                    olderThan=$(date -d "30 days ago" +%s)
                    if [[ $createDate -lt $olderThan ]]; then
                      fileName=$(echo $line | awk '{print $4}')
                      if [[ $fileName != "" ]]; then
                        aws s3 rm "s3://${S3_BUCKET}/postgres/${fileName}"
                        echo "Deleted old backup: ${fileName}"
                      fi
                    fi
                  done
                  
                  echo "Backup completed successfully"
              resources:
                requests:
                  cpu: 100m
                  memory: 256Mi
                limits:
                  cpu: 500m
                  memory: 1Gi
```

### 6.2 应用数据备份

```yaml
# backup/app-data-backup.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: app-data-backup
  namespace: yoghurt-ai-prod
spec:
  schedule: "0 3 * * *"  # Daily at 3 AM
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 7
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          serviceAccountName: backup-service-account
          containers:
            - name: app-data-backup
              image: amazon/aws-cli:latest
              env:
                - name: AWS_ACCESS_KEY_ID
                  valueFrom:
                    secretKeyRef:
                      name: aws-credentials
                      key: access-key-id
                - name: AWS_SECRET_ACCESS_KEY
                  valueFrom:
                    secretKeyRef:
                      name: aws-credentials
                      key: secret-access-key
                - name: AWS_DEFAULT_REGION
                  value: "us-west-2"
                - name: SOURCE_BUCKET
                  value: "prod-yoghurt-ai-data"
                - name: BACKUP_BUCKET
                  value: "yoghurt-ai-backups"
              command:
                - /bin/sh
                - -c
                - |
                  set -e
                  
                  # Create backup timestamp
                  BACKUP_DATE=$(date +%Y%m%d-%H%M%S)
                  
                  # Sync application data to backup bucket
                  echo "Starting application data backup..."
                  aws s3 sync "s3://${SOURCE_BUCKET}/" "s3://${BACKUP_BUCKET}/app-data/${BACKUP_DATE}/" \
                    --exclude "*.tmp" \
                    --exclude "cache/*" \
                    --storage-class STANDARD_IA
                  
                  # Create backup manifest
                  echo "Creating backup manifest..."
                  cat > /tmp/backup-manifest.json << EOF
                  {
                    "backup_date": "${BACKUP_DATE}",
                    "source_bucket": "${SOURCE_BUCKET}",
                    "backup_bucket": "${BACKUP_BUCKET}",
                    "backup_path": "app-data/${BACKUP_DATE}",
                    "created_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
                    "backup_type": "full",
                    "retention_days": 90
                  }
                  EOF
                  
                  aws s3 cp /tmp/backup-manifest.json "s3://${BACKUP_BUCKET}/app-data/${BACKUP_DATE}/manifest.json"
                  
                  # Clean up old backups (keep last 90 days)
                  echo "Cleaning up old backups..."
                  aws s3api list-objects-v2 \
                    --bucket "${BACKUP_BUCKET}" \
                    --prefix "app-data/" \
                    --query "Contents[?LastModified<='$(date -d '90 days ago' -u +%Y-%m-%dT%H:%M:%SZ)'].Key" \
                    --output text | while read -r key; do
                      if [[ "$key" != "None" && "$key" != "" ]]; then
                        aws s3 rm "s3://${BACKUP_BUCKET}/${key}"
                        echo "Deleted old backup: ${key}"
                      fi
                    done
                  
                  echo "Application data backup completed successfully"
              resources:
                requests:
                  cpu: 200m
                  memory: 512Mi
                limits:
                  cpu: 1000m
                  memory: 2Gi
```

### 6.3 灾难恢复计划

```yaml
# disaster-recovery/restore-job.yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: disaster-recovery-restore
  namespace: yoghurt-ai-prod
  annotations:
    description: "Disaster recovery restore job - USE WITH CAUTION"
spec:
  template:
    spec:
      restartPolicy: Never
      serviceAccountName: disaster-recovery-service-account
      containers:
        - name: restore
          image: yoghurt-ai/disaster-recovery:latest
          env:
            - name: RESTORE_TYPE
              value: "full"  # Options: full, database-only, app-data-only
            - name: BACKUP_DATE
              value: "********-020000"  # Specify backup to restore
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: access-key-id
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: secret-access-key
            - name: AWS_DEFAULT_REGION
              value: "us-west-2"
            - name: BACKUP_BUCKET
              value: "yoghurt-ai-backups"
            - name: TARGET_ENVIRONMENT
              value: "disaster-recovery"  # Restore to DR environment first
          command:
            - /bin/sh
            - -c
            - |
              set -e
              
              echo "Starting disaster recovery restore process..."
              echo "Restore type: ${RESTORE_TYPE}"
              echo "Backup date: ${BACKUP_DATE}"
              echo "Target environment: ${TARGET_ENVIRONMENT}"
              
              # Validate backup exists
              if ! aws s3 ls "s3://${BACKUP_BUCKET}/app-data/${BACKUP_DATE}/manifest.json"; then
                echo "ERROR: Backup manifest not found for date ${BACKUP_DATE}"
                exit 1
              fi
              
              # Download and validate manifest
              aws s3 cp "s3://${BACKUP_BUCKET}/app-data/${BACKUP_DATE}/manifest.json" /tmp/manifest.json
              
              # Restore database if needed
              if [[ "${RESTORE_TYPE}" == "full" || "${RESTORE_TYPE}" == "database-only" ]]; then
                echo "Restoring database..."
                
                # Find latest database backup for the date
                DB_BACKUP=$(aws s3 ls "s3://${BACKUP_BUCKET}/postgres/" | \
                  grep "${BACKUP_DATE:0:8}" | \
                  sort | tail -1 | awk '{print $4}')
                
                if [[ -z "$DB_BACKUP" ]]; then
                  echo "ERROR: No database backup found for date ${BACKUP_DATE}"
                  exit 1
                fi
                
                # Download and restore database
                aws s3 cp "s3://${BACKUP_BUCKET}/postgres/${DB_BACKUP}" /tmp/db-backup.sql.gz
                
                # Connect to target database and restore
                # Note: This should connect to a separate DR database instance
                gunzip -c /tmp/db-backup.sql.gz | psql "${DR_DATABASE_URL}"
                
                echo "Database restore completed"
              fi
              
              # Restore application data if needed
              if [[ "${RESTORE_TYPE}" == "full" || "${RESTORE_TYPE}" == "app-data-only" ]]; then
                echo "Restoring application data..."
                
                # Sync data to DR S3 bucket
                aws s3 sync "s3://${BACKUP_BUCKET}/app-data/${BACKUP_DATE}/" "s3://dr-yoghurt-ai-data/" \
                  --exclude "manifest.json"
                
                echo "Application data restore completed"
              fi
              
              # Update Kubernetes deployments to point to DR resources
              if [[ "${TARGET_ENVIRONMENT}" == "disaster-recovery" ]]; then
                echo "Updating Kubernetes deployments for DR environment..."
                
                # Apply DR-specific configurations
                kubectl apply -f /config/dr-configmaps.yaml
                kubectl apply -f /config/dr-secrets.yaml
                
                # Scale up DR deployments
                kubectl scale deployment yoghurt-ai-backend --replicas=2 -n yoghurt-ai-dr
                kubectl scale deployment yoghurt-ai-ai-service --replicas=1 -n yoghurt-ai-dr
                
                # Wait for deployments to be ready
                kubectl wait --for=condition=available --timeout=300s deployment/yoghurt-ai-backend -n yoghurt-ai-dr
                kubectl wait --for=condition=available --timeout=300s deployment/yoghurt-ai-ai-service -n yoghurt-ai-dr
                
                echo "DR environment is ready"
              fi
              
              echo "Disaster recovery restore completed successfully"
              echo "Please verify the restored environment before switching traffic"
          resources:
            requests:
              cpu: 500m
              memory: 1Gi
            limits:
              cpu: 2000m
              memory: 4Gi
          volumeMounts:
            - name: dr-config
              mountPath: /config
              readOnly: true
      volumes:
        - name: dr-config
          configMap:
            name: disaster-recovery-config
```

---

## 7. 实施路线图与总结

### 7.1 分阶段实施计划

```mermaid
gantt
    title 基础设施即代码实施路线图
    dateFormat  YYYY-MM-DD
    section 第一阶段：基础设施
    Terraform基础配置    :done, terraform-base, 2024-01-01, 2024-01-15
    AWS资源创建         :done, aws-resources, 2024-01-10, 2024-01-25
    Kubernetes集群部署   :done, k8s-cluster, 2024-01-20, 2024-02-05
    
    section 第二阶段：应用部署
    Helm Charts开发     :active, helm-charts, 2024-01-25, 2024-02-10
    CI/CD流水线配置     :active, cicd-setup, 2024-02-01, 2024-02-15
    ArgoCD部署配置      :argocd-setup, 2024-02-10, 2024-02-20
    
    section 第三阶段：监控观测
    Prometheus部署      :monitoring-setup, 2024-02-15, 2024-02-25
    ELK Stack配置       :logging-setup, 2024-02-20, 2024-03-05
    Grafana仪表板       :dashboard-setup, 2024-02-25, 2024-03-10
    
    section 第四阶段：安全加固
    网络策略配置        :network-policy, 2024-03-01, 2024-03-15
    RBAC权限配置        :rbac-setup, 2024-03-05, 2024-03-20
    安全扫描集成        :security-scan, 2024-03-10, 2024-03-25
    
    section 第五阶段：备份恢复
    备份策略实施        :backup-setup, 2024-03-15, 2024-03-30
    灾难恢复测试        :dr-testing, 2024-03-25, 2024-04-10
    文档完善培训        :documentation, 2024-04-01, 2024-04-15
```

### 7.2 成功关键因素

#### 7.2.1 技术层面
- **模块化设计**: 确保所有基础设施组件都是模块化的，便于维护和扩展
- **版本控制**: 所有配置文件都应纳入版本控制，确保变更可追溯
- **自动化测试**: 建立完整的自动化测试流程，包括基础设施测试和应用测试
- **监控覆盖**: 确保所有关键组件都有适当的监控和告警

#### 7.2.2 流程层面
- **GitOps实践**: 严格遵循GitOps工作流，所有变更都通过Git进行
- **环境一致性**: 确保开发、测试、生产环境的一致性
- **变更管理**: 建立完善的变更管理流程，包括审批、测试、回滚机制
- **文档维护**: 保持文档的及时更新和准确性

#### 7.2.3 团队层面
- **技能培训**: 确保团队成员具备必要的IaC和Kubernetes技能
- **责任分工**: 明确各角色的责任和权限
- **知识共享**: 建立知识共享机制，避免单点依赖
- **持续学习**: 跟上技术发展趋势，持续优化基础设施

### 7.3 风险控制

#### 7.3.1 技术风险
- **配置错误**: 通过自动化测试和代码审查降低配置错误风险
- **资源限制**: 合理规划资源使用，设置适当的限制和告警
- **依赖管理**: 明确管理所有外部依赖，确保版本兼容性
- **安全漏洞**: 定期进行安全扫描和漏洞评估

#### 7.3.2 运营风险
- **人员风险**: 建立知识文档和交接流程，避免关键人员离职影响
- **流程风险**: 建立标准化流程和应急预案
- **合规风险**: 确保所有配置符合安全和合规要求
- **成本风险**: 定期监控和优化资源使用成本

### 7.4 总结

本基础设施即代码文档为酸奶AI质控系统提供了完整的IaC解决方案，涵盖了从基础设施配置到应用部署、监控观测、安全加固和备份恢复的全生命周期管理。

**核心价值**:
1. **一致性**: 通过代码定义基础设施，确保环境一致性
2. **可重复性**: 支持快速创建和销毁环境，便于测试和灾难恢复
3. **可追溯性**: 所有变更都有版本记录，便于审计和回滚
4. **自动化**: 减少人工操作，提高效率和可靠性
5. **可扩展性**: 模块化设计支持系统的快速扩展

**预期效果**:
- 部署时间从数天缩短到数小时
- 环境一致性达到99%以上
- 基础设施变更的自动化率达到95%
- 灾难恢复时间目标(RTO)小于4小时
- 基础设施相关故障减少80%

通过实施这套IaC方案，酸奶AI质控系统将具备现代化的基础设施管理能力，为业务的快速发展和规模化运营提供坚实的技术基础。
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
}

provider "helm" {
  kubernetes {
    host                   = data.aws_eks_cluster.cluster.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
    token                  = data.aws_eks_cluster_auth.cluster.token
  }
}

# Local values
locals {
  environment = "prod"
  region      = var.aws_region
  
  common_tags = {
    Environment = local.environment
    Project     = "yoghurt-ai"
    ManagedBy   = "terraform"
  }
}

# VPC Module
module "vpc" {
  source = "../../modules/vpc"

  environment = local.environment
  vpc_cidr    = var.vpc_cidr
  
  public_subnet_cidrs   = var.public_subnet_cidrs
  private_subnet_cidrs  = var.private_subnet_cidrs
  database_subnet_cidrs = var.database_subnet_cidrs
  
  common_tags = local.common_tags
}

# EKS Module
module "eks" {
  source = "../../modules/eks"

  cluster_name       = "${local.environment}-yoghurt-ai"
  kubernetes_version = var.kubernetes_version
  
  vpc_id     = module.vpc.vpc_id
  subnet_ids = concat(module.vpc.private_subnet_ids, module.vpc.public_subnet_ids)
  private_subnet_ids = module.vpc.private_subnet_ids
  
  endpoint_public_access = false
  public_access_cidrs   = []
  workstation_cidr_blocks = var.workstation_cidr_blocks
  
  node_groups = {
    main = {
      capacity_type  = "ON_DEMAND"
      instance_types = ["m5.xlarge"]
      ami_type       = "AL2_x86_64"
      disk_size      = 50
      
      desired_size = 3
      max_size     = 10
      min_size     = 3
      
      labels = {
        role = "main"
      }
      taints = []
    }
    
    ai_workload = {
      capacity_type  = "ON_DEMAND"
      instance_types = ["g4dn.xlarge"]
      ami_type       = "AL2_x86_64_GPU"
      disk_size      = 100
      
      desired_size = 2
      max_size     = 5
      min_size     = 1
      
      labels = {
        role = "ai-workload"
        "nvidia.com/gpu" = "true"
      }
      taints = [
        {
          key    = "nvidia.com/gpu"
          value  = "true"
          effect = "NO_SCHEDULE"
        }
      ]
    }
  }
  
  fargate_profiles = {
    system = {
      selectors = [
        {
          namespace = "kube-system"
          labels = {}
        },
        {
          namespace = "default"
          labels = {
            compute-type = "fargate"
          }
        }
      ]
    }
  }
  
  environment = local.environment
  tags        = local.common_tags
}

# RDS Module
module "rds" {
  source = "../../modules/rds"

  identifier = "${local.environment}-yoghurt-ai"
  
  engine         = "postgres"
  engine_version = "15.4"
  instance_class = "db.r6g.xlarge"
  
  allocated_storage     = 100
  max_allocated_storage = 1000
  storage_type          = "gp3"
  
  database_name = "yoghurt_ai"
  username      = var.db_username
  password      = var.db_password
  port          = 5432
  
  vpc_id     = module.vpc.vpc_id
  subnet_ids = module.vpc.database_subnet_ids
  allowed_security_groups = [module.eks.node_security_group_id]
  
  backup_retention_period = 30
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  
  monitoring_interval = 60
  performance_insights_enabled = true
  performance_insights_retention_period = 7
  
  enabled_cloudwatch_logs_exports = ["postgresql"]
  
  multi_az = true
  deletion_protection = true
  skip_final_snapshot = false
  
  create_read_replica = true
  read_replica_instance_class = "db.r6g.large"
  
  parameters = [
    {
      name  = "shared_preload_libraries"
      value = "pg_stat_statements"
    },
    {
      name  = "log_statement"
      value = "all"
    },
    {
      name  = "log_min_duration_statement"
      value = "1000"
    }
  ]
  
  tags = local.common_tags
}

# ElastiCache Module
module "elasticache" {
  source = "../../modules/elasticache"

  cluster_id = "${local.environment}-yoghurt-ai"
  
  engine         = "redis"
  engine_version = "7.0"
  node_type      = "cache.r6g.large"
  
  num_cache_nodes = 3
  
  vpc_id     = module.vpc.vpc_id
  subnet_ids = module.vpc.private_subnet_ids
  allowed_security_groups = [module.eks.node_security_group_id]
  
  at_rest_encryption_enabled = true
  transit_encryption_enabled = true
  auth_token                = var.redis_auth_token
  
  automatic_failover_enabled = true
  multi_az_enabled          = true
  
  backup_retention_limit = 7
  backup_window         = "03:00-05:00"
  maintenance_window    = "sun:05:00-sun:07:00"
  
  tags = local.common_tags
}

# S3 Module
module "s3" {
  source = "../../modules/s3"

  buckets = {
    "${local.environment}-yoghurt-ai-data" = {
      versioning_enabled = true
      lifecycle_rules = [
        {
          id     = "transition_to_ia"
          status = "Enabled"
          transition = {
            days          = 30
            storage_class = "STANDARD_IA"
          }
        },
        {
          id     = "transition_to_glacier"
          status = "Enabled"
          transition = {
            days          = 90
            storage_class = "GLACIER"
          }
        }
      ]
    }
    
    "${local.environment}-yoghurt-ai-models" = {
      versioning_enabled = true
      lifecycle_rules = []
    }
    
    "${local.environment}-yoghurt-ai-backups" = {
      versioning_enabled = true
      lifecycle_rules = [
        {
          id     = "delete_old_backups"
          status = "Enabled"
          expiration = {
            days = 365
          }
        }
      ]
    }
  }
  
  tags = local.common_tags
}
```

---

## 3. Kubernetes配置与Helm Charts

### 3.1 Kubernetes清单结构

```
kubernetes/
├── namespaces/
│   ├── yoghurt-ai-prod.yaml
│   ├── yoghurt-ai-staging.yaml
│   └── monitoring.yaml
├── rbac/
│   ├── service-accounts.yaml
│   ├── cluster-roles.yaml
│   └── role-bindings.yaml
├── network-policies/
│   ├── default-deny.yaml
│   ├── backend-policy.yaml
│   └── ai-service-policy.yaml
├── secrets/
│   ├── database-secrets.yaml
│   ├── api-keys.yaml
│   └── tls-certificates.yaml
└── monitoring/
    ├── prometheus/
    ├── grafana/
    └── alertmanager/
```

### 3.2 核心应用Helm Chart

#### 3.2.1 Chart结构

```
helm/yoghurt-ai/
├── Chart.yaml
├── values.yaml
├── values-prod.yaml
├── values-staging.yaml
├── templates/
│   ├── _helpers.tpl
│   ├── configmap.yaml
│   ├── secret.yaml
│   ├── deployment.yaml
│   ├── service.yaml
│   ├── ingress.yaml
│   ├── hpa.yaml
│   ├── pdb.yaml
│   ├── networkpolicy.yaml
│   └── servicemonitor.yaml
└── charts/
    ├── postgresql/
    ├── redis/
    └── nginx-ingress/
```

#### 3.2.2 Chart.yaml

```yaml
# helm/yoghurt-ai/Chart.yaml
apiVersion: v2
name: yoghurt-ai
description: Yoghurt AI Quality Control System
type: application
version: 1.0.0
appVersion: "1.0.0"

keywords:
  - yoghurt
  - ai
  - quality-control
  - food-safety

home: https://github.com/yoghurt-ai/platform
sources:
  - https://github.com/yoghurt-ai/platform

maintainers:
  - name: Platform Team
    email: <EMAIL>

dependencies:
  - name: postgresql
    version: "12.1.9"
    repository: https://charts.bitnami.com/bitnami
    condition: postgresql.enabled
  
  - name: redis
    version: "17.3.7"
    repository: https://charts.bitnami.com/bitnami
    condition: redis.enabled
  
  - name: nginx-ingress
    version: "4.4.2"
    repository: https://kubernetes.github.io/ingress-nginx
    condition: ingress.enabled

annotations:
  category: Application
  licenses: Apache-2.0
```

#### 3.2.3 主要模板文件

**Deployment模板**:

```yaml
# helm/yoghurt-ai/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "yoghurt-ai.fullname" . }}
  labels:
    {{- include "yoghurt-ai.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "yoghurt-ai.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
        {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "yoghurt-ai.selectorLabels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "yoghurt-ai.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        # Backend API Container
        - name: backend
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.backend.image.repository }}:{{ .Values.backend.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.backend.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.backend.service.port }}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /ready
              port: http
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
          env:
            - name: NODE_ENV
              value: {{ .Values.environment }}
            - name: PORT
              value: "{{ .Values.backend.service.port }}"
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: {{ include "yoghurt-ai.fullname" . }}-secrets
                  key: database-url
            - name: REDIS_URL
              valueFrom:
                secretKeyRef:
                  name: {{ include "yoghurt-ai.fullname" . }}-secrets
                  key: redis-url
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "yoghurt-ai.fullname" . }}-secrets
                  key: jwt-secret
            - name: AWS_REGION
              value: {{ .Values.aws.region }}
            - name: S3_BUCKET
              value: {{ .Values.aws.s3.bucket }}
            {{- range $key, $value := .Values.backend.env }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
          envFrom:
            - configMapRef:
                name: {{ include "yoghurt-ai.fullname" . }}-config
          resources:
            {{- toYaml .Values.backend.resources | nindent 12 }}
          volumeMounts:
            - name: tmp
              mountPath: /tmp
            - name: logs
              mountPath: /app/logs
            {{- if .Values.backend.persistence.enabled }}
            - name: data
              mountPath: /app/data
            {{- end }}
        
        # AI Service Container
        - name: ai-service
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.aiService.image.repository }}:{{ .Values.aiService.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.aiService.image.pullPolicy }}
          ports:
            - name: grpc
              containerPort: {{ .Values.aiService.service.port }}
              protocol: TCP
          livenessProbe:
            grpc:
              port: {{ .Values.aiService.service.port }}
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          readinessProbe:
            grpc:
              port: {{ .Values.aiService.service.port }}
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          env:
            - name: GRPC_PORT
              value: "{{ .Values.aiService.service.port }}"
            - name: MODEL_PATH
              value: "/models"
            - name: CUDA_VISIBLE_DEVICES
              value: "{{ .Values.aiService.gpu.devices }}"
            {{- range $key, $value := .Values.aiService.env }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
          resources:
            {{- toYaml .Values.aiService.resources | nindent 12 }}
          volumeMounts:
            - name: models
              mountPath: /models
              readOnly: true
            - name: tmp
              mountPath: /tmp
          {{- if .Values.aiService.gpu.enabled }}
          runtimeClassName: nvidia
          {{- end }}
        
        # Sidecar: Log Shipper
        {{- if .Values.logging.enabled }}
        - name: log-shipper
          image: "{{ .Values.logging.image.repository }}:{{ .Values.logging.image.tag }}"
          imagePullPolicy: {{ .Values.logging.image.pullPolicy }}
          env:
            - name: FLUENTD_CONF
              value: "fluent.conf"
            - name: FLUENTD_OPT
              value: "-v"
          volumeMounts:
            - name: logs
              mountPath: /fluentd/log
            - name: fluentd-config
              mountPath: /fluentd/etc
          resources:
            {{- toYaml .Values.logging.resources | nindent 12 }}
        {{- end }}
      
      volumes:
        - name: tmp
          emptyDir: {}
        - name: logs
          emptyDir: {}
        - name: models
          persistentVolumeClaim:
            claimName: {{ include "yoghurt-ai.fullname" . }}-models
        {{- if .Values.logging.enabled }}
        - name: fluentd-config
          configMap:
            name: {{ include "yoghurt-ai.fullname" . }}-fluentd-config
        {{- end }}
        {{- if .Values.backend.persistence.enabled }}
        - name: data
          persistentVolumeClaim:
            claimName: {{ include "yoghurt-ai.fullname" . }}-data
        {{- end }}
      
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      
      # Pod Disruption Budget
      {{- if .Values.podDisruptionBudget.enabled }}
      podDisruptionBudget:
        {{- toYaml .Values.podDisruptionBudget | nindent 8 }}
      {{- end }}
```

**Service模板**:

```yaml
# helm/yoghurt-ai/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: {{ include "yoghurt-ai.fullname" . }}
  labels:
    {{- include "yoghurt-ai.labels" . | nindent 4 }}
  {{- with .Values.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "yoghurt-ai.selectorLabels" . | nindent 4 }}
  {{- if eq .Values.service.type "LoadBalancer" }}
  {{- with .Values.service.loadBalancerIP }}
  loadBalancerIP: {{ . }}
  {{- end }}
  {{- with .Values.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- end }}

---
# AI Service
apiVersion: v1
kind: Service
metadata:
  name: {{ include "yoghurt-ai.fullname" . }}-ai
  labels:
    {{- include "yoghurt-ai.labels" . | nindent 4 }}
    component: ai-service
spec:
  type: ClusterIP
  ports:
    - port: {{ .Values.aiService.service.port }}
      targetPort: grpc
      protocol: TCP
      name: grpc
  selector:
    {{- include "yoghurt-ai.selectorLabels" . | nindent 4 }}
```

**Ingress模板**:

```yaml
# helm/yoghurt-ai/templates/ingress.yaml
{{- if .Values.ingress.enabled -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "yoghurt-ai.fullname" . }}
  labels:
    {{- include "yoghurt-ai.labels" . | nindent 4 }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if and .Values.ingress.className (semverCompare ">=1.18-0" .Capabilities.KubeVersion.GitVersion) }}
  ingressClassName: {{ .Values.ingress.className }}
  {{- end }}
  {{- if .Values.ingress.tls }}
  tls:
    {{- range .Values.ingress.tls }}
    - hosts:
        {{- range .hosts }}
        - {{ . | quote }}
        {{- end }}
      secretName: {{ .secretName }}
    {{- end }}
  {{- end }}
  rules:
    {{- range .Values.ingress.hosts }}
    - host: {{ .host | quote }}
      http:
        paths:
          {{- range .paths }}
          - path: {{ .path }}
            {{- if and .pathType (semverCompare ">=1.18-0" $.Capabilities.KubeVersion.GitVersion) }}
            pathType: {{ .pathType }}
            {{- end }}
            backend:
              {{- if semverCompare ">=1.19-0" $.Capabilities.KubeVersion.GitVersion }}
              service:
                name: {{ include "yoghurt-ai.fullname" $ }}
                port:
                  number: {{ $.Values.service.port }}
              {{- else }}
              serviceName: {{ include "yoghurt-ai.fullname" $ }}
              servicePort: {{ $.Values.service.port }}
              {{- end }}
          {{- end }}
    {{- end }}
{{- end }}
```

#### 3.2.4 Values配置

**生产环境Values**:

```yaml
# helm/yoghurt-ai/values-prod.yaml
# Global settings
global:
  imageRegistry: "123456789012.dkr.ecr.us-west-2.amazonaws.com"
  storageClass: "gp3"

# Environment
environment: production

# Replica count
replicaCount: 3

# Image settings
backend:
  image:
    repository: yoghurt-ai/backend
    tag: "v1.0.0"
    pullPolicy: IfNotPresent
  
  service:
    port: 3000
  
  resources:
    limits:
      cpu: 1000m
      memory: 2Gi
    requests:
      cpu: 500m
      memory: 1Gi
  
  env:
    LOG_LEVEL: "info"
    METRICS_ENABLED: "true"
    TRACING_ENABLED: "true"
  
  persistence:
    enabled: true
    size: 10Gi
    storageClass: gp3

aiService:
  image:
    repository: yoghurt-ai/ai-service
    tag: "v1.0.0"
    pullPolicy: IfNotPresent
  
  service:
    port: 50051
  
  resources:
    limits:
      cpu: 4000m
      memory: 8Gi
      nvidia.com/gpu: 1
    requests:
      cpu: 2000m
      memory: 4Gi
      nvidia.com/gpu: 1
  
  gpu:
    enabled: true
    devices: "0"
  
  env:
    MODEL_CACHE_SIZE: "2048"
    BATCH_SIZE: "32"
    INFERENCE_TIMEOUT: "30"

# Service configuration
service:
  type: ClusterIP
  port: 80
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"

# Ingress configuration
ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
  hosts:
    - host: api.yoghurt-ai.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: yoghurt-ai-tls
      hosts:
        - api.yoghurt-ai.com

# Autoscaling
autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 20
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
        - type: Percent
          value: 10
          periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
        - type: Percent
          value: 50
          periodSeconds: 60

# Pod Disruption Budget
podDisruptionBudget:
  enabled: true
  minAvailable: 2

# Security Context
podSecurityContext:
  runAsNonRoot: true
  runAsUser: 1000
  runAsGroup: 1000
  fsGroup: 1000
  seccompProfile:
    type: RuntimeDefault

securityContext:
  allowPrivilegeEscalation: false
  readOnlyRootFilesystem: true
  capabilities:
    drop:
      - ALL

# Node selection
nodeSelector:
  kubernetes.io/arch: amd64
  node.kubernetes.io/instance-type: m5.xlarge

affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
              - key: app.kubernetes.io/name
                operator: In
                values:
                  - yoghurt-ai
          topologyKey: kubernetes.io/hostname

tolerations:
  - key: "node.kubernetes.io/not-ready"
    operator: "Exists"
    effect: "NoExecute"
    tolerationSeconds: 300
  - key: "node.kubernetes.io/unreachable"
    operator: "Exists"
    effect: "NoExecute"
    tolerationSeconds: 300

# AWS configuration
aws:
  region: us-west-2
  s3:
    bucket: prod-yoghurt-ai-data
  ecr:
    registry: 123456789012.dkr.ecr.us-west-2.amazonaws.com

# Database configuration
postgresql:
  enabled: false  # Using external RDS
  external:
    host: prod-yoghurt-ai.cluster-xyz.us-west-2.rds.amazonaws.com
    port: 5432
    database: yoghurt_ai
    username: yoghurt_ai_user

# Redis configuration
redis:
  enabled: false  # Using external ElastiCache
  external:
    host: prod-yoghurt-ai.cache.amazonaws.com
    port: 6379

# Monitoring
monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
    interval: 30s
    scrapeTimeout: 10s
    path: /metrics
  
  prometheusRule:
    enabled: true
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
      
      - alert: HighLatency
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High latency detected"

# Logging
logging:
  enabled: true
  image:
    repository: fluent/fluentd-kubernetes-daemonset
    tag: v1.16-debian-elasticsearch7-1
    pullPolicy: IfNotPresent
  resources:
    limits:
      cpu: 200m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 256Mi

# Network Policies
networkPolicy:
  enabled: true
  ingress:
    - from:
        - namespaceSelector:
            matchLabels:
              name: ingress-nginx
      ports:
        - protocol: TCP
          port: 3000
    - from:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: yoghurt-ai
      ports:
        - protocol: TCP
          port: 50051
  egress:
    - to: []
      ports:
        - protocol: TCP
          port: 5432  # PostgreSQL
        - protocol: TCP
          port: 6379  # Redis
        - protocol: TCP
          port: 443   # HTTPS
        - protocol: TCP
          port: 53    # DNS
        - protocol: UDP
          port: 53    # DNS
```

---

## 4. GitOps工作流与ArgoCD配置

### 4.1 GitOps架构

```mermaid
graph TB
    subgraph "Development"
        DEV[Developer]
        IDE[IDE/Local]
    end
    
    subgraph "Source Control"
        GIT[Git Repository]
        PR[Pull Request]
        MAIN[Main Branch]
    end
    
    subgraph "CI Pipeline"
        BUILD[Build & Test]
        SCAN[Security Scan]
        PUSH[Push Images]
    end
    
    subgraph "GitOps Repository"
        CONFIG[Config Repo]
        MANIFEST[K8s Manifests]
        HELM[Helm Charts]
    end
    
    subgraph "ArgoCD"
        ARGO[ArgoCD Controller]
        SYNC[Auto Sync]
        HEALTH[Health Check]
    end
    
    subgraph "Kubernetes Clusters"
        DEV_CLUSTER[Dev Cluster]
        STAGING_CLUSTER[Staging Cluster]
        PROD_CLUSTER[Prod Cluster]
    end
    
    DEV --> IDE
    IDE --> GIT
    GIT --> PR
    PR --> BUILD
    BUILD --> SCAN
    SCAN --> PUSH
    PUSH --> CONFIG
    CONFIG --> MANIFEST
    MANIFEST --> HELM
    HELM --> ARGO
    ARGO --> SYNC
    SYNC --> HEALTH
    HEALTH --> DEV_CLUSTER
    HEALTH --> STAGING_CLUSTER
    HEALTH --> PROD_CLUSTER
    
    PR --> MAIN
    MAIN --> CONFIG
```

### 4.2 ArgoCD应用配置

#### 4.2.1 ArgoCD安装配置

```yaml
# argocd/install/argocd-values.yaml
argo-cd:
  global:
    image:
      tag: v2.8.4
  
  configs:
    params:
      server.insecure: false
      server.grpc.web: true
      application.instanceLabelKey: argocd.argoproj.io/instance
    
    cm:
      url: https://argocd.yoghurt-ai.com
      application.instanceLabelKey: argocd.argoproj.io/instance
      
      # OIDC configuration
      oidc.config: |
        name: OIDC
        issuer: https://auth.yoghurt-ai.com
        clientId: argocd
        clientSecret: $oidc.clientSecret
        requestedScopes: ["openid", "profile", "email", "groups"]
        requestedIDTokenClaims: {"groups": {"essential": true}}
      
      # Repository credentials
      repositories: |
        - type: git
          url: https://github.com/yoghurt-ai/k8s-manifests
        - type: helm
          url: https://charts.bitnami.com/bitnami
          name: bitnami
    
    rbac:
      policy.default: role:readonly
      policy.csv: |
        p, role:admin, applications, *, */*, allow
        p, role:admin, clusters, *, *, allow
        p, role:admin, repositories, *, *, allow
        
        p, role:developer, applications, get, */*, allow
        p, role:developer, applications, sync, dev/*, allow
        p, role:developer, applications, sync, staging/*, allow
        
        g, argocd-admins, role:admin
        g, platform-team, role:admin
        g, developers, role:developer
  
  server:
    ingress:
      enabled: true
      ingressClassName: nginx
      annotations:
        nginx.ingress.kubernetes.io/ssl-redirect: "true"
        nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
        cert-manager.io/cluster-issuer: "letsencrypt-prod"
        nginx.ingress.kubernetes.io/backend-protocol: "GRPC"
      hosts:
        - argocd.yoghurt-ai.com
      tls:
        - secretName: argocd-server-tls
          hosts:
            - argocd.yoghurt-ai.com
    
    resources:
      limits:
        cpu: 500m
        memory: 1Gi
      requests:
        cpu: 250m
        memory: 512Mi
  
  controller:
    resources:
      limits:
        cpu: 1000m
        memory: 2Gi
      requests:
        cpu: 500m
        memory: 1Gi
    
    metrics:
      enabled: true
      serviceMonitor:
        enabled: true
  
  repoServer:
    resources:
      limits:
        cpu: 500m
        memory: 1Gi
      requests:
        cpu: 250m
        memory: 512Mi
    
    metrics:
      enabled: true
      serviceMonitor:
        enabled: true
  
  redis:
    resources:
      limits:
        cpu: 200m
        memory: 256Mi
      requests:
        cpu: 100m
        memory: 128Mi
```