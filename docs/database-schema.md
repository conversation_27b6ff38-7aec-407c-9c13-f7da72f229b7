# Yoghurt AI QC - 数据库架构图

## 完整的实体关系图

```mermaid
erDiagram
    User ||--o{ Recipe : creates
    User ||--o{ Batch : manages
    User ||--o{ SensoryAssessment : evaluates
    User ||--o{ MicroscopeImage : uploads
    User ||--o{ QualityReport : generates
    User ||--o{ SystemLog : performs
    
    Recipe ||--o{ Batch : uses
    Batch ||--o{ SensoryAssessment : has
    Batch ||--o{ MicroscopeImage : contains
    Batch ||--o{ AIAnalysis : analyzes
    Batch ||--o{ QualityReport : produces
    
    MicroscopeImage ||--o{ AIAnalysis : generates
    
    User {
        uuid id PK
        string email UK "唯一邮箱"
        string password_hash "密码哈希"
        string name "用户姓名"
        enum role "ADMIN|USER|VIEWER"
        boolean is_active "账户状态"
        timestamp created_at
        timestamp updated_at
    }
    
    Recipe {
        uuid id PK
        uuid user_id FK
        string name "配方名称"
        text description "配方描述"
        jsonb ingredients "配料列表"
        jsonb process "制作步骤"
        decimal fermentation_temperature "发酵温度"
        integer fermentation_duration "发酵时长(小时)"
        integer filtration_duration "过滤时长(小时)"
        integer version "版本号"
        boolean is_active "是否启用"
        timestamp created_at
        timestamp updated_at
    }
    
    Batch {
        uuid id PK
        string batch_number UK "批次号"
        uuid recipe_id FK
        uuid user_id FK
        enum status "PLANNING|IN_PROGRESS|FERMENTING|FILTERING|COMPLETED|FAILED"
        date production_date "生产日期"
        decimal quantity "数量(L或kg)"
        integer actual_fermentation_duration "实际发酵时长"
        decimal actual_temperature "实际温度"
        text notes "备注"
        timestamp created_at
        timestamp updated_at
    }
    
    SensoryAssessment {
        uuid id PK
        uuid batch_id FK
        uuid assessor_id FK
        smallint texture_score "质地评分(1-5)"
        smallint acidity_score "酸度评分(1-5)"
        jsonb flavor_tags "风味标签"
        text flavor_notes "风味备注"
        smallint overall_score "综合评分(1-5)"
        text notes "评估备注"
        timestamp created_at
        timestamp updated_at
    }
    
    MicroscopeImage {
        uuid id PK
        uuid batch_id FK
        string filename "文件名"
        string original_name "原始文件名"
        string file_path "文件路径"
        integer file_size "文件大小(bytes)"
        string mime_type "MIME类型"
        integer magnification "放大倍数"
        string staining_method "染色方法"
        integer image_width "图像宽度"
        integer image_height "图像高度"
        jsonb metadata "图像元数据"
        uuid uploaded_by FK
        timestamp created_at
        timestamp updated_at
    }
    
    AIAnalysis {
        uuid id PK
        uuid batch_id FK
        uuid microscope_image_id FK
        enum analysis_status "PENDING|PROCESSING|COMPLETED|FAILED|CANCELLED"
        decimal quality_score "质量评分(0-100)"
        integer bacterial_count "细菌计数"
        boolean contamination_detected "是否检测到污染"
        string contamination_type "污染类型"
        jsonb morphology_analysis "形态学分析"
        decimal confidence_score "置信度(0-100)"
        text summary_recommendation "总结和建议"
        integer processing_time_ms "处理时间(毫秒)"
        string model_version "模型版本"
        string api_provider "API提供商"
        jsonb raw_response "原始AI响应"
        text error_message "错误信息"
        integer retry_count "重试次数"
        timestamp created_at
        timestamp updated_at
    }
    
    QualityReport {
        uuid id PK
        uuid batch_id FK
        enum report_type "QUALITY_SUMMARY|BATCH_ANALYSIS|TREND_ANALYSIS|CONTAMINATION_REPORT|CUSTOM"
        string title "报告标题"
        text description "报告描述"
        jsonb date_range "日期范围"
        jsonb filters "筛选条件"
        jsonb report_data "报告数据"
        uuid generated_by FK
        boolean is_public "是否公开"
        timestamp created_at
        timestamp updated_at
    }
    
    SystemLog {
        uuid id PK
        uuid user_id FK
        string action "操作类型"
        string resource_type "资源类型"
        uuid resource_id "资源ID"
        jsonb details "详细信息"
        inet ip_address "IP地址"
        text user_agent "用户代理"
        timestamp created_at
    }
```

## 数据流程图

```mermaid
flowchart TD
    A[用户登录] --> B[创建配方]
    B --> C[开始新批次]
    C --> D[生产酸奶]
    D --> E[感官评估]
    D --> F[显微镜拍照]
    F --> G[上传图像]
    G --> H[AI分析]
    H --> I[生成分析报告]
    E --> J[记录评估结果]
    I --> K[质量报告]
    J --> K
    K --> L[数据看板]
    
    style A fill:#e1f5fe
    style H fill:#fff3e0
    style K fill:#f3e5f5
    style L fill:#e8f5e8
```

## 索引设计图

```mermaid
graph TD
    subgraph "用户表索引"
        U1[email - 唯一索引]
        U2[role - 普通索引]
        U3[is_active - 普通索引]
        U4[created_at - 普通索引]
    end
    
    subgraph "批次表索引"
        B1[batch_number - 唯一索引]
        B2[recipe_id - 外键索引]
        B3[user_id - 外键索引]
        B4[status - 普通索引]
        B5[production_date - 普通索引]
        B6[user_id + status - 组合索引]
        B7[recipe_id + status - 组合索引]
    end
    
    subgraph "AI分析表索引"
        A1[batch_id - 外键索引]
        A2[analysis_status - 普通索引]
        A3[quality_score - 普通索引]
        A4[contamination_detected - 普通索引]
        A5[batch_id + analysis_status - 组合索引]
    end
    
    style U1 fill:#ffcdd2
    style B1 fill:#ffcdd2
    style B6 fill:#c8e6c9
    style B7 fill:#c8e6c9
    style A5 fill:#c8e6c9
```

## 数据类型说明

### 枚举类型

```sql
-- 用户角色
CREATE TYPE user_role AS ENUM ('ADMIN', 'USER', 'VIEWER');

-- 批次状态
CREATE TYPE batch_status AS ENUM (
    'PLANNING', 'IN_PROGRESS', 'FERMENTING', 
    'FILTERING', 'COMPLETED', 'FAILED'
);

-- 分析状态
CREATE TYPE analysis_status AS ENUM (
    'PENDING', 'PROCESSING', 'COMPLETED', 
    'FAILED', 'CANCELLED'
);

-- 报告类型
CREATE TYPE report_type AS ENUM (
    'QUALITY_SUMMARY', 'BATCH_ANALYSIS', 
    'TREND_ANALYSIS', 'CONTAMINATION_REPORT', 'CUSTOM'
);
```

### JSONB 字段结构

#### 配方配料 (Recipe.ingredients)
```json
[
  {
    "name": "全脂牛奶",
    "amount": 1000,
    "unit": "ml"
  },
  {
    "name": "酸奶菌粉", 
    "amount": 1,
    "unit": "包"
  }
]
```

#### 制作步骤 (Recipe.process)
```json
[
  {
    "step": 1,
    "description": "牛奶加热至85°C杀菌",
    "duration": 30,
    "temperature": 85
  },
  {
    "step": 2,
    "description": "冷却至43°C",
    "duration": 60,
    "temperature": 43
  }
]
```

#### 形态学分析 (AIAnalysis.morphology_analysis)
```json
{
  "bacterial_morphology": "清晰可见杆状乳酸菌和链状球菌",
  "balance_ratio": "乳酸杆菌与链球菌比例约为 1.2:1",
  "activity_level": "菌体饱满，分布均匀，活性良好"
}
```

## 性能考虑

### 查询优化
1. **分页查询**: 使用 LIMIT 和 OFFSET
2. **索引覆盖**: 常用查询字段建立组合索引
3. **JSONB查询**: 对频繁查询的JSONB字段考虑GIN索引

### 存储优化
1. **UUID vs 自增ID**: UUID保证全局唯一，便于分布式
2. **JSONB vs 关系表**: 灵活性vs查询性能的权衡
3. **文件存储**: 图像文件存储在文件系统，数据库仅存路径

### 扩展性考虑
1. **水平分片**: 按用户或时间分片
2. **读写分离**: 主从复制提高读性能
3. **缓存策略**: Redis缓存热点数据
