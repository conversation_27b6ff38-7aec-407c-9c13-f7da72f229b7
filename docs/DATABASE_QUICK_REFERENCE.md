# Yoghurt AI QC - 数据库快速参考

**更新日期**: 2025年7月19日  
**状态**: ✅ 运行正常

---

## 🚀 快速启动

```bash
# 启动数据库管理界面
cd backend && bash start-studio.sh

# 访问地址
http://localhost:5555
```

## 📊 当前状态

| 项目 | 状态 | 数量 |
|------|------|------|
| 数据表 | ✅ 正常 | 8个 |
| 总记录数 | ✅ 正常 | 14条 |
| 测试用户 | ✅ 可用 | 4个 |
| 示例配方 | ✅ 可用 | 3个 |

## 🔑 测试账户

| 邮箱 | 密码 | 角色 |
|------|------|------|
| <EMAIL> | password123 | ADMIN |
| <EMAIL> | password123 | USER |
| <EMAIL> | password123 | USER |
| <EMAIL> | password123 | VIEWER |

## 🛠️ 常用命令

### 状态检查
```bash
npm run db:init:status      # 数据库状态
npm run migrate:status      # 迁移状态
npm run db:seed:status      # 数据统计
```

### 数据操作
```bash
npm run db:seed:test-users    # 创建测试用户
npm run db:seed:sample-data   # 创建示例数据
npm run db:backup            # 备份数据库
```

### 重置操作（谨慎使用）
```bash
npm run db:init:reset        # 完全重置数据库
```

## 🗄️ 数据表概览

| 表名 | 记录数 | 用途 |
|------|--------|------|
| users | 4 | 用户管理 |
| recipes | 3 | 配方管理 |
| batches | 3 | 批次管理 |
| sensory_assessments | 2 | 感官评估 |
| microscope_images | 0 | 显微镜图像 |
| ai_analyses | 2 | AI分析结果 |
| quality_reports | 0 | 质量报告 |
| system_logs | 0 | 系统日志 |

## 🔧 故障排除

### 连接问题
```bash
# 检查Docker容器
docker ps | grep postgres

# 重启数据库
docker-compose restart postgres
```

### Prisma Studio问题
```bash
# 使用专用脚本启动
cd backend && bash start-studio.sh

# 手动指定环境变量
DATABASE_URL=postgresql://postgres:postgres@localhost:6432/postgres npx prisma studio
```

## 📞 支持信息

- **技术文档**: `docs/DATABASE_STATUS_REPORT.md`
- **架构图**: `docs/database-schema.md`
- **设计文档**: `backend/DATABASE_DESIGN.md`

---

**最后更新**: 2025年7月19日 19:05
