# 端口配置说明

## 🔌 端口分配表

为了避免与本机其他项目冲突，本项目所有端口都使用6开头的配置。

### 主要服务端口

| 服务 | 本地端口 | 容器内端口 | 访问地址 | 说明 |
|------|----------|------------|----------|------|
| **前端应用** | 6173 | 5173 | http://localhost:6173 | React + Vite 开发服务器 |
| **后端API** | 6000 | 3000 | http://localhost:6000 | Node.js + Express API |
| **AI服务** | 6800 | 8000 | http://localhost:6800 | Python + FastAPI |
| **PostgreSQL** | 6432 | 5432 | localhost:6432 | 主数据库 |
| **Redis** | 6479 | 6379 | localhost:6479 | 缓存和会话存储 |

### 监控和工具端口

| 服务 | 端口 | 访问地址 | 说明 |
|------|------|----------|------|
| **Prometheus** | 6090 | http://localhost:6090 | 指标监控 |
| **负载均衡器** | 6080 | http://localhost:6080 | 生产环境负载均衡 |

### 重要访问地址

- **应用主页**: http://localhost:6173
- **API文档**: http://localhost:6000/api/docs
- **AI服务文档**: http://localhost:6800/docs
- **健康检查**: 
  - 后端: http://localhost:6000/health
  - AI服务: http://localhost:6800/health

## 🔧 配置文件位置

### 环境变量配置
- **模板文件**: `.env.example`
- **实际配置**: `.env`

### 关键配置项
```env
# 应用端口
APP_PORT=6000
VITE_PORT=6173
AI_SERVICE_PORT=6800

# 数据库端口
DB_PORT=6432
REDIS_PORT=6479

# 监控端口
METRICS_PORT=6090

# 前端API地址
VITE_API_URL=http://localhost:6000
VITE_AI_SERVICE_URL=http://localhost:6800

# CORS配置
CORS_ORIGIN=http://localhost:6173,http://localhost:6000
```

## 🐳 Docker 端口映射

```yaml
services:
  postgres:
    ports:
      - "6432:5432"  # 本地6432 -> 容器5432
  
  redis:
    ports:
      - "6479:6379"  # 本地6479 -> 容器6379
  
  backend:
    ports:
      - "6000:3000"  # 本地6000 -> 容器3000
  
  ai-service:
    ports:
      - "6800:8000"  # 本地6800 -> 容器8000
  
  frontend:
    ports:
      - "6173:5173"  # 本地6173 -> 容器5173
```

## 🚀 启动命令

```bash
# 启动所有服务
npm run dev

# 分别启动服务
npm run dev:frontend    # 前端 (6173)
npm run dev:backend     # 后端 (6000)
npm run dev:ai          # AI服务 (6800)
npm run docker:up       # 数据库服务 (6432, 6479)
```

## 🔍 端口检查

检查端口是否被占用：

```bash
# 检查所有6开头的端口
lsof -i :6000
lsof -i :6173
lsof -i :6432
lsof -i :6479
lsof -i :6800

# 或者使用 netstat
netstat -an | grep 6000
netstat -an | grep 6173
netstat -an | grep 6432
netstat -an | grep 6479
netstat -an | grep 6800
```

## 🛠️ 故障排除

### 端口冲突
如果仍然遇到端口冲突，可以修改以下文件：

1. **`.env`** - 修改环境变量
2. **`docker-compose.yml`** - 修改Docker端口映射
3. **`frontend/vite.config.ts`** - 修改前端开发服务器端口
4. **`backend/src/config/index.ts`** - 修改后端默认端口
5. **`ai-service/src/config.py`** - 修改AI服务默认端口

### 服务无法启动
1. 检查端口是否被占用
2. 检查Docker是否正常运行
3. 检查环境变量配置是否正确
4. 查看服务日志：`docker-compose logs -f [service-name]`

### 网络连接问题
1. 确认防火墙设置
2. 检查CORS配置
3. 验证代理设置（如果使用）

## 📝 注意事项

1. **开发环境**: 所有端口都配置为6开头，避免冲突
2. **生产环境**: 建议使用标准端口（80, 443）配合反向代理
3. **安全性**: 生产环境请修改所有默认密码和密钥
4. **监控**: 启用Prometheus监控时注意6090端口
5. **备份**: 数据库备份任务会在凌晨2点自动运行

## 🔄 端口变更流程

如需修改端口配置：

1. 更新 `.env` 文件中的端口配置
2. 修改 `docker-compose.yml` 中的端口映射
3. 更新相关配置文件中的默认值
4. 重启所有服务
5. 更新文档中的端口说明

---

**最后更新**: 2024年7月18日
**维护者**: Yogurt AI QC 开发团队
