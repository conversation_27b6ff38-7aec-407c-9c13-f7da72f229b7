# 🚀 Yogurt AI QC - 环境自动激活指南

## ✅ 问题已解决

您的项目现在已经配置了自动环境激活功能！

## 🔧 配置状态

### 已配置的组件：
- ✅ **direnv**: 已安装并配置
- ✅ **zsh hook**: 已正确设置
- ✅ **.envrc**: 已配置并允许
- ✅ **conda环境**: yoghurt-ai-qc 环境存在
- ✅ **环境变量**: .env 文件已配置

## 🎯 自动激活功能

当您进入项目目录时，以下操作会自动执行：

1. **激活conda环境**: `yoghurt-ai-qc`
2. **加载环境变量**: 从 `.env` 和 `.env.local` 文件
3. **设置项目变量**: 
   - `PROJECT_ROOT`
   - `AI_SERVICE_PORT=6800`
   - `FRONTEND_PORT=6174`
   - `BACKEND_PORT=3010`
4. **验证关键组件**: FastAPI, OpenAI, OpenCV, NumPy

## 🧪 验证自动激活

打开新的终端窗口并运行：

```bash
# 进入项目目录
cd /Volumes/acasis/yoghurt

# 检查环境
echo "当前环境: $CONDA_DEFAULT_ENV"
echo "Python路径: $(which python)"
echo "项目根目录: $PROJECT_ROOT"
```

您应该看到：
- 环境自动激活的消息
- `CONDA_DEFAULT_ENV` 显示为 `yoghurt-ai-qc`
- Python路径指向conda环境
- 项目变量已设置

## 🔄 手动激活（备用方案）

如果自动激活不工作，您可以手动激活：

```bash
# 使用项目激活脚本
source ./activate.sh

# 或者手动激活conda环境
conda activate yoghurt-ai-qc
```

## 🛠️ 故障排除

### 如果自动激活不工作：

1. **检查direnv状态**:
   ```bash
   direnv status
   ```

2. **重新允许.envrc**:
   ```bash
   direnv allow
   ```

3. **检查zsh配置**:
   ```bash
   grep "direnv hook" ~/.zshrc
   ```

4. **重新加载shell配置**:
   ```bash
   source ~/.zshrc
   ```

### 常见问题：

- **环境不自动切换**: 确保direnv hook在~/.zshrc中
- **权限问题**: 运行 `direnv allow` 允许.envrc文件
- **conda环境不存在**: 运行 `./scripts/create-latest-env.sh`

## 📋 下一步

现在您可以：

1. **启动开发服务器**:
   ```bash
   npm run dev        # 启动所有服务
   npm run dev:ai     # 仅启动AI服务
   ```

2. **运行测试**:
   ```bash
   npm test
   ```

3. **查看环境信息**:
   ```bash
   conda info
   ```

## 🎉 完成！

您的开发环境现在已经完全配置好了，每次进入项目目录都会自动激活正确的conda环境和加载所需的环境变量。
